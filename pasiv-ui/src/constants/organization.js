/**
 * Константы для работы с организациями
 */

// Статусы организаций
export const ORGANIZATION_STATUS = {
    ACTIVE: 'active',
    INACTIVE: 'inactive'
};

// Метки статусов для отображения
export const ORGANIZATION_STATUS_LABELS = {
    [ORGANIZATION_STATUS.ACTIVE]: 'Активная',
    [ORGANIZATION_STATUS.INACTIVE]: 'Неактивная'
};

// Цвета статусов для PrimeVue компонентов
export const ORGANIZATION_STATUS_SEVERITIES = {
    [ORGANIZATION_STATUS.ACTIVE]: 'success',
    [ORGANIZATION_STATUS.INACTIVE]: 'secondary'
};

// Статусы синхронизации
export const SYNC_STATUS = {
    NEVER: 'never',
    SYNCED: 'synced',
    ERROR: 'error',
    PENDING: 'pending'
};

// Метки статусов синхронизации
export const SYNC_STATUS_LABELS = {
    [SYNC_STATUS.NEVER]: 'Не синхронизировано',
    [SYNC_STATUS.SYNCED]: 'Синхронизировано',
    [SYNC_STATUS.ERROR]: 'Ошибка синхронизации',
    [SYNC_STATUS.PENDING]: 'Ожидает синхронизации'
};

// Цвета статусов синхронизации
export const SYNC_STATUS_SEVERITIES = {
    [SYNC_STATUS.NEVER]: 'secondary',
    [SYNC_STATUS.SYNCED]: 'success',
    [SYNC_STATUS.ERROR]: 'danger',
    [SYNC_STATUS.PENDING]: 'warning'
};

// Типы организаций
export const ORGANIZATION_TYPE = {
    ORGANIZATION: 'organization',
    INDIVIDUAL_ENTREPRENEUR: 'individual_entrepreneur'
};

// Метки типов организаций
export const ORGANIZATION_TYPE_LABELS = {
    [ORGANIZATION_TYPE.ORGANIZATION]: 'Организация',
    [ORGANIZATION_TYPE.INDIVIDUAL_ENTREPRENEUR]: 'Индивидуальный предприниматель'
};

// Формы собственности
export const OWNERSHIP_FORMS = [
    'ООО',
    'ОАО',
    'ЗАО',
    'ПАО',
    'АО',
    'ИП',
    'ГУП',
    'МУП',
    'НКО',
    'Фонд',
    'Учреждение'
];

// Типы адресов
export const ADDRESS_TYPE = {
    LEGAL: 'legal',
    ACTUAL: 'actual',
    MAILING: 'mailing'
};

// Метки типов адресов
export const ADDRESS_TYPE_LABELS = {
    [ADDRESS_TYPE.LEGAL]: 'Юридический адрес',
    [ADDRESS_TYPE.ACTUAL]: 'Фактический адрес',
    [ADDRESS_TYPE.MAILING]: 'Почтовый адрес'
};

// Типы контактов
export const CONTACT_TYPE = {
    PHONE: 'phone',
    EMAIL: 'email'
};

// Метки типов контактов
export const CONTACT_TYPE_LABELS = {
    [CONTACT_TYPE.PHONE]: 'Телефон',
    [CONTACT_TYPE.EMAIL]: 'Email'
};

// Регулярные выражения для валидации
export const VALIDATION_PATTERNS = {
    INN: /^\d{10}$|^\d{12}$/,
    KPP: /^\d{9}$/,
    OGRN: /^\d{13}$|^\d{15}$/,
    OKPO: /^\d{8}$|^\d{10}$/,
    PHONE: /^(\+7|8)?[\s\-]?\(?[489][0-9]{2}\)?[\s\-]?[0-9]{3}[\s\-]?[0-9]{2}[\s\-]?[0-9]{2}$/,
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
};

// Сообщения об ошибках валидации
export const VALIDATION_MESSAGES = {
    REQUIRED: 'Поле обязательно для заполнения',
    INN_INVALID: 'ИНН должен содержать 10 или 12 цифр',
    KPP_INVALID: 'КПП должен содержать 9 цифр',
    OGRN_INVALID: 'ОГРН должен содержать 13 или 15 цифр',
    OKPO_INVALID: 'ОКПО должен содержать 8 или 10 цифр',
    PHONE_INVALID: 'Некорректный формат телефона',
    EMAIL_INVALID: 'Некорректный формат email'
};

// Опции для выпадающих списков
export const STATUS_OPTIONS = Object.entries(ORGANIZATION_STATUS_LABELS).map(([value, label]) => ({
    label,
    value
}));

export const SYNC_STATUS_OPTIONS = Object.entries(SYNC_STATUS_LABELS).map(([value, label]) => ({
    label,
    value
}));

export const TYPE_OPTIONS = Object.entries(ORGANIZATION_TYPE_LABELS).map(([value, label]) => ({
    label,
    value
}));

export const OWNERSHIP_FORM_OPTIONS = OWNERSHIP_FORMS.map(form => ({
    label: form,
    value: form
}));

// Функции-помощники
export const getStatusLabel = (status) => ORGANIZATION_STATUS_LABELS[status] || status;
export const getStatusSeverity = (status) => ORGANIZATION_STATUS_SEVERITIES[status] || 'secondary';

export const getSyncStatusLabel = (status) => SYNC_STATUS_LABELS[status] || status;
export const getSyncStatusSeverity = (status) => SYNC_STATUS_SEVERITIES[status] || 'secondary';

export const getTypeLabel = (type) => ORGANIZATION_TYPE_LABELS[type] || type;

export const getAddressTypeLabel = (type) => ADDRESS_TYPE_LABELS[type] || type;

export const getContactTypeLabel = (type) => CONTACT_TYPE_LABELS[type] || type;

// Валидация полей
export const validateINN = (inn) => {
    if (!inn) return VALIDATION_MESSAGES.REQUIRED;
    if (!VALIDATION_PATTERNS.INN.test(inn)) return VALIDATION_MESSAGES.INN_INVALID;
    return null;
};

export const validateKPP = (kpp) => {
    if (kpp && !VALIDATION_PATTERNS.KPP.test(kpp)) return VALIDATION_MESSAGES.KPP_INVALID;
    return null;
};

export const validateOGRN = (ogrn) => {
    if (!ogrn) return VALIDATION_MESSAGES.REQUIRED;
    if (!VALIDATION_PATTERNS.OGRN.test(ogrn)) return VALIDATION_MESSAGES.OGRN_INVALID;
    return null;
};

export const validateOKPO = (okpo) => {
    if (okpo && !VALIDATION_PATTERNS.OKPO.test(okpo)) return VALIDATION_MESSAGES.OKPO_INVALID;
    return null;
};

export const validatePhone = (phone) => {
    if (phone && !VALIDATION_PATTERNS.PHONE.test(phone)) return VALIDATION_MESSAGES.PHONE_INVALID;
    return null;
};

export const validateEmail = (email) => {
    if (email && !VALIDATION_PATTERNS.EMAIL.test(email)) return VALIDATION_MESSAGES.EMAIL_INVALID;
    return null;
};

// Экспорт всех констант одним объектом
export default {
    ORGANIZATION_STATUS,
    ORGANIZATION_STATUS_LABELS,
    ORGANIZATION_STATUS_SEVERITIES,
    SYNC_STATUS,
    SYNC_STATUS_LABELS,
    SYNC_STATUS_SEVERITIES,
    ORGANIZATION_TYPE,
    ORGANIZATION_TYPE_LABELS,
    OWNERSHIP_FORMS,
    ADDRESS_TYPE,
    ADDRESS_TYPE_LABELS,
    CONTACT_TYPE,
    CONTACT_TYPE_LABELS,
    VALIDATION_PATTERNS,
    VALIDATION_MESSAGES,
    STATUS_OPTIONS,
    SYNC_STATUS_OPTIONS,
    TYPE_OPTIONS,
    OWNERSHIP_FORM_OPTIONS,
    getStatusLabel,
    getStatusSeverity,
    getSyncStatusLabel,
    getSyncStatusSeverity,
    getTypeLabel,
    getAddressTypeLabel,
    getContactTypeLabel,
    validateINN,
    validateKPP,
    validateOGRN,
    validateOKPO,
    validatePhone,
    validateEmail
};
