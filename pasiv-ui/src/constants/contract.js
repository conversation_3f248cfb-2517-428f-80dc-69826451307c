/**
 * Константы для работы с договорами
 */

// Статусы договоров
export const CONTRACT_STATUS = {
    DRAFT: 'draft',
    ACTIVE: 'active',
    EXPIRING: 'expiring',
    COMPLETED: 'completed',
    TERMINATED: 'terminated'
};

// Типы договоров
export const CONTRACT_TYPE = {
    SYSTEM_RULES: 'system_rules',
    SERVICE: 'service',
    TRANSPORT: 'transport',
    PROCESSING: 'processing'
};

// Типы проектов
export const PROJECT_TYPE = {
    TRANSPORT_SYSTEM: 'transport_system',
    METRO_SYSTEM: 'metro_system',
    BUS_SYSTEM: 'bus_system',
    TAXI_SYSTEM: 'taxi_system'
};

// Роли организаций в договоре
export const ORGANIZATION_ROLE = {
    OPERATOR: 'operator',
    CARRIER: 'carrier',
    PROCESSING_CENTER: 'processing_center',
    CONTRACTOR: 'contractor',
    PARTNER: 'partner'
};

// Статусы синхронизации
export const SYNC_STATUS = {
    NEVER: 'never',
    PENDING: 'pending',
    SYNCED: 'synced',
    ERROR: 'error'
};

// Опции для выпадающих списков
export const CONTRACT_STATUS_OPTIONS = [
    { label: 'Черновик', value: CONTRACT_STATUS.DRAFT, severity: 'secondary' },
    { label: 'Активный', value: CONTRACT_STATUS.ACTIVE, severity: 'success' },
    { label: 'Истекает', value: CONTRACT_STATUS.EXPIRING, severity: 'warning' },
    { label: 'Завершен', value: CONTRACT_STATUS.COMPLETED, severity: 'info' },
    { label: 'Расторгнут', value: CONTRACT_STATUS.TERMINATED, severity: 'danger' }
];

export const CONTRACT_TYPE_OPTIONS = [
    { label: 'Правила системы', value: CONTRACT_TYPE.SYSTEM_RULES },
    { label: 'Сервисный договор', value: CONTRACT_TYPE.SERVICE },
    { label: 'Транспортный договор', value: CONTRACT_TYPE.TRANSPORT },
    { label: 'Процессинговый договор', value: CONTRACT_TYPE.PROCESSING }
];

export const PROJECT_TYPE_OPTIONS = [
    { label: 'Транспортная система', value: PROJECT_TYPE.TRANSPORT_SYSTEM },
    { label: 'Система метро', value: PROJECT_TYPE.METRO_SYSTEM },
    { label: 'Автобусная система', value: PROJECT_TYPE.BUS_SYSTEM },
    { label: 'Система такси', value: PROJECT_TYPE.TAXI_SYSTEM }
];

export const ORGANIZATION_ROLE_OPTIONS = [
    { label: 'Оператор', value: ORGANIZATION_ROLE.OPERATOR, description: 'Оператор - организатор ТКП' },
    { label: 'Перевозчик', value: ORGANIZATION_ROLE.CARRIER, description: 'Транспортная компания' },
    { label: 'Процессинговый центр', value: ORGANIZATION_ROLE.PROCESSING_CENTER, description: 'Обработка платежей' },
    { label: 'Контрагент', value: ORGANIZATION_ROLE.CONTRACTOR, description: 'Подрядчик' },
    { label: 'Партнер', value: ORGANIZATION_ROLE.PARTNER, description: 'Партнерская организация' }
];

export const SYNC_STATUS_OPTIONS = [
    { label: 'Не синхронизировался', value: SYNC_STATUS.NEVER, severity: 'secondary' },
    { label: 'Ожидает синхронизации', value: SYNC_STATUS.PENDING, severity: 'warning' },
    { label: 'Синхронизирован', value: SYNC_STATUS.SYNCED, severity: 'success' },
    { label: 'Ошибка синхронизации', value: SYNC_STATUS.ERROR, severity: 'danger' }
];

// Валюты
export const CURRENCY_OPTIONS = [
    { label: 'Российский рубль (₽)', value: 'RUB' },
    { label: 'Доллар США ($)', value: 'USD' },
    { label: 'Евро (€)', value: 'EUR' }
];

// Ставки НДС
export const VAT_RATE_OPTIONS = [
    { label: 'Без НДС (0%)', value: 0 },
    { label: 'НДС 10%', value: 10 },
    { label: 'НДС 20%', value: 20 }
];

// Условия оплаты (дни)
export const PAYMENT_TERMS_OPTIONS = [
    { label: 'Предоплата', value: 0 },
    { label: '10 дней', value: 10 },
    { label: '15 дней', value: 15 },
    { label: '30 дней', value: 30 },
    { label: '45 дней', value: 45 },
    { label: '60 дней', value: 60 },
    { label: '90 дней', value: 90 }
];

// Правила валидации
export const CONTRACT_VALIDATION_RULES = {
    projectCode: {
        required: true,
        minLength: 3,
        maxLength: 20,
        pattern: /^[A-Z0-9-]+$/,
        message: 'Код проекта должен содержать только заглавные буквы, цифры и дефисы'
    },
    projectName: {
        required: true,
        minLength: 5,
        maxLength: 200,
        message: 'Название проекта должно быть от 5 до 200 символов'
    },
    contractName: {
        required: true,
        minLength: 10,
        maxLength: 500,
        message: 'Название договора должно быть от 10 до 500 символов'
    },
    contractNumber: {
        required: true,
        minLength: 5,
        maxLength: 50,
        pattern: /^[A-ZА-Я0-9-\/]+$/i,
        message: 'Номер договора может содержать буквы, цифры, дефисы и слеши'
    },
    totalAmount: {
        required: false,
        min: 0,
        max: 999999999999.99,
        message: 'Сумма должна быть положительным числом'
    },
    description: {
        required: false,
        maxLength: 2000,
        message: 'Описание не должно превышать 2000 символов'
    },
    paymentTerms: {
        required: false,
        min: 0,
        max: 365,
        message: 'Условия оплаты должны быть от 0 до 365 дней'
    },
    vatRate: {
        required: false,
        min: 0,
        max: 100,
        message: 'Ставка НДС должна быть от 0 до 100%'
    }
};

// Форматы экспорта
export const EXPORT_FORMATS = [
    { label: 'Excel (.xlsx)', value: 'excel', icon: 'pi pi-file-excel' },
    { label: 'PDF (.pdf)', value: 'pdf', icon: 'pi pi-file-pdf' },
    { label: 'CSV (.csv)', value: 'csv', icon: 'pi pi-file' }
];

// Поля для экспорта
export const EXPORT_FIELDS = [
    { label: 'Код проекта', value: 'projectCode', default: true },
    { label: 'Название проекта', value: 'projectName', default: true },
    { label: 'Номер договора', value: 'contractNumber', default: true },
    { label: 'Название договора', value: 'contractName', default: true },
    { label: 'Тип договора', value: 'contractType', default: true },
    { label: 'Статус', value: 'status', default: true },
    { label: 'Дата подписания', value: 'signatureDate', default: true },
    { label: 'Дата заключения', value: 'conclusionDate', default: false },
    { label: 'Дата завершения', value: 'completionDate', default: true },
    { label: 'Сумма договора', value: 'totalAmount', default: true },
    { label: 'Валюта', value: 'currency', default: false },
    { label: 'Условия оплаты', value: 'paymentTerms', default: false },
    { label: 'Ставка НДС', value: 'vatRate', default: false },
    { label: 'Внешний ID 1С', value: 'externalId1C', default: false },
    { label: 'Описание', value: 'description', default: false },
    { label: 'Дата создания', value: 'createdDate', default: false },
    { label: 'Последняя синхронизация', value: 'lastSyncDate', default: false }
];

// Настройки таблицы по умолчанию
export const DEFAULT_TABLE_SETTINGS = {
    columns: [
        'contractNumber',
        'contractName',
        'projectName',
        'status',
        'signatureDate',
        'totalAmount',
        'actions'
    ],
    pageSize: 20,
    sortField: 'createdDate',
    sortOrder: 'desc'
};

// Иконки для типов договоров
export const CONTRACT_TYPE_ICONS = {
    [CONTRACT_TYPE.SYSTEM_RULES]: 'pi pi-cog',
    [CONTRACT_TYPE.SERVICE]: 'pi pi-wrench',
    [CONTRACT_TYPE.TRANSPORT]: 'pi pi-car',
    [CONTRACT_TYPE.PROCESSING]: 'pi pi-credit-card'
};

// Иконки для статусов договоров
export const CONTRACT_STATUS_ICONS = {
    [CONTRACT_STATUS.DRAFT]: 'pi pi-file-edit',
    [CONTRACT_STATUS.ACTIVE]: 'pi pi-check-circle',
    [CONTRACT_STATUS.EXPIRING]: 'pi pi-exclamation-triangle',
    [CONTRACT_STATUS.COMPLETED]: 'pi pi-check',
    [CONTRACT_STATUS.TERMINATED]: 'pi pi-times-circle'
};

// Цвета для статусов
export const CONTRACT_STATUS_COLORS = {
    [CONTRACT_STATUS.DRAFT]: '#6c757d',
    [CONTRACT_STATUS.ACTIVE]: '#28a745',
    [CONTRACT_STATUS.EXPIRING]: '#ffc107',
    [CONTRACT_STATUS.COMPLETED]: '#17a2b8',
    [CONTRACT_STATUS.TERMINATED]: '#dc3545'
};

// Настройки уведомлений
export const NOTIFICATION_SETTINGS = {
    expiringDays: 30, // За сколько дней до истечения уведомлять
    syncErrorRetries: 3, // Количество попыток синхронизации
    autoSyncInterval: 3600000, // Интервал автосинхронизации (1 час)
    maxNotifications: 50 // Максимальное количество уведомлений
};

// Лимиты и ограничения
export const CONTRACT_LIMITS = {
    maxOrganizations: 10, // Максимальное количество организаций в договоре
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['.pdf', '.doc', '.docx', '.xls', '.xlsx'],
    maxDescriptionLength: 2000,
    maxContractNameLength: 500,
    maxProjectNameLength: 200
};

// Сообщения об ошибках
export const ERROR_MESSAGES = {
    REQUIRED_FIELD: 'Поле обязательно для заполнения',
    INVALID_FORMAT: 'Неверный формат данных',
    INVALID_DATE_RANGE: 'Дата окончания должна быть позже даты начала',
    DUPLICATE_CONTRACT_NUMBER: 'Договор с таким номером уже существует',
    ORGANIZATION_REQUIRED: 'Необходимо добавить хотя бы одну организацию',
    SYNC_ERROR: 'Ошибка синхронизации с внешней системой',
    NETWORK_ERROR: 'Ошибка сети. Проверьте подключение к интернету',
    PERMISSION_DENIED: 'Недостаточно прав для выполнения операции',
    CONTRACT_NOT_FOUND: 'Договор не найден',
    INVALID_PROJECT_CODE: 'Неверный код проекта'
};

// Сообщения об успехе
export const SUCCESS_MESSAGES = {
    CONTRACT_CREATED: 'Договор успешно создан',
    CONTRACT_UPDATED: 'Договор успешно обновлен',
    CONTRACT_DELETED: 'Договор успешно удален',
    SYNC_COMPLETED: 'Синхронизация завершена успешно',
    EXPORT_COMPLETED: 'Экспорт данных завершен',
    TEMPLATE_SAVED: 'Шаблон договора сохранен'
};

// Конфигурация по умолчанию
export const DEFAULT_CONFIG = {
    currency: 'RUB',
    vatRate: 20,
    paymentTerms: 30,
    enableSync: true,
    enableTemplates: true,
    enableAudit: true,
    enableNotifications: true
};
