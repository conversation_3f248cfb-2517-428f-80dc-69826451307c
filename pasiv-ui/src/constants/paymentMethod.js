/**
 * Константы и утилиты для средств оплаты
 */

/**
 * Типы средств оплаты с названиями
 */
export const PAYMENT_METHOD_TYPES = {
    BANK_CARD: 'Банковская карта',
    CASH: 'Наличные денежные средства',
    TROIKA_SINGLE: 'Транспортная карта "Тройка" (разовые поездки)',
    TROIKA_SUBSCRIPTION: 'Транспортная карта "Тройка" (абонемент)',
    MPC_DISCOUNT: 'МПК Дисконт',
    MPC_SOCIAL: 'МПК Социальная карта',
    MPC_SCHOOL: 'М<PERSON>К "Карта Школьника"',
    MPC_STUDENT_SINGLE: 'МПК "Карта Студента" (разовые поездки)',
    MPC_STUDENT_SUBSCRIPTION: 'МПК "Карта Студента" (абонемент)',
    TC_RESIDENT: 'ТК Карта жителя',
    MOBILE_BC: 'Мобильное приложение БК',
    MOBILE_VIRTUAL_TC: 'Мобильное приложение Виртуальная ТК',
    MOBILE_SBP: 'Мобильное приложение СБП',
    REGIONAL_TC: 'Транспортная карта региона',
    SOCIAL_TC: 'Социальная транспортная карта',
    OTHER_CARDS: 'Иные карты, предусмотренные договором'
};

/**
 * Категории средств оплаты
 */
export const PAYMENT_METHOD_CATEGORIES = {
    bank_cards: {
        name: 'Банковские карты',
        description: 'Банковские карты и платежные системы',
        icon: 'pi-credit-card',
        color: '#3b82f6',
        types: ['BANK_CARD']
    },
    cash: {
        name: 'Наличные',
        description: 'Наличные денежные средства',
        icon: 'pi-money-bill',
        color: '#10b981',
        types: ['CASH']
    },
    transport_cards: {
        name: 'Транспортные карты',
        description: 'Транспортные карты и проездные',
        icon: 'pi-id-card',
        color: '#f59e0b',
        types: ['TROIKA_SINGLE', 'TROIKA_SUBSCRIPTION', 'TC_RESIDENT', 'REGIONAL_TC']
    },
    mobile_apps: {
        name: 'Мобильные приложения',
        description: 'Мобильные приложения и цифровые кошельки',
        icon: 'pi-mobile',
        color: '#8b5cf6',
        types: ['MOBILE_BC', 'MOBILE_VIRTUAL_TC', 'MOBILE_SBP']
    },
    social_cards: {
        name: 'Социальные карты',
        description: 'МПК и социальные карты',
        icon: 'pi-users',
        color: '#ef4444',
        types: ['MPC_DISCOUNT', 'MPC_SOCIAL', 'MPC_SCHOOL', 'MPC_STUDENT_SINGLE', 'MPC_STUDENT_SUBSCRIPTION', 'SOCIAL_TC']
    },
    other: {
        name: 'Прочие',
        description: 'Другие способы оплаты',
        icon: 'pi-ellipsis-h',
        color: '#6b7280',
        types: ['OTHER_CARDS']
    }
};

/**
 * Статусы синхронизации
 */
export const SYNC_STATUSES = {
    never: {
        name: 'Не синхронизировано',
        description: 'Синхронизация не выполнялась',
        icon: 'pi-circle',
        color: '#6b7280',
        severity: 'secondary'
    },
    pending: {
        name: 'Ожидает синхронизации',
        description: 'Синхронизация запланирована',
        icon: 'pi-clock',
        color: '#f59e0b',
        severity: 'warning'
    },
    synced: {
        name: 'Синхронизировано',
        description: 'Успешно синхронизировано',
        icon: 'pi-check-circle',
        color: '#10b981',
        severity: 'success'
    },
    error: {
        name: 'Ошибка синхронизации',
        description: 'Произошла ошибка при синхронизации',
        icon: 'pi-exclamation-circle',
        color: '#ef4444',
        severity: 'danger'
    }
};

/**
 * Правила валидации
 */
export const VALIDATION_RULES = {
    code: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[A-Z_]+$/,
        message: 'Код должен содержать только заглавные буквы и подчеркивания'
    },
    name: {
        required: true,
        minLength: 3,
        maxLength: 200,
        message: 'Название должно содержать от 3 до 200 символов'
    },
    description: {
        required: false,
        maxLength: 500,
        message: 'Описание не должно превышать 500 символов'
    }
};

/**
 * Настройки по умолчанию
 */
export const DEFAULT_SETTINGS = {
    pagination: {
        page: 1,
        limit: 20,
        maxLimit: 100
    },
    sorting: {
        sortBy: 'name',
        sortOrder: 'asc'
    },
    filters: {
        showInactive: false,
        showDeleted: false
    },
    display: {
        groupByCategory: true,
        showStatistics: true,
        showFilters: true
    }
};

/**
 * Сообщения об ошибках
 */
export const ERROR_MESSAGES = {
    VALIDATION_ERROR: 'Ошибка валидации данных',
    CREATE_ERROR: 'Ошибка создания средства оплаты',
    UPDATE_ERROR: 'Ошибка обновления средства оплаты',
    DELETE_ERROR: 'Ошибка удаления средства оплаты',
    FETCH_ERROR: 'Ошибка загрузки данных',
    NETWORK_ERROR: 'Ошибка сети',
    PERMISSION_ERROR: 'Недостаточно прав доступа',
    NOT_FOUND_ERROR: 'Средство оплаты не найдено',
    DUPLICATE_ERROR: 'Средство оплаты с таким кодом уже существует',
    CONTRACT_NOT_FOUND: 'Договор не найден',
    SYNC_ERROR: 'Ошибка синхронизации'
};

/**
 * Сообщения об успехе
 */
export const SUCCESS_MESSAGES = {
    CREATED: 'Средство оплаты успешно создано',
    UPDATED: 'Средство оплаты успешно обновлено',
    DELETED: 'Средство оплаты успешно удалено',
    SYNCED: 'Синхронизация выполнена успешно',
    EXPORTED: 'Данные успешно экспортированы',
    IMPORTED: 'Данные успешно импортированы'
};

/**
 * Конфигурация модуля
 */
export const MODULE_CONFIG = {
    enableSync: true,
    enableValidation: true,
    enableAudit: true,
    enableNotifications: true,
    maxMethodsPerContract: 50,
    allowCustomTypes: false,
    syncInterval: 300000, // 5 минут
    autoSaveDelay: 1000, // 1 секунда
    maxFileSize: 10485760, // 10MB
    allowedFileTypes: ['csv', 'xlsx', 'json']
};

/**
 * Утилиты для работы со средствами оплаты
 */
export const PaymentMethodUtils = {
    /**
     * Получить название типа средства оплаты
     * @param {string} code - Код типа
     * @returns {string} Название типа
     */
    getTypeName(code) {
        return PAYMENT_METHOD_TYPES[code] || code;
    },

    /**
     * Получить категорию средства оплаты
     * @param {string} code - Код типа
     * @returns {Object|null} Категория
     */
    getCategory(code) {
        for (const [categoryKey, category] of Object.entries(PAYMENT_METHOD_CATEGORIES)) {
            if (category.types.includes(code)) {
                return { key: categoryKey, ...category };
            }
        }
        return null;
    },

    /**
     * Получить информацию о статусе синхронизации
     * @param {string} status - Статус синхронизации
     * @returns {Object} Информация о статусе
     */
    getSyncStatusInfo(status) {
        return SYNC_STATUSES[status] || SYNC_STATUSES.never;
    },

    /**
     * Валидация данных средства оплаты
     * @param {Object} data - Данные для валидации
     * @returns {Object} Результат валидации
     */
    validate(data) {
        const errors = {};

        // Валидация кода
        if (VALIDATION_RULES.code.required && !data.code) {
            errors.code = 'Код обязателен';
        } else if (data.code) {
            if (data.code.length < VALIDATION_RULES.code.minLength) {
                errors.code = `Код должен содержать минимум ${VALIDATION_RULES.code.minLength} символов`;
            } else if (data.code.length > VALIDATION_RULES.code.maxLength) {
                errors.code = `Код не должен превышать ${VALIDATION_RULES.code.maxLength} символов`;
            } else if (!VALIDATION_RULES.code.pattern.test(data.code)) {
                errors.code = VALIDATION_RULES.code.message;
            }
        }

        // Валидация названия
        if (VALIDATION_RULES.name.required && !data.name) {
            errors.name = 'Название обязательно';
        } else if (data.name) {
            if (data.name.length < VALIDATION_RULES.name.minLength) {
                errors.name = `Название должно содержать минимум ${VALIDATION_RULES.name.minLength} символов`;
            } else if (data.name.length > VALIDATION_RULES.name.maxLength) {
                errors.name = `Название не должно превышать ${VALIDATION_RULES.name.maxLength} символов`;
            }
        }

        // Валидация описания
        if (data.description && data.description.length > VALIDATION_RULES.description.maxLength) {
            errors.description = `Описание не должно превышать ${VALIDATION_RULES.description.maxLength} символов`;
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    },

    /**
     * Форматирование даты
     * @param {string} dateString - Строка даты
     * @returns {string} Отформатированная дата
     */
    formatDate(dateString) {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleDateString('ru-RU', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    /**
     * Форматирование для отображения
     * @param {Object} paymentMethod - Средство оплаты
     * @returns {Object} Отформатированные данные
     */
    formatForDisplay(paymentMethod) {
        return {
            ...paymentMethod,
            typeName: this.getTypeName(paymentMethod.code),
            category: this.getCategory(paymentMethod.code),
            syncStatusInfo: this.getSyncStatusInfo(paymentMethod.syncStatus),
            formattedCreatedDate: this.formatDate(paymentMethod.createdDate),
            formattedLastSyncDate: this.formatDate(paymentMethod.lastSyncDate),
            statusBadge: {
                label: paymentMethod.isActive ? 'Активно' : 'Неактивно',
                severity: paymentMethod.isActive ? 'success' : 'secondary'
            }
        };
    },

    /**
     * Группировка средств оплаты по категориям
     * @param {Array} paymentMethods - Массив средств оплаты
     * @returns {Array} Сгруппированные данные
     */
    groupByCategory(paymentMethods) {
        const groups = {};

        paymentMethods.forEach(pm => {
            const category = this.getCategory(pm.code);
            const categoryKey = category ? category.key : 'other';
            
            if (!groups[categoryKey]) {
                groups[categoryKey] = {
                    category: categoryKey,
                    name: category ? category.name : 'Прочие',
                    description: category ? category.description : 'Другие способы оплаты',
                    icon: category ? category.icon : 'pi-ellipsis-h',
                    color: category ? category.color : '#6b7280',
                    methods: [],
                    count: 0,
                    activeCount: 0
                };
            }

            groups[categoryKey].methods.push(pm);
            groups[categoryKey].count++;
            if (pm.isActive) {
                groups[categoryKey].activeCount++;
            }
        });

        return Object.values(groups);
    },

    /**
     * Фильтрация средств оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {Object} filters - Фильтры
     * @returns {Array} Отфильтрованный массив
     */
    applyFilters(paymentMethods, filters) {
        let filtered = [...paymentMethods];

        if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            filtered = filtered.filter(pm => 
                (pm.code && pm.code.toLowerCase().includes(searchLower)) ||
                (pm.name && pm.name.toLowerCase().includes(searchLower)) ||
                (pm.description && pm.description.toLowerCase().includes(searchLower))
            );
        }

        if (filters.category) {
            filtered = filtered.filter(pm => {
                const category = this.getCategory(pm.code);
                return category && category.key === filters.category;
            });
        }

        if (filters.isActive !== undefined) {
            filtered = filtered.filter(pm => pm.isActive === filters.isActive);
        }

        if (filters.syncStatus) {
            filtered = filtered.filter(pm => pm.syncStatus === filters.syncStatus);
        }

        if (!filters.showDeleted) {
            filtered = filtered.filter(pm => !pm.isDeleted);
        }

        return filtered;
    },

    /**
     * Сортировка средств оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {string} sortBy - Поле для сортировки
     * @param {string} sortOrder - Порядок сортировки
     * @returns {Array} Отсортированный массив
     */
    applySorting(paymentMethods, sortBy = 'name', sortOrder = 'asc') {
        return [...paymentMethods].sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
            return 0;
        });
    }
};

export default {
    PAYMENT_METHOD_TYPES,
    PAYMENT_METHOD_CATEGORIES,
    SYNC_STATUSES,
    VALIDATION_RULES,
    DEFAULT_SETTINGS,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    MODULE_CONFIG,
    PaymentMethodUtils
};
