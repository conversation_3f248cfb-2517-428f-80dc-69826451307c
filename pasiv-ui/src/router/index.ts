import PasivLayout from '@/components/PasivLayout.vue';
import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/',
            component: PasivLayout,
            children: [
                {
                    path: '/',
                    redirect: '/pasiv'
                },
                {
                    path: '/organizations',
                    name: 'organizations',
                    component: () => import('@/views/OrganizationList.vue')
                },
                {
                    path: '/organizations/create',
                    name: 'organizations_create',
                    component: () => import('@/views/OrganizationForm.vue')
                },
                {
                    path: '/organizations/:organizationId',
                    name: 'organizations_detail',
                    component: () => import('@/views/OrganizationDetail.vue'),
                    children: [
                        {
                            path: 'employees',
                            name: 'organization_employees',
                            component: () => import('@/views/organization/EmployeeList.vue')
                        },
                        {
                            path: 'employees',
                            name: 'organization_employees',
                            component: () => import('@/views/organization/EmployeeList.vue')
                        },
                        {
                            path: 'contracts',
                            name: 'organization_contracts',
                            component: () => import('@/views/organization/ContractList.vue')
                        },
                        {
                            path: 'finance',
                            name: 'organization_finance',
                            component: () => import('@/views/organization/FinanceInfo.vue')
                        },
                        {
                            path: 'history',
                            name: 'organization_history',
                            component: () => import('@/views/organization/OperationHistory.vue')
                        },
                        {
                            path: 'audit',
                            name: 'organization_audit',
                            component: () => import('@/views/organization/OrganizationAuditLog.vue')
                        }
                    ]
                },
                {
                    path: '/organizations/:organizationId/edit',
                    name: 'organizations_edit',
                    component: () => import('@/views/OrganizationForm.vue')
                },
                {
                    path: '/contracts',
                    name: 'contracts',
                    component: () => import('@/views/finance/ContractList.vue')
                },
                {
                    path: '/contracts/create',
                    name: 'contracts_create',
                    component: () => import('@/views/finance/ContractForm.vue')
                },
                {
                    path: '/contracts/:contractId',
                    name: 'contracts_detail',
                    component: () => import('@/views/finance/ContractDetail.vue')
                },
                {
                    path: '/contracts/:contractId/edit',
                    name: 'contracts_edit',
                    component: () => import('@/views/finance/ContractForm.vue')
                },
                {
                    path: '/pasiv',
                    name: 'pasiv',
                    component: () => import('@/views/Pasiv.vue')
                }
            ]
        },
        // АС ПАСИВ - Биллинг и взаиморасчеты
        {
            path: '/pasiv',
            component: PasivLayout,
            children: [
                // Дефолтный редирект на транзакции
                {
                    path: '',
                    redirect: 'transactions'
                },
                // Транзакции
                {
                    path: 'transactions',
                    name: 'pasiv_transactions',
                    component: () => import('@/views/pasiv/TransactionList.vue')
                },
                {
                    path: 'billing',
                    name: 'pasiv_billing',
                    component: () => import('@/views/pasiv/BillingList.vue')
                },
                {
                    path: 'settlements',
                    name: 'pasiv_settlements',
                    component: () => import('@/views/pasiv/SettlementList.vue')
                },
                {
                    path: 'reports',
                    name: 'pasiv_reports',
                    component: () => import('@/views/pasiv/ReportList.vue')
                },
                {
                    path: 'clearing',
                    name: 'pasiv_clearing',
                    component: () => import('@/views/pasiv/ClearingList.vue')
                },

                // Взаиморасчеты
                {
                    path: 'settlement-schemes',
                    name: 'pasiv_settlement_schemes',
                    component: () => import('@/views/pasiv/SettlementSchemes.vue')
                },

                // Отчетность
                {
                    path: 'financial-reports',
                    name: 'pasiv_financial_reports',
                    component: () => import('@/views/reports/FinancialReports.vue'),
                    children: [
                        {
                            path: 'revenue',
                            name: 'financial_reports_revenue',
                            component: () => import('@/views/reports/financial/RevenueReport.vue')
                        }
                    ]
                }
            ]
        }
    ]
});

export default router;
