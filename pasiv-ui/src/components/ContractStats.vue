<template>
    <div class="contract-stats">
        <div class="grid">
            <!-- Общая статистика -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-primary mb-2">{{ totalCount }}</div>
                    <div class="text-color-secondary">Всего договоров</div>
                </div>
            </div>

            <!-- Активные договоры -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-green-500 mb-2">{{ activeCount }}</div>
                    <div class="text-color-secondary">Активных</div>
                </div>
            </div>

            <!-- Истекающие договоры -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-orange-500 mb-2">{{ expiringCount }}</div>
                    <div class="text-color-secondary">Истекает скоро</div>
                </div>
            </div>

            <!-- Синхронизация -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-blue-500 mb-2">{{ syncedCount }}</div>
                    <div class="text-color-secondary">Синхронизировано</div>
                </div>
            </div>
        </div>

        <!-- Детальная статистика по типам -->
        <div v-if="showTypeDetails" class="grid mt-3">
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-purple-500 mb-2">{{ systemRulesCount }}</div>
                    <div class="text-color-secondary">Правила системы</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-cyan-500 mb-2">{{ serviceCount }}</div>
                    <div class="text-color-secondary">Сервисные</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-indigo-500 mb-2">{{ transportCount }}</div>
                    <div class="text-color-secondary">Транспортные</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-teal-500 mb-2">{{ processingCount }}</div>
                    <div class="text-color-secondary">Процессинговые</div>
                </div>
            </div>
        </div>

        <!-- Статистика по синхронизации -->
        <div v-if="showSyncDetails" class="grid mt-3">
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-red-500 mb-2">{{ syncErrorCount }}</div>
                    <div class="text-color-secondary">Ошибки синхронизации</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-yellow-500 mb-2">{{ syncPendingCount }}</div>
                    <div class="text-color-secondary">Ожидают синхронизации</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-gray-500 mb-2">{{ syncNeverCount }}</div>
                    <div class="text-color-secondary">Не синхронизировались</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-color-secondary mb-2">
                        {{ lastSyncDate ? formatDate(lastSyncDate) : 'Никогда' }}
                    </div>
                    <div class="text-color-secondary">Последняя синхронизация</div>
                </div>
            </div>
        </div>

        <!-- Кнопки действий -->
        <div v-if="showActions" class="flex justify-content-center gap-2 mt-3">
            <Button 
                label="Обновить статистику" 
                icon="pi pi-refresh" 
                outlined 
                size="small"
                @click="$emit('refresh')"
                :loading="refreshing"
            />
            <Button 
                v-if="syncErrorCount > 0"
                label="Повторить синхронизацию" 
                icon="pi pi-sync" 
                outlined 
                severity="warning"
                size="small"
                @click="$emit('retry-sync')"
            />
            <Button 
                v-if="expiringCount > 0"
                label="Показать истекающие" 
                icon="pi pi-exclamation-triangle" 
                outlined 
                severity="warning"
                size="small"
                @click="$emit('show-expiring')"
            />
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { CONTRACT_STATUS, CONTRACT_TYPE } from '@/constants/contract';
import { formatContractDate, isContractExpiringSoon } from '@/utils/contractUtils';

const props = defineProps({
    contracts: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    refreshing: {
        type: Boolean,
        default: false
    },
    showTypeDetails: {
        type: Boolean,
        default: true
    },
    showSyncDetails: {
        type: Boolean,
        default: true
    },
    showActions: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['refresh', 'retry-sync', 'show-expiring']);

// Общая статистика
const totalCount = computed(() => props.contracts.length);

const activeCount = computed(() => 
    props.contracts.filter(contract => contract.status === CONTRACT_STATUS.ACTIVE).length
);

const expiringCount = computed(() => 
    props.contracts.filter(contract => 
        contract.status === CONTRACT_STATUS.ACTIVE && isContractExpiringSoon(contract.completionDate)
    ).length
);

// Статистика по типам договоров
const systemRulesCount = computed(() => 
    props.contracts.filter(contract => contract.contractType === CONTRACT_TYPE.SYSTEM_RULES).length
);

const serviceCount = computed(() => 
    props.contracts.filter(contract => contract.contractType === CONTRACT_TYPE.SERVICE).length
);

const transportCount = computed(() => 
    props.contracts.filter(contract => contract.contractType === CONTRACT_TYPE.TRANSPORT).length
);

const processingCount = computed(() => 
    props.contracts.filter(contract => contract.contractType === CONTRACT_TYPE.PROCESSING).length
);

// Статистика синхронизации
const syncedCount = computed(() => 
    props.contracts.filter(contract => contract.syncStatus === 'synced').length
);

const syncErrorCount = computed(() => 
    props.contracts.filter(contract => contract.syncStatus === 'error').length
);

const syncPendingCount = computed(() => 
    props.contracts.filter(contract => contract.syncStatus === 'pending').length
);

const syncNeverCount = computed(() => 
    props.contracts.filter(contract => contract.syncStatus === 'never').length
);

// Последняя дата синхронизации
const lastSyncDate = computed(() => {
    const syncedContracts = props.contracts.filter(contract => 
        contract.syncStatus === 'synced' && contract.lastSyncDate
    );
    
    if (syncedContracts.length === 0) return null;
    
    return syncedContracts.reduce((latest, contract) => {
        const contractDate = new Date(contract.lastSyncDate);
        return !latest || contractDate > latest ? contractDate : latest;
    }, null);
});

// Форматирование даты
const formatDate = (date) => {
    if (!date) return 'Никогда';
    
    const d = new Date(date);
    return d.toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};
</script>

<style scoped>
.contract-stats .card {
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.contract-stats .card:hover {
    transform: translateY(-2px);
}

.contract-stats .text-2xl {
    font-size: 1.5rem;
}

.contract-stats .text-xl {
    font-size: 1.25rem;
}

@media (max-width: 768px) {
    .contract-stats .text-2xl {
        font-size: 1.25rem;
    }
    
    .contract-stats .text-xl {
        font-size: 1rem;
    }
}
</style>
