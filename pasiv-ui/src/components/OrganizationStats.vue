<template>
    <div class="organization-stats">
        <div class="grid">
            <!-- Общая статистика -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-primary mb-2">{{ totalCount }}</div>
                    <div class="text-color-secondary">Всего организаций</div>
                </div>
            </div>

            <!-- Активные организации -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-green-500 mb-2">{{ activeCount }}</div>
                    <div class="text-color-secondary">Активных</div>
                </div>
            </div>

            <!-- Неактивные организации -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-orange-500 mb-2">{{ inactiveCount }}</div>
                    <div class="text-color-secondary">Неактивных</div>
                </div>
            </div>

            <!-- Синхронизация -->
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-2xl font-bold text-blue-500 mb-2">{{ syncedCount }}</div>
                    <div class="text-color-secondary">Синхронизировано</div>
                </div>
            </div>
        </div>

        <!-- Детальная статистика по синхронизации -->
        <div v-if="showSyncDetails" class="grid mt-3">
            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-red-500 mb-2">{{ syncErrorCount }}</div>
                    <div class="text-color-secondary">Ошибки синхронизации</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-yellow-500 mb-2">{{ syncPendingCount }}</div>
                    <div class="text-color-secondary">Ожидают синхронизации</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-gray-500 mb-2">{{ syncNeverCount }}</div>
                    <div class="text-color-secondary">Не синхронизировались</div>
                </div>
            </div>

            <div class="col-12 md:col-3">
                <div class="card text-center">
                    <div class="text-xl font-bold text-color-secondary mb-2">
                        {{ lastSyncDate ? formatDate(lastSyncDate) : 'Никогда' }}
                    </div>
                    <div class="text-color-secondary">Последняя синхронизация</div>
                </div>
            </div>
        </div>

        <!-- Кнопки действий -->
        <div v-if="showActions" class="flex justify-content-center gap-2 mt-3">
            <Button 
                label="Обновить статистику" 
                icon="pi pi-refresh" 
                outlined 
                size="small"
                @click="$emit('refresh')"
                :loading="refreshing"
            />
            <Button 
                v-if="syncErrorCount > 0"
                label="Повторить синхронизацию" 
                icon="pi pi-sync" 
                outlined 
                severity="warning"
                size="small"
                @click="$emit('retry-sync')"
            />
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { ORGANIZATION_STATUS, SYNC_STATUS } from '@/constants/organization';

const props = defineProps({
    organizations: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    refreshing: {
        type: Boolean,
        default: false
    },
    showSyncDetails: {
        type: Boolean,
        default: true
    },
    showActions: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['refresh', 'retry-sync']);

// Общая статистика
const totalCount = computed(() => props.organizations.length);

const activeCount = computed(() => 
    props.organizations.filter(org => org.status === ORGANIZATION_STATUS.ACTIVE).length
);

const inactiveCount = computed(() => 
    props.organizations.filter(org => org.status === ORGANIZATION_STATUS.INACTIVE).length
);

// Статистика синхронизации
const syncedCount = computed(() => 
    props.organizations.filter(org => org.syncStatus === SYNC_STATUS.SYNCED).length
);

const syncErrorCount = computed(() => 
    props.organizations.filter(org => org.syncStatus === SYNC_STATUS.ERROR).length
);

const syncPendingCount = computed(() => 
    props.organizations.filter(org => org.syncStatus === SYNC_STATUS.PENDING).length
);

const syncNeverCount = computed(() => 
    props.organizations.filter(org => org.syncStatus === SYNC_STATUS.NEVER).length
);

// Последняя дата синхронизации
const lastSyncDate = computed(() => {
    const syncedOrgs = props.organizations.filter(org => 
        org.syncStatus === SYNC_STATUS.SYNCED && org.lastSyncDate
    );
    
    if (syncedOrgs.length === 0) return null;
    
    return syncedOrgs.reduce((latest, org) => {
        const orgDate = new Date(org.lastSyncDate);
        return !latest || orgDate > latest ? orgDate : latest;
    }, null);
});

// Форматирование даты
const formatDate = (date) => {
    if (!date) return 'Никогда';
    
    const d = new Date(date);
    return d.toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};
</script>

<style scoped>
.organization-stats .card {
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.organization-stats .card:hover {
    transform: translateY(-2px);
}

.organization-stats .text-2xl {
    font-size: 1.5rem;
}

.organization-stats .text-xl {
    font-size: 1.25rem;
}

@media (max-width: 768px) {
    .organization-stats .text-2xl {
        font-size: 1.25rem;
    }
    
    .organization-stats .text-xl {
        font-size: 1rem;
    }
}
</style>
