<template>
    <div class="payment-method-filters">
        <!-- Быстрые фильтры -->
        <div class="card mb-4">
            <div class="flex align-items-center justify-content-between mb-3">
                <h3 class="text-lg font-semibold m-0">Фильтры</h3>
                <div class="flex gap-2">
                    <Button 
                        label="Сбросить" 
                        icon="pi pi-filter-slash" 
                        size="small"
                        outlined
                        @click="clearAllFilters"
                        :disabled="!hasActiveFilters"
                    />
                    <Button 
                        :label="showAdvanced ? 'Скрыть' : 'Расширенные'" 
                        :icon="showAdvanced ? 'pi pi-chevron-up' : 'pi pi-chevron-down'" 
                        size="small"
                        text
                        @click="showAdvanced = !showAdvanced"
                    />
                </div>
            </div>

            <!-- Основные фильтры -->
            <div class="grid">
                <div class="col-12 md:col-6 lg:col-3">
                    <label class="block text-sm font-medium mb-2">Поиск</label>
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText 
                            v-model="localFilters.search" 
                            placeholder="Поиск по коду, названию..."
                            class="w-full"
                        />
                    </IconField>
                </div>
                <div class="col-12 md:col-6 lg:col-3">
                    <label class="block text-sm font-medium mb-2">Категория</label>
                    <Select 
                        v-model="localFilters.category" 
                        :options="categoryOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Все категории"
                        showClear
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-6 lg:col-3">
                    <label class="block text-sm font-medium mb-2">Статус</label>
                    <Select 
                        v-model="localFilters.isActive" 
                        :options="statusOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Все статусы"
                        showClear
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-6 lg:col-3">
                    <label class="block text-sm font-medium mb-2">Синхронизация</label>
                    <Select 
                        v-model="localFilters.syncStatus" 
                        :options="syncStatusOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Все статусы"
                        showClear
                        class="w-full"
                    />
                </div>
            </div>

            <!-- Быстрые действия -->
            <div class="flex flex-wrap gap-2 mt-3">
                <Button 
                    label="Только активные" 
                    size="small"
                    outlined
                    :severity="localFilters.isActive === true ? 'success' : 'secondary'"
                    @click="toggleQuickFilter('isActive', true)"
                />
                <Button 
                    label="Только неактивные" 
                    size="small"
                    outlined
                    :severity="localFilters.isActive === false ? 'warning' : 'secondary'"
                    @click="toggleQuickFilter('isActive', false)"
                />
                <Button 
                    label="Банковские карты" 
                    size="small"
                    outlined
                    :severity="localFilters.category === 'bank_cards' ? 'info' : 'secondary'"
                    @click="toggleQuickFilter('category', 'bank_cards')"
                />
                <Button 
                    label="Транспортные карты" 
                    size="small"
                    outlined
                    :severity="localFilters.category === 'transport_cards' ? 'info' : 'secondary'"
                    @click="toggleQuickFilter('category', 'transport_cards')"
                />
                <Button 
                    label="Мобильные приложения" 
                    size="small"
                    outlined
                    :severity="localFilters.category === 'mobile_apps' ? 'info' : 'secondary'"
                    @click="toggleQuickFilter('category', 'mobile_apps')"
                />
            </div>
        </div>

        <!-- Расширенные фильтры -->
        <div v-if="showAdvanced" class="card">
            <h4 class="text-md font-semibold mb-3">Расширенные фильтры</h4>
            
            <div class="grid">
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Дата создания</label>
                    <div class="flex gap-2">
                        <Calendar 
                            v-model="localFilters.createdAfter" 
                            placeholder="От"
                            dateFormat="dd.mm.yy"
                            showIcon
                            class="flex-1"
                        />
                        <Calendar 
                            v-model="localFilters.createdBefore" 
                            placeholder="До"
                            dateFormat="dd.mm.yy"
                            showIcon
                            class="flex-1"
                        />
                    </div>
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Дата синхронизации</label>
                    <div class="flex gap-2">
                        <Calendar 
                            v-model="localFilters.lastSyncAfter" 
                            placeholder="От"
                            dateFormat="dd.mm.yy"
                            showIcon
                            class="flex-1"
                        />
                        <Calendar 
                            v-model="localFilters.lastSyncBefore" 
                            placeholder="До"
                            dateFormat="dd.mm.yy"
                            showIcon
                            class="flex-1"
                        />
                    </div>
                </div>
                <div class="col-12 md:col-6">
                    <div class="flex align-items-center">
                        <Checkbox 
                            v-model="localFilters.showDeleted" 
                            inputId="showDeleted" 
                            binary
                        />
                        <label for="showDeleted" class="ml-2">Показать удаленные</label>
                    </div>
                </div>
                <div class="col-12 md:col-6" v-if="!contractId">
                    <label class="block text-sm font-medium mb-2">Договор</label>
                    <Select 
                        v-model="localFilters.contractId" 
                        :options="contractOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Все договоры"
                        showClear
                        filter
                        class="w-full"
                    />
                </div>
            </div>

            <!-- Сохраненные фильтры -->
            <div class="mt-4">
                <div class="flex align-items-center justify-content-between mb-3">
                    <h5 class="text-sm font-semibold m-0">Сохраненные фильтры</h5>
                    <Button 
                        label="Сохранить текущий" 
                        icon="pi pi-save" 
                        size="small"
                        text
                        @click="showSaveFilterDialog = true"
                        :disabled="!hasActiveFilters"
                    />
                </div>
                <div class="flex flex-wrap gap-2">
                    <Button 
                        v-for="filter in savedFilters" 
                        :key="filter.id"
                        :label="filter.name" 
                        size="small"
                        outlined
                        @click="applySavedFilter(filter)"
                    />
                    <span v-if="savedFilters.length === 0" class="text-color-secondary text-sm">
                        Нет сохраненных фильтров
                    </span>
                </div>
            </div>
        </div>

        <!-- Диалог сохранения фильтра -->
        <Dialog 
            v-model:visible="showSaveFilterDialog" 
            header="Сохранить фильтр" 
            :modal="true"
            :style="{ width: '400px' }"
        >
            <div class="space-y-3">
                <div>
                    <label class="block text-sm font-medium mb-2">Название фильтра</label>
                    <InputText 
                        v-model="newFilterName" 
                        placeholder="Введите название..."
                        class="w-full"
                        @keyup.enter="saveCurrentFilter"
                    />
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Описание (опционально)</label>
                    <Textarea 
                        v-model="newFilterDescription" 
                        placeholder="Описание фильтра..."
                        rows="3"
                        class="w-full"
                    />
                </div>
            </div>
            <template #footer>
                <Button 
                    label="Отмена" 
                    icon="pi pi-times" 
                    text 
                    @click="showSaveFilterDialog = false" 
                />
                <Button 
                    label="Сохранить" 
                    icon="pi pi-save" 
                    @click="saveCurrentFilter"
                    :disabled="!newFilterName.trim()"
                />
            </template>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { PAYMENT_METHOD_CATEGORIES, SYNC_STATUSES } from '@/constants/paymentMethod';

// Props
const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({})
    },
    contractId: {
        type: String,
        default: null
    },
    contracts: {
        type: Array,
        default: () => []
    }
});

// Emits
const emit = defineEmits(['update:modelValue', 'filtersChanged']);

// Состояние компонента
const showAdvanced = ref(false);
const showSaveFilterDialog = ref(false);
const newFilterName = ref('');
const newFilterDescription = ref('');
const savedFilters = ref([]);

// Локальные фильтры
const localFilters = ref({
    search: '',
    category: null,
    isActive: null,
    syncStatus: null,
    createdAfter: null,
    createdBefore: null,
    lastSyncAfter: null,
    lastSyncBefore: null,
    showDeleted: false,
    contractId: null,
    ...props.modelValue
});

// Опции для селектов
const categoryOptions = computed(() => {
    return Object.entries(PAYMENT_METHOD_CATEGORIES).map(([key, category]) => ({
        label: category.name,
        value: key
    }));
});

const statusOptions = [
    { label: 'Активные', value: true },
    { label: 'Неактивные', value: false }
];

const syncStatusOptions = computed(() => {
    return Object.entries(SYNC_STATUSES).map(([key, status]) => ({
        label: status.name,
        value: key
    }));
});

const contractOptions = computed(() => {
    return props.contracts.map(contract => ({
        label: `${contract.name} (${contract.number})`,
        value: contract.id
    }));
});

// Вычисляемые свойства
const hasActiveFilters = computed(() => {
    return Object.values(localFilters.value).some(value => {
        if (value === null || value === undefined || value === '') return false;
        if (Array.isArray(value)) return value.length > 0;
        return true;
    });
});

// Методы
const clearAllFilters = () => {
    Object.keys(localFilters.value).forEach(key => {
        if (typeof localFilters.value[key] === 'boolean') {
            localFilters.value[key] = false;
        } else {
            localFilters.value[key] = null;
        }
    });
    localFilters.value.search = '';
};

const toggleQuickFilter = (filterName, value) => {
    if (localFilters.value[filterName] === value) {
        localFilters.value[filterName] = null;
    } else {
        localFilters.value[filterName] = value;
    }
};

const saveCurrentFilter = () => {
    if (!newFilterName.value.trim()) return;
    
    const filter = {
        id: Date.now().toString(),
        name: newFilterName.value.trim(),
        description: newFilterDescription.value.trim(),
        filters: { ...localFilters.value },
        createdAt: new Date().toISOString()
    };
    
    savedFilters.value.push(filter);
    localStorage.setItem('paymentMethodSavedFilters', JSON.stringify(savedFilters.value));
    
    newFilterName.value = '';
    newFilterDescription.value = '';
    showSaveFilterDialog.value = false;
};

const applySavedFilter = (filter) => {
    Object.assign(localFilters.value, filter.filters);
};

const loadSavedFilters = () => {
    try {
        const saved = localStorage.getItem('paymentMethodSavedFilters');
        if (saved) {
            savedFilters.value = JSON.parse(saved);
        }
    } catch (error) {
        console.error('Ошибка загрузки сохраненных фильтров:', error);
    }
};

// Жизненный цикл
onMounted(() => {
    loadSavedFilters();
});

// Наблюдатели
watch(localFilters, (newFilters) => {
    emit('update:modelValue', newFilters);
    emit('filtersChanged', newFilters);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
    Object.assign(localFilters.value, newValue);
}, { deep: true });
</script>

<style scoped>
.payment-method-filters .space-y-3 > * + * {
    margin-top: 0.75rem;
}
</style>
