<template>
    <div class="payment-method-stats">
        <!-- Основная статистика -->
        <div class="grid mb-4">
            <div class="col-12 md:col-3">
                <div class="card text-center p-3">
                    <div class="flex align-items-center justify-content-center mb-2">
                        <i class="pi pi-credit-card text-3xl text-primary"></i>
                    </div>
                    <div class="text-2xl font-bold text-primary mb-1">{{ statistics?.total || 0 }}</div>
                    <div class="text-color-secondary text-sm">Всего средств оплаты</div>
                </div>
            </div>
            <div class="col-12 md:col-3">
                <div class="card text-center p-3">
                    <div class="flex align-items-center justify-content-center mb-2">
                        <i class="pi pi-check-circle text-3xl text-green-500"></i>
                    </div>
                    <div class="text-2xl font-bold text-green-500 mb-1">{{ statistics?.active || 0 }}</div>
                    <div class="text-color-secondary text-sm">Активных</div>
                </div>
            </div>
            <div class="col-12 md:col-3">
                <div class="card text-center p-3">
                    <div class="flex align-items-center justify-content-center mb-2">
                        <i class="pi pi-times-circle text-3xl text-orange-500"></i>
                    </div>
                    <div class="text-2xl font-bold text-orange-500 mb-1">{{ statistics?.inactive || 0 }}</div>
                    <div class="text-color-secondary text-sm">Неактивных</div>
                </div>
            </div>
            <div class="col-12 md:col-3">
                <div class="card text-center p-3">
                    <div class="flex align-items-center justify-content-center mb-2">
                        <i class="pi pi-list text-3xl text-blue-500"></i>
                    </div>
                    <div class="text-2xl font-bold text-blue-500 mb-1">{{ typeCount }}</div>
                    <div class="text-color-secondary text-sm">Типов используется</div>
                </div>
            </div>
        </div>

        <!-- Статистика по категориям -->
        <div class="grid mb-4" v-if="showCategoryStats">
            <div class="col-12 lg:col-6">
                <div class="card">
                    <div class="flex align-items-center justify-content-between mb-3">
                        <h3 class="text-lg font-semibold m-0">По категориям</h3>
                        <Button
                            icon="pi pi-refresh"
                            size="small"
                            text
                            @click="refreshStats"
                            :loading="loading"
                        />
                    </div>
                    <div class="space-y-3">
                        <div
                            v-for="(category, key) in categoryStats"
                            :key="key"
                            class="flex align-items-center justify-content-between p-2 border-round surface-border border-1"
                        >
                            <div class="flex align-items-center">
                                <i
                                    :class="category.icon"
                                    :style="{ color: category.color }"
                                    class="mr-2"
                                ></i>
                                <span class="font-medium">{{ category.name }}</span>
                            </div>
                            <div class="flex align-items-center gap-2">
                                <Tag
                                    :value="category.count.toString()"
                                    :style="{ backgroundColor: category.color + '20', color: category.color }"
                                />
                                <div class="text-sm text-color-secondary">
                                    {{ Math.round((category.count / (statistics?.total || 1)) * 100) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-6">
                <div class="card">
                    <div class="flex align-items-center justify-content-between mb-3">
                        <h3 class="text-lg font-semibold m-0">Диаграмма</h3>
                        <Select
                            v-model="chartType"
                            :options="chartTypeOptions"
                            optionLabel="label"
                            optionValue="value"
                            class="w-auto"
                        />
                    </div>
                    <Chart
                        v-if="chartData && statistics?.total > 0"
                        :type="chartType"
                        :data="chartData"
                        :options="chartOptions"
                        class="w-full h-20rem"
                    />
                    <div v-else class="text-center p-4 text-color-secondary">
                        <i class="pi pi-chart-pie text-4xl mb-3"></i>
                        <p>Нет данных для отображения</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Статистика по синхронизации -->
        <div class="grid" v-if="showSyncStats">
            <div class="col-12 lg:col-6">
                <div class="card">
                    <h3 class="text-lg font-semibold mb-3">Статус синхронизации</h3>
                    <div class="space-y-2">
                        <div
                            v-for="(status, key) in syncStats"
                            :key="key"
                            class="flex align-items-center justify-content-between p-2"
                        >
                            <div class="flex align-items-center">
                                <i
                                    :class="status.icon"
                                    :style="{ color: status.color }"
                                    class="mr-2"
                                ></i>
                                <span>{{ status.name }}</span>
                            </div>
                            <Tag
                                :value="status.count.toString()"
                                :severity="status.severity"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-6">
                <div class="card">
                    <h3 class="text-lg font-semibold mb-3">Последнее обновление</h3>
                    <div class="text-center p-4">
                        <i class="pi pi-clock text-4xl text-color-secondary mb-3"></i>
                        <div class="text-lg font-medium mb-1">
                            {{ formatDate(statistics?.lastUpdated) }}
                        </div>
                        <div class="text-sm text-color-secondary">
                            Статистика обновлена
                        </div>
                        <Button
                            label="Обновить сейчас"
                            icon="pi pi-refresh"
                            size="small"
                            outlined
                            class="mt-3"
                            @click="refreshStats"
                            :loading="loading"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { usePaymentMethodStatistics } from '@/composables/usePaymentMethods';
import { PAYMENT_METHOD_CATEGORIES, SYNC_STATUSES } from '@/constants/paymentMethod';
import { PaymentMethodFormatter } from '@/utils/paymentMethodUtils';

// Props
const props = defineProps({
    contractId: {
        type: String,
        default: null
    },
    showCategoryStats: {
        type: Boolean,
        default: true
    },
    showSyncStats: {
        type: Boolean,
        default: true
    },
    autoRefresh: {
        type: Boolean,
        default: false
    },
    refreshInterval: {
        type: Number,
        default: 300000 // 5 минут
    }
});

// Emits
const emit = defineEmits(['statsUpdated']);

// Композабл для статистики
const { statistics, loading, loadStatistics } = usePaymentMethodStatistics(props.contractId);

// Состояние компонента
const chartType = ref('doughnut');
const refreshTimer = ref(null);

// Опции для типа диаграммы
const chartTypeOptions = [
    { label: 'Кольцевая', value: 'doughnut' },
    { label: 'Круговая', value: 'pie' },
    { label: 'Столбчатая', value: 'bar' }
];

// Вычисляемые свойства
const typeCount = computed(() => {
    if (!statistics.value?.byType) return 0;
    return Object.values(statistics.value.byType).filter(count => count > 0).length;
});

const categoryStats = computed(() => {
    if (!statistics.value?.byCategory) return {};

    const result = {};
    Object.entries(PAYMENT_METHOD_CATEGORIES).forEach(([key, category]) => {
        const count = statistics.value.byCategory[key] || 0;
        if (count > 0) {
            result[key] = {
                ...category,
                count
            };
        }
    });

    return result;
});

const syncStats = computed(() => {
    if (!statistics.value?.bySyncStatus) return {};

    const result = {};
    Object.entries(SYNC_STATUSES).forEach(([key, status]) => {
        const count = statistics.value.bySyncStatus[key] || 0;
        result[key] = {
            ...status,
            count
        };
    });

    return result;
});

const chartData = computed(() => {
    if (!statistics.value?.byCategory) return null;

    const categories = Object.entries(categoryStats.value);
    if (categories.length === 0) return null;

    return {
        labels: categories.map(([key, category]) => category.name),
        datasets: [{
            data: categories.map(([key, category]) => category.count),
            backgroundColor: categories.map(([key, category]) => category.color + '80'),
            borderColor: categories.map(([key, category]) => category.color),
            borderWidth: 2
        }]
    };
});

const chartOptions = computed(() => {
    const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = Math.round((context.parsed / total) * 100);
                        return `${context.label}: ${context.parsed} (${percentage}%)`;
                    }
                }
            }
        }
    };

    if (chartType.value === 'bar') {
        return {
            ...baseOptions,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        };
    }

    return baseOptions;
});

// Методы
const refreshStats = async () => {
    await loadStatistics();
    emit('statsUpdated', statistics.value);
};

const formatDate = (dateString) => {
    return PaymentMethodFormatter.formatDate(dateString);
};

const startAutoRefresh = () => {
    if (props.autoRefresh && props.refreshInterval > 0) {
        refreshTimer.value = setInterval(refreshStats, props.refreshInterval);
    }
};

const stopAutoRefresh = () => {
    if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
        refreshTimer.value = null;
    }
};

// Жизненный цикл
onMounted(async () => {
    await refreshStats();
    startAutoRefresh();
});

// Наблюдатели
watch(() => props.contractId, async () => {
    await refreshStats();
});

watch(() => props.autoRefresh, (newValue) => {
    if (newValue) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
});

// Очистка при размонтировании
onUnmounted(() => {
    stopAutoRefresh();
});
</script>

<style scoped>
.payment-method-stats .space-y-3 > * + * {
    margin-top: 0.75rem;
}

.payment-method-stats .space-y-2 > * + * {
    margin-top: 0.5rem;
}
</style>
