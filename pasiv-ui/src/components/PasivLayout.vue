<script setup>
import { AppLayout } from '@tkp3/common-ui';

// Конфигурация для PASIV приложения
const appConfig = {
  appName: 'СберТройка ПАСИВ',
  menuItems: [
    {
      label: 'Главная',
      icon: 'pi pi-home',
      to: '/pasiv'
    },
    {
      label: 'Справочники',
      icon: 'pi pi-database',
      items: [
        {
          label: 'Организации',
          icon: 'pi pi-building',
          to: '/organizations'
        },
        {
          label: 'Договоры',
          icon: 'pi pi-file-edit',
          to: '/contracts'
        }
      ]
    },
    {
      separator: true
    },
    {
      label: 'Транзакции',
      icon: 'pi pi-list',
      to: '/pasiv/transactions'
    },
    {
      label: 'Биллинг и клиринг',
      icon: 'pi pi-credit-card',
      items: [
        {
          label: 'Биллинг',
          icon: 'pi pi-money-bill',
          to: '/pasiv/billing'
        },
        {
          label: 'Клиринг',
          icon: 'pi pi-sync',
          to: '/pasiv/clearing'
        },
        {
          label: 'Клиринговые файлы',
          icon: 'pi pi-file-export',
          to: '/pasiv/clearing-files'
        }
      ]
    },
    {
      label: 'Взаиморасчеты',
      icon: 'pi pi-arrows-h',
      items: [
        {
          label: 'Схемы расчетов',
          icon: 'pi pi-share-alt',
          to: '/pasiv/settlement-schemes'
        },
        {
          label: 'Взаиморасчеты',
          icon: 'pi pi-arrows-h',
          to: '/pasiv/settlements'
        },
        {
          label: 'Статусы расчетов',
          icon: 'pi pi-check-circle',
          to: '/pasiv/settlement-status'
        },
        {
          label: 'Сверка реестров',
          icon: 'pi pi-verified',
          to: '/pasiv/registry-reconciliation'
        }
      ]
    },
    {
      label: 'Отчетность',
      icon: 'pi pi-chart-bar',
      items: [
        {
          label: 'Финансовые отчеты',
          icon: 'pi pi-chart-line',
          to: '/pasiv/financial-reports'
        },
        {
          label: 'Статистические отчеты',
          icon: 'pi pi-chart-pie',
          to: '/pasiv/statistical-reports'
        },
        {
          label: 'Обязательные отчеты',
          icon: 'pi pi-file-pdf',
          to: '/pasiv/mandatory-reports'
        }
            ]
    },
    {
      label: 'Администрирование',
      icon: 'pi pi-cog',
      items: [
        {
          label: 'Расчетные периоды',
          icon: 'pi pi-calendar',
          to: '/pasiv/calculation-periods'
        },
        {
          label: 'Аудит операций',
          icon: 'pi pi-eye',
          to: '/pasiv/audit'
        }
      ]
    }
  ],
  showConfigurator: true,
  showThemeToggle: true
};
</script>

<template>
  <AppLayout :config="appConfig">
    <router-view />
  </AppLayout>
</template>
