<template>
    <div class="loading-state">
        <!-- Состояние загрузки -->
        <div v-if="loading" class="text-center p-6">
            <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
            <p class="text-lg font-medium mb-2">{{ loadingMessage }}</p>
            <p v-if="loadingSubtext" class="text-color-secondary">{{ loadingSubtext }}</p>
        </div>

        <!-- Состояние ошибки -->
        <div v-else-if="error" class="text-center p-6">
            <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-3"></i>
            <p class="text-lg font-medium mb-2 text-red-600">{{ errorTitle }}</p>
            <p class="text-color-secondary mb-4">{{ errorMessage }}</p>
            <div v-if="showRetry" class="flex justify-content-center gap-2">
                <Button 
                    label="Повторить" 
                    icon="pi pi-refresh" 
                    outlined 
                    @click="$emit('retry')"
                    :loading="retrying"
                />
                <Button 
                    v-if="showCancel"
                    label="Отмена" 
                    icon="pi pi-times" 
                    outlined 
                    severity="secondary"
                    @click="$emit('cancel')"
                />
            </div>
        </div>

        <!-- Пустое состояние -->
        <div v-else-if="empty" class="text-center p-6">
            <i :class="emptyIcon" class="text-4xl text-color-secondary mb-3"></i>
            <p class="text-lg font-medium mb-2">{{ emptyTitle }}</p>
            <p class="text-color-secondary mb-4">{{ emptyMessage }}</p>
            <Button 
                v-if="showCreateButton"
                :label="createButtonLabel" 
                :icon="createButtonIcon" 
                @click="$emit('create')"
            />
        </div>

        <!-- Контент -->
        <div v-else>
            <slot />
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    error: {
        type: [String, Object, Boolean],
        default: null
    },
    empty: {
        type: Boolean,
        default: false
    },
    retrying: {
        type: Boolean,
        default: false
    },
    loadingMessage: {
        type: String,
        default: 'Загрузка данных...'
    },
    loadingSubtext: {
        type: String,
        default: null
    },
    errorTitle: {
        type: String,
        default: 'Произошла ошибка'
    },
    errorMessage: {
        type: String,
        default: null
    },
    emptyTitle: {
        type: String,
        default: 'Нет данных'
    },
    emptyMessage: {
        type: String,
        default: 'Данные отсутствуют'
    },
    emptyIcon: {
        type: String,
        default: 'pi pi-info-circle'
    },
    showRetry: {
        type: Boolean,
        default: true
    },
    showCancel: {
        type: Boolean,
        default: false
    },
    showCreateButton: {
        type: Boolean,
        default: false
    },
    createButtonLabel: {
        type: String,
        default: 'Создать'
    },
    createButtonIcon: {
        type: String,
        default: 'pi pi-plus'
    }
});

const emit = defineEmits(['retry', 'cancel', 'create']);

// Вычисляемые свойства для обработки ошибок
const computedErrorMessage = computed(() => {
    if (!props.error) return null;
    
    if (typeof props.error === 'string') {
        return props.error;
    }
    
    if (typeof props.error === 'object') {
        return props.error.message || props.error.detail || 'Неизвестная ошибка';
    }
    
    return props.errorMessage || 'Произошла неизвестная ошибка';
});
</script>

<style scoped>
.loading-state {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-state .card {
    width: 100%;
}

/* Анимация для спиннера */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pi-spin {
    animation: spin 1s linear infinite;
}
</style>
