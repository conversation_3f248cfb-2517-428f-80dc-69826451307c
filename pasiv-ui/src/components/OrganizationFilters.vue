<template>
    <div class="organization-filters">
        <div class="card">
            <div class="flex flex-column md:flex-row gap-3 align-items-end">
                <!-- Поиск по названию -->
                <div class="flex-1">
                    <label for="search-name" class="block text-sm font-medium mb-2">
                        Поиск по названию
                    </label>
                    <InputText
                        id="search-name"
                        v-model="localFilters.name"
                        placeholder="Введите название организации..."
                        class="w-full"
                        @input="onFilterChange"
                    />
                </div>

                <!-- Поиск по ИНН -->
                <div class="flex-1">
                    <label for="search-inn" class="block text-sm font-medium mb-2">
                        Поиск по ИНН
                    </label>
                    <InputText
                        id="search-inn"
                        v-model="localFilters.inn"
                        placeholder="Введите ИНН..."
                        class="w-full"
                        @input="onFilterChange"
                    />
                </div>

                <!-- Поиск по КПП -->
                <div class="flex-1">
                    <label for="search-kpp" class="block text-sm font-medium mb-2">
                        Поиск по КПП
                    </label>
                    <InputText
                        id="search-kpp"
                        v-model="localFilters.kpp"
                        placeholder="Введите КПП..."
                        class="w-full"
                        @input="onFilterChange"
                    />
                </div>

                <!-- Статус -->
                <div class="flex-1">
                    <label for="filter-status" class="block text-sm font-medium mb-2">
                        Статус
                    </label>
                    <Dropdown
                        id="filter-status"
                        v-model="localFilters.status"
                        :options="statusOptions"
                        option-label="label"
                        option-value="value"
                        placeholder="Все статусы"
                        class="w-full"
                        show-clear
                        @change="onFilterChange"
                    />
                </div>

                <!-- Статус синхронизации -->
                <div class="flex-1">
                    <label for="filter-sync-status" class="block text-sm font-medium mb-2">
                        Синхронизация
                    </label>
                    <Dropdown
                        id="filter-sync-status"
                        v-model="localFilters.syncStatus"
                        :options="syncStatusOptions"
                        option-label="label"
                        option-value="value"
                        placeholder="Все статусы"
                        class="w-full"
                        show-clear
                        @change="onFilterChange"
                    />
                </div>

                <!-- Кнопки действий -->
                <div class="flex gap-2">
                    <Button
                        icon="pi pi-search"
                        label="Найти"
                        @click="onSearch"
                        :loading="searching"
                    />
                    <Button
                        icon="pi pi-times"
                        label="Очистить"
                        outlined
                        @click="onClear"
                    />
                </div>
            </div>

            <!-- Дополнительные фильтры (сворачиваемые) -->
            <div v-if="showAdvanced" class="mt-4 pt-4 border-top-1 surface-border">
                <div class="grid">
                    <!-- Форма собственности -->
                    <div class="col-12 md:col-4">
                        <label for="filter-ownership" class="block text-sm font-medium mb-2">
                            Форма собственности
                        </label>
                        <Dropdown
                            id="filter-ownership"
                            v-model="localFilters.ownershipForm"
                            :options="ownershipFormOptions"
                            option-label="label"
                            option-value="value"
                            placeholder="Все формы"
                            class="w-full"
                            show-clear
                            @change="onFilterChange"
                        />
                    </div>

                    <!-- Дата создания (от) -->
                    <div class="col-12 md:col-4">
                        <label for="filter-date-from" class="block text-sm font-medium mb-2">
                            Создано с
                        </label>
                        <Calendar
                            id="filter-date-from"
                            v-model="localFilters.createdFrom"
                            placeholder="Выберите дату"
                            class="w-full"
                            show-icon
                            date-format="dd.mm.yy"
                            @date-select="onFilterChange"
                        />
                    </div>

                    <!-- Дата создания (до) -->
                    <div class="col-12 md:col-4">
                        <label for="filter-date-to" class="block text-sm font-medium mb-2">
                            Создано до
                        </label>
                        <Calendar
                            id="filter-date-to"
                            v-model="localFilters.createdTo"
                            placeholder="Выберите дату"
                            class="w-full"
                            show-icon
                            date-format="dd.mm.yy"
                            @date-select="onFilterChange"
                        />
                    </div>
                </div>
            </div>

            <!-- Переключатель расширенных фильтров -->
            <div class="flex justify-content-between align-items-center mt-3">
                <Button
                    :icon="showAdvanced ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
                    :label="showAdvanced ? 'Скрыть фильтры' : 'Дополнительные фильтры'"
                    text
                    size="small"
                    @click="showAdvanced = !showAdvanced"
                />

                <!-- Счетчик активных фильтров -->
                <div v-if="activeFiltersCount > 0" class="text-sm text-color-secondary">
                    Активных фильтров: {{ activeFiltersCount }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { 
    STATUS_OPTIONS, 
    SYNC_STATUS_OPTIONS, 
    OWNERSHIP_FORM_OPTIONS 
} from '@/constants/organization';

const props = defineProps({
    filters: {
        type: Object,
        default: () => ({})
    },
    searching: {
        type: Boolean,
        default: false
    },
    debounceMs: {
        type: Number,
        default: 500
    }
});

const emit = defineEmits(['update:filters', 'search', 'clear']);

// Локальные фильтры
const localFilters = ref({
    name: '',
    inn: '',
    kpp: '',
    status: null,
    syncStatus: null,
    ownershipForm: null,
    createdFrom: null,
    createdTo: null,
    ...props.filters
});

// Состояние расширенных фильтров
const showAdvanced = ref(false);

// Опции для выпадающих списков
const statusOptions = ref([
    { label: 'Все статусы', value: null },
    ...STATUS_OPTIONS
]);

const syncStatusOptions = ref([
    { label: 'Все статусы', value: null },
    ...SYNC_STATUS_OPTIONS
]);

const ownershipFormOptions = ref([
    { label: 'Все формы', value: null },
    ...OWNERSHIP_FORM_OPTIONS
]);

// Счетчик активных фильтров
const activeFiltersCount = computed(() => {
    let count = 0;
    
    if (localFilters.value.name?.trim()) count++;
    if (localFilters.value.inn?.trim()) count++;
    if (localFilters.value.kpp?.trim()) count++;
    if (localFilters.value.status) count++;
    if (localFilters.value.syncStatus) count++;
    if (localFilters.value.ownershipForm) count++;
    if (localFilters.value.createdFrom) count++;
    if (localFilters.value.createdTo) count++;
    
    return count;
});

// Debounce для автоматического поиска
let debounceTimer = null;

const onFilterChange = () => {
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }
    
    debounceTimer = setTimeout(() => {
        emit('update:filters', { ...localFilters.value });
        emit('search');
    }, props.debounceMs);
};

const onSearch = () => {
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }
    
    emit('update:filters', { ...localFilters.value });
    emit('search');
};

const onClear = () => {
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }
    
    localFilters.value = {
        name: '',
        inn: '',
        kpp: '',
        status: null,
        syncStatus: null,
        ownershipForm: null,
        createdFrom: null,
        createdTo: null
    };
    
    emit('update:filters', { ...localFilters.value });
    emit('clear');
};

// Синхронизация с внешними фильтрами
watch(() => props.filters, (newFilters) => {
    localFilters.value = { ...localFilters.value, ...newFilters };
}, { deep: true });
</script>

<style scoped>
.organization-filters .card {
    padding: 1.5rem;
}

.organization-filters .border-top-1 {
    border-top: 1px solid var(--surface-border);
}

.organization-filters .surface-border {
    color: var(--surface-border);
}

@media (max-width: 768px) {
    .organization-filters .flex-column {
        gap: 1rem;
    }
    
    .organization-filters .flex-1 {
        min-width: 100%;
    }
}
</style>
