<template>
    <div class="contract-filters">
        <div class="card">
            <div class="flex flex-column md:flex-row gap-3 align-items-end">
                <!-- Поиск по номеру договора -->
                <div class="flex-1">
                    <label for="search-number" class="block text-sm font-medium mb-2">
                        Поиск по номеру
                    </label>
                    <InputText
                        id="search-number"
                        v-model="localFilters.contractNumber"
                        placeholder="Введите номер договора..."
                        class="w-full"
                        @input="onFilterChange"
                    />
                </div>

                <!-- Поиск по названию -->
                <div class="flex-1">
                    <label for="search-name" class="block text-sm font-medium mb-2">
                        Поиск по названию
                    </label>
                    <InputText
                        id="search-name"
                        v-model="localFilters.contractName"
                        placeholder="Введите название договора..."
                        class="w-full"
                        @input="onFilterChange"
                    />
                </div>

                <!-- Код проекта -->
                <div class="flex-1">
                    <label for="search-project" class="block text-sm font-medium mb-2">
                        Код проекта
                    </label>
                    <InputText
                        id="search-project"
                        v-model="localFilters.projectCode"
                        placeholder="Введите код проекта..."
                        class="w-full"
                        @input="onFilterChange"
                    />
                </div>

                <!-- Статус договора -->
                <div class="flex-1">
                    <label for="filter-status" class="block text-sm font-medium mb-2">
                        Статус
                    </label>
                    <Dropdown
                        id="filter-status"
                        v-model="localFilters.status"
                        :options="statusOptions"
                        option-label="label"
                        option-value="value"
                        placeholder="Все статусы"
                        class="w-full"
                        show-clear
                        @change="onFilterChange"
                    />
                </div>

                <!-- Тип договора -->
                <div class="flex-1">
                    <label for="filter-type" class="block text-sm font-medium mb-2">
                        Тип договора
                    </label>
                    <Dropdown
                        id="filter-type"
                        v-model="localFilters.contractType"
                        :options="contractTypeOptions"
                        option-label="label"
                        option-value="value"
                        placeholder="Все типы"
                        class="w-full"
                        show-clear
                        @change="onFilterChange"
                    />
                </div>

                <!-- Кнопки действий -->
                <div class="flex gap-2">
                    <Button
                        icon="pi pi-search"
                        label="Найти"
                        @click="onSearch"
                        :loading="searching"
                    />
                    <Button
                        icon="pi pi-times"
                        label="Очистить"
                        outlined
                        @click="onClear"
                    />
                </div>
            </div>

            <!-- Дополнительные фильтры (сворачиваемые) -->
            <div v-if="showAdvanced" class="mt-4 pt-4 border-top-1 surface-border">
                <div class="grid">
                    <!-- Тип проекта -->
                    <div class="col-12 md:col-4">
                        <label for="filter-project-type" class="block text-sm font-medium mb-2">
                            Тип проекта
                        </label>
                        <Dropdown
                            id="filter-project-type"
                            v-model="localFilters.projectType"
                            :options="projectTypeOptions"
                            option-label="label"
                            option-value="value"
                            placeholder="Все типы"
                            class="w-full"
                            show-clear
                            @change="onFilterChange"
                        />
                    </div>

                    <!-- Дата подписания (от) -->
                    <div class="col-12 md:col-4">
                        <label for="filter-date-from" class="block text-sm font-medium mb-2">
                            Подписан с
                        </label>
                        <Calendar
                            id="filter-date-from"
                            v-model="localFilters.signatureDateFrom"
                            placeholder="Выберите дату"
                            class="w-full"
                            show-icon
                            date-format="dd.mm.yy"
                            @date-select="onFilterChange"
                        />
                    </div>

                    <!-- Дата подписания (до) -->
                    <div class="col-12 md:col-4">
                        <label for="filter-date-to" class="block text-sm font-medium mb-2">
                            Подписан до
                        </label>
                        <Calendar
                            id="filter-date-to"
                            v-model="localFilters.signatureDateTo"
                            placeholder="Выберите дату"
                            class="w-full"
                            show-icon
                            date-format="dd.mm.yy"
                            @date-select="onFilterChange"
                        />
                    </div>

                    <!-- Сумма договора (от) -->
                    <div class="col-12 md:col-4">
                        <label for="filter-amount-from" class="block text-sm font-medium mb-2">
                            Сумма от
                        </label>
                        <InputNumber
                            id="filter-amount-from"
                            v-model="localFilters.totalAmountFrom"
                            placeholder="0"
                            class="w-full"
                            mode="currency"
                            currency="RUB"
                            locale="ru-RU"
                            @input="onFilterChange"
                        />
                    </div>

                    <!-- Сумма договора (до) -->
                    <div class="col-12 md:col-4">
                        <label for="filter-amount-to" class="block text-sm font-medium mb-2">
                            Сумма до
                        </label>
                        <InputNumber
                            id="filter-amount-to"
                            v-model="localFilters.totalAmountTo"
                            placeholder="0"
                            class="w-full"
                            mode="currency"
                            currency="RUB"
                            locale="ru-RU"
                            @input="onFilterChange"
                        />
                    </div>

                    <!-- Организация -->
                    <div class="col-12 md:col-4">
                        <label for="filter-organization" class="block text-sm font-medium mb-2">
                            Организация
                        </label>
                        <Dropdown
                            id="filter-organization"
                            v-model="localFilters.organizationId"
                            :options="organizationOptions"
                            option-label="name"
                            option-value="id"
                            placeholder="Все организации"
                            class="w-full"
                            show-clear
                            filter
                            @change="onFilterChange"
                        />
                    </div>
                </div>
            </div>

            <!-- Переключатель расширенных фильтров -->
            <div class="flex justify-content-between align-items-center mt-3">
                <Button
                    :icon="showAdvanced ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
                    :label="showAdvanced ? 'Скрыть фильтры' : 'Дополнительные фильтры'"
                    text
                    size="small"
                    @click="showAdvanced = !showAdvanced"
                />

                <!-- Счетчик активных фильтров -->
                <div v-if="activeFiltersCount > 0" class="text-sm text-color-secondary">
                    Активных фильтров: {{ activeFiltersCount }}
                </div>
            </div>

            <!-- Быстрые фильтры -->
            <div class="flex flex-wrap gap-2 mt-3">
                <Button
                    label="Активные"
                    size="small"
                    outlined
                    @click="applyQuickFilter('active')"
                />
                <Button
                    label="Истекающие"
                    size="small"
                    outlined
                    severity="warning"
                    @click="applyQuickFilter('expiring')"
                />
                <Button
                    label="Черновики"
                    size="small"
                    outlined
                    severity="secondary"
                    @click="applyQuickFilter('draft')"
                />
                <Button
                    label="Ошибки синхронизации"
                    size="small"
                    outlined
                    severity="danger"
                    @click="applyQuickFilter('sync_error')"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { 
    CONTRACT_STATUS_OPTIONS, 
    CONTRACT_TYPE_OPTIONS, 
    PROJECT_TYPE_OPTIONS 
} from '@/constants/contract';

const props = defineProps({
    filters: {
        type: Object,
        default: () => ({})
    },
    searching: {
        type: Boolean,
        default: false
    },
    debounceMs: {
        type: Number,
        default: 500
    },
    organizations: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['update:filters', 'search', 'clear']);

// Локальные фильтры
const localFilters = ref({
    contractNumber: '',
    contractName: '',
    projectCode: '',
    status: null,
    contractType: null,
    projectType: null,
    signatureDateFrom: null,
    signatureDateTo: null,
    totalAmountFrom: null,
    totalAmountTo: null,
    organizationId: null,
    ...props.filters
});

// Состояние расширенных фильтров
const showAdvanced = ref(false);

// Опции для выпадающих списков
const statusOptions = ref([
    { label: 'Все статусы', value: null },
    ...CONTRACT_STATUS_OPTIONS
]);

const contractTypeOptions = ref([
    { label: 'Все типы', value: null },
    ...CONTRACT_TYPE_OPTIONS
]);

const projectTypeOptions = ref([
    { label: 'Все типы', value: null },
    ...PROJECT_TYPE_OPTIONS
]);

const organizationOptions = computed(() => [
    { id: null, name: 'Все организации' },
    ...props.organizations
]);

// Счетчик активных фильтров
const activeFiltersCount = computed(() => {
    let count = 0;
    
    if (localFilters.value.contractNumber?.trim()) count++;
    if (localFilters.value.contractName?.trim()) count++;
    if (localFilters.value.projectCode?.trim()) count++;
    if (localFilters.value.status) count++;
    if (localFilters.value.contractType) count++;
    if (localFilters.value.projectType) count++;
    if (localFilters.value.signatureDateFrom) count++;
    if (localFilters.value.signatureDateTo) count++;
    if (localFilters.value.totalAmountFrom) count++;
    if (localFilters.value.totalAmountTo) count++;
    if (localFilters.value.organizationId) count++;
    
    return count;
});

// Debounce для автоматического поиска
let debounceTimer = null;

const onFilterChange = () => {
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }
    
    debounceTimer = setTimeout(() => {
        emit('update:filters', { ...localFilters.value });
        emit('search');
    }, props.debounceMs);
};

const onSearch = () => {
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }
    
    emit('update:filters', { ...localFilters.value });
    emit('search');
};

const onClear = () => {
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }
    
    localFilters.value = {
        contractNumber: '',
        contractName: '',
        projectCode: '',
        status: null,
        contractType: null,
        projectType: null,
        signatureDateFrom: null,
        signatureDateTo: null,
        totalAmountFrom: null,
        totalAmountTo: null,
        organizationId: null
    };
    
    emit('update:filters', { ...localFilters.value });
    emit('clear');
};

// Быстрые фильтры
const applyQuickFilter = (type) => {
    onClear();
    
    switch (type) {
        case 'active':
            localFilters.value.status = 'active';
            break;
        case 'expiring':
            localFilters.value.status = 'expiring';
            break;
        case 'draft':
            localFilters.value.status = 'draft';
            break;
        case 'sync_error':
            // Этот фильтр требует дополнительной логики в родительском компоненте
            break;
    }
    
    onSearch();
};

// Синхронизация с внешними фильтрами
watch(() => props.filters, (newFilters) => {
    localFilters.value = { ...localFilters.value, ...newFilters };
}, { deep: true });
</script>

<style scoped>
.contract-filters .card {
    padding: 1.5rem;
}

.contract-filters .border-top-1 {
    border-top: 1px solid var(--surface-border);
}

.contract-filters .surface-border {
    color: var(--surface-border);
}

@media (max-width: 768px) {
    .contract-filters .flex-column {
        gap: 1rem;
    }
    
    .contract-filters .flex-1 {
        min-width: 100%;
    }
}
</style>
