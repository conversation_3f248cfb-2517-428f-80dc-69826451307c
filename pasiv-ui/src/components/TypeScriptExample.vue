<template>
  <div class="typescript-example">
    <h2>TypeScript Example Component</h2>
    
    <div class="organization-info">
      <h3>Организация</h3>
      <p><strong>Название:</strong> {{ organization.name }}</p>
      <p><strong>ИНН:</strong> {{ organization.inn }}</p>
      <p><strong>Статус:</strong> {{ organization.status }}</p>
      <p><strong>Последняя синхронизация:</strong> {{ formattedSyncDate }}</p>
    </div>

    <div class="actions">
      <button @click="loadOrganization" :disabled="loading">
        {{ loading ? 'Загрузка...' : 'Загрузить организацию' }}
      </button>
      <button @click="testGrpcService" :disabled="loading">
        Тест gRPC сервиса
      </button>
    </div>

    <div v-if="error" class="error">
      <p><strong>Ошибка:</strong> {{ error }}</p>
    </div>

    <div v-if="serviceInfo" class="service-info">
      <h3>Информация о сервисе</h3>
      <p>Сервис успешно импортирован: {{ serviceInfo.imported ? 'Да' : 'Нет' }}</p>
      <p>Доступные методы: {{ serviceInfo.methodCount }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { FrontendOrganization } from '@/types/grpc';
import pasivGatePrivateService from '@/service/PasivGatePrivateService';

// Реактивные данные с типизацией
const organization = ref<FrontendOrganization>({
  id: '1',
  name: 'ООО "Пример Организации"',
  inn: '1234567890',
  kpp: '123456789',
  status: 'active',
  syncStatus: 'synced',
  lastSyncDate: new Date().toISOString()
});

const loading = ref<boolean>(false);
const error = ref<string | null>(null);
const serviceInfo = ref<{
  imported: boolean;
  methodCount: number;
} | null>(null);

// Вычисляемые свойства с типизацией
const formattedSyncDate = computed<string>(() => {
  if (!organization.value.lastSyncDate) return 'Неизвестно';
  
  const date = new Date(organization.value.lastSyncDate);
  return date.toLocaleString('ru-RU');
});

// Методы с типизацией
const loadOrganization = async (): Promise<void> => {
  loading.value = true;
  error.value = null;
  
  try {
    // Симуляция загрузки данных
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    organization.value = {
      ...organization.value,
      name: 'ООО "Загруженная Организация"',
      inn: '9876543210',
      lastSyncDate: new Date().toISOString()
    };
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Неизвестная ошибка';
  } finally {
    loading.value = false;
  }
};

const testGrpcService = async (): Promise<void> => {
  loading.value = true;
  error.value = null;
  
  try {
    // Тестируем методы сервиса
    const testOrg: FrontendOrganization = {
      name: 'Тестовая организация',
      inn: '1111111111'
    };
    
    // Тестируем маппинг (это работает, так как не требует gRPC соединения)
    const mappedOrg = pasivGatePrivateService.mapOrganizationToProto(testOrg);
    console.log('Mapped organization:', mappedOrg);
    
    serviceInfo.value = {
      imported: true,
      methodCount: Object.getOwnPropertyNames(Object.getPrototypeOf(pasivGatePrivateService)).length
    };
    
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Ошибка тестирования сервиса';
  } finally {
    loading.value = false;
  }
};

// Хук жизненного цикла
onMounted((): void => {
  console.log('TypeScript компонент смонтирован');
  
  // Проверяем доступность сервиса
  serviceInfo.value = {
    imported: !!pasivGatePrivateService,
    methodCount: Object.getOwnPropertyNames(Object.getPrototypeOf(pasivGatePrivateService)).length
  };
});
</script>

<style scoped>
.typescript-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.organization-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.organization-info h3 {
  margin-top: 0;
  color: #333;
}

.actions {
  margin-bottom: 20px;
}

.actions button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
  font-size: 14px;
}

.actions button:hover:not(:disabled) {
  background: #0056b3;
}

.actions button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin-bottom: 20px;
}

.service-info {
  background: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
}

.service-info h3 {
  margin-top: 0;
}
</style>
