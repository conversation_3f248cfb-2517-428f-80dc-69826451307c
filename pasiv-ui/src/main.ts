// Инициализируем polyfill для gRPC перед всеми остальными импортами
import './utils/grpc-polyfill';

import { createApp } from 'vue';
import App from './App.vue';
import router from './router/index';

import Aura from '@primeuix/themes/aura';
import PrimeVue from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import ToastService from 'primevue/toastservice';
import Tooltip from 'primevue/tooltip';
import StyleClass from 'primevue/styleclass';

import '@/assets/styles.scss';

// Асинхронная инициализация приложения
async function initApp() {
    // Асинхронный импорт common-ui для безопасности
    let Tkp3CommonUI: any = null;
    try {
        const commonUIModule = await import('@tkp3/common-ui');
        Tkp3CommonUI = commonUIModule.default;
        // Импортируем стили
        await import('@tkp3/common-ui/style.css');
        console.log('✅ Tkp3CommonUI module loaded successfully');
    } catch (error) {
        console.warn('⚠️ Could not load Tkp3CommonUI:', error);
    }

    // Создаем приложение Vue
    const app = createApp(App);

    // Настраиваем роутер
    app.use(router);

    // Настраиваем PrimeVue
    app.use(PrimeVue, {
        theme: {
            preset: Aura
        }
    });

    // Добавляем сервисы PrimeVue
    app.use(ToastService);
    app.use(ConfirmationService);

    // Добавляем директивы
    app.directive('tooltip', Tooltip);
    app.directive('styleclass', StyleClass);

    // Добавляем common-ui если загружен
    if (Tkp3CommonUI) {
        try {
            app.use(Tkp3CommonUI);
            console.log('✅ Tkp3CommonUI successfully registered');
        } catch (error) {
            console.error('❌ Error registering Tkp3CommonUI:', error);
        }
    }

    // Монтируем приложение с проверкой существования элемента
    const appElement = document.getElementById('app');
    if (appElement) {
        app.mount('#app');
        console.log('✅ Vue app mounted successfully');
    } else {
        console.error('❌ Element #app not found in DOM');
    }
}

// Запускаем инициализацию
initApp().catch(error => {
    console.error('❌ Failed to initialize app:', error);
});
