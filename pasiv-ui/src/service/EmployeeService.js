export class EmployeeService {
    static getData() {
        return [
            {
                id: 1,
                firstName: 'Иван',
                lastName: 'Петров',
                middleName: 'Серг<PERSON>евич',
                email: '<EMAIL>',
                phone: '+7 (495) 123-45-67',
                position: 'Главный бухгалтер',
                organizationId: 1,
                status: 'active',
                roles: ['pasiv_admin', 'billing_operator'],
                createdDate: '2024-01-15T10:00:00Z',
                lastLoginDate: '2024-02-01T09:30:00Z'
            },
            {
                id: 2,
                firstName: 'Мария',
                lastName: 'Сидорова',
                middleName: 'Александровна',
                email: 'm.si<PERSON><PERSON>@example.com',
                phone: '+7 (495) 234-56-78',
                position: 'Аналитик',
                organizationId: 1,
                status: 'active',
                roles: ['report_analyst'],
                createdDate: '2024-01-20T14:00:00Z',
                lastLoginDate: '2024-01-31T16:45:00Z'
            },
            {
                id: 3,
                firstName: 'Алексей',
                lastName: 'Козлов',
                middleName: 'Владимирович',
                email: '<EMAIL>',
                phone: '+7 (495) 345-67-89',
                position: 'Контролер',
                organizationId: 2,
                status: 'pending',
                roles: ['settlement_controller'],
                createdDate: '2024-02-01T11:00:00Z',
                lastLoginDate: null
            },
            {
                id: 4,
                firstName: 'Елена',
                lastName: 'Морозова',
                middleName: 'Игоревна',
                email: '<EMAIL>',
                phone: '+7 (495) 456-78-90',
                position: 'Оператор',
                organizationId: 2,
                status: 'blocked',
                roles: ['billing_operator'],
                createdDate: '2024-01-10T09:00:00Z',
                lastLoginDate: '2024-01-25T12:00:00Z'
            }
        ];
    }

    static getEmployeesByOrganization(organizationId) {
        const employees = this.getData();
        const filtered = employees.filter(emp => emp.organizationId == organizationId);
        return Promise.resolve(filtered);
    }

    static getEmployeeById(employeeId) {
        const employees = this.getData();
        const employee = employees.find(emp => emp.id == employeeId);
        return Promise.resolve(employee);
    }

    static createEmployee(organizationId, employeeData) {
        console.log('Creating employee for organization:', organizationId, employeeData);

        const newEmployee = {
            ...employeeData,
            id: Date.now(),
            organizationId: parseInt(organizationId),
            status: 'pending',
            createdDate: new Date().toISOString(),
            lastLoginDate: null
        };

        return Promise.resolve(newEmployee);
    }

    static updateEmployee(employeeId, employeeData) {
        console.log('Updating employee:', employeeId, employeeData);
        return Promise.resolve({ ...employeeData, id: employeeId });
    }

    static deleteEmployee(employeeId) {
        console.log('Deleting employee:', employeeId);
        return Promise.resolve({ success: true });
    }

    static assignRoles(employeeId, roles) {
        console.log('Assigning roles to employee:', employeeId, roles);
        return Promise.resolve({ success: true });
    }

    static blockEmployee(employeeId) {
        console.log('Blocking employee:', employeeId);
        return Promise.resolve({ success: true });
    }

    static unblockEmployee(employeeId) {
        console.log('Unblocking employee:', employeeId);
        return Promise.resolve({ success: true });
    }

    static getEmployeeAuditLog(employeeId) {
        return Promise.resolve([
            {
                id: 1,
                action: 'CREATE',
                description: 'Сотрудник создан',
                userId: 'admin',
                timestamp: '2024-01-15T10:00:00Z',
                details: { status: 'pending' }
            },
            {
                id: 2,
                action: 'UPDATE',
                description: 'Назначены роли',
                userId: 'admin',
                timestamp: '2024-01-15T10:30:00Z',
                details: { roles: ['pasiv_admin'] }
            },
            {
                id: 3,
                action: 'ACTIVATE',
                description: 'Сотрудник активирован',
                userId: 'admin',
                timestamp: '2024-01-15T11:00:00Z',
                details: { status: 'active' }
            }
        ]);
    }
}
