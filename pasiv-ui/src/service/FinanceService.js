export class FinanceService {
    static getOrganizationFinanceInfo(organizationId, period = 'current_month') {
        // Мок данные для демонстрации
        const mockData = {
            totalRevenue: 2450000,
            revenueChange: 12.5,
            totalCommissions: 245000,
            commissionsChange: 8.3,
            totalTransactions: 15420,
            transactionsChange: 15.7,
            averageTicket: 158.9,
            averageTicketChange: -2.1,
            paymentMethods: [
                {
                    name: 'Банковские карты',
                    amount: 1470000,
                    percentage: 60.0,
                    color: '#3b82f6'
                },
                {
                    name: 'Транспортные карты',
                    amount: 735000,
                    percentage: 30.0,
                    color: '#10b981'
                },
                {
                    name: 'Наличные',
                    amount: 245000,
                    percentage: 10.0,
                    color: '#f59e0b'
                }
            ],
            topRoutes: [
                {
                    id: 1,
                    name: 'Маршрут №1 (Центр - Аэропорт)',
                    revenue: 450000,
                    transactions: 2850
                },
                {
                    id: 2,
                    name: 'Маршрут №15 (Вокзал - Университет)',
                    revenue: 380000,
                    transactions: 2390
                },
                {
                    id: 3,
                    name: 'Маршрут №7 (Площадь - Рынок)',
                    revenue: 320000,
                    transactions: 2015
                },
                {
                    id: 4,
                    name: 'Маршрут №22 (Больница - Школа)',
                    revenue: 280000,
                    transactions: 1760
                },
                {
                    id: 5,
                    name: 'Маршрут №9 (Парк - Театр)',
                    revenue: 250000,
                    transactions: 1575
                }
            ]
        };

        return Promise.resolve(mockData);
    }

    static getContractFinanceInfo(contractId, period = 'current_month') {
        const mockData = {
            totalRevenue: 850000,
            revenueChange: 8.2,
            totalCommissions: 85000,
            commissionsChange: 5.1,
            totalTransactions: 5340,
            transactionsChange: 12.3,
            averageTicket: 159.2,
            averageTicketChange: -1.8,
            paymentMethods: [
                {
                    name: 'EMV карты',
                    amount: 510000,
                    percentage: 60.0,
                    color: '#3b82f6'
                },
                {
                    name: 'Транспортные карты',
                    amount: 255000,
                    percentage: 30.0,
                    color: '#10b981'
                },
                {
                    name: 'Наличные',
                    amount: 85000,
                    percentage: 10.0,
                    color: '#f59e0b'
                }
            ],
            calculationPeriods: [
                {
                    id: 1,
                    name: 'Январь 2024',
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    status: 'closed',
                    revenue: 780000,
                    transactions: 4920
                },
                {
                    id: 2,
                    name: 'Февраль 2024',
                    startDate: '2024-02-01',
                    endDate: '2024-02-29',
                    status: 'active',
                    revenue: 850000,
                    transactions: 5340
                }
            ]
        };

        return Promise.resolve(mockData);
    }

    static getBillingData(filters = {}) {
        const mockData = [
            {
                id: 1,
                organizationId: 1,
                contractId: 1,
                period: '2024-02',
                totalAmount: 850000,
                commissionAmount: 85000,
                transactionCount: 5340,
                status: 'calculated',
                calculatedDate: '2024-03-01T10:00:00Z',
                paymentDueDate: '2024-03-15T23:59:59Z'
            },
            {
                id: 2,
                organizationId: 2,
                contractId: 2,
                period: '2024-02',
                totalAmount: 1200000,
                commissionAmount: 120000,
                transactionCount: 7560,
                status: 'paid',
                calculatedDate: '2024-03-01T10:00:00Z',
                paymentDueDate: '2024-03-15T23:59:59Z',
                paidDate: '2024-03-10T14:30:00Z'
            }
        ];

        return Promise.resolve(mockData);
    }

    static getClearingData(filters = {}) {
        const mockData = [
            {
                id: 1,
                period: '2024-02',
                participantCount: 15,
                totalAmount: 12500000,
                status: 'completed',
                startDate: '2024-03-01T09:00:00Z',
                completedDate: '2024-03-01T18:30:00Z',
                fileGenerated: true
            },
            {
                id: 2,
                period: '2024-01',
                participantCount: 14,
                totalAmount: 11800000,
                status: 'completed',
                startDate: '2024-02-01T09:00:00Z',
                completedDate: '2024-02-01T17:45:00Z',
                fileGenerated: true
            }
        ];

        return Promise.resolve(mockData);
    }

    static getSettlementData(filters = {}) {
        const mockData = [
            {
                id: 1,
                organizationId: 1,
                contractId: 1,
                period: '2024-02',
                scheme: 'through_operator',
                amount: 765000,
                commission: 85000,
                status: 'pending',
                dueDate: '2024-03-15T23:59:59Z'
            },
            {
                id: 2,
                organizationId: 2,
                contractId: 2,
                period: '2024-02',
                scheme: 'direct',
                amount: 1080000,
                commission: 120000,
                status: 'completed',
                dueDate: '2024-03-15T23:59:59Z',
                completedDate: '2024-03-10T16:20:00Z'
            }
        ];

        return Promise.resolve(mockData);
    }

    static generateReport(reportType, parameters) {
        console.log('Generating report:', reportType, parameters);
        
        const mockReport = {
            id: Date.now(),
            type: reportType,
            parameters,
            status: 'generating',
            createdDate: new Date().toISOString(),
            estimatedCompletion: new Date(Date.now() + 30000).toISOString()
        };

        return Promise.resolve(mockReport);
    }

    static getReportStatus(reportId) {
        return Promise.resolve({
            id: reportId,
            status: 'completed',
            downloadUrl: `/api/reports/${reportId}/download`,
            completedDate: new Date().toISOString()
        });
    }
}
