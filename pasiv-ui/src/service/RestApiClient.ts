/**
 * REST API клиент для работы с PASIV Gate Private API
 * Заменяет gRPC клиент на стандартные HTTP запросы
 */

import { defaultHttpClient, type HttpClient } from './HttpClient';
import type {
    ApiResponse,
    OrganizationDto,
    OrganizationWithAddressesDto,
    OrganizationFilterDto,
    OrganizationListResponse,
    AddressDto,
    AddressCreateOrDeleteDto,
    AddressListResponse,
    ContactDto,
    ContactListResponse,
    OrganizationHintDto,
    OrganizationHintListDto,
    HistoryResultDto,
    PaginationRequest,
    FrontendOrganization,
    FrontendAddress,
    FrontendContact,
    OrganizationListParams,
    ServiceResponse
} from '@/types/rest-api';

/**
 * REST API клиент для работы с организациями, адресами и контактами
 */
export class RestApiClient {
    private httpClient: HttpClient;

    constructor(httpClient: HttpClient = defaultHttpClient) {
        this.httpClient = httpClient;
    }

    // ===== ОРГАНИЗАЦИИ =====

    /**
     * Создание организации с адресами
     */
    async createOrganization(data: OrganizationWithAddressesDto): Promise<ApiResponse<void>> {
        return this.httpClient.post('/organizations', data);
    }

    /**
     * Обновление организации
     */
    async updateOrganization(id: string, data: OrganizationDto): Promise<ApiResponse<void>> {
        return this.httpClient.put(`/organizations/${id}`, data);
    }

    /**
     * Получение списка организаций
     */
    async getOrganizations(params: {
        page?: number;
        size?: number;
        name?: string;
        inn?: string;
        kpp?: string;
        includeDeleted?: boolean;
    } = {}): Promise<OrganizationListResponse> {
        const searchParams = new URLSearchParams();

        if (params.page !== undefined) searchParams.set('page', params.page.toString());
        if (params.size !== undefined) searchParams.set('size', params.size.toString());
        if (params.name) searchParams.set('name', params.name);
        if (params.inn) searchParams.set('inn', params.inn);
        if (params.kpp) searchParams.set('kpp', params.kpp);
        if (params.includeDeleted) searchParams.set('includeDeleted', 'true');

        const url = `/organizations?${searchParams.toString()}`;
        const response = await this.httpClient.get<OrganizationListResponse>(url);

        if (response.success && response.data) {
            return response.data;
        } else {
            return {
                content: [],
                pagination: {
                    page: params.page || 0,
                    size: params.size || 20,
                    totalElements: 0,
                    totalPages: 0
                },
                success: false,
                error: response.error
            };
        }
    }

    /**
     * Получение организации по ID
     */
    async getOrganizationById(id: string): Promise<ApiResponse<OrganizationDto>> {
        return this.httpClient.get(`/organizations/${id}`);
    }

    /**
     * Удаление организации (мягкое)
     */
    async deleteOrganization(id: string): Promise<ApiResponse<void>> {
        return this.httpClient.delete(`/organizations/${id}`);
    }

    /**
     * Восстановление организации
     */
    async recoverOrganization(id: string): Promise<ApiResponse<void>> {
        return this.httpClient.post(`/organizations/${id}/recover`);
    }

    /**
     * Получение истории изменений организации
     */
    async getOrganizationHistory(id: string, pagination?: PaginationRequest): Promise<ApiResponse<HistoryResultDto>> {
        const searchParams = new URLSearchParams();
        if (pagination?.page !== undefined) searchParams.set('page', pagination.page.toString());
        if (pagination?.size !== undefined) searchParams.set('size', pagination.size.toString());

        const url = `/organizations/${id}/history?${searchParams.toString()}`;
        return this.httpClient.get(url);
    }

    /**
     * Добавление организации в проект
     */
    async addOrganizationToProject(organizationId: string, projectId: string): Promise<ApiResponse<void>> {
        return this.httpClient.post(`/organizations/${organizationId}/projects/${projectId}`);
    }

    /**
     * Удаление организации из проекта
     */
    async removeOrganizationFromProject(organizationId: string, projectId: string): Promise<ApiResponse<void>> {
        return this.httpClient.delete(`/organizations/${organizationId}/projects/${projectId}`);
    }

    /**
     * Получение организаций для проекта
     */
    async getOrganizationsForProject(projectId: string, pagination?: PaginationRequest): Promise<OrganizationListResponse> {
        const searchParams = new URLSearchParams();
        if (pagination?.page !== undefined) searchParams.set('page', pagination.page.toString());
        if (pagination?.size !== undefined) searchParams.set('size', pagination.size.toString());

        const url = `/organizations/projects/${projectId}?${searchParams.toString()}`;
        const response = await this.httpClient.get<OrganizationListResponse>(url);

        if (response.success && response.data) {
            return response.data;
        } else {
            return {
                content: [],
                pagination: {
                    page: pagination?.page || 0,
                    size: pagination?.size || 20,
                    totalElements: 0,
                    totalPages: 0
                },
                success: false,
                error: response.error
            };
        }
    }

    // ===== АДРЕСА =====

    /**
     * Создание адреса
     */
    async createAddress(data: AddressCreateOrDeleteDto): Promise<ApiResponse<void>> {
        return this.httpClient.post('/addresses', data);
    }

    /**
     * Обновление адреса
     */
    async updateAddress(id: string, data: AddressCreateOrDeleteDto): Promise<ApiResponse<void>> {
        return this.httpClient.put(`/addresses/${id}`, data);
    }

    /**
     * Получение списка адресов организации
     */
    async getAddresses(organizationId: string, pagination?: PaginationRequest): Promise<AddressListResponse> {
        const searchParams = new URLSearchParams();
        searchParams.set('organizationId', organizationId);
        if (pagination?.page !== undefined) searchParams.set('page', pagination.page.toString());
        if (pagination?.size !== undefined) searchParams.set('size', pagination.size.toString());

        const url = `/addresses?${searchParams.toString()}`;
        const response = await this.httpClient.get<AddressListResponse>(url);

        if (response.success && response.data) {
            return response.data;
        } else {
            return {
                addresses: [],
                success: false,
                error: response.error
            };
        }
    }

    /**
     * Получение адреса по ID
     */
    async getAddressById(id: string): Promise<ApiResponse<AddressDto>> {
        return this.httpClient.get(`/addresses/${id}`);
    }

    /**
     * Удаление адреса
     */
    async deleteAddress(id: string): Promise<ApiResponse<void>> {
        return this.httpClient.delete(`/addresses/${id}`);
    }

    /**
     * Восстановление адреса
     */
    async recoverAddress(id: string): Promise<ApiResponse<void>> {
        return this.httpClient.post(`/addresses/${id}/recover`);
    }

    /**
     * Получение истории изменений адреса
     */
    async getAddressHistory(id: string, pagination?: PaginationRequest): Promise<ApiResponse<HistoryResultDto>> {
        const searchParams = new URLSearchParams();
        if (pagination?.page !== undefined) searchParams.set('page', pagination.page.toString());
        if (pagination?.size !== undefined) searchParams.set('size', pagination.size.toString());

        const url = `/addresses/${id}/history?${searchParams.toString()}`;
        return this.httpClient.get(url);
    }

    // ===== КОНТАКТЫ =====

    /**
     * Создание контакта
     */
    async createContact(data: ContactDto): Promise<ApiResponse<void>> {
        return this.httpClient.post('/contacts', data);
    }

    /**
     * Обновление контакта
     */
    async updateContact(id: string, data: ContactDto): Promise<ApiResponse<void>> {
        return this.httpClient.put(`/contacts/${id}`, data);
    }

    /**
     * Получение списка контактов организации
     */
    async getContacts(organizationId: string, pagination?: PaginationRequest): Promise<ContactListResponse> {
        const searchParams = new URLSearchParams();
        searchParams.set('organizationId', organizationId);
        if (pagination?.page !== undefined) searchParams.set('page', pagination.page.toString());
        if (pagination?.size !== undefined) searchParams.set('size', pagination.size.toString());

        const url = `/contacts?${searchParams.toString()}`;
        const response = await this.httpClient.get<ContactListResponse>(url);

        if (response.success && response.data) {
            return response.data;
        } else {
            return {
                contacts: [],
                success: false,
                error: response.error
            };
        }
    }

    /**
     * Получение контакта по ID
     */
    async getContactById(id: string): Promise<ApiResponse<ContactDto>> {
        return this.httpClient.get(`/contacts/${id}`);
    }

    /**
     * Удаление контакта
     */
    async deleteContact(id: string): Promise<ApiResponse<void>> {
        return this.httpClient.delete(`/contacts/${id}`);
    }

    /**
     * Восстановление контакта
     */
    async recoverContact(id: string): Promise<ApiResponse<void>> {
        return this.httpClient.post(`/contacts/${id}/recover`);
    }

    /**
     * Получение истории изменений контакта
     */
    async getContactHistory(id: string, pagination?: PaginationRequest): Promise<ApiResponse<HistoryResultDto>> {
        const searchParams = new URLSearchParams();
        if (pagination?.page !== undefined) searchParams.set('page', pagination.page.toString());
        if (pagination?.size !== undefined) searchParams.set('size', pagination.size.toString());

        const url = `/contacts/${id}/history?${searchParams.toString()}`;
        return this.httpClient.get(url);
    }

    // ===== DADATA =====

    /**
     * Получение подсказки организации по ИНН
     */
    async getOrganizationHintByInn(inn: string): Promise<ApiResponse<OrganizationHintDto>> {
        const searchParams = new URLSearchParams();
        searchParams.set('inn', inn);

        const url = `/dadata/organizations/hint?${searchParams.toString()}`;
        return this.httpClient.get(url);
    }

    /**
     * Получение подсказок организаций по списку ИНН
     */
    async getOrganizationHints(innList: string[]): Promise<ApiResponse<OrganizationHintListDto>> {
        return this.httpClient.post('/dadata/organizations/hints', innList);
    }

    /**
     * Валидация ИНН
     */
    async validateInn(inn: string): Promise<ApiResponse<any>> {
        const searchParams = new URLSearchParams();
        searchParams.set('inn', inn);

        const url = `/dadata/organizations/validate-inn?${searchParams.toString()}`;
        return this.httpClient.get(url);
    }
}

/**
 * Утилиты для маппинга между REST API типами и frontend типами
 */
export class ApiMapper {
    /**
     * Конвертация OrganizationDto в FrontendOrganization
     */
    static mapOrganizationToFrontend(dto: OrganizationDto): FrontendOrganization {
        return {
            id: dto.id,
            name: dto.name,
            shortName: dto.shortName,
            fullName: dto.name, // Используем name как fullName
            inn: dto.inn,
            kpp: dto.kpp,
            ogrn: dto.ogrn,
            okpo: dto.okpo,
            oktmo: dto.oktmo,
            okved: dto.okved,
            note: dto.note,
            fioDirector: dto.fioDirector,
            generalDirector: dto.fioDirector, // Алиас
            managerActionReason: dto.managerActionReason,
            legalAddress: dto.addressLegal,
            actualAddress: dto.addressActual,
            mailingAddress: dto.addressMailing,
            isDeleted: dto.isDeleted,
            parent: dto.parent ? this.mapOrganizationToFrontend(dto.parent) : undefined,

            // Дополнительные поля для совместимости
            status: dto.isDeleted ? 'inactive' : 'active',
            type: 'organization',
            ownershipForm: this.extractOwnershipForm(dto.name),
            syncStatus: 'synced',
            lastSyncDate: new Date().toISOString(),
            createdDate: new Date().toISOString()
        };
    }

    /**
     * Конвертация FrontendOrganization в OrganizationDto
     */
    static mapOrganizationFromFrontend(frontend: FrontendOrganization): OrganizationDto {
        return {
            id: frontend.id,
            name: frontend.name,
            shortName: frontend.shortName,
            inn: frontend.inn,
            kpp: frontend.kpp,
            ogrn: frontend.ogrn,
            okpo: frontend.okpo,
            oktmo: frontend.oktmo,
            okved: frontend.okved,
            note: frontend.note,
            fioDirector: frontend.fioDirector || frontend.generalDirector,
            managerActionReason: frontend.managerActionReason,
            addressLegal: typeof frontend.legalAddress === 'string' ? frontend.legalAddress : undefined,
            addressActual: typeof frontend.actualAddress === 'string' ? frontend.actualAddress : undefined,
            addressMailing: typeof frontend.mailingAddress === 'string' ? frontend.mailingAddress : undefined,
            isDeleted: frontend.isDeleted || frontend.status === 'inactive' || false,
            parent: frontend.parent ? this.mapOrganizationFromFrontend(frontend.parent) : undefined
        };
    }

    /**
     * Конвертация AddressDto в FrontendAddress
     */
    static mapAddressToFrontend(dto: AddressDto): FrontendAddress {
        return {
            id: dto.id,
            name: dto.name,
            index: dto.index,
            country: dto.country,
            region: dto.region || '',
            district: dto.district,
            city: dto.city || '',
            street: dto.street,
            house: dto.house || '',
            buildingOrHousing: dto.buildingOrHousing,
            officeOrRoom: dto.officeOrRoom,
            longitude: dto.longitude,
            latitude: dto.latitude,
            comment: dto.comment,
            oktmo: dto.oktmo,
            fiac: dto.fiac,
            isDeleted: dto.isDeleted
        };
    }

    /**
     * Конвертация FrontendAddress в AddressDto
     */
    static mapAddressFromFrontend(frontend: FrontendAddress): AddressDto {
        return {
            id: frontend.id,
            name: frontend.name,
            index: frontend.index,
            country: frontend.country,
            region: frontend.region,
            district: frontend.district,
            city: frontend.city,
            street: frontend.street,
            house: frontend.house,
            buildingOrHousing: frontend.buildingOrHousing,
            officeOrRoom: frontend.officeOrRoom,
            longitude: frontend.longitude,
            latitude: frontend.latitude,
            comment: frontend.comment,
            oktmo: frontend.oktmo,
            fiac: frontend.fiac,
            isDeleted: frontend.isDeleted || false
        };
    }

    /**
     * Конвертация ContactDto в FrontendContact
     */
    static mapContactToFrontend(dto: ContactDto): FrontendContact {
        return {
            id: dto.id,
            organizationId: dto.organizationId,
            type: dto.type.toLowerCase() as 'phone' | 'email',
            value: dto.value,
            isDeleted: dto.isDeleted
        };
    }

    /**
     * Конвертация FrontendContact в ContactDto
     */
    static mapContactFromFrontend(frontend: FrontendContact): ContactDto {
        return {
            id: frontend.id,
            organizationId: frontend.organizationId,
            type: frontend.type.toUpperCase() as any,
            value: frontend.value,
            isDeleted: frontend.isDeleted || false
        };
    }

    /**
     * Извлечение формы собственности из названия организации
     */
    static extractOwnershipForm(name: string): string {
        const forms = ['ООО', 'ОАО', 'ЗАО', 'ИП', 'АО', 'ПАО', 'НАО'];
        for (const form of forms) {
            if (name.includes(form)) {
                return form;
            }
        }
        return 'Не определено';
    }
}

// Экспортируем готовый к использованию клиент
export const restApiClient = new RestApiClient();
