/**
 * Сервис для работы с PASIV Gate Private API через REST
 * Заменяет gRPC клиент на REST API вызовы, сохраняя совместимость интерфейсов
 */

import { restApiClient, ApiMapper } from './RestApiClient';
import type {
    FrontendOrganization,
    FrontendAddress,
    ServiceResponse,
    OrganizationListParams,
    PaginationParams
} from '@/types/rest-api';
import type { ApiResponse } from '@/types/organization';

// Интерфейс для совместимости с существующим кодом
export interface PasivGatePrivateServiceMethods {
    // Методы для организаций
    createOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>>;
    updateOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>>;
    organizationList(params?: OrganizationListParams): Promise<ApiResponse<FrontendOrganization[]>>;
    organizationById(id: string): Promise<ApiResponse<FrontendOrganization>>;
    deleteOrganization(id: string): Promise<ApiResponse<void>>;
    recoverOrganization(id: string): Promise<ApiResponse<void>>;

    // Методы для адресов
    createAddress(addressData: FrontendAddress): Promise<ApiResponse<void>>;
    updateAddress(addressData: FrontendAddress): Promise<ApiResponse<void>>;
    addressById(id: string): Promise<ApiResponse<FrontendAddress>>;
    addressList(params?: { pagination?: PaginationParams }): Promise<ApiResponse<FrontendAddress[]>>;
    deleteAddress(id: string): Promise<ApiResponse<void>>;
    recoverAddress(id: string): Promise<ApiResponse<void>>;

    // Утилитарные методы
    mapOrganizationFromProto(protoOrg: any): FrontendOrganization;
    mapOrganizationToProto(orgData: FrontendOrganization): any;
    mapAddressToProto(addressData: FrontendAddress | string): any;
    extractOwnershipForm(name: string): string;
}

// Извлекаем сообщения из common_pb
const { PaginationRequest: CommonPaginationRequest } = require('@/generated/common_pb') as any;

/**
 * Конфигурация gRPC клиента
 */
const GRPC_CONFIG: GrpcConfig = {
    // URL бэкенда - должен быть настроен через переменные окружения
    url: import.meta.env.VITE_PASIV_GATE_PRIVATE_URL || 'http://localhost:5005',
    // Опции для gRPC клиента
    options: {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.http2.min_time_between_pings_ms': 10000,
        'grpc.http2.min_ping_interval_without_data_ms': 300000
    }
};

/**
 * Класс для работы с PASIV Gate Private API
 */
class PasivGatePrivateService implements PasivGatePrivateServiceMethods {
    private client: grpcWebClient = null;

    /**
     * Получить или создать gRPC клиент
     */
    private getClient(): any {
        if (!this.client) {
            this.client = new PASIVGatePrivateServiceClient(
                GRPC_CONFIG.url,
                null, // credentials
                GRPC_CONFIG.options
            );
        }
        return this.client;
    }

    /**
     * Преобразовать организацию из proto в объект фронтенда
     */
    mapOrganizationFromProto(protoOrg: any): FrontendOrganization {
        return {
            id: protoOrg.getId(),
            name: protoOrg.getName(),
            shortName: protoOrg.getShortname(),
            fullName: protoOrg.getName(), // Используем name как fullName
            inn: protoOrg.getInn(),
            kpp: protoOrg.getKpp(),
            ogrn: protoOrg.getOgrn(),
            okpo: protoOrg.getOkpo(),
            oktmo: protoOrg.getOktmo(),
            okved: protoOrg.getOkved(),
            note: protoOrg.getNote(),
            fioDirector: protoOrg.getFiodirector(),
            generalDirector: protoOrg.getFiodirector(), // Алиас для совместимости
            managerActionReason: protoOrg.getManageractionreason(),
            legalAddress: protoOrg.getAddresslegal(),
            actualAddress: protoOrg.getAddressactual(),
            mailingAddress: protoOrg.getAddressmailing(),
            isDeleted: protoOrg.getIsdeleted(),
            parent: protoOrg.getParent() ? this.mapOrganizationFromProto(protoOrg.getParent()!) : null,
            // Дополнительные поля для совместимости с текущим фронтендом
            status: protoOrg.getIsdeleted() ? 'inactive' : 'active',
            type: 'organization', // По умолчанию
            ownershipForm: this.extractOwnershipForm(protoOrg.getName()),
            syncStatus: 'synced', // Если получили из API, значит синхронизировано
            lastSyncDate: new Date().toISOString()
        };
    }

    /**
     * Преобразовать организацию из объекта фронтенда в proto
     */
    mapOrganizationToProto(orgData: FrontendOrganization): any {
        const organization = new Organization();

        if (orgData.id) organization.setId(orgData.id);
        organization.setName(orgData.name || orgData.fullName || '');
        organization.setShortname(orgData.shortName || '');
        organization.setInn(orgData.inn || '');
        organization.setKpp(orgData.kpp || '');
        organization.setOgrn(orgData.ogrn || '');

        if (orgData.okpo) organization.setOkpo(orgData.okpo);
        if (orgData.oktmo) organization.setOktmo(orgData.oktmo);
        if (orgData.okved) organization.setOkved(orgData.okved);
        if (orgData.note) organization.setNote(orgData.note);
        if (orgData.fioDirector || orgData.generalDirector) {
            organization.setFiodirector(orgData.fioDirector || orgData.generalDirector);
        }
        if (orgData.managerActionReason) {
            organization.setManageractionreason(orgData.managerActionReason);
        }

        // Адреса как строки (для совместимости)
        if (orgData.legalAddress && typeof orgData.legalAddress === 'string') {
            organization.setAddresslegal(orgData.legalAddress);
        }
        if (orgData.actualAddress && typeof orgData.actualAddress === 'string') {
            organization.setAddressactual(orgData.actualAddress);
        }
        if (orgData.mailingAddress && typeof orgData.mailingAddress === 'string') {
            organization.setAddressmailing(orgData.mailingAddress);
        }

        organization.setIsdeleted(orgData.isDeleted || orgData.status === 'inactive' || false);

        return organization;
    }

    /**
     * Преобразовать адрес в proto объект
     */
    mapAddressToProto(addressData: FrontendAddress | string): any {
        const address = new Address();

        if (typeof addressData === 'string') {
            // Если адрес передан как строка, парсим его
            address.setName(addressData);
            // Можно добавить парсинг строки адреса на компоненты
            return address;
        }

        // Если адрес передан как объект
        if (addressData.id) address.setId(addressData.id);
        address.setName(addressData.name || '');
        if (addressData.index) address.setIndex(addressData.index);
        if (addressData.country) address.setCountry(addressData.country);
        address.setRegion(addressData.region || '');
        if (addressData.district) address.setDistrict(addressData.district);
        address.setCity(addressData.city || '');
        if (addressData.street) address.setStreet(addressData.street);
        address.setHouse(addressData.house || '');
        if (addressData.buildingOrHousing) address.setBuildingOrHousing(addressData.buildingOrHousing);
        if (addressData.officeOrRoom) address.setOfficeOrRoom(addressData.officeOrRoom);
        if (addressData.longitude) address.setLongitude(addressData.longitude);
        if (addressData.latitude) address.setLatitude(addressData.latitude);
        if (addressData.comment) address.setComment(addressData.comment);
        if (addressData.oktmo) address.setOktmo(addressData.oktmo);
        if (addressData.fiac) address.setFiac(addressData.fiac);
        address.setIsdeleted(addressData.isDeleted || false);

        return address;
    }

    /**
     * Извлечь форму собственности из названия организации
     */
    extractOwnershipForm(name: string): string {
        if (!name) return '';

        const forms = ['ООО', 'ОАО', 'ЗАО', 'ПАО', 'АО', 'ИП', 'ГУП', 'МУП'];
        for (const form of forms) {
            if (name.includes(form)) {
                return form;
            }
        }
        return '';
    }

    // API методы (заглушки для будущей реализации)

    async createOrganization(_orgData: FrontendOrganization): Promise<ApiResponse<void>> {
        // TODO: Реализовать создание организации
        throw new Error('Method not implemented yet');
    }

    async updateOrganization(_orgData: FrontendOrganization): Promise<ApiResponse<void>> {
        // TODO: Реализовать обновление организации
        throw new Error('Method not implemented yet');
    }

    /**
     * Получить список организаций
     * @param params Параметры запроса (пагинация, фильтры, сортировка)
     * @returns Promise с результатом запроса
     */
    async organizationList(params?: OrganizationListParams): Promise<ApiResponse<FrontendOrganization[]>> {
        return new Promise((resolve) => {
            try {
                // Создаем запрос через polyfill
                const request = new (protobufMessages as any).OrganizationListRequest();

                // Настраиваем пагинацию
                if (params?.pagination) {
                    const pagination = new (require('@/generated/common_pb') as any).PaginationRequest();
                    pagination.setPage(params.pagination.page || 1);
                    pagination.setLimit(params.pagination.limit || 10);
                    request.setPagination(pagination);
                }

                // Настраиваем фильтры
                if (params?.filter) {
                    const filter = new (protobufMessages as any).OrganizationFilter();

                    if (params.filter.name) {
                        filter.setName(params.filter.name);
                    }
                    if (params.filter.inn) {
                        filter.setInn(params.filter.inn);
                    }
                    if (params.filter.kpp) {
                        filter.setKpp(params.filter.kpp);
                    }
                    if (params.filter.parentId) {
                        filter.setParentId(params.filter.parentId);
                    }
                    if (typeof params.filter.isDeleted === 'boolean') {
                        filter.setIsDeleted(params.filter.isDeleted);
                    }

                    request.setFilter(filter);
                }

                // Настраиваем сортировку
                if (params?.sorted && params.sorted.length > 0) {
                    // TODO: Добавить поддержку сортировки в protobuf схему
                    console.log('Сортировка пока не поддерживается:', params.sorted);
                }

                // Выполняем gRPC запрос
                const client = this.getClient();
                client.organizationList(request, {}, (error: any, response: any) => {
                    if (error) {
                        console.error('gRPC ошибка при получении списка организаций:', error);
                        resolve({
                            success: false,
                            error: {
                                code: error.code || 'GRPC_ERROR',
                                message: error.message || 'Ошибка при получении списка организаций',
                                details: error
                            }
                        });
                        return;
                    }

                    try {
                        // Преобразуем ответ
                        const responseObj = response.toObject();
                        const organizations: FrontendOrganization[] = [];

                        // Преобразуем каждую организацию из proto в frontend формат
                        if (response.getOrganizationsList) {
                            const protoOrganizations = response.getOrganizationsList();
                            for (const protoOrg of protoOrganizations) {
                                organizations.push(this.mapOrganizationFromProto(protoOrg));
                            }
                        }

                        // Формируем ответ с пагинацией
                        const result: ApiResponse<FrontendOrganization[]> = {
                            success: true,
                            data: organizations
                        };

                        // Добавляем информацию о пагинации если есть
                        if (response.getPagination && response.getPagination()) {
                            const paginationProto = response.getPagination();
                            result.pagination = {
                                page: paginationProto.getPage() || 1,
                                limit: paginationProto.getLimit() || 10,
                                totalPage: paginationProto.getTotalPage() || 1,
                                totalCount: paginationProto.getTotalCount() || organizations.length
                            };
                        }

                        console.log(`✅ Получено ${organizations.length} организаций из gRPC API`);
                        resolve(result);

                    } catch (parseError) {
                        console.error('Ошибка при обработке ответа gRPC:', parseError);
                        resolve({
                            success: false,
                            error: {
                                code: 'PARSE_ERROR',
                                message: 'Ошибка при обработке ответа сервера',
                                details: parseError
                            }
                        });
                    }
                });

            } catch (requestError) {
                console.error('Ошибка при создании gRPC запроса:', requestError);
                resolve({
                    success: false,
                    error: {
                        code: 'REQUEST_ERROR',
                        message: 'Ошибка при создании запроса',
                        details: requestError
                    }
                });
            }
        });
    }

    async organizationById(_id: string): Promise<ApiResponse<FrontendOrganization>> {
        // TODO: Реализовать получение организации по ID
        throw new Error('Method not implemented yet');
    }

    async deleteOrganization(_id: string): Promise<ApiResponse<void>> {
        // TODO: Реализовать удаление организации
        throw new Error('Method not implemented yet');
    }

    async recoverOrganization(_id: string): Promise<ApiResponse<void>> {
        // TODO: Реализовать восстановление организации
        throw new Error('Method not implemented yet');
    }

    async createAddress(_addressData: FrontendAddress): Promise<ApiResponse<void>> {
        // TODO: Реализовать создание адреса
        throw new Error('Method not implemented yet');
    }

    async updateAddress(_addressData: FrontendAddress): Promise<ApiResponse<void>> {
        // TODO: Реализовать обновление адреса
        throw new Error('Method not implemented yet');
    }

    async addressById(_id: string): Promise<ApiResponse<FrontendAddress>> {
        // TODO: Реализовать получение адреса по ID
        throw new Error('Method not implemented yet');
    }

    async addressList(_params?: { pagination?: any }): Promise<ApiResponse<FrontendAddress[]>> {
        // TODO: Реализовать получение списка адресов
        throw new Error('Method not implemented yet');
    }

    async deleteAddress(_id: string): Promise<ApiResponse<void>> {
        // TODO: Реализовать удаление адреса
        throw new Error('Method not implemented yet');
    }

    async recoverAddress(_id: string): Promise<ApiResponse<void>> {
        // TODO: Реализовать восстановление адреса
        throw new Error('Method not implemented yet');
    }


}

// Создаем единственный экземпляр сервиса
const pasivGatePrivateService = new PasivGatePrivateService();

export default pasivGatePrivateService;
export type { PasivGatePrivateService };
