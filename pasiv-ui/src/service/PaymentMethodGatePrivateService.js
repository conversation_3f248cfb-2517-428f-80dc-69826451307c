/**
 * Сервис для работы с Payment Method API через gRPC
 * Обертка над сгенерированным gRPC клиентом для средств оплаты
 */

import { PASIVGatePrivateServiceClient } from '../generated/pasiv-gate-private_grpc_pb';
import {
    ContractPaymentMethod,
    PaymentMethodFilter,
    PaymentMethodListRequest,
    PaymentMethodsByContractRequest,
    PaymentMethodType,
    ByIdRequest
} from '../generated/pasiv-gate-private_pb';
import { PaginationRequest } from '../generated/common_pb';
import { Timestamp } from 'google-protobuf/google/protobuf/timestamp_pb';

/**
 * Конфигурация gRPC клиента для средств оплаты
 */
const GRPC_CONFIG = {
    url: process.env.VUE_APP_PASIV_GATE_PRIVATE_URL || 'http://localhost:5005',
    options: {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.http2.min_time_between_pings_ms': 10000,
        'grpc.http2.min_ping_interval_without_data_ms': 300000
    }
};

/**
 * Маппинг типов средств оплаты между proto и фронтенд
 */
const PAYMENT_METHOD_TYPE_MAP = {
    // Proto -> Frontend
    [PaymentMethodType.PMT_BANK_CARD]: 'BANK_CARD',
    [PaymentMethodType.PMT_CASH]: 'CASH',
    [PaymentMethodType.PMT_TROIKA_SINGLE]: 'TROIKA_SINGLE',
    [PaymentMethodType.PMT_TROIKA_SUBSCRIPTION]: 'TROIKA_SUBSCRIPTION',
    [PaymentMethodType.PMT_MPC_DISCOUNT]: 'MPC_DISCOUNT',
    [PaymentMethodType.PMT_MPC_SOCIAL]: 'MPC_SOCIAL',
    [PaymentMethodType.PMT_MPC_SCHOOL]: 'MPC_SCHOOL',
    [PaymentMethodType.PMT_MPC_STUDENT_SINGLE]: 'MPC_STUDENT_SINGLE',
    [PaymentMethodType.PMT_MPC_STUDENT_SUBSCRIPTION]: 'MPC_STUDENT_SUBSCRIPTION',
    [PaymentMethodType.PMT_TC_RESIDENT]: 'TC_RESIDENT',
    [PaymentMethodType.PMT_MOBILE_BC]: 'MOBILE_BC',
    [PaymentMethodType.PMT_MOBILE_VIRTUAL_TC]: 'MOBILE_VIRTUAL_TC',
    [PaymentMethodType.PMT_MOBILE_SBP]: 'MOBILE_SBP',
    [PaymentMethodType.PMT_REGIONAL_TC]: 'REGIONAL_TC',
    [PaymentMethodType.PMT_SOCIAL_TC]: 'SOCIAL_TC',
    [PaymentMethodType.PMT_OTHER_CARDS]: 'OTHER_CARDS'
};

// Обратный маппинг Frontend -> Proto
const FRONTEND_TO_PROTO_TYPE_MAP = Object.fromEntries(
    Object.entries(PAYMENT_METHOD_TYPE_MAP).map(([proto, frontend]) => [frontend, parseInt(proto)])
);

/**
 * Названия типов средств оплаты
 */
const PAYMENT_METHOD_TYPE_NAMES = {
    'BANK_CARD': 'Банковская карта',
    'CASH': 'Наличные денежные средства',
    'TROIKA_SINGLE': 'Транспортная карта "Тройка" (разовые поездки)',
    'TROIKA_SUBSCRIPTION': 'Транспортная карта "Тройка" (абонемент)',
    'MPC_DISCOUNT': 'МПК Дисконт',
    'MPC_SOCIAL': 'МПК Социальная карта',
    'MPC_SCHOOL': 'МПК "Карта Школьника"',
    'MPC_STUDENT_SINGLE': 'МПК "Карта Студента" (разовые поездки)',
    'MPC_STUDENT_SUBSCRIPTION': 'МПК "Карта Студента" (абонемент)',
    'TC_RESIDENT': 'ТК Карта жителя',
    'MOBILE_BC': 'Мобильное приложение БК',
    'MOBILE_VIRTUAL_TC': 'Мобильное приложение Виртуальная ТК',
    'MOBILE_SBP': 'Мобильное приложение СБП',
    'REGIONAL_TC': 'Транспортная карта региона',
    'SOCIAL_TC': 'Социальная транспортная карта',
    'OTHER_CARDS': 'Иные карты, предусмотренные договором'
};

/**
 * Класс для работы с gRPC API средств оплаты
 */
class PaymentMethodGatePrivateService {
    constructor() {
        this.client = new PASIVGatePrivateServiceClient(GRPC_CONFIG.url, null, GRPC_CONFIG.options);
    }

    /**
     * Получить метаданные для аутентификации
     * @returns {Object} Метаданные с токеном авторизации
     */
    getAuthMetadata() {
        const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
        return {
            'authorization': `Bearer ${token}`
        };
    }

    /**
     * Преобразовать средство оплаты из proto в объект фронтенда
     * @param {ContractPaymentMethod} protoPaymentMethod - Proto объект средства оплаты
     * @returns {Object} Объект средства оплаты для фронтенда
     */
    mapPaymentMethodFromProto(protoPaymentMethod) {
        const methodType = PAYMENT_METHOD_TYPE_MAP[protoPaymentMethod.getMethodtype()];
        return {
            id: protoPaymentMethod.getId(),
            contractId: protoPaymentMethod.getContractid(),
            methodType: methodType,
            code: protoPaymentMethod.getCode() || methodType,
            name: protoPaymentMethod.getName() || PAYMENT_METHOD_TYPE_NAMES[methodType],
            description: protoPaymentMethod.getDescription(),
            isActive: protoPaymentMethod.getIsactive(),
            isDeleted: protoPaymentMethod.getIsdeleted(),
            createdDate: this.timestampToISOString(protoPaymentMethod.getCreateddate()),
            lastSyncDate: this.timestampToISOString(protoPaymentMethod.getLastsyncdate()),
            externalId: protoPaymentMethod.getExternalid()
        };
    }

    /**
     * Преобразовать средство оплаты из объекта фронтенда в proto
     * @param {Object} paymentMethod - Объект средства оплаты фронтенда
     * @returns {ContractPaymentMethod} Proto объект средства оплаты
     */
    mapPaymentMethodToProto(paymentMethod) {
        const protoPaymentMethod = new ContractPaymentMethod();

        if (paymentMethod.id) {
            protoPaymentMethod.setId(paymentMethod.id);
        }

        protoPaymentMethod.setContractid(paymentMethod.contractId);

        // Определяем тип средства оплаты
        const methodType = FRONTEND_TO_PROTO_TYPE_MAP[paymentMethod.code] ||
                          FRONTEND_TO_PROTO_TYPE_MAP[paymentMethod.methodType] ||
                          PaymentMethodType.PMT_OTHER_CARDS;
        protoPaymentMethod.setMethodtype(methodType);

        protoPaymentMethod.setCode(paymentMethod.code || paymentMethod.methodType);
        protoPaymentMethod.setName(paymentMethod.name || PAYMENT_METHOD_TYPE_NAMES[paymentMethod.code]);

        if (paymentMethod.description) {
            protoPaymentMethod.setDescription(paymentMethod.description);
        }

        protoPaymentMethod.setIsactive(paymentMethod.isActive !== false);
        protoPaymentMethod.setIsdeleted(paymentMethod.isDeleted || false);

        if (paymentMethod.createdDate) {
            protoPaymentMethod.setCreateddate(this.isoStringToTimestamp(paymentMethod.createdDate));
        }

        if (paymentMethod.lastSyncDate) {
            protoPaymentMethod.setLastsyncdate(this.isoStringToTimestamp(paymentMethod.lastSyncDate));
        }

        if (paymentMethod.externalId) {
            protoPaymentMethod.setExternalid(paymentMethod.externalId);
        }

        return protoPaymentMethod;
    }

    /**
     * Преобразовать Timestamp в ISO строку
     * @param {Timestamp} timestamp - Proto Timestamp
     * @returns {string|null} ISO строка или null
     */
    timestampToISOString(timestamp) {
        if (!timestamp) return null;
        return new Date(timestamp.getSeconds() * 1000 + timestamp.getNanos() / 1000000).toISOString();
    }

    /**
     * Преобразовать ISO строку в Timestamp
     * @param {string} isoString - ISO строка
     * @returns {Timestamp} Proto Timestamp
     */
    isoStringToTimestamp(isoString) {
        const date = new Date(isoString);
        const timestamp = new Timestamp();
        timestamp.setSeconds(Math.floor(date.getTime() / 1000));
        timestamp.setNanos((date.getTime() % 1000) * 1000000);
        return timestamp;
    }

    /**
     * Обработать ошибку gRPC
     * @param {Error} error - Ошибка gRPC
     * @returns {Object} Объект ошибки для фронтенда
     */
    handleGrpcError(error) {
        console.error('gRPC Error:', error);

        return {
            success: false,
            error: {
                code: error.code || 'UNKNOWN_ERROR',
                message: error.message || 'Произошла неизвестная ошибка',
                details: error.details || null
            }
        };
    }

    /**
     * Создать объект пагинации для запроса
     * @param {number} page - Номер страницы (начиная с 1)
     * @param {number} limit - Количество элементов на странице
     * @returns {PaginationRequest} Proto объект пагинации
     */
    createPaginationRequest(page = 1, limit = 20) {
        const pagination = new PaginationRequest();
        pagination.setPage(page);
        pagination.setLimit(limit);
        return pagination;
    }

    /**
     * Получить список средств оплаты
     * @param {Object} options - Опции запроса
     * @param {number} options.page - Номер страницы
     * @param {number} options.limit - Количество элементов на странице
     * @param {Object} options.filter - Фильтры
     * @returns {Promise<Object>} Результат запроса
     */
    async getPaymentMethods(options = {}) {
        try {
            const request = new PaymentMethodListRequest();

            // Добавляем пагинацию
            if (options.page || options.limit) {
                const pagination = this.createPaginationRequest(options.page, options.limit);
                request.setPagination(pagination);
            }

            // Добавляем фильтры
            if (options.filter) {
                const filter = new PaymentMethodFilter();

                if (options.filter.contractId) {
                    filter.setContractid(options.filter.contractId);
                }

                if (options.filter.methodType) {
                    const protoType = FRONTEND_TO_PROTO_TYPE_MAP[options.filter.methodType];
                    if (protoType !== undefined) {
                        filter.setMethodtype(protoType);
                    }
                }

                if (options.filter.code) {
                    filter.setCode(options.filter.code);
                }

                if (options.filter.isActive !== undefined) {
                    filter.setIsactive(options.filter.isActive);
                }

                if (options.filter.isDeleted !== undefined) {
                    filter.setIsdeleted(options.filter.isDeleted);
                }

                request.setFilter(filter);
            }

            return new Promise((resolve, reject) => {
                this.client.paymentMethodList(request, this.getAuthMetadata(), (error, response) => {
                    if (error) {
                        reject(this.handleGrpcError(error));
                        return;
                    }

                    if (response.hasError()) {
                        reject({
                            success: false,
                            error: {
                                code: response.getError().getCode(),
                                message: response.getError().getMessage()
                            }
                        });
                        return;
                    }

                    const result = response.getResult();
                    const paymentMethods = result.getPaymentmethodsList().map(pm =>
                        this.mapPaymentMethodFromProto(pm)
                    );

                    resolve({
                        success: true,
                        data: paymentMethods,
                        pagination: result.getPagination() ? {
                            page: result.getPagination().getPage(),
                            limit: result.getPagination().getLimit(),
                            totalPage: result.getPagination().getTotalpage(),
                            totalCount: result.getPagination().getTotalcount()
                        } : null,
                        filter: result.getFilter()
                    });
                });
            });
        } catch (error) {
            return this.handleGrpcError(error);
        }
    }

    /**
     * Получить средство оплаты по ID
     * @param {string} id - ID средства оплаты
     * @returns {Promise<Object>} Результат запроса
     */
    async getPaymentMethodById(id) {
        try {
            const request = new ByIdRequest();
            request.setId(id);

            return new Promise((resolve, reject) => {
                this.client.paymentMethodById(request, this.getAuthMetadata(), (error, response) => {
                    if (error) {
                        reject(this.handleGrpcError(error));
                        return;
                    }

                    if (response.hasError()) {
                        reject({
                            success: false,
                            error: {
                                code: response.getError().getCode(),
                                message: response.getError().getMessage()
                            }
                        });
                        return;
                    }

                    const paymentMethod = this.mapPaymentMethodFromProto(response.getResult());

                    resolve({
                        success: true,
                        data: paymentMethod
                    });
                });
            });
        } catch (error) {
            return this.handleGrpcError(error);
        }
    }

    /**
     * Получить средства оплаты по договору
     * @param {string} contractId - ID договора
     * @param {Object} options - Опции запроса
     * @returns {Promise<Object>} Результат запроса
     */
    async getPaymentMethodsByContract(contractId, options = {}) {
        try {
            const request = new PaymentMethodsByContractRequest();
            request.setContractid(contractId);

            if (options.page || options.limit) {
                const pagination = this.createPaginationRequest(options.page, options.limit);
                request.setPagination(pagination);
            }

            if (options.includeInactive !== undefined) {
                request.setIncludeinactive(options.includeInactive);
            }

            return new Promise((resolve, reject) => {
                this.client.paymentMethodsByContract(request, this.getAuthMetadata(), (error, response) => {
                    if (error) {
                        reject(this.handleGrpcError(error));
                        return;
                    }

                    if (response.hasError()) {
                        reject({
                            success: false,
                            error: {
                                code: response.getError().getCode(),
                                message: response.getError().getMessage()
                            }
                        });
                        return;
                    }

                    const result = response.getResult();
                    const paymentMethods = result.getPaymentmethodsList().map(pm =>
                        this.mapPaymentMethodFromProto(pm)
                    );

                    resolve({
                        success: true,
                        data: paymentMethods,
                        pagination: result.getPagination() ? {
                            page: result.getPagination().getPage(),
                            limit: result.getPagination().getLimit(),
                            totalPage: result.getPagination().getTotalpage(),
                            totalCount: result.getPagination().getTotalcount()
                        } : null
                    });
                });
            });
        } catch (error) {
            return this.handleGrpcError(error);
        }
    }

    /**
     * Создать средство оплаты
     * @param {Object} paymentMethodData - Данные средства оплаты
     * @returns {Promise<Object>} Результат запроса
     */
    async createPaymentMethod(paymentMethodData) {
        try {
            const protoPaymentMethod = this.mapPaymentMethodToProto(paymentMethodData);

            return new Promise((resolve, reject) => {
                this.client.createPaymentMethod(protoPaymentMethod, this.getAuthMetadata(), (error, response) => {
                    if (error) {
                        reject(this.handleGrpcError(error));
                        return;
                    }

                    if (response.hasError()) {
                        reject({
                            success: false,
                            error: {
                                code: response.getError().getCode(),
                                message: response.getError().getMessage()
                            }
                        });
                        return;
                    }

                    resolve({
                        success: true,
                        message: 'Средство оплаты успешно создано'
                    });
                });
            });
        } catch (error) {
            return this.handleGrpcError(error);
        }
    }

    /**
     * Обновить средство оплаты
     * @param {Object} paymentMethodData - Данные средства оплаты
     * @returns {Promise<Object>} Результат запроса
     */
    async updatePaymentMethod(paymentMethodData) {
        try {
            const protoPaymentMethod = this.mapPaymentMethodToProto(paymentMethodData);

            return new Promise((resolve, reject) => {
                this.client.updatePaymentMethod(protoPaymentMethod, this.getAuthMetadata(), (error, response) => {
                    if (error) {
                        reject(this.handleGrpcError(error));
                        return;
                    }

                    if (response.hasError()) {
                        reject({
                            success: false,
                            error: {
                                code: response.getError().getCode(),
                                message: response.getError().getMessage()
                            }
                        });
                        return;
                    }

                    resolve({
                        success: true,
                        message: 'Средство оплаты успешно обновлено'
                    });
                });
            });
        } catch (error) {
            return this.handleGrpcError(error);
        }
    }

    /**
     * Удалить средство оплаты
     * @param {string} id - ID средства оплаты
     * @returns {Promise<Object>} Результат запроса
     */
    async deletePaymentMethod(id) {
        try {
            const request = new ByIdRequest();
            request.setId(id);

            return new Promise((resolve, reject) => {
                this.client.deletePaymentMethod(request, this.getAuthMetadata(), (error, response) => {
                    if (error) {
                        reject(this.handleGrpcError(error));
                        return;
                    }

                    if (response.hasError()) {
                        reject({
                            success: false,
                            error: {
                                code: response.getError().getCode(),
                                message: response.getError().getMessage()
                            }
                        });
                        return;
                    }

                    resolve({
                        success: true,
                        message: 'Средство оплаты успешно удалено'
                    });
                });
            });
        } catch (error) {
            return this.handleGrpcError(error);
        }
    }

    /**
     * Поиск средств оплаты
     * @param {string} searchTerm - Поисковый запрос
     * @param {Object} options - Дополнительные опции
     * @returns {Promise<Object>} Результат запроса
     */
    async searchPaymentMethods(searchTerm, options = {}) {
        const searchOptions = {
            ...options,
            filter: {
                ...options.filter,
                // Можно добавить поиск по коду или названию
                code: searchTerm
            }
        };

        return this.getPaymentMethods(searchOptions);
    }

    /**
     * Получить статистику по средствам оплаты
     * @param {string} contractId - ID договора (опционально)
     * @returns {Promise<Object>} Статистика
     */
    async getPaymentMethodStatistics(contractId = null) {
        try {
            const filter = contractId ? { contractId, isDeleted: false } : { isDeleted: false };
            const result = await this.getPaymentMethods({ filter });

            if (!result.success) {
                return result;
            }

            const paymentMethods = result.data;
            const total = paymentMethods.length;
            const active = paymentMethods.filter(pm => pm.isActive).length;
            const inactive = total - active;

            // Группировка по типам
            const byType = paymentMethods.reduce((acc, pm) => {
                const type = pm.code || pm.methodType;
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, {});

            return {
                success: true,
                data: {
                    total,
                    active,
                    inactive,
                    byType,
                    lastUpdated: new Date().toISOString()
                }
            };
        } catch (error) {
            return this.handleGrpcError(error);
        }
    }
}

// Создаем и экспортируем экземпляр сервиса
const paymentMethodGatePrivateService = new PaymentMethodGatePrivateService();

export default paymentMethodGatePrivateService;
export { PaymentMethodGatePrivateService, PAYMENT_METHOD_TYPE_NAMES, PAYMENT_METHOD_TYPE_MAP };
