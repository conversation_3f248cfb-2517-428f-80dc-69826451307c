/**
 * Тесты для REST API клиента
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { HttpClient } from '../HttpClient';
import { RestApiClient, ApiMapper } from '../RestApiClient';
import type { OrganizationDto, FrontendOrganization } from '@/types/rest-api';

// Мокаем fetch
global.fetch = vi.fn();

describe('HttpClient', () => {
    let httpClient: HttpClient;

    beforeEach(() => {
        httpClient = new HttpClient({
            baseURL: 'http://localhost:8080/api/v1'
        });
        vi.clearAllMocks();
    });

    it('должен выполнять GET запрос', async () => {
        const mockResponse = {
            ok: true,
            status: 200,
            headers: new Headers({ 'content-type': 'application/json' }),
            json: () => Promise.resolve({ success: true, data: { id: '1', name: 'Test' } })
        };

        (fetch as any).mockResolvedValueOnce(mockResponse);

        const result = await httpClient.get('/test');

        expect(fetch).toHaveBeenCalledWith(
            'http://localhost:8080/api/v1/test',
            expect.objectContaining({
                method: 'GET',
                headers: expect.objectContaining({
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                })
            })
        );

        expect(result.success).toBe(true);
        expect(result.data).toEqual({ success: true, data: { id: '1', name: 'Test' } });
    });

    it('должен выполнять POST запрос с данными', async () => {
        const mockResponse = {
            ok: true,
            status: 201,
            headers: new Headers({ 'content-type': 'application/json' }),
            json: () => Promise.resolve({ success: true })
        };

        (fetch as any).mockResolvedValueOnce(mockResponse);

        const testData = { name: 'Test Organization' };
        const result = await httpClient.post('/organizations', testData);

        expect(fetch).toHaveBeenCalledWith(
            'http://localhost:8080/api/v1/organizations',
            expect.objectContaining({
                method: 'POST',
                headers: expect.objectContaining({
                    'Content-Type': 'application/json'
                }),
                body: JSON.stringify(testData)
            })
        );

        expect(result.success).toBe(true);
    });

    it('должен обрабатывать ошибки HTTP', async () => {
        const mockResponse = {
            ok: false,
            status: 404,
            headers: new Headers({ 'content-type': 'application/json' }),
            json: () => Promise.resolve({ 
                error: { 
                    code: 'NOT_FOUND', 
                    message: 'Организация не найдена' 
                } 
            })
        };

        (fetch as any).mockResolvedValueOnce(mockResponse);

        const result = await httpClient.get('/organizations/nonexistent');

        expect(result.success).toBe(false);
        expect(result.error?.code).toBe('HTTP_404');
        expect(result.error?.status).toBe(404);
    });

    it('должен обрабатывать сетевые ошибки', async () => {
        (fetch as any).mockRejectedValueOnce(new TypeError('Network error'));

        const result = await httpClient.get('/test');

        expect(result.success).toBe(false);
        expect(result.error?.code).toBe('NETWORK_ERROR');
    });
});

describe('ApiMapper', () => {
    it('должен конвертировать OrganizationDto в FrontendOrganization', () => {
        const dto: OrganizationDto = {
            id: '123',
            name: 'ООО "Тестовая организация"',
            shortName: 'Тестовая',
            inn: '1234567890',
            kpp: '123456789',
            ogrn: '1234567890123',
            isDeleted: false
        };

        const frontend = ApiMapper.mapOrganizationToFrontend(dto);

        expect(frontend.id).toBe('123');
        expect(frontend.name).toBe('ООО "Тестовая организация"');
        expect(frontend.shortName).toBe('Тестовая');
        expect(frontend.inn).toBe('1234567890');
        expect(frontend.status).toBe('active');
        expect(frontend.ownershipForm).toBe('ООО');
        expect(frontend.syncStatus).toBe('synced');
    });

    it('должен конвертировать FrontendOrganization в OrganizationDto', () => {
        const frontend: FrontendOrganization = {
            id: '123',
            name: 'ООО "Тестовая организация"',
            shortName: 'Тестовая',
            inn: '1234567890',
            kpp: '123456789',
            ogrn: '1234567890123',
            status: 'active'
        };

        const dto = ApiMapper.mapOrganizationFromFrontend(frontend);

        expect(dto.id).toBe('123');
        expect(dto.name).toBe('ООО "Тестовая организация"');
        expect(dto.shortName).toBe('Тестовая');
        expect(dto.inn).toBe('1234567890');
        expect(dto.isDeleted).toBe(false);
    });

    it('должен правильно извлекать форму собственности', () => {
        expect(ApiMapper.extractOwnershipForm('ООО "Тест"')).toBe('ООО');
        expect(ApiMapper.extractOwnershipForm('АО "Тест"')).toBe('АО');
        expect(ApiMapper.extractOwnershipForm('ИП Иванов')).toBe('ИП');
        expect(ApiMapper.extractOwnershipForm('Тестовая организация')).toBe('Не определено');
    });
});

describe('RestApiClient', () => {
    let restApiClient: RestApiClient;
    let mockHttpClient: any;

    beforeEach(() => {
        mockHttpClient = {
            get: vi.fn(),
            post: vi.fn(),
            put: vi.fn(),
            delete: vi.fn()
        };
        restApiClient = new RestApiClient(mockHttpClient);
    });

    it('должен получать список организаций', async () => {
        const mockResponse = {
            success: true,
            content: [
                {
                    id: '1',
                    name: 'ООО "Тест"',
                    shortName: 'Тест',
                    inn: '1234567890',
                    kpp: '123456789',
                    ogrn: '1234567890123',
                    isDeleted: false
                }
            ],
            pagination: {
                page: 0,
                size: 20,
                totalElements: 1,
                totalPages: 1
            }
        };

        mockHttpClient.get.mockResolvedValueOnce({
            success: true,
            data: mockResponse
        });

        const result = await restApiClient.getOrganizations({ page: 0, size: 20 });

        expect(mockHttpClient.get).toHaveBeenCalledWith('/organizations?page=0&size=20');
        expect(result.success).toBe(true);
        expect(result.content).toHaveLength(1);
        expect(result.content[0].name).toBe('ООО "Тест"');
    });

    it('должен создавать организацию', async () => {
        const organizationData = {
            organization: {
                name: 'ООО "Новая организация"',
                shortName: 'Новая',
                inn: '9876543210',
                kpp: '987654321',
                ogrn: '9876543210987',
                isDeleted: false
            },
            addressLegal: {
                name: 'Юридический адрес',
                region: 'Москва',
                city: 'Москва',
                house: '1',
                isDeleted: false
            }
        };

        mockHttpClient.post.mockResolvedValueOnce({
            success: true
        });

        const result = await restApiClient.createOrganization(organizationData);

        expect(mockHttpClient.post).toHaveBeenCalledWith('/organizations', organizationData);
        expect(result.success).toBe(true);
    });

    it('должен получать организацию по ID', async () => {
        const mockOrganization = {
            id: '123',
            name: 'ООО "Тест"',
            shortName: 'Тест',
            inn: '1234567890',
            kpp: '123456789',
            ogrn: '1234567890123',
            isDeleted: false
        };

        mockHttpClient.get.mockResolvedValueOnce({
            success: true,
            data: mockOrganization
        });

        const result = await restApiClient.getOrganizationById('123');

        expect(mockHttpClient.get).toHaveBeenCalledWith('/organizations/123');
        expect(result.success).toBe(true);
        expect(result.data?.id).toBe('123');
    });

    it('должен обрабатывать ошибки API', async () => {
        mockHttpClient.get.mockResolvedValueOnce({
            success: false,
            error: {
                code: 'NOT_FOUND',
                message: 'Организация не найдена'
            }
        });

        const result = await restApiClient.getOrganizationById('nonexistent');

        expect(result.success).toBe(false);
        expect(result.error?.code).toBe('NOT_FOUND');
    });
});
