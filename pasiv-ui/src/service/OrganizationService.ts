import pasivGatePrivateService from './PasivGatePrivateServiceRest';
import type { FrontendOrganization } from '@/types/grpc';

// Типы для параметров запросов
interface GetDataParams {
    pagination?: {
        page: number;
        limit: number;
    };
    filter?: Record<string, any>;
}

interface OrganizationListResponse {
    organizations: FrontendOrganization[];
    pagination?: {
        page: number;
        size: number;
        totalPages: number;
        totalCount: number;
    };
}

interface ServiceResponse<T = any> {
    success: boolean;
    data?: T;
    error?: {
        code: string | number;
        message: string;
    };
    message?: string;
}

interface SyncResponse {
    success: boolean;
    message: string;
    syncDate: string;
    totalCount?: number;
    successCount?: number;
    errorCount?: number;
    error?: {
        code: string | number;
        message: string;
    };
}

/**
 * Сервис для работы с организациями
 * Обновленная версия с интеграцией gRPC API
 * Сохраняет совместимость с существующими компонентами
 */
export const OrganizationService = {

    /**
     * Получить список организаций (с поддержкой фильтрации и пагинации)
     */
    async getData(params: GetDataParams = {}): Promise<FrontendOrganization[]> {
        try {
            const result = await pasivGatePrivateService.organizationList({
                pagination: params.pagination || { page: 1, limit: 100 },
                filter: params.filter || {}
            });

            if (result.success) {
                return result.data || [];
            } else {
                console.error('Ошибка получения списка организаций:', result.error);
                // Возвращаем пустой массив в случае ошибки для совместимости
                return [];
            }
        } catch (error) {
            console.error('Ошибка при вызове API:', error);
            return [];
        }
    },

    /**
     * Получить мок-данные (для обратной совместимости)
     */
    getMockData: (): FrontendOrganization[] => {
        return [
            {
                id: '1',
                type: 'organization',
                ownershipForm: 'ООО',
                name: 'Городской транспорт',
                fullName: 'Общество с ограниченной ответственностью "Городской транспорт"',
                shortName: 'Городской транспорт',
                inn: '7701234567',
                ogrn: '1027700123456',
                kpp: '770101001',
                okpo: '12345678',
                oktmo: '45000000',
                okved: '49.31 - Деятельность городского и пригородного пассажирского наземного транспорта',
                legalAddress: '123456, г. Москва, ул. Транспортная, д. 1, стр. 1',
                actualAddress: '123456, г. Москва, ул. Транспортная, д. 1, стр. 1',
                mailingAddress: '123456, г. Москва, ул. Транспортная, д. 1, стр. 1',
                fioDirector: 'Иванов Иван Иванович',
                generalDirector: 'Иванов Иван Иванович',
                status: 'active',
                syncStatus: 'synced',
                lastSyncDate: '2024-01-15T10:30:00Z'
            } as FrontendOrganization,
            {
                id: '2',
                name: 'Метрополитен',
                inn: '7702345678',
                kpp: '770201001',
                status: 'active',
                syncStatus: 'pending',
                lastSyncDate: '2024-01-10T14:20:00Z'
            } as FrontendOrganization,
            {
                id: '3',
                name: 'Автобусный парк №1',
                inn: '7703456789',
                kpp: '770301001',
                status: 'active',
                syncStatus: 'error',
                lastSyncDate: '2024-01-05T16:45:00Z'
            } as FrontendOrganization


        ]
    },

    /**
     * Получить список организаций (алиас для getData)
     */
    async getOrganizations(): Promise<FrontendOrganization[]> {
        return this.getData();
    },

    /**
     * Получить организацию по ID
     */
    async getOrganizationById(organizationId: string | number): Promise<FrontendOrganization | null> {
        try {
            const result = await pasivGatePrivateService.organizationById(String(organizationId));

            if (result.success) {
                return result.data || null;
            } else {
                console.error('Ошибка получения организации:', result.error);
                return null;
            }
        } catch (error) {
            console.error('Ошибка при вызове API:', error);
            return null;
        }
    },

    /**
     * Получить организации по массиву ID
     */
    async getOrganizationsByIds(ids: (string | number)[]): Promise<FrontendOrganization[]> {
        try {
            // Получаем организации по одной (можно оптимизировать в будущем)
            const promises = ids.map(id => this.getOrganizationById(id));
            const results = await Promise.all(promises);

            // Фильтруем null значения
            return results.filter(org => org !== null);
        } catch (error) {
            console.error('Ошибка при получении организаций по ID:', error);
            return [];
        }
    },

    /**
     * Создать новую организацию
     */
    async createOrganization(organizationData: any): Promise<ServiceResponse> {
        try {
            console.log('Creating organization:', organizationData);

            const result = await pasivGatePrivateService.createOrganization(organizationData);

            if (result.success) {
                // Возвращаем объект в формате, ожидаемом компонентами
                return {
                    success: true,
                    data: {
                        ...organizationData,
                        status: 'active',
                        syncStatus: 'synced',
                        lastSyncDate: new Date().toISOString(),
                        createdDate: new Date().toISOString()
                    },
                    message: 'Организация успешно создана'
                };
            } else {
                return {
                    success: false,
                    error: {
                        code: 'API_ERROR',
                        message: 'Ошибка при создании организации'
                    }
                };
            }
        } catch (error) {
            console.error('Ошибка при создании организации:', error);
            return {
                success: false,
                error: {
                    code: 'UNKNOWN',
                    message: 'Произошла неизвестная ошибка при создании организации'
                }
            };
        }
    },

    /**
     * Обновить организацию
     */
    async updateOrganization(organizationId: string | number, organizationData: any): Promise<ServiceResponse> {
        try {
            console.log('Updating organization:', organizationId, organizationData);

            // Добавляем ID к данным организации
            const dataWithId = {
                ...organizationData,
                id: String(organizationId)
            };

            const result = await pasivGatePrivateService.updateOrganization(dataWithId);

            if (result.success) {
                return {
                    success: true,
                    data: dataWithId,
                    message: 'Организация успешно создана'
                };
            } else {
                return {
                    success: false,
                    error: result.error ? {
                        code: String(result.error.code),
                        message: result.error.message
                    } : undefined
                };
            }
        } catch (error) {
            console.error('Ошибка при обновлении организации:', error);
            return {
                success: false,
                error: {
                    code: 'UNKNOWN',
                    message: 'Произошла неизвестная ошибка при обновлении организации'
                }
            };
        }
    },

    /**
     * Удалить организацию
     */
    async deleteOrganization(organizationId: string | number): Promise<ServiceResponse> {
        try {
            console.log('Deleting organization:', organizationId);

            const result = await pasivGatePrivateService.deleteOrganization(String(organizationId));

            return {
                success: result.success,
                message: 'Организация успешно удалена',
                error: result.error
            };
        } catch (error) {
            console.error('Ошибка при удалении организации:', error);
            return {
                success: false,
                error: {
                    code: 'UNKNOWN',
                    message: 'Произошла неизвестная ошибка при удалении организации'
                }
            };
        }
    },

    /**
     * Синхронизация организации с внешней системой (1С)
     * Пока оставляем как мок, так как нет соответствующего API
     */
    syncOrganization(organizationId: string | number): Promise<SyncResponse> {
        console.log('Syncing organization with 1C:', organizationId);

        // Имитация синхронизации (пока нет реального API)
        return new Promise((resolve) => {
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80% успеха
                resolve({
                    success,
                    message: success
                        ? 'Синхронизация с 1С выполнена успешно'
                        : 'Ошибка синхронизации с 1С: таймаут соединения',
                    syncDate: new Date().toISOString()
                });
            }, 2000); // Имитация задержки
        });
    },

    /**
     * Массовая синхронизация всех организаций
     * Пока оставляем как мок
     */
    async syncAllOrganizations(): Promise<SyncResponse> {
        console.log('Syncing all organizations with 1C');

        try {
            // Получаем актуальный список организаций из API
            const organizations = await this.getData();
            const totalCount = organizations.length;

            // Имитация массовой синхронизации
            return new Promise((resolve) => {
                setTimeout(() => {
                    const successCount = Math.floor(totalCount * 0.8); // 80% успеха
                    const errorCount = totalCount - successCount;

                    resolve({
                        success: true,
                        totalCount,
                        successCount,
                        errorCount,
                        message: `Синхронизация завершена. Успешно: ${successCount}, с ошибками: ${errorCount}`,
                        syncDate: new Date().toISOString()
                    });
                }, 5000); // Имитация более длительной операции
            });
        } catch (error) {
            console.error('Ошибка при массовой синхронизации:', error);
            return {
                success: false,
                error: {
                    code: 'SYNC_ERROR',
                    message: 'Ошибка при массовой синхронизации организаций'
                }
            };
        }
    },

    /**
     * Получить подсказки по ИНН организации
     */
    async getOrganizationHintByINN(inn: string, kpp: string | null = null): Promise<ServiceResponse> {
        // TODO: Реализовать метод getOrganizationHintByINN в PasivGatePrivateService
        return {
            success: false,
            error: {
                code: 'NOT_IMPLEMENTED',
                message: 'Метод getOrganizationHintByINN еще не реализован'
            }
        };
    },

    /**
     * Поиск организаций с фильтрацией
     */
    async searchOrganizations(filters: Record<string, any> = {}): Promise<FrontendOrganization[]> {
        try {
            const result = await pasivGatePrivateService.organizationList({
                filter: filters,
                pagination: { page: 1, size: 100 }
            });

            if (result.success) {
                return result.data.organizations;
            } else {
                console.error('Ошибка поиска организаций:', result.error);
                return [];
            }
        } catch (error) {
            console.error('Ошибка при поиске организаций:', error);
            return [];
        }
    }
}
