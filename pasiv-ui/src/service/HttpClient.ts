/**
 * Базовый HTTP клиент для работы с REST API
 * Заменяет gRPC клиент на стандартные HTTP запросы
 */

export interface HttpClientConfig {
    baseURL: string;
    timeout?: number;
    headers?: Record<string, string>;
    withCredentials?: boolean;
}

export interface RequestConfig {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
    signal?: AbortSignal;
}

export interface ApiError {
    code: string;
    message: string;
    details?: any;
    status?: number;
}

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: ApiError;
    message?: string;
}

/**
 * Базовый HTTP клиент с поддержкой:
 * - Автоматической обработки ошибок
 * - Перехватчиков запросов и ответов
 * - Таймаутов
 * - Аутентификации
 */
export class HttpClient {
    private config: HttpClientConfig;
    private requestInterceptors: Array<(config: RequestConfig) => RequestConfig | Promise<RequestConfig>> = [];
    private responseInterceptors: Array<(response: any) => any | Promise<any>> = [];

    constructor(config: HttpClientConfig) {
        this.config = {
            timeout: 30000,
            withCredentials: true,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            ...config
        };
    }

    /**
     * Добавить перехватчик запросов
     */
    addRequestInterceptor(interceptor: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>) {
        this.requestInterceptors.push(interceptor);
    }

    /**
     * Добавить перехватчик ответов
     */
    addResponseInterceptor(interceptor: (response: any) => any | Promise<any>) {
        this.responseInterceptors.push(interceptor);
    }

    /**
     * Выполнить HTTP запрос
     */
    async request<T = any>(url: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
        try {
            // Применяем перехватчики запросов
            let requestConfig = { ...config };
            for (const interceptor of this.requestInterceptors) {
                requestConfig = await interceptor(requestConfig);
            }

            // Подготавливаем URL
            const fullUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`;

            // Подготавливаем заголовки
            const headers = {
                ...this.config.headers,
                ...requestConfig.headers
            };

            // Создаем AbortController для таймаута
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, requestConfig.timeout || this.config.timeout);

            // Подготавливаем опции fetch
            const fetchOptions: RequestInit = {
                method: requestConfig.method || 'GET',
                headers,
                signal: requestConfig.signal || controller.signal,
                credentials: this.config.withCredentials ? 'include' : 'same-origin'
            };

            // Добавляем тело запроса для методов, которые его поддерживают
            if (requestConfig.body && ['POST', 'PUT', 'PATCH'].includes(fetchOptions.method!)) {
                if (typeof requestConfig.body === 'object') {
                    fetchOptions.body = JSON.stringify(requestConfig.body);
                } else {
                    fetchOptions.body = requestConfig.body;
                }
            }

            // Выполняем запрос
            const response = await fetch(fullUrl, fetchOptions);
            clearTimeout(timeoutId);

            // Обрабатываем ответ
            let responseData: any;
            const contentType = response.headers.get('content-type');
            
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                responseData = await response.text();
            }

            // Применяем перехватчики ответов
            for (const interceptor of this.responseInterceptors) {
                responseData = await interceptor(responseData);
            }

            // Проверяем статус ответа
            if (!response.ok) {
                const error: ApiError = {
                    code: `HTTP_${response.status}`,
                    message: responseData?.error?.message || responseData?.message || `HTTP Error ${response.status}`,
                    details: responseData,
                    status: response.status
                };

                return {
                    success: false,
                    error
                };
            }

            // Возвращаем успешный ответ
            return {
                success: true,
                data: responseData?.data || responseData,
                message: responseData?.message
            };

        } catch (error: any) {
            // Обработка ошибок сети и других исключений
            let apiError: ApiError;

            if (error.name === 'AbortError') {
                apiError = {
                    code: 'TIMEOUT',
                    message: 'Превышено время ожидания запроса'
                };
            } else if (error instanceof TypeError && error.message.includes('fetch')) {
                apiError = {
                    code: 'NETWORK_ERROR',
                    message: 'Ошибка сети. Проверьте подключение к интернету'
                };
            } else {
                apiError = {
                    code: 'UNKNOWN_ERROR',
                    message: error.message || 'Неизвестная ошибка',
                    details: error
                };
            }

            return {
                success: false,
                error: apiError
            };
        }
    }

    /**
     * GET запрос
     */
    async get<T = any>(url: string, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
        return this.request<T>(url, { ...config, method: 'GET' });
    }

    /**
     * POST запрос
     */
    async post<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'method'>): Promise<ApiResponse<T>> {
        return this.request<T>(url, { ...config, method: 'POST', body: data });
    }

    /**
     * PUT запрос
     */
    async put<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'method'>): Promise<ApiResponse<T>> {
        return this.request<T>(url, { ...config, method: 'PUT', body: data });
    }

    /**
     * DELETE запрос
     */
    async delete<T = any>(url: string, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
        return this.request<T>(url, { ...config, method: 'DELETE' });
    }

    /**
     * PATCH запрос
     */
    async patch<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'method'>): Promise<ApiResponse<T>> {
        return this.request<T>(url, { ...config, method: 'PATCH', body: data });
    }
}

/**
 * Создать экземпляр HTTP клиента с базовой конфигурацией
 */
export function createHttpClient(config: HttpClientConfig): HttpClient {
    const client = new HttpClient(config);

    // Добавляем базовые перехватчики
    client.addRequestInterceptor((config) => {
        // Логирование запросов в режиме разработки
        if (import.meta.env.DEV) {
            console.log(`🌐 ${config.method || 'GET'} запрос:`, config);
        }
        return config;
    });

    client.addResponseInterceptor((response) => {
        // Логирование ответов в режиме разработки
        if (import.meta.env.DEV) {
            console.log('📥 Ответ API:', response);
        }
        return response;
    });

    return client;
}

/**
 * Получить базовый URL для API из переменных окружения
 */
export function getApiBaseUrl(): string {
    // Проверяем переменные окружения
    const envUrl = import.meta.env.VITE_API_BASE_URL;
    if (envUrl) {
        return envUrl;
    }

    // Определяем URL на основе текущего окружения
    if (import.meta.env.PROD) {
        // В продакшене используем относительный путь
        return '/api/v1';
    } else {
        // В разработке используем localhost
        return 'http://localhost:8080/api/v1';
    }
}

// Экспортируем готовый к использованию клиент
export const defaultHttpClient = createHttpClient({
    baseURL: getApiBaseUrl()
});
