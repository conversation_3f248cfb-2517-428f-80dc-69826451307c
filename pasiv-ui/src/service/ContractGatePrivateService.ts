/**
 * Сервис для работы с Contract API через gRPC
 * Обертка над сгенерированным gRPC клиентом для договоров
 */

import type {
    Contract as FrontendContract,
    ContractOrganization as FrontendContractOrganization,
    ContractStatus,
    ContractType,
    ProjectType,
    OrganizationRole,
    ContractListParams,
    ContractFilterParams,
    ContractApiResponse,
    ContractListResponse,
    AuthMetadata,
    ContractGrpcConfig
} from '@/types/contract';

// Импорты gRPC (с заглушками через polyfill)
import * as grpcWebClient from '@/generated/pasiv-gate-private_grpc_web_pb.js';
import * as protobufMessages from '@/generated/pasiv-gate-private_pb.js';
import * as commonMessages from '@/generated/common_pb.js';

// Извлекаем классы из модулей
const { PASIVGatePrivateServiceClient } = grpcWebClient as any;
const {
    Contract,
    ContractWithOrganizations,
    ContractOrganization,
    ContractListRequest,
    ContractFilter,
    ContractsByProjectRequest,
    ByIdRequest,
    ContractStatus: ProtoContractStatus,
    ContractType: ProtoContractType,
    ProjectType: ProtoProjectType,
    OrganizationRole: ProtoOrganizationRole
} = protobufMessages as any;
const { PaginationRequest } = commonMessages as any;

// Заглушка для Timestamp (будет заменена на реальную реализацию)
class Timestamp {
    static fromDate(date: Date): Timestamp {
        return new Timestamp();
    }

    fromDate(date: Date): this {
        return this;
    }

    getSeconds(): number {
        return Date.now() / 1000;
    }
}

/**
 * Конфигурация gRPC клиента для договоров
 */
const GRPC_CONFIG: ContractGrpcConfig = {
    url: import.meta.env.VITE_PASIV_GATE_PRIVATE_URL || 'http://localhost:5005',
    options: {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.http2.min_time_between_pings_ms': 10000,
        'grpc.http2.min_ping_interval_without_data_ms': 300000
    }
};

/**
 * Класс для работы с Contract API через gRPC
 */
class ContractGatePrivateService {
    private client: any;

    constructor() {
        this.client = new PASIVGatePrivateServiceClient(GRPC_CONFIG.url, null, GRPC_CONFIG.options);
    }

    /**
     * Преобразовать договор из proto в объект фронтенда
     */
    mapContractFromProto(protoContract: any): FrontendContract {
        return {
            id: protoContract.getId(),
            projectCode: protoContract.getProjectcode(),
            projectName: protoContract.getProjectname(),
            projectType: this.mapProjectTypeFromProto(protoContract.getProjecttype()),
            contractType: this.mapContractTypeFromProto(protoContract.getContracttype()),
            contractName: protoContract.getContractname(),
            contractNumber: protoContract.getContractnumber(),
            signatureDate: protoContract.getSignaturedate() ?
                new Date(protoContract.getSignaturedate().getSeconds() * 1000).toISOString() : null,
            conclusionDate: protoContract.getConclusiondate() ?
                new Date(protoContract.getConclusiondate().getSeconds() * 1000).toISOString() : null,
            completionDate: protoContract.getCompletiondate() ?
                new Date(protoContract.getCompletiondate().getSeconds() * 1000).toISOString() : null,
            status: this.mapContractStatusFromProto(protoContract.getStatus()),
            externalId1C: protoContract.getExternalid1c(),
            description: protoContract.getDescription(),
            totalAmount: protoContract.getTotalamount(),
            currency: protoContract.getCurrency() || 'RUB',
            paymentTerms: protoContract.getPaymentterms(),
            vatRate: protoContract.getVatrate(),
            isDeleted: protoContract.getIsdeleted(),
            createdDate: protoContract.getCreateddate() ?
                new Date(protoContract.getCreateddate().getSeconds() * 1000).toISOString() : null,
            lastSyncDate: protoContract.getLastsyncdate() ?
                new Date(protoContract.getLastsyncdate().getSeconds() * 1000).toISOString() : null,
            // Дополнительные поля для совместимости
            syncStatus: protoContract.getLastsyncdate() ? 'synced' : 'never'
        };
    }

    /**
     * Преобразовать договор из объекта фронтенда в proto
     */
    mapContractToProto(contractData: FrontendContract): any {
        const contract = new Contract();

        if (contractData.id) contract.setId(contractData.id);
        contract.setProjectcode(contractData.projectCode || '');
        contract.setProjectname(contractData.projectName || '');
        contract.setProjecttype(this.mapProjectTypeToProto(contractData.projectType));
        contract.setContracttype(this.mapContractTypeToProto(contractData.contractType));
        contract.setContractname(contractData.contractName || contractData.name || '');
        contract.setContractnumber(contractData.contractNumber || contractData.number || '');

        // Преобразование дат
        if (contractData.signatureDate) {
            const timestamp = new Timestamp();
            timestamp.fromDate(new Date(contractData.signatureDate));
            contract.setSignaturedate(timestamp);
        }
        if (contractData.conclusionDate) {
            const timestamp = new Timestamp();
            timestamp.fromDate(new Date(contractData.conclusionDate));
            contract.setConclusiondate(timestamp);
        }
        if (contractData.completionDate) {
            const timestamp = new Timestamp();
            timestamp.fromDate(new Date(contractData.completionDate));
            contract.setCompletiondate(timestamp);
        }

        contract.setStatus(this.mapContractStatusToProto(contractData.status));
        if (contractData.externalId1C) contract.setExternalid1c(contractData.externalId1C);
        if (contractData.description) contract.setDescription(contractData.description);
        if (contractData.totalAmount) contract.setTotalamount(contractData.totalAmount);
        contract.setCurrency(contractData.currency || 'RUB');
        if (contractData.paymentTerms) contract.setPaymentterms(contractData.paymentTerms);
        if (contractData.vatRate) contract.setVatrate(contractData.vatRate);
        contract.setIsdeleted(contractData.isDeleted || false);

        return contract;
    }

    /**
     * Преобразовать связь договор-организация в proto
     */
    mapContractOrganizationToProto(orgData: FrontendContractOrganization): any {
        const contractOrg = new ContractOrganization();

        if (orgData.id) contractOrg.setId(orgData.id);
        if (orgData.contractId) contractOrg.setContractid(orgData.contractId);
        contractOrg.setOrganizationid(orgData.organizationId || '');
        contractOrg.setOrganizationname(orgData.organizationName || '');
        contractOrg.setRole(this.mapOrganizationRoleToProto(orgData.role));
        if (orgData.roleDescription) contractOrg.setRoledescription(orgData.roleDescription);

        // Даты активности
        if (orgData.activeFrom) {
            const timestamp = new Timestamp();
            timestamp.fromDate(new Date(orgData.activeFrom));
            contractOrg.setActivefrom(timestamp);
        }
        if (orgData.activeTill) {
            const timestamp = new Timestamp();
            timestamp.fromDate(new Date(orgData.activeTill));
            contractOrg.setActivetill(timestamp);
        }

        contractOrg.setIsdeleted(orgData.isDeleted || false);

        return contractOrg;
    }

    // Методы маппинга enum значений
    mapContractStatusFromProto(status: any): ContractStatus {
        const statusMap: Record<number, ContractStatus> = {
            [ProtoContractStatus.CS_DRAFT]: 'draft',
            [ProtoContractStatus.CS_ACTIVE]: 'active',
            [ProtoContractStatus.CS_EXPIRING]: 'expiring',
            [ProtoContractStatus.CS_COMPLETED]: 'completed',
            [ProtoContractStatus.CS_TERMINATED]: 'terminated'
        };
        return statusMap[status] || 'draft';
    }

    mapContractStatusToProto(status: ContractStatus): any {
        const statusMap: Record<ContractStatus, any> = {
            'draft': ProtoContractStatus.CS_DRAFT,
            'active': ProtoContractStatus.CS_ACTIVE,
            'expiring': ProtoContractStatus.CS_EXPIRING,
            'completed': ProtoContractStatus.CS_COMPLETED,
            'terminated': ProtoContractStatus.CS_TERMINATED
        };
        return statusMap[status] || ProtoContractStatus.CS_DRAFT;
    }

    mapContractTypeFromProto(type: any): ContractType {
        const typeMap: Record<number, ContractType> = {
            [ProtoContractType.CT_SYSTEM_RULES]: 'system_rules',
            [ProtoContractType.CT_SERVICE]: 'service',
            [ProtoContractType.CT_TRANSPORT]: 'transport',
            [ProtoContractType.CT_PROCESSING]: 'processing'
        };
        return typeMap[type] || 'service';
    }

    mapContractTypeToProto(type: ContractType): any {
        const typeMap: Record<ContractType, any> = {
            'system_rules': ProtoContractType.CT_SYSTEM_RULES,
            'service': ProtoContractType.CT_SERVICE,
            'transport': ProtoContractType.CT_TRANSPORT,
            'processing': ProtoContractType.CT_PROCESSING
        };
        return typeMap[type] || ProtoContractType.CT_SERVICE;
    }

    mapProjectTypeFromProto(type: any): ProjectType {
        const typeMap: Record<number, ProjectType> = {
            [ProtoProjectType.PT_TRANSPORT_SYSTEM]: 'transport_system',
            [ProtoProjectType.PT_METRO_SYSTEM]: 'metro_system',
            [ProtoProjectType.PT_BUS_SYSTEM]: 'bus_system',
            [ProtoProjectType.PT_TAXI_SYSTEM]: 'taxi_system'
        };
        return typeMap[type] || 'transport_system';
    }

    mapProjectTypeToProto(type: ProjectType): any {
        const typeMap: Record<ProjectType, any> = {
            'transport_system': ProtoProjectType.PT_TRANSPORT_SYSTEM,
            'metro_system': ProtoProjectType.PT_METRO_SYSTEM,
            'bus_system': ProtoProjectType.PT_BUS_SYSTEM,
            'taxi_system': ProtoProjectType.PT_TAXI_SYSTEM
        };
        return typeMap[type] || ProtoProjectType.PT_TRANSPORT_SYSTEM;
    }

    mapOrganizationRoleToProto(role: OrganizationRole): any {
        const roleMap: Record<OrganizationRole, any> = {
            'operator': ProtoOrganizationRole.OR_OPERATOR,
            'carrier': ProtoOrganizationRole.OR_CARRIER,
            'processing_center': ProtoOrganizationRole.OR_PROCESSING_CENTER,
            'contractor': ProtoOrganizationRole.OR_CONTRACTOR,
            'partner': ProtoOrganizationRole.OR_PARTNER
        };
        return roleMap[role] || ProtoOrganizationRole.OR_CONTRACTOR;
    }

    /**
     * Получить метаданные для аутентификации
     */
    getAuthMetadata(): AuthMetadata {
        const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
        return {
            authorization: `Bearer ${token}`
        };
    }

    /**
     * Обработка ошибок gRPC
     */
    handleError(error: any): ContractApiResponse<never> {
        console.error('Contract gRPC Error:', error);

        return {
            success: false,
            error: {
                code: error.code || 'UNKNOWN',
                message: error.message || 'Произошла неизвестная ошибка',
                details: error.details || null
            }
        };
    }

    /**
     * Получить список договоров
     */
    async getContractList(params: ContractListParams = {}): Promise<ContractApiResponse<ContractListResponse>> {
        // TODO: Реализовать получение списка договоров
        throw new Error('Method not implemented yet');
    }

    /**
     * Получить договор по ID
     */
    async getContractById(id: string): Promise<ContractApiResponse<FrontendContract>> {
        // TODO: Реализовать получение договора по ID
        throw new Error('Method not implemented yet');
    }

    /**
     * Создать новый договор
     */
    async createContract(contractData: FrontendContract): Promise<ContractApiResponse<void>> {
        // TODO: Реализовать создание договора
        throw new Error('Method not implemented yet');
    }

    /**
     * Обновить договор
     */
    async updateContract(contractData: FrontendContract): Promise<ContractApiResponse<void>> {
        // TODO: Реализовать обновление договора
        throw new Error('Method not implemented yet');
    }

    /**
     * Удалить договор (мягкое удаление)
     */
    async deleteContract(id: string): Promise<ContractApiResponse<void>> {
        // TODO: Реализовать удаление договора
        throw new Error('Method not implemented yet');
    }

    /**
     * Получить договоры по проекту
     */
    async getContractsByProject(projectCode: string, pagination: { page?: number; size?: number } = {}): Promise<ContractApiResponse<ContractListResponse>> {
        // TODO: Реализовать получение договоров по проекту
        throw new Error('Method not implemented yet');
    }
}

// Создаем единственный экземпляр сервиса
const contractGatePrivateService = new ContractGatePrivateService();

export default contractGatePrivateService;
export type { ContractGatePrivateService };
