/**
 * Сервис для работы со средствами оплаты
 * Интегрирован с gRPC API, сохраняет обратную совместимость
 */

import paymentMethodGatePrivateService, { PAYMENT_METHOD_TYPE_NAMES } from './PaymentMethodGatePrivateService';

/**
 * Предустановленные типы средств оплаты для совместимости
 */
const PREDEFINED_PAYMENT_METHODS = [
    { code: 'BANK_CARD', name: 'Банковская карта' },
    { code: 'CASH', name: 'Наличные денежные средства' },
    { code: 'TROIKA_SINGLE', name: 'Транспортная карта "Тройка" (разовые поездки)' },
    { code: 'TROIKA_SUBSCRIPTION', name: 'Транспортная карта "Тройка" (абонемент)' },
    { code: 'MPC_DISCOUNT', name: 'МПК Дисконт' },
    { code: 'MPC_SOCIAL', name: 'МПК Социальная карта' },
    { code: 'MPC_SCHOOL', name: 'МПК "Карта Школьника"' },
    { code: 'MPC_STUDENT_SINGLE', name: 'МПК "Карта Студента" (разовые поездки)' },
    { code: 'MPC_STUDENT_SUBSCRIPTION', name: 'МПК "Карта Студента" (абонемент)' },
    { code: 'TC_RESIDENT', name: 'ТК Карта жителя' },
    { code: 'MOBILE_BC', name: 'Мобильное приложение БК' },
    { code: 'MOBILE_VIRTUAL_TC', name: 'Мобильное приложение Виртуальная ТК' },
    { code: 'MOBILE_SBP', name: 'Мобильное приложение СБП' },
    { code: 'REGIONAL_TC', name: 'Транспортная карта региона' },
    { code: 'SOCIAL_TC', name: 'Социальная транспортная карта' },
    { code: 'OTHER_CARDS', name: 'Иные карты, предусмотренные договором' }
];

/**
 * Сервис для работы со средствами оплаты
 */
export const PaymentMethodService = {
    /**
     * Получить все средства оплаты
     * @param {Object} options - Опции запроса
     * @returns {Promise<Array>} Массив средств оплаты
     */
    async getPaymentMethods(options = {}) {
        try {
            const result = await paymentMethodGatePrivateService.getPaymentMethods({
                filter: { isDeleted: false, ...options.filter },
                page: options.page,
                limit: options.limit
            });

            if (result.success) {
                return result.data;
            } else {
                console.error('Ошибка получения средств оплаты:', result.error);
                return [];
            }
        } catch (error) {
            console.error('Ошибка получения средств оплаты:', error);
            return [];
        }
    },

    /**
     * Получить средства оплаты по договору
     * @param {string|number} contractId - ID договора
     * @param {Object} options - Опции запроса
     * @returns {Promise<Array>} Массив средств оплаты
     */
    async getPaymentMethodsByContract(contractId, options = {}) {
        try {
            const result = await paymentMethodGatePrivateService.getPaymentMethodsByContract(
                String(contractId),
                {
                    includeInactive: options.includeInactive || false,
                    page: options.page,
                    limit: options.limit
                }
            );

            if (result.success) {
                return result.data;
            } else {
                console.error('Ошибка получения средств оплаты по договору:', result.error);
                return [];
            }
        } catch (error) {
            console.error('Ошибка получения средств оплаты по договору:', error);
            return [];
        }
    },

    /**
     * Получить средство оплаты по ID
     * @param {string|number} methodId - ID средства оплаты
     * @returns {Promise<Object|null>} Средство оплаты или null
     */
    async getPaymentMethodById(methodId) {
        try {
            const result = await paymentMethodGatePrivateService.getPaymentMethodById(String(methodId));

            if (result.success) {
                return result.data;
            } else {
                console.error('Ошибка получения средства оплаты по ID:', result.error);
                return null;
            }
        } catch (error) {
            console.error('Ошибка получения средства оплаты по ID:', error);
            return null;
        }
    },

    /**
     * Создать средство оплаты
     * @param {string|number} contractId - ID договора
     * @param {Object} methodData - Данные средства оплаты
     * @returns {Promise<Object>} Результат создания
     */
    async createPaymentMethod(contractId, methodData) {
        try {
            // Валидация данных
            const validationResult = this.validatePaymentMethodData(methodData);
            if (!validationResult.isValid) {
                return {
                    success: false,
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Ошибка валидации данных',
                        details: validationResult.errors
                    }
                };
            }

            const paymentMethodData = {
                contractId: String(contractId),
                code: methodData.code,
                name: methodData.name || PAYMENT_METHOD_TYPE_NAMES[methodData.code],
                description: methodData.description,
                isActive: methodData.isActive !== false,
                methodType: methodData.code
            };

            const result = await paymentMethodGatePrivateService.createPaymentMethod(paymentMethodData);

            if (result.success) {
                return {
                    success: true,
                    message: 'Средство оплаты успешно создано',
                    data: paymentMethodData
                };
            } else {
                return result;
            }
        } catch (error) {
            console.error('Ошибка создания средства оплаты:', error);
            return {
                success: false,
                error: {
                    code: 'CREATE_ERROR',
                    message: 'Ошибка создания средства оплаты',
                    details: error.message
                }
            };
        }
    },

    /**
     * Обновить средство оплаты
     * @param {string|number} methodId - ID средства оплаты
     * @param {Object} methodData - Данные средства оплаты
     * @returns {Promise<Object>} Результат обновления
     */
    async updatePaymentMethod(methodId, methodData) {
        try {
            // Валидация данных
            const validationResult = this.validatePaymentMethodData(methodData);
            if (!validationResult.isValid) {
                return {
                    success: false,
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Ошибка валидации данных',
                        details: validationResult.errors
                    }
                };
            }

            const paymentMethodData = {
                id: String(methodId),
                contractId: String(methodData.contractId),
                code: methodData.code,
                name: methodData.name || PAYMENT_METHOD_TYPE_NAMES[methodData.code],
                description: methodData.description,
                isActive: methodData.isActive !== false,
                methodType: methodData.code
            };

            const result = await paymentMethodGatePrivateService.updatePaymentMethod(paymentMethodData);

            if (result.success) {
                return {
                    success: true,
                    message: 'Средство оплаты успешно обновлено',
                    data: paymentMethodData
                };
            } else {
                return result;
            }
        } catch (error) {
            console.error('Ошибка обновления средства оплаты:', error);
            return {
                success: false,
                error: {
                    code: 'UPDATE_ERROR',
                    message: 'Ошибка обновления средства оплаты',
                    details: error.message
                }
            };
        }
    },

    /**
     * Удалить средство оплаты
     * @param {string|number} methodId - ID средства оплаты
     * @returns {Promise<Object>} Результат удаления
     */
    async deletePaymentMethod(methodId) {
        try {
            const result = await paymentMethodGatePrivateService.deletePaymentMethod(String(methodId));

            if (result.success) {
                return {
                    success: true,
                    message: 'Средство оплаты успешно удалено'
                };
            } else {
                return result;
            }
        } catch (error) {
            console.error('Ошибка удаления средства оплаты:', error);
            return {
                success: false,
                error: {
                    code: 'DELETE_ERROR',
                    message: 'Ошибка удаления средства оплаты',
                    details: error.message
                }
            };
        }
    },

    /**
     * Поиск средств оплаты
     * @param {string} searchTerm - Поисковый запрос
     * @param {Object} options - Дополнительные опции
     * @returns {Promise<Array>} Массив найденных средств оплаты
     */
    async searchPaymentMethods(searchTerm, options = {}) {
        try {
            const result = await paymentMethodGatePrivateService.searchPaymentMethods(searchTerm, {
                filter: { isDeleted: false, ...options.filter },
                page: options.page,
                limit: options.limit
            });

            if (result.success) {
                return result.data;
            } else {
                console.error('Ошибка поиска средств оплаты:', result.error);
                return [];
            }
        } catch (error) {
            console.error('Ошибка поиска средств оплаты:', error);
            return [];
        }
    },

    /**
     * Получить статистику по средствам оплаты
     * @param {string} contractId - ID договора (опционально)
     * @returns {Promise<Object>} Статистика
     */
    async getPaymentMethodStatistics(contractId = null) {
        try {
            const result = await paymentMethodGatePrivateService.getPaymentMethodStatistics(contractId);

            if (result.success) {
                return result.data;
            } else {
                console.error('Ошибка получения статистики средств оплаты:', result.error);
                return {
                    total: 0,
                    active: 0,
                    inactive: 0,
                    byType: {},
                    lastUpdated: new Date().toISOString()
                };
            }
        } catch (error) {
            console.error('Ошибка получения статистики средств оплаты:', error);
            return {
                total: 0,
                active: 0,
                inactive: 0,
                byType: {},
                lastUpdated: new Date().toISOString()
            };
        }
    },

    /**
     * Получить предустановленные типы средств оплаты
     * @returns {Array} Массив предустановленных типов
     */
    getPredefinedPaymentMethods() {
        return [...PREDEFINED_PAYMENT_METHODS];
    },

    /**
     * Валидация данных средства оплаты
     * @param {Object} methodData - Данные для валидации
     * @returns {Object} Результат валидации
     */
    validatePaymentMethodData(methodData) {
        const errors = {};

        if (!methodData.code || methodData.code.trim() === '') {
            errors.code = 'Код средства оплаты обязателен';
        }

        if (!methodData.name || methodData.name.trim() === '') {
            errors.name = 'Название средства оплаты обязательно';
        }

        // Проверка на допустимый код
        const validCodes = PREDEFINED_PAYMENT_METHODS.map(pm => pm.code);
        if (methodData.code && !validCodes.includes(methodData.code)) {
            errors.code = 'Недопустимый код средства оплаты';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    },

    /**
     * Получить название типа средства оплаты по коду
     * @param {string} code - Код типа
     * @returns {string} Название типа
     */
    getPaymentMethodTypeName(code) {
        return PAYMENT_METHOD_TYPE_NAMES[code] || code;
    },

    /**
     * Проверить, является ли код допустимым
     * @param {string} code - Код для проверки
     * @returns {boolean} Результат проверки
     */
    isValidPaymentMethodCode(code) {
        const validCodes = PREDEFINED_PAYMENT_METHODS.map(pm => pm.code);
        return validCodes.includes(code);
    },

    /**
     * Фильтрация средств оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {Object} filters - Фильтры
     * @returns {Array} Отфильтрованный массив
     */
    filterPaymentMethods(paymentMethods, filters = {}) {
        let filtered = [...paymentMethods];

        if (filters.code) {
            filtered = filtered.filter(pm =>
                pm.code && pm.code.toLowerCase().includes(filters.code.toLowerCase())
            );
        }

        if (filters.name) {
            filtered = filtered.filter(pm =>
                pm.name && pm.name.toLowerCase().includes(filters.name.toLowerCase())
            );
        }

        if (filters.isActive !== undefined) {
            filtered = filtered.filter(pm => pm.isActive === filters.isActive);
        }

        if (filters.contractId) {
            filtered = filtered.filter(pm => pm.contractId === String(filters.contractId));
        }

        return filtered;
    },

    /**
     * Сортировка средств оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {string} sortBy - Поле для сортировки
     * @param {string} sortOrder - Порядок сортировки (asc/desc)
     * @returns {Array} Отсортированный массив
     */
    sortPaymentMethods(paymentMethods, sortBy = 'name', sortOrder = 'asc') {
        const sorted = [...paymentMethods].sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        return sorted;
    },

    /**
     * Экспорт средств оплаты в CSV
     * @param {Array} paymentMethods - Массив средств оплаты
     * @returns {string} CSV строка
     */
    exportToCSV(paymentMethods) {
        const headers = ['ID', 'Код', 'Название', 'Описание', 'Активно', 'Дата создания'];
        const csvRows = [headers.join(',')];

        paymentMethods.forEach(pm => {
            const row = [
                pm.id || '',
                pm.code || '',
                `"${pm.name || ''}"`,
                `"${pm.description || ''}"`,
                pm.isActive ? 'Да' : 'Нет',
                pm.createdDate ? new Date(pm.createdDate).toLocaleDateString() : ''
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    },

    // Методы для обратной совместимости с существующими компонентами

    /**
     * Получить данные (для совместимости)
     * @returns {Promise<Array>} Массив средств оплаты
     */
    async getData() {
        return this.getPaymentMethods();
    }
};

export default PaymentMethodService;
