/**
 * Сервис для работы с PASIV Gate Private API через REST
 * Заменяет gRPC клиент на REST API вызовы, сохраняя совместимость интерфейсов
 */

import { restApiClient, ApiMapper } from './RestApiClient';
import type {
    FrontendOrganization,
    FrontendAddress,
    ServiceResponse,
    OrganizationListParams,
    PaginationParams,
    AddressType
} from '@/types/rest-api';
import type { ApiResponse } from '@/types/organization';

// Интерфейс для совместимости с существующим кодом
export interface PasivGatePrivateServiceMethods {
    // Методы для организаций
    createOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>>;
    updateOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>>;
    organizationList(params?: OrganizationListParams): Promise<ApiResponse<FrontendOrganization[]>>;
    organizationById(id: string): Promise<ApiResponse<FrontendOrganization>>;
    deleteOrganization(id: string): Promise<ApiResponse<void>>;
    recoverOrganization(id: string): Promise<ApiResponse<void>>;
    
    // Методы для адресов
    createAddress(addressData: FrontendAddress): Promise<ApiResponse<void>>;
    updateAddress(addressData: FrontendAddress): Promise<ApiResponse<void>>;
    addressById(id: string): Promise<ApiResponse<FrontendAddress>>;
    addressList(params?: { pagination?: PaginationParams }): Promise<ApiResponse<FrontendAddress[]>>;
    deleteAddress(id: string): Promise<ApiResponse<void>>;
    recoverAddress(id: string): Promise<ApiResponse<void>>;
    
    // Утилитарные методы
    mapOrganizationFromProto(protoOrg: any): FrontendOrganization;
    mapOrganizationToProto(orgData: FrontendOrganization): any;
    mapAddressToProto(addressData: FrontendAddress | string): any;
    extractOwnershipForm(name: string): string;
}

/**
 * Сервис для работы с PASIV Gate Private API
 * Предоставляет высокоуровневые методы для работы с организациями через REST API
 */
class PasivGatePrivateService implements PasivGatePrivateServiceMethods {
    private enableDevLogs: boolean;

    constructor(config: { enableDevLogs?: boolean } = {}) {
        this.enableDevLogs = config.enableDevLogs ?? true;

        if (this.enableDevLogs) {
            console.log('🔧 PasivGatePrivateService инициализирован с REST API клиентом');
        }
    }

    // ===== МЕТОДЫ ДЛЯ ОРГАНИЗАЦИЙ =====

    /**
     * Создание организации
     */
    async createOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>> {
        try {
            if (this.enableDevLogs) {
                console.log('🔧 createOrganization:', orgData);
            }

            // Конвертируем frontend данные в DTO
            const organizationDto = ApiMapper.mapOrganizationFromFrontend(orgData);
            
            // Создаем адрес для организации
            const addressDto = typeof orgData.legalAddress === 'object' 
                ? ApiMapper.mapAddressFromFrontend(orgData.legalAddress)
                : {
                    name: 'Юридический адрес',
                    region: '',
                    city: '',
                    house: '',
                    isDeleted: false
                };

            const requestData = {
                organization: organizationDto,
                addressLegal: addressDto
            };

            const result = await restApiClient.createOrganization(requestData);

            return {
                success: result.success,
                data: undefined,
                error: result.error ? {
                    code: result.error.code,
                    message: result.error.message,
                    details: result.error.details
                } : undefined
            };

        } catch (error: any) {
            console.error('Ошибка при создании организации:', error);
            return {
                success: false,
                error: {
                    code: 'CLIENT_ERROR',
                    message: 'Ошибка при создании организации',
                    details: error
                }
            };
        }
    }

    /**
     * Обновление организации
     */
    async updateOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>> {
        try {
            if (!orgData.id) {
                return {
                    success: false,
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'ID организации обязателен для обновления'
                    }
                };
            }

            if (this.enableDevLogs) {
                console.log('🔧 updateOrganization:', orgData);
            }

            const organizationDto = ApiMapper.mapOrganizationFromFrontend(orgData);
            const result = await restApiClient.updateOrganization(orgData.id, organizationDto);

            return {
                success: result.success,
                data: undefined,
                error: result.error ? {
                    code: result.error.code,
                    message: result.error.message,
                    details: result.error.details
                } : undefined
            };

        } catch (error: any) {
            console.error('Ошибка при обновлении организации:', error);
            return {
                success: false,
                error: {
                    code: 'CLIENT_ERROR',
                    message: 'Ошибка при обновлении организации',
                    details: error
                }
            };
        }
    }

    /**
     * Получение списка организаций
     */
    async organizationList(params: OrganizationListParams = {}): Promise<ApiResponse<FrontendOrganization[]>> {
        try {
            if (this.enableDevLogs) {
                console.log('🔧 organizationList:', params);
            }

            const requestParams = {
                page: params.pagination?.page || 0,
                size: params.pagination?.size || 20,
                name: params.filter?.name,
                inn: params.filter?.inn,
                kpp: params.filter?.kpp,
                includeDeleted: params.filter?.isDeleted
            };

            const result = await restApiClient.getOrganizations(requestParams);

            if (result.success) {
                const organizations = result.content.map(org => ApiMapper.mapOrganizationToFrontend(org));
                return {
                    success: true,
                    data: organizations
                };
            } else {
                return {
                    success: false,
                    data: [],
                    error: result.error ? {
                        code: result.error.code,
                        message: result.error.message,
                        details: result.error.details
                    } : undefined
                };
            }

        } catch (error: any) {
            console.error('Ошибка при получении списка организаций:', error);
            return {
                success: false,
                data: [],
                error: {
                    code: 'CLIENT_ERROR',
                    message: 'Ошибка при получении списка организаций',
                    details: error
                }
            };
        }
    }

    /**
     * Получение организации по ID
     */
    async organizationById(id: string): Promise<ApiResponse<FrontendOrganization>> {
        try {
            if (this.enableDevLogs) {
                console.log('🔧 organizationById:', id);
            }

            const result = await restApiClient.getOrganizationById(id);

            if (result.success && result.data) {
                const organization = ApiMapper.mapOrganizationToFrontend(result.data);
                return {
                    success: true,
                    data: organization
                };
            } else {
                return {
                    success: false,
                    error: result.error ? {
                        code: result.error.code,
                        message: result.error.message,
                        details: result.error.details
                    } : undefined
                };
            }

        } catch (error: any) {
            console.error('Ошибка при получении организации:', error);
            return {
                success: false,
                error: {
                    code: 'CLIENT_ERROR',
                    message: 'Ошибка при получении организации',
                    details: error
                }
            };
        }
    }

    /**
     * Удаление организации
     */
    async deleteOrganization(id: string): Promise<ApiResponse<void>> {
        try {
            if (this.enableDevLogs) {
                console.log('🔧 deleteOrganization:', id);
            }

            const result = await restApiClient.deleteOrganization(id);

            return {
                success: result.success,
                data: undefined,
                error: result.error ? {
                    code: result.error.code,
                    message: result.error.message,
                    details: result.error.details
                } : undefined
            };

        } catch (error: any) {
            console.error('Ошибка при удалении организации:', error);
            return {
                success: false,
                error: {
                    code: 'CLIENT_ERROR',
                    message: 'Ошибка при удалении организации',
                    details: error
                }
            };
        }
    }

    /**
     * Восстановление организации
     */
    async recoverOrganization(id: string): Promise<ApiResponse<void>> {
        try {
            if (this.enableDevLogs) {
                console.log('🔧 recoverOrganization:', id);
            }

            const result = await restApiClient.recoverOrganization(id);

            return {
                success: result.success,
                data: undefined,
                error: result.error ? {
                    code: result.error.code,
                    message: result.error.message,
                    details: result.error.details
                } : undefined
            };

        } catch (error: any) {
            console.error('Ошибка при восстановлении организации:', error);
            return {
                success: false,
                error: {
                    code: 'CLIENT_ERROR',
                    message: 'Ошибка при восстановлении организации',
                    details: error
                }
            };
        }
    }

    // ===== МЕТОДЫ ДЛЯ АДРЕСОВ =====

    /**
     * Создание адреса
     */
    async createAddress(addressData: FrontendAddress): Promise<ApiResponse<void>> {
        try {
            if (this.enableDevLogs) {
                console.log('🔧 createAddress:', addressData);
            }

            // Для создания адреса нужен organizationId
            // Пока используем заглушку, так как в интерфейсе FrontendAddress нет organizationId
            const addressDto = ApiMapper.mapAddressFromFrontend(addressData);
            const requestData = {
                address: addressDto,
                type: AddressType.LEGAL, // По умолчанию юридический
                organizationId: 'unknown' // Нужно будет передавать отдельно
            };

            const result = await restApiClient.createAddress(requestData);

            return {
                success: result.success,
                data: undefined,
                error: result.error ? {
                    code: result.error.code,
                    message: result.error.message,
                    details: result.error.details
                } : undefined
            };

        } catch (error: any) {
            console.error('Ошибка при создании адреса:', error);
            return {
                success: false,
                error: {
                    code: 'CLIENT_ERROR',
                    message: 'Ошибка при создании адреса',
                    details: error
                }
            };
        }
    }

    // Остальные методы для адресов реализуются аналогично...
    async updateAddress(addressData: FrontendAddress): Promise<ApiResponse<void>> {
        // TODO: Реализовать обновление адреса
        throw new Error('Method not implemented yet');
    }

    async addressById(id: string): Promise<ApiResponse<FrontendAddress>> {
        // TODO: Реализовать получение адреса по ID
        throw new Error('Method not implemented yet');
    }

    async addressList(params?: { pagination?: PaginationParams }): Promise<ApiResponse<FrontendAddress[]>> {
        // TODO: Реализовать получение списка адресов
        throw new Error('Method not implemented yet');
    }

    async deleteAddress(id: string): Promise<ApiResponse<void>> {
        // TODO: Реализовать удаление адреса
        throw new Error('Method not implemented yet');
    }

    async recoverAddress(id: string): Promise<ApiResponse<void>> {
        // TODO: Реализовать восстановление адреса
        throw new Error('Method not implemented yet');
    }

    // ===== УТИЛИТАРНЫЕ МЕТОДЫ =====

    /**
     * Маппинг организации из proto (для совместимости)
     */
    mapOrganizationFromProto(protoOrg: any): FrontendOrganization {
        // Для совместимости с существующим кодом
        return ApiMapper.mapOrganizationToFrontend(protoOrg);
    }

    /**
     * Маппинг организации в proto (для совместимости)
     */
    mapOrganizationToProto(orgData: FrontendOrganization): any {
        // Для совместимости с существующим кодом
        return ApiMapper.mapOrganizationFromFrontend(orgData);
    }

    /**
     * Маппинг адреса в proto (для совместимости)
     */
    mapAddressToProto(addressData: FrontendAddress | string): any {
        if (typeof addressData === 'string') {
            return {
                name: addressData,
                region: '',
                city: '',
                house: '',
                isDeleted: false
            };
        }
        return ApiMapper.mapAddressFromFrontend(addressData);
    }

    /**
     * Извлечение формы собственности из названия организации
     */
    extractOwnershipForm(name: string): string {
        return ApiMapper.extractOwnershipForm(name);
    }
}

// Создаем и экспортируем экземпляр сервиса
const pasivGatePrivateService = new PasivGatePrivateService();

export default pasivGatePrivateService;
export { PasivGatePrivateService };
