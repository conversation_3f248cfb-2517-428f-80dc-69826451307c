export class TransactionService {
    static getData() {
        return [
            {
                id: 1,
                organizationId: 1,
                contractId: 1,
                type: 'trip',
                amount: 45.50,
                paymentMethod: 'emv_card',
                routeId: 'route_001',
                routeName: 'Маршрут №1',
                vehicleId: 'bus_123',
                timestamp: '2024-02-01T08:30:15Z',
                status: 'completed',
                processingSystem: 'EMV',
                cardNumber: '****1234',
                terminalId: 'term_001'
            },
            {
                id: 2,
                organizationId: 1,
                contractId: 1,
                type: 'trip',
                amount: 35.00,
                paymentMethod: 'transport_card',
                routeId: 'route_002',
                routeName: 'Маршрут №15',
                vehicleId: 'bus_456',
                timestamp: '2024-02-01T09:15:30Z',
                status: 'completed',
                processingSystem: 'TMS',
                cardNumber: '****5678',
                terminalId: 'term_002'
            },
            {
                id: 3,
                organizationId: 2,
                contractId: 2,
                type: 'trip',
                amount: 40.00,
                paymentMethod: 'cash',
                routeId: 'route_003',
                routeName: 'Маршрут №7',
                vehicleId: 'bus_789',
                timestamp: '2024-02-01T10:45:00Z',
                status: 'completed',
                processingSystem: 'CASH',
                cardNumber: null,
                terminalId: null
            }
        ];
    }

    static getTransactions(filters = {}) {
        let transactions = this.getData();

        if (filters.organizationId) {
            transactions = transactions.filter(t => t.organizationId == filters.organizationId);
        }

        if (filters.contractId) {
            transactions = transactions.filter(t => t.contractId == filters.contractId);
        }

        if (filters.paymentMethod) {
            transactions = transactions.filter(t => t.paymentMethod === filters.paymentMethod);
        }

        if (filters.dateFrom) {
            transactions = transactions.filter(t => 
                new Date(t.timestamp) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            const dateTo = new Date(filters.dateTo);
            dateTo.setHours(23, 59, 59, 999);
            transactions = transactions.filter(t => 
                new Date(t.timestamp) <= dateTo
            );
        }

        return Promise.resolve(transactions);
    }

    static getOrganizationOperations(organizationId, filters = {}) {
        const mockOperations = [
            {
                id: 1,
                organizationId: parseInt(organizationId),
                type: 'transaction',
                description: 'Поездка по маршруту №1',
                amount: 45.50,
                status: 'success',
                timestamp: '2024-02-01T08:30:15Z',
                reference: 'TXN_001'
            },
            {
                id: 2,
                organizationId: parseInt(organizationId),
                type: 'billing',
                description: 'Расчет биллинга за февраль 2024',
                amount: 125000,
                status: 'success',
                timestamp: '2024-03-01T10:00:00Z',
                reference: 'BILL_202402'
            },
            {
                id: 3,
                organizationId: parseInt(organizationId),
                type: 'settlement',
                description: 'Взаиморасчет с перевозчиком',
                amount: 112500,
                status: 'processing',
                timestamp: '2024-03-05T14:30:00Z',
                reference: 'SETT_001'
            },
            {
                id: 4,
                organizationId: parseInt(organizationId),
                type: 'clearing',
                description: 'Клиринг за февраль 2024',
                amount: 2450000,
                status: 'success',
                timestamp: '2024-03-01T18:00:00Z',
                reference: 'CLR_202402'
            },
            {
                id: 5,
                organizationId: parseInt(organizationId),
                type: 'transaction',
                description: 'Поездка по маршруту №15',
                amount: 35.00,
                status: 'error',
                timestamp: '2024-02-01T09:15:30Z',
                reference: 'TXN_002'
            }
        ];

        let filtered = mockOperations.filter(op => op.organizationId === parseInt(organizationId));

        if (filters.type) {
            filtered = filtered.filter(op => op.type === filters.type);
        }

        if (filters.status) {
            filtered = filtered.filter(op => op.status === filters.status);
        }

        if (filters.dateFrom) {
            filtered = filtered.filter(op => 
                new Date(op.timestamp) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            const dateTo = new Date(filters.dateTo);
            dateTo.setHours(23, 59, 59, 999);
            filtered = filtered.filter(op => 
                new Date(op.timestamp) <= dateTo
            );
        }

        return Promise.resolve(filtered);
    }

    static getTransactionById(transactionId) {
        const transactions = this.getData();
        const transaction = transactions.find(t => t.id == transactionId);
        return Promise.resolve(transaction);
    }

    static getTransactionStatistics(filters = {}) {
        return Promise.resolve({
            totalTransactions: 15420,
            totalAmount: 2450000,
            averageAmount: 158.9,
            successRate: 98.5,
            byPaymentMethod: {
                emv_card: { count: 9252, amount: 1470000, percentage: 60.0 },
                transport_card: { count: 4626, amount: 735000, percentage: 30.0 },
                cash: { count: 1542, amount: 245000, percentage: 10.0 }
            },
            byHour: [
                { hour: 6, count: 150, amount: 23850 },
                { hour: 7, count: 890, amount: 141420 },
                { hour: 8, count: 1250, amount: 198625 },
                { hour: 9, count: 980, amount: 155722 },
                { hour: 10, count: 750, amount: 119175 },
                { hour: 11, count: 650, amount: 103285 },
                { hour: 12, count: 720, count: 114408 },
                { hour: 13, count: 680, amount: 108052 },
                { hour: 14, count: 890, amount: 141420 },
                { hour: 15, count: 1100, amount: 174790 },
                { hour: 16, count: 1200, amount: 190680 },
                { hour: 17, count: 1350, amount: 214515 },
                { hour: 18, count: 1450, amount: 230405 },
                { hour: 19, count: 1200, amount: 190680 },
                { hour: 20, count: 950, amount: 150955 },
                { hour: 21, count: 650, amount: 103285 },
                { hour: 22, count: 450, amount: 71505 },
                { hour: 23, count: 250, amount: 39725 }
            ]
        });
    }

    static processTransaction(transactionData) {
        console.log('Processing transaction:', transactionData);
        
        const newTransaction = {
            ...transactionData,
            id: Date.now(),
            timestamp: new Date().toISOString(),
            status: 'processing'
        };

        return Promise.resolve(newTransaction);
    }

    static retryTransaction(transactionId) {
        console.log('Retrying transaction:', transactionId);
        return Promise.resolve({ success: true });
    }

    static cancelTransaction(transactionId) {
        console.log('Cancelling transaction:', transactionId);
        return Promise.resolve({ success: true });
    }

    static exportTransactions(filters = {}, format = 'xlsx') {
        console.log('Exporting transactions:', filters, format);
        
        const exportJob = {
            id: Date.now(),
            status: 'processing',
            format,
            filters,
            createdDate: new Date().toISOString(),
            estimatedCompletion: new Date(Date.now() + 30000).toISOString()
        };

        return Promise.resolve(exportJob);
    }
}
