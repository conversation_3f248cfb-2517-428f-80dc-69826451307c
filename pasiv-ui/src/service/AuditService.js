export class AuditService {
    static getOrganizationAuditLog(organizationId, filters = {}) {
        const mockData = [
            {
                id: 1,
                organizationId: parseInt(organizationId),
                action: 'CREATE',
                description: 'Организация создана в системе',
                userId: 'admin',
                ipAddress: '*************',
                timestamp: '2024-01-15T10:00:00Z',
                details: {
                    name: 'ООО "Транспорт Плюс"',
                    inn: '7701234567'
                }
            },
            {
                id: 2,
                organizationId: parseInt(organizationId),
                action: 'SYNC',
                description: 'Синхронизация данных с 1С',
                userId: 'system',
                ipAddress: '********',
                timestamp: '2024-01-15T10:30:00Z',
                details: {
                    syncStatus: 'success',
                    recordsUpdated: 1
                }
            },
            {
                id: 3,
                organizationId: parseInt(organizationId),
                action: 'UPDATE',
                description: 'Обновлены контактные данные',
                userId: 'operator1',
                ipAddress: '*************',
                timestamp: '2024-01-20T14:15:00Z',
                details: {
                    field: 'phone',
                    oldValue: '+7 (495) 123-45-67',
                    newValue: '+7 (495) 123-45-68'
                }
            },
            {
                id: 4,
                organizationId: parseInt(organizationId),
                action: 'UPDATE',
                description: 'Добавлен новый сотрудник',
                userId: 'admin',
                ipAddress: '*************',
                timestamp: '2024-01-25T09:45:00Z',
                details: {
                    employeeName: 'Иванов И.И.',
                    position: 'Бухгалтер'
                }
            },
            {
                id: 5,
                organizationId: parseInt(organizationId),
                action: 'SYNC',
                description: 'Плановая синхронизация с 1С',
                userId: 'system',
                ipAddress: '********',
                timestamp: '2024-02-01T02:00:00Z',
                details: {
                    syncStatus: 'success',
                    recordsUpdated: 0
                }
            }
        ];

        // Применяем фильтры
        let filtered = mockData.filter(log => log.organizationId === parseInt(organizationId));

        if (filters.action) {
            filtered = filtered.filter(log => log.action === filters.action);
        }

        if (filters.user) {
            filtered = filtered.filter(log => 
                log.userId.toLowerCase().includes(filters.user.toLowerCase())
            );
        }

        if (filters.dateFrom) {
            filtered = filtered.filter(log => 
                new Date(log.timestamp) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            const dateTo = new Date(filters.dateTo);
            dateTo.setHours(23, 59, 59, 999);
            filtered = filtered.filter(log => 
                new Date(log.timestamp) <= dateTo
            );
        }

        return Promise.resolve(filtered);
    }

    static getContractAuditLog(contractId, filters = {}) {
        const mockData = [
            {
                id: 1,
                contractId: parseInt(contractId),
                action: 'CREATE',
                description: 'Договор импортирован из 1С',
                userId: 'system',
                ipAddress: '********',
                timestamp: '2024-01-10T08:00:00Z',
                details: {
                    number: 'Д-2024-001',
                    amount: 1000000
                }
            },
            {
                id: 2,
                contractId: parseInt(contractId),
                action: 'UPDATE',
                description: 'Обновлен расчетный период',
                userId: 'admin',
                ipAddress: '*************',
                timestamp: '2024-01-15T11:30:00Z',
                details: {
                    field: 'calculationPeriod',
                    oldValue: 'monthly',
                    newValue: 'weekly'
                }
            },
            {
                id: 3,
                contractId: parseInt(contractId),
                action: 'UPDATE',
                description: 'Добавлен способ оплаты',
                userId: 'operator1',
                ipAddress: '*************',
                timestamp: '2024-01-20T16:45:00Z',
                details: {
                    paymentMethod: 'EMV карты',
                    commission: '2.5%'
                }
            }
        ];

        let filtered = mockData.filter(log => log.contractId === parseInt(contractId));

        if (filters.action) {
            filtered = filtered.filter(log => log.action === filters.action);
        }

        if (filters.user) {
            filtered = filtered.filter(log => 
                log.userId.toLowerCase().includes(filters.user.toLowerCase())
            );
        }

        if (filters.dateFrom) {
            filtered = filtered.filter(log => 
                new Date(log.timestamp) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            const dateTo = new Date(filters.dateTo);
            dateTo.setHours(23, 59, 59, 999);
            filtered = filtered.filter(log => 
                new Date(log.timestamp) <= dateTo
            );
        }

        return Promise.resolve(filtered);
    }

    static getEmployeeAuditLog(employeeId, filters = {}) {
        const mockData = [
            {
                id: 1,
                employeeId: parseInt(employeeId),
                action: 'CREATE',
                description: 'Сотрудник добавлен в систему',
                userId: 'admin',
                ipAddress: '*************',
                timestamp: '2024-01-15T10:00:00Z',
                details: {
                    name: 'Иванов И.И.',
                    position: 'Бухгалтер'
                }
            },
            {
                id: 2,
                employeeId: parseInt(employeeId),
                action: 'UPDATE',
                description: 'Назначены роли в системе',
                userId: 'admin',
                ipAddress: '*************',
                timestamp: '2024-01-15T10:30:00Z',
                details: {
                    roles: ['pasiv_admin', 'billing_operator']
                }
            },
            {
                id: 3,
                employeeId: parseInt(employeeId),
                action: 'UPDATE',
                description: 'Активирован доступ в систему',
                userId: 'admin',
                ipAddress: '*************',
                timestamp: '2024-01-15T11:00:00Z',
                details: {
                    status: 'active'
                }
            }
        ];

        let filtered = mockData.filter(log => log.employeeId === parseInt(employeeId));

        if (filters.action) {
            filtered = filtered.filter(log => log.action === filters.action);
        }

        return Promise.resolve(filtered);
    }

    static getSystemAuditLog(filters = {}) {
        const mockData = [
            {
                id: 1,
                action: 'SYSTEM_START',
                description: 'Система запущена',
                userId: 'system',
                ipAddress: '********',
                timestamp: '2024-02-01T00:00:00Z',
                details: {
                    version: '1.0.0',
                    environment: 'production'
                }
            },
            {
                id: 2,
                action: 'BACKUP_CREATED',
                description: 'Создана резервная копия базы данных',
                userId: 'system',
                ipAddress: '********',
                timestamp: '2024-02-01T02:00:00Z',
                details: {
                    backupSize: '2.5GB',
                    duration: '15 minutes'
                }
            },
            {
                id: 3,
                action: 'USER_LOGIN',
                description: 'Пользователь вошел в систему',
                userId: 'admin',
                ipAddress: '*************',
                timestamp: '2024-02-01T08:30:00Z',
                details: {
                    sessionId: 'sess_123456789'
                }
            }
        ];

        let filtered = [...mockData];

        if (filters.action) {
            filtered = filtered.filter(log => log.action === filters.action);
        }

        if (filters.user) {
            filtered = filtered.filter(log => 
                log.userId.toLowerCase().includes(filters.user.toLowerCase())
            );
        }

        if (filters.dateFrom) {
            filtered = filtered.filter(log => 
                new Date(log.timestamp) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            const dateTo = new Date(filters.dateTo);
            dateTo.setHours(23, 59, 59, 999);
            filtered = filtered.filter(log => 
                new Date(log.timestamp) <= dateTo
            );
        }

        return Promise.resolve(filtered);
    }
}
