import contractGatePrivateService from './ContractGatePrivateService';
import type { Contract, ContractOrganization } from '@/types/contract';

/**
 * Сервис для работы с договорами
 * Интегрирован с gRPC API, сохраняет обратную совместимость
 */
export const ContractService = {
    getData: (): Contract[] => {
        return [
            {
                id: 1,
                // Основные атрибуты договора
                projectCode: 'MSK-001',
                projectName: 'СберТройка ПРО Москва',
                projectType: 'transport_system',
                contractType: 'system_rules',
                contractName: 'Правила системы СберТройка ПРО г. Москва',
                contractNumber: 'ПС-СТ-2024-001',
                signatureDate: '2024-01-15T00:00:00Z',
                conclusionDate: '2024-01-01T00:00:00Z',
                completionDate: '2024-12-31T23:59:59Z',
                status: 'active',
                externalId1C: 'DOC_000001_2024',

                // Организации в договоре с ролями
                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'operator',
                        roleDescription: 'Оператор - организатор ТКП СТ'
                    },
                    {
                        organizationId: 2,
                        organizationName: 'ООО "Городской транспорт"',
                        role: 'carrier',
                        roleDescription: 'Перевозчик'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "СберТех"',
                        role: 'processing_center',
                        roleDescription: 'Процессинговый центр'
                    }
                ],

                // Системная информация
                syncStatus: 'synced',
                lastSyncDate: '2024-01-20T10:30:00Z',
                createdDate: '2023-12-15T09:00:00Z'
            },
            {
                id: 2,
                projectCode: 'MSK-002',
                projectName: 'СберТройка ПРО Метро',
                projectType: 'metro_system',
                contractType: 'system_rules',
                contractName: 'Правила системы СберТройка ПРО Метрополитен',
                contractNumber: 'ПС-СТ-2024-002',
                signatureDate: '2024-02-10T00:00:00Z',
                conclusionDate: '2024-02-01T00:00:00Z',
                completionDate: '2025-01-31T23:59:59Z',
                status: 'active',
                externalId1C: 'DOC_000002_2024',

                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'operator',
                        roleDescription: 'Оператор - организатор ТКП СТ'
                    },
                    {
                        organizationId: 2,
                        organizationName: 'АО "Метрополитен"',
                        role: 'carrier',
                        roleDescription: 'Перевозчик'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "СберТех"',
                        role: 'processing_center',
                        roleDescription: 'Процессинговый центр'
                    }
                ],

                syncStatus: 'synced',
                lastSyncDate: '2024-01-18T14:20:00Z',
                createdDate: '2024-01-10T11:30:00Z'
            },
            {
                id: 3,
                projectCode: 'MSK-003',
                projectName: 'СберТройка ПРО Автобусы',
                projectType: 'bus_system',
                contractType: 'system_rules',
                contractName: 'Правила системы СберТройка ПРО Автобусы',
                contractNumber: 'ПС-СТ-2023-045',
                signatureDate: '2023-05-25T00:00:00Z',
                conclusionDate: '2023-06-01T00:00:00Z',
                completionDate: '2024-05-31T23:59:59Z',
                status: 'expiring',
                externalId1C: 'DOC_000045_2023',

                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'operator',
                        roleDescription: 'Оператор - организатор ТКП СТ'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "Автобусный парк №1"',
                        role: 'carrier',
                        roleDescription: 'Перевозчик'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "СберТех"',
                        role: 'processing_center',
                        roleDescription: 'Процессинговый центр'
                    }
                ],

                syncStatus: 'pending',
                lastSyncDate: '2024-01-15T16:45:00Z',
                createdDate: '2023-05-20T13:15:00Z'
            },
            {
                id: 4,
                projectCode: 'MSK-004',
                projectName: 'СберТройка ПРО Инновации',
                projectType: 'innovation_system',
                contractType: 'development_agreement',
                contractName: 'Договор на внедрение инновационных решений СберТройка ПРО',
                contractNumber: 'ДОГ-СТ-2024-003',
                signatureDate: null, // Еще не подписан
                conclusionDate: '2024-03-01T00:00:00Z',
                completionDate: '2024-12-31T23:59:59Z',
                status: 'draft',
                externalId1C: null, // Еще не синхронизирован

                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'contractor',
                        roleDescription: 'Подрядчик'
                    },
                    {
                        organizationId: 4,
                        organizationName: 'ГУП "Мосгортранс"',
                        role: 'customer',
                        roleDescription: 'Заказчик'
                    }
                ],

                syncStatus: 'never',
                lastSyncDate: null,
                createdDate: '2024-01-25T15:20:00Z'
            }
        ]
    },

    /**
     * Получить список всех договоров
     * @param {Object} params - Параметры запроса
     * @returns {Promise<Array>} Список договоров
     */
    async getContracts(params = {}) {
        try {
            const result = await contractGatePrivateService.getContractList({
                pagination: params.pagination || { page: 1, size: 100 },
                filter: params.filter || {}
            });

            if (result.success) {
                return result.data.contracts;
            } else {
                console.error('Ошибка получения договоров:', result.error);
                // Возвращаем мок-данные в случае ошибки для обратной совместимости
                return this.getData();
            }
        } catch (error) {
            console.error('Исключение при получении договоров:', error);
            return this.getData();
        }
    },

    /**
     * Получить договоры по проекту
     * @param {string} projectCode - Код проекта
     * @param {Object} pagination - Параметры пагинации
     * @returns {Promise<Array>} Список договоров проекта
     */
    async getContractsByProject(projectCode, pagination = {}) {
        try {
            const result = await contractGatePrivateService.getContractsByProject(projectCode, pagination);

            if (result.success) {
                return result.data.contracts;
            } else {
                console.error('Ошибка получения договоров проекта:', result.error);
                // Фильтруем мок-данные по проекту
                return this.getData().filter(contract => contract.projectCode === projectCode);
            }
        } catch (error) {
            console.error('Исключение при получении договоров проекта:', error);
            return this.getData().filter(contract => contract.projectCode === projectCode);
        }
    },

    /**
     * Получить договор по ID
     * @param {string} contractId - ID договора
     * @returns {Promise<Object|null>} Договор или null
     */
    async getContractById(contractId) {
        try {
            const result = await contractGatePrivateService.getContractById(contractId);

            if (result.success) {
                return result.data;
            } else {
                console.error('Ошибка получения договора:', result.error);
                // Возвращаем из мок-данных
                const contracts = this.getData();
                return contracts.find(c => c.id == contractId) || null;
            }
        } catch (error) {
            console.error('Исключение при получении договора:', error);
            const contracts = this.getData();
            return contracts.find(c => c.id == contractId) || null;
        }
    },

    /**
     * Создать новый договор
     * @param {string} projectCode - Код проекта
     * @param {Object} contractData - Данные договора
     * @returns {Promise<Object>} Результат создания
     */
    async createContract(projectCode, contractData) {
        try {
            // Подготавливаем данные для API
            const apiData = {
                ...contractData,
                projectCode,
                status: contractData.status || 'draft',
                createdDate: new Date().toISOString()
            };

            const result = await contractGatePrivateService.createContract(apiData);

            if (result.success) {
                return {
                    success: true,
                    data: apiData,
                    message: 'Договор успешно создан'
                };
            } else {
                return {
                    success: false,
                    error: result.error
                };
            }
        } catch (error) {
            console.error('Исключение при создании договора:', error);
            return {
                success: false,
                error: {
                    code: 'UNKNOWN',
                    message: 'Произошла ошибка при создании договора'
                }
            };
        }
    },

    /**
     * Обновить договор
     * @param {string} contractId - ID договора
     * @param {Object} contractData - Данные для обновления
     * @returns {Promise<Object>} Результат обновления
     */
    async updateContract(contractId, contractData) {
        try {
            const apiData = {
                ...contractData,
                id: contractId
            };

            const result = await contractGatePrivateService.updateContract(apiData);

            if (result.success) {
                return {
                    success: true,
                    data: apiData,
                    message: 'Договор успешно обновлен'
                };
            } else {
                return {
                    success: false,
                    error: result.error
                };
            }
        } catch (error) {
            console.error('Исключение при обновлении договора:', error);
            return {
                success: false,
                error: {
                    code: 'UNKNOWN',
                    message: 'Произошла ошибка при обновлении договора'
                }
            };
        }
    },

    /**
     * Удалить договор
     * @param {string} contractId - ID договора
     * @returns {Promise<Object>} Результат удаления
     */
    async deleteContract(contractId) {
        try {
            const result = await contractGatePrivateService.deleteContract(contractId);

            if (result.success) {
                return {
                    success: true,
                    message: 'Договор успешно удален'
                };
            } else {
                return {
                    success: false,
                    error: result.error
                };
            }
        } catch (error) {
            console.error('Исключение при удалении договора:', error);
            return {
                success: false,
                error: {
                    code: 'UNKNOWN',
                    message: 'Произошла ошибка при удалении договора'
                }
            };
        }
    },

    syncContract(contractId) {
        console.log('Syncing contract with 1C:', contractId);

        return new Promise((resolve) => {
            setTimeout(() => {
                const success = Math.random() > 0.2;
                resolve({
                    success,
                    message: success
                        ? 'Синхронизация договора с 1С выполнена успешно'
                        : 'Ошибка синхронизации с 1С: договор не найден в системе',
                    syncDate: new Date().toISOString()
                });
            }, 2000);
        });
    },

    syncAllContracts() {
        console.log('Syncing all contracts with 1C');

        return new Promise((resolve) => {
            setTimeout(() => {
                const totalCount = this.getData().length;
                const successCount = Math.floor(totalCount * 0.75);
                const errorCount = totalCount - successCount;

                resolve({
                    success: true,
                    totalCount,
                    successCount,
                    errorCount,
                    message: `Синхронизация завершена. Успешно: ${successCount}, с ошибками: ${errorCount}`,
                    syncDate: new Date().toISOString()
                });
            }, 4000);
        });
    },

    /**
     * Поиск договоров по фильтрам
     * @param {Object} filters - Фильтры поиска
     * @returns {Promise<Array>} Результаты поиска
     */
    async searchContracts(filters = {}) {
        try {
            const result = await contractGatePrivateService.getContractList({
                pagination: { page: 1, size: 100 },
                filter: filters
            });

            if (result.success) {
                return result.data.contracts;
            } else {
                console.error('Ошибка поиска договоров:', result.error);
                // Фильтруем мок-данные
                return this.filterMockData(filters);
            }
        } catch (error) {
            console.error('Исключение при поиске договоров:', error);
            return this.filterMockData(filters);
        }
    },

    /**
     * Получить договоры по организации
     * @param {string} organizationId - ID организации
     * @returns {Promise<Array>} Договоры организации
     */
    async getContractsByOrganization(organizationId) {
        try {
            const result = await contractGatePrivateService.getContractList({
                pagination: { page: 1, size: 100 },
                filter: { organizationId }
            });

            if (result.success) {
                return result.data.contracts;
            } else {
                console.error('Ошибка получения договоров организации:', result.error);
                // Фильтруем мок-данные
                return this.getData().filter(contract =>
                    contract.contractOrganizations?.some(org => org.organizationId == organizationId)
                );
            }
        } catch (error) {
            console.error('Исключение при получении договоров организации:', error);
            return this.getData().filter(contract =>
                contract.contractOrganizations?.some(org => org.organizationId == organizationId)
            );
        }
    },

    /**
     * Фильтрация мок-данных (для обратной совместимости)
     * @param {Object} filters - Фильтры
     * @returns {Array} Отфильтрованные данные
     */
    filterMockData(filters) {
        let contracts = this.getData();

        if (filters.projectCode) {
            contracts = contracts.filter(c =>
                c.projectCode?.toLowerCase().includes(filters.projectCode.toLowerCase())
            );
        }

        if (filters.contractNumber) {
            contracts = contracts.filter(c =>
                c.contractNumber?.toLowerCase().includes(filters.contractNumber.toLowerCase())
            );
        }

        if (filters.contractName) {
            contracts = contracts.filter(c =>
                c.contractName?.toLowerCase().includes(filters.contractName.toLowerCase())
            );
        }

        if (filters.status) {
            contracts = contracts.filter(c => c.status === filters.status);
        }

        if (filters.contractType) {
            contracts = contracts.filter(c => c.contractType === filters.contractType);
        }

        if (filters.projectType) {
            contracts = contracts.filter(c => c.projectType === filters.projectType);
        }

        return contracts;
    }
}
