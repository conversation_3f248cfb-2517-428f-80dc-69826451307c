/**
 * Композабл для работы со средствами оплаты
 */

import { ref, computed, reactive, watch } from 'vue';
import { PaymentMethodService } from '@/service/PaymentMethodService';
import { PaymentMethodHelper, PaymentMethodValidator, PaymentMethodFormatter } from '@/utils/paymentMethodUtils';
import { PAYMENT_METHOD_TYPES, DEFAULT_SETTINGS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/constants/paymentMethod';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';

/**
 * Основной композабл для работы со средствами оплаты
 */
export function usePaymentMethods(contractId = null) {
    const toast = useToast();
    const confirm = useConfirm();

    // Состояние
    const paymentMethods = ref([]);
    const loading = ref(false);
    const error = ref(null);
    const statistics = ref(null);

    // Фильтры и сортировка
    const filters = reactive({
        search: '',
        category: '',
        isActive: undefined,
        syncStatus: '',
        showDeleted: false
    });

    const sorting = reactive({
        sortBy: DEFAULT_SETTINGS.sorting.sortBy,
        sortOrder: DEFAULT_SETTINGS.sorting.sortOrder
    });

    // Пагинация
    const pagination = reactive({
        page: DEFAULT_SETTINGS.pagination.page,
        limit: DEFAULT_SETTINGS.pagination.limit,
        totalCount: 0,
        totalPages: 0
    });

    // Вычисляемые свойства
    const filteredPaymentMethods = computed(() => {
        let filtered = PaymentMethodHelper.applyFilters(paymentMethods.value, filters);
        return PaymentMethodHelper.applySorting(filtered, sorting.sortBy, sorting.sortOrder);
    });

    const groupedPaymentMethods = computed(() => {
        return PaymentMethodHelper.groupByCategory(filteredPaymentMethods.value);
    });

    const formattedPaymentMethods = computed(() => {
        return filteredPaymentMethods.value.map(pm => PaymentMethodFormatter.formatForDisplay(pm));
    });

    const hasData = computed(() => paymentMethods.value.length > 0);
    const hasFilters = computed(() => {
        return filters.search || filters.category || filters.isActive !== undefined || 
               filters.syncStatus || filters.showDeleted;
    });

    // Методы загрузки данных
    const loadPaymentMethods = async (options = {}) => {
        try {
            loading.value = true;
            error.value = null;

            const requestOptions = {
                page: pagination.page,
                limit: pagination.limit,
                filter: contractId ? { contractId, ...filters } : filters,
                ...options
            };

            let result;
            if (contractId) {
                result = await PaymentMethodService.getPaymentMethodsByContract(contractId, requestOptions);
                paymentMethods.value = result || [];
            } else {
                result = await PaymentMethodService.getPaymentMethods(requestOptions);
                paymentMethods.value = result || [];
            }

            // Обновляем статистику
            await loadStatistics();

        } catch (err) {
            error.value = err.message || ERROR_MESSAGES.FETCH_ERROR;
            console.error('Ошибка загрузки средств оплаты:', err);
            toast.add({
                severity: 'error',
                summary: 'Ошибка',
                detail: error.value,
                life: 5000
            });
        } finally {
            loading.value = false;
        }
    };

    const loadStatistics = async () => {
        try {
            const stats = await PaymentMethodService.getPaymentMethodStatistics(contractId);
            statistics.value = stats;
        } catch (err) {
            console.error('Ошибка загрузки статистики:', err);
        }
    };

    const refreshData = async () => {
        await loadPaymentMethods();
    };

    // Методы CRUD операций
    const createPaymentMethod = async (data) => {
        try {
            loading.value = true;
            
            const validation = PaymentMethodValidator.validate(data);
            if (!validation.isValid) {
                throw new Error('Ошибка валидации: ' + Object.values(validation.errors).join(', '));
            }

            const result = await PaymentMethodService.createPaymentMethod(contractId || data.contractId, data);
            
            if (result.success) {
                toast.add({
                    severity: 'success',
                    summary: 'Успех',
                    detail: SUCCESS_MESSAGES.CREATED,
                    life: 3000
                });
                await refreshData();
                return result;
            } else {
                throw new Error(result.error?.message || ERROR_MESSAGES.CREATE_ERROR);
            }
        } catch (err) {
            const errorMessage = err.message || ERROR_MESSAGES.CREATE_ERROR;
            toast.add({
                severity: 'error',
                summary: 'Ошибка',
                detail: errorMessage,
                life: 5000
            });
            throw err;
        } finally {
            loading.value = false;
        }
    };

    const updatePaymentMethod = async (id, data) => {
        try {
            loading.value = true;
            
            const validation = PaymentMethodValidator.validate(data);
            if (!validation.isValid) {
                throw new Error('Ошибка валидации: ' + Object.values(validation.errors).join(', '));
            }

            const result = await PaymentMethodService.updatePaymentMethod(id, data);
            
            if (result.success) {
                toast.add({
                    severity: 'success',
                    summary: 'Успех',
                    detail: SUCCESS_MESSAGES.UPDATED,
                    life: 3000
                });
                await refreshData();
                return result;
            } else {
                throw new Error(result.error?.message || ERROR_MESSAGES.UPDATE_ERROR);
            }
        } catch (err) {
            const errorMessage = err.message || ERROR_MESSAGES.UPDATE_ERROR;
            toast.add({
                severity: 'error',
                summary: 'Ошибка',
                detail: errorMessage,
                life: 5000
            });
            throw err;
        } finally {
            loading.value = false;
        }
    };

    const deletePaymentMethod = async (paymentMethod) => {
        return new Promise((resolve, reject) => {
            confirm.require({
                message: `Вы уверены, что хотите удалить средство оплаты "${paymentMethod.name}"?`,
                header: 'Подтверждение удаления',
                icon: 'pi pi-exclamation-triangle',
                acceptClass: 'p-button-danger',
                acceptLabel: 'Удалить',
                rejectLabel: 'Отмена',
                accept: async () => {
                    try {
                        loading.value = true;
                        
                        const result = await PaymentMethodService.deletePaymentMethod(paymentMethod.id);
                        
                        if (result.success) {
                            toast.add({
                                severity: 'success',
                                summary: 'Успех',
                                detail: SUCCESS_MESSAGES.DELETED,
                                life: 3000
                            });
                            await refreshData();
                            resolve(result);
                        } else {
                            throw new Error(result.error?.message || ERROR_MESSAGES.DELETE_ERROR);
                        }
                    } catch (err) {
                        const errorMessage = err.message || ERROR_MESSAGES.DELETE_ERROR;
                        toast.add({
                            severity: 'error',
                            summary: 'Ошибка',
                            detail: errorMessage,
                            life: 5000
                        });
                        reject(err);
                    } finally {
                        loading.value = false;
                    }
                },
                reject: () => {
                    resolve(null);
                }
            });
        });
    };

    // Методы фильтрации и поиска
    const clearFilters = () => {
        filters.search = '';
        filters.category = '';
        filters.isActive = undefined;
        filters.syncStatus = '';
        filters.showDeleted = false;
    };

    const applyFilter = (filterName, value) => {
        filters[filterName] = value;
    };

    const toggleSort = (field) => {
        if (sorting.sortBy === field) {
            sorting.sortOrder = sorting.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            sorting.sortBy = field;
            sorting.sortOrder = 'asc';
        }
    };

    // Методы экспорта
    const exportToCSV = () => {
        try {
            const csvData = PaymentMethodFormatter.formatForExport(filteredPaymentMethods.value, 'csv');
            const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `payment_methods_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            toast.add({
                severity: 'success',
                summary: 'Успех',
                detail: SUCCESS_MESSAGES.EXPORTED,
                life: 3000
            });
        } catch (err) {
            toast.add({
                severity: 'error',
                summary: 'Ошибка',
                detail: 'Ошибка экспорта данных',
                life: 5000
            });
        }
    };

    // Наблюдатели
    watch([filters, sorting], () => {
        pagination.page = 1; // Сброс на первую страницу при изменении фильтров
    }, { deep: true });

    return {
        // Состояние
        paymentMethods,
        loading,
        error,
        statistics,
        
        // Фильтры и сортировка
        filters,
        sorting,
        pagination,
        
        // Вычисляемые свойства
        filteredPaymentMethods,
        groupedPaymentMethods,
        formattedPaymentMethods,
        hasData,
        hasFilters,
        
        // Методы
        loadPaymentMethods,
        loadStatistics,
        refreshData,
        createPaymentMethod,
        updatePaymentMethod,
        deletePaymentMethod,
        clearFilters,
        applyFilter,
        toggleSort,
        exportToCSV
    };
}

/**
 * Композабл для работы с формой средства оплаты
 */
export function usePaymentMethodForm(initialData = null) {
    const toast = useToast();

    // Состояние формы
    const form = reactive({
        contractId: '',
        code: '',
        name: '',
        description: '',
        isActive: true,
        ...initialData
    });

    const errors = ref({});
    const saving = ref(false);

    // Предустановленные типы
    const predefinedMethods = computed(() => {
        return Object.entries(PAYMENT_METHOD_TYPES).map(([code, name]) => ({
            code,
            name,
            category: PaymentMethodHelper.getCategory(code)
        }));
    });

    // Валидация формы
    const validateForm = () => {
        const validation = PaymentMethodValidator.validate(form);
        errors.value = validation.errors;
        return validation.isValid;
    };

    // Сброс формы
    const resetForm = () => {
        Object.assign(form, {
            contractId: '',
            code: '',
            name: '',
            description: '',
            isActive: true,
            ...initialData
        });
        errors.value = {};
    };

    // Заполнение названия по коду
    const updateNameByCode = () => {
        if (form.code && PAYMENT_METHOD_TYPES[form.code]) {
            form.name = PAYMENT_METHOD_TYPES[form.code];
        }
    };

    // Наблюдатель за изменением кода
    watch(() => form.code, updateNameByCode);

    return {
        form,
        errors,
        saving,
        predefinedMethods,
        validateForm,
        resetForm,
        updateNameByCode
    };
}

/**
 * Композабл для статистики средств оплаты
 */
export function usePaymentMethodStatistics(contractId = null) {
    const statistics = ref(null);
    const loading = ref(false);

    const loadStatistics = async () => {
        try {
            loading.value = true;
            const stats = await PaymentMethodService.getPaymentMethodStatistics(contractId);
            statistics.value = stats;
        } catch (err) {
            console.error('Ошибка загрузки статистики:', err);
        } finally {
            loading.value = false;
        }
    };

    const chartData = computed(() => {
        if (!statistics.value) return null;

        return {
            labels: Object.keys(statistics.value.byType).map(type => PAYMENT_METHOD_TYPES[type] || type),
            datasets: [{
                data: Object.values(statistics.value.byType),
                backgroundColor: [
                    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
                    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
                ]
            }]
        };
    });

    return {
        statistics,
        loading,
        chartData,
        loadStatistics
    };
}
