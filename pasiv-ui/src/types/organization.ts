/**
 * Типы данных для работы с организациями
 */

export interface Organization {
    id?: string;
    name: string;
    shortName: string;
    fullName?: string;
    inn: string;
    kpp: string;
    ogrn: string;
    okpo?: string;
    oktmo?: string;
    okved?: string;
    note?: string;
    fioDirector?: string;
    generalDirector?: string; // Алиас для совместимости
    managerActionReason?: string;
    legalAddress?: string | Address;
    actualAddress?: string | Address;
    mailingAddress?: string | Address;
    isDeleted?: boolean;
    parent?: Organization;
    
    // Дополнительные поля для совместимости с текущим фронтендом
    status?: 'active' | 'inactive';
    type?: 'organization' | 'individual_entrepreneur';
    ownershipForm?: string;
    syncStatus?: 'never' | 'synced' | 'error' | 'pending';
    lastSyncDate?: string;
    createdDate?: string;
    
    // Контактная информация
    email?: string;
    phone?: string;
    website?: string;
    
    // Дополнительная информация
    basedOn?: string;
    responsibleForSignature?: string;
}

export interface Address {
    id?: string;
    name: string;
    index?: number;
    country?: string;
    region: string;
    district?: string;
    city: string;
    street?: string;
    house: string;
    buildingOrHousing?: string;
    officeOrRoom?: string;
    longitude?: number;
    latitude?: number;
    comment?: string;
    oktmo?: number;
    fiac?: string;
    isDeleted?: boolean;
}

export interface Contact {
    id?: string;
    organizationId: string;
    type: 'phone' | 'email';
    value: string;
    isDeleted?: boolean;
}

export interface OrganizationFilter {
    name?: string;
    inn?: string;
    kpp?: string;
    isDeleted?: boolean;
}

export interface PaginationRequest {
    page?: number;
    size?: number;
}

export interface PaginationResponse {
    page: number;
    size: number;
    total: number;
}

export interface OrganizationListParams {
    pagination?: PaginationRequest;
    filter?: OrganizationFilter;
}

export interface OrganizationListResponse {
    success: boolean;
    data?: {
        organizations: Organization[];
        pagination?: PaginationResponse;
    };
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}

export interface OrganizationResponse {
    success: boolean;
    data?: Organization;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}

export interface OrganizationHint {
    name: string;
    shortName: string;
    inn: string;
    kpp: string;
    ogrn: string;
    okpo?: string;
    oktmo?: string;
    okved?: string;
    fioDirector?: string;
    managerActionReason?: string;
    note?: string;
}

export interface OrganizationHintResponse {
    success: boolean;
    data?: OrganizationHint[];
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}
