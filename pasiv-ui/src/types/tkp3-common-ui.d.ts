/**
 * Типы для @tkp3/common-ui
 */

declare module '@tkp3/common-ui' {
  import type { App, Plugin } from 'vue';
  
  // Интерфейс для опций плагина
  export interface Tkp3CommonUIOptions {
    // Добавьте опции если они есть
  }

  // Основной плагин
  const Tkp3CommonUI: Plugin<[Tkp3CommonUIOptions?]>;
  export default Tkp3CommonUI;

  // Экспорт компонентов
  export const DataTableWrapper: any;
  
  // Добавьте другие экспортируемые компоненты по мере необходимости
  // export const SomeOtherComponent: any;
}

declare module '@tkp3/common-ui/style.css' {
  const content: string;
  export default content;
}
