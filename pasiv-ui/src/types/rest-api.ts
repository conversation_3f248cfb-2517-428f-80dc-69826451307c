/**
 * Типы для REST API, соответствующие DTO из backend проекта
 * Заменяют gRPC типы на стандартные REST API типы
 */

// ===== БАЗОВЫЕ ТИПЫ =====

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: OperationError;
    message?: string;
}

export interface OperationError {
    code: string;
    message: string;
    details?: string;
}

export interface PaginationRequest {
    page: number;
    size: number;
}

export interface PaginationResponse {
    page: number;
    size: number;
    totalElements: number;
    totalPages: number;
}

export interface PagedResponse<T> {
    content: T[];
    pagination: PaginationResponse;
    success: boolean;
    error?: OperationError;
}

// ===== ОРГАНИЗАЦИИ =====

export interface OrganizationDto {
    id?: string;
    parent?: OrganizationDto;
    name: string;
    shortName: string;
    inn: string;
    kpp: string;
    note?: string;
    okpo?: string;
    oktmo?: string;
    okved?: string;
    fioDirector?: string;
    ogrn: string;
    addressLegal?: string;
    addressActual?: string;
    addressMailing?: string;
    managerActionReason?: string;
    isDeleted: boolean;
}

export interface OrganizationWithAddressesDto {
    organization: OrganizationDto;
    addressLegal: AddressDto;
    addressActual?: AddressDto;
    addressMailing?: AddressDto;
}

export interface OrganizationFilterDto {
    name?: string;
    inn?: string;
    kpp?: string;
    isDeleted?: boolean;
}

// ===== АДРЕСА =====

export interface AddressDto {
    id?: string;
    name: string;
    index?: number;
    country?: string;
    region?: string;
    district?: string;
    city?: string;
    street?: string;
    house?: string;
    buildingOrHousing?: string;
    officeOrRoom?: string;
    longitude?: number;
    latitude?: number;
    comment?: string;
    oktmo?: number;
    fiac?: string;
    isDeleted: boolean;
}

export interface AddressCreateOrDeleteDto {
    address: AddressDto;
    type: AddressType;
    organizationId: string;
}

export enum AddressType {
    LEGAL = 'LEGAL',
    ACTUAL = 'ACTUAL',
    MAILING = 'MAILING'
}

// ===== КОНТАКТЫ =====

export interface ContactDto {
    id?: string;
    organizationId: string;
    type: ContactType;
    value: string;
    isDeleted: boolean;
}

export enum ContactType {
    PHONE = 'PHONE',
    EMAIL = 'EMAIL'
}

// ===== ИСТОРИЯ =====

export interface HistoryItemDto {
    id: string;
    version: number;
    createdAt: number; // timestamp
    createdBy: string;
    data: string; // JSON string
}

export interface HistoryResultDto {
    pagination?: PaginationResponse;
    items: HistoryItemDto[];
}

// ===== DADATA =====

export interface OrganizationHintDto {
    inn: string;
    kpp?: string;
    name: string;
    shortName?: string;
    ogrn?: string;
    okpo?: string;
    oktmo?: string;
    okved?: string;
    fioDirector?: string;
    managerActionReason?: string;
    addressLegalHint?: AddressHintDto;
    contactHints: ContactHintDto[];
}

export interface AddressHintDto {
    index?: number;
    country?: string;
    region: string;
    district?: string;
    city: string;
    street?: string;
    house: string;
    buildingOrHousing?: string;
    officeOrRoom?: string;
    longitude?: number;
    latitude?: number;
    oktmo?: number;
    fiac?: string;
}

export interface ContactHintDto {
    type: ContactType;
    value: string;
}

export interface OrganizationHintListDto {
    organizationHints: OrganizationHintDto[];
}

// ===== ЗАПРОСЫ =====

export interface OrganizationListRequest {
    pagination?: PaginationRequest;
    filter?: OrganizationFilterDto;
}

export interface AddressListRequest {
    organizationId: string;
    pagination?: PaginationRequest;
}

export interface ContactListRequest {
    organizationId: string;
    pagination?: PaginationRequest;
}

export interface HistoryRequest {
    id: string;
    pagination?: PaginationRequest;
}

export interface OrganizationHintRequest {
    inn: string;
}

// ===== ОТВЕТЫ =====

export interface OrganizationListResponse extends PagedResponse<OrganizationDto> {}

export interface AddressListResponse {
    addresses: AddressDto[];
    success: boolean;
    error?: OperationError;
}

export interface ContactListResponse {
    contacts: ContactDto[];
    success: boolean;
    error?: OperationError;
}

// ===== СОВМЕСТИМОСТЬ С СУЩЕСТВУЮЩИМИ ТИПАМИ =====

/**
 * Маппинг для совместимости с существующими типами фронтенда
 */
export interface FrontendOrganization {
    id?: string;
    name: string;
    shortName: string;
    fullName?: string;
    inn: string;
    kpp: string;
    ogrn: string;
    okpo?: string;
    oktmo?: string;
    okved?: string;
    note?: string;
    fioDirector?: string;
    generalDirector?: string;
    managerActionReason?: string;
    legalAddress?: string | FrontendAddress;
    actualAddress?: string | FrontendAddress;
    mailingAddress?: string | FrontendAddress;
    isDeleted?: boolean;
    parent?: FrontendOrganization;
    
    // Дополнительные поля для совместимости
    status?: 'active' | 'inactive';
    type?: 'organization' | 'individual_entrepreneur';
    ownershipForm?: string;
    syncStatus?: 'never' | 'synced' | 'error' | 'pending';
    lastSyncDate?: string;
    createdDate?: string;
    
    // Контактная информация
    email?: string;
    phone?: string;
    website?: string;
    
    // Дополнительная информация
    basedOn?: string;
    responsibleForSignature?: string;
}

export interface FrontendAddress {
    id?: string;
    name: string;
    index?: number;
    country?: string;
    region: string;
    district?: string;
    city: string;
    street?: string;
    house: string;
    buildingOrHousing?: string;
    officeOrRoom?: string;
    longitude?: number;
    latitude?: number;
    comment?: string;
    oktmo?: number;
    fiac?: string;
    isDeleted?: boolean;
}

export interface FrontendContact {
    id?: string;
    organizationId: string;
    type: 'phone' | 'email';
    value: string;
    isDeleted?: boolean;
}

// ===== ПАРАМЕТРЫ ЗАПРОСОВ =====

export interface OrganizationListParams {
    pagination?: {
        page?: number;
        size?: number;
    };
    filter?: {
        name?: string;
        inn?: string;
        kpp?: string;
        isDeleted?: boolean;
    };
}

export interface PaginationParams {
    page?: number;
    size?: number;
}

// ===== ОТВЕТЫ API =====

export interface ServiceResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}
