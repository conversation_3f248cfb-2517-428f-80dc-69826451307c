/**
 * TypeScript типы для средств оплаты
 */

// Основные типы средств оплаты
export type PaymentMethodType = 
    | 'BANK_CARD'
    | 'CASH'
    | 'TROIKA_SINGLE'
    | 'TROIKA_SUBSCRIPTION'
    | 'MPC_DISCOUNT'
    | 'MPC_SOCIAL'
    | 'MPC_SCHOOL'
    | 'MPC_STUDENT_SINGLE'
    | 'MPC_STUDENT_SUBSCRIPTION'
    | 'TC_RESIDENT'
    | 'MOBILE_BC'
    | 'MOBILE_VIRTUAL_TC'
    | 'MOBILE_SBP'
    | 'REGIONAL_TC'
    | 'SOCIAL_TC'
    | 'OTHER_CARDS';

// Статус синхронизации
export type SyncStatus = 'never' | 'pending' | 'synced' | 'error';

// Основной интерфейс средства оплаты
export interface PaymentMethod {
    id?: string;
    contractId: string;
    methodType: PaymentMethodType;
    code: string;
    name: string;
    description?: string;
    isActive?: boolean;
    isDeleted?: boolean;
    createdDate?: string;
    lastSyncDate?: string;
    externalId?: string;
    syncStatus?: SyncStatus;
}

// Фильтр для средств оплаты
export interface PaymentMethodFilter {
    contractId?: string;
    methodType?: PaymentMethodType;
    code?: string;
    name?: string;
    isActive?: boolean;
    isDeleted?: boolean;
    syncStatus?: SyncStatus;
    createdAfter?: string;
    createdBefore?: string;
    lastSyncAfter?: string;
    lastSyncBefore?: string;
}

// Опции для запросов
export interface PaymentMethodRequestOptions {
    page?: number;
    limit?: number;
    filter?: PaymentMethodFilter;
    sortBy?: keyof PaymentMethod;
    sortOrder?: 'asc' | 'desc';
    includeInactive?: boolean;
}

// Результат запроса списка
export interface PaymentMethodListResult {
    success: boolean;
    data?: PaymentMethod[];
    pagination?: {
        page: number;
        limit: number;
        totalPage: number;
        totalCount: number;
    };
    filter?: PaymentMethodFilter;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}

// Результат запроса одного элемента
export interface PaymentMethodResult {
    success: boolean;
    data?: PaymentMethod;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}

// Статистика по средствам оплаты
export interface PaymentMethodStatistics {
    total: number;
    active: number;
    inactive: number;
    byType: Record<PaymentMethodType, number>;
    byContract?: Record<string, number>;
    bySyncStatus: Record<SyncStatus, number>;
    lastUpdated: string;
}

// Форма для создания/редактирования средства оплаты
export interface PaymentMethodFormData {
    contractId: string;
    methodType: PaymentMethodType;
    code: string;
    name: string;
    description?: string;
    isActive: boolean;
}

// Валидация формы средства оплаты
export interface PaymentMethodValidationErrors {
    contractId?: string;
    methodType?: string;
    code?: string;
    name?: string;
    description?: string;
}

// Предустановленный тип средства оплаты
export interface PredefinedPaymentMethod {
    code: PaymentMethodType;
    name: string;
    description?: string;
    category: PaymentMethodCategory;
    isPopular?: boolean;
}

// Категории средств оплаты
export type PaymentMethodCategory = 
    | 'bank_cards'
    | 'cash'
    | 'transport_cards'
    | 'mobile_apps'
    | 'social_cards'
    | 'other';

// Опции для выпадающих списков
export interface SelectOption<T = string> {
    label: string;
    value: T;
    description?: string;
    disabled?: boolean;
    category?: string;
}

// Конфигурация модуля средств оплаты
export interface PaymentMethodModuleConfig {
    enableSync: boolean;
    enableValidation: boolean;
    enableAudit: boolean;
    enableNotifications: boolean;
    maxMethodsPerContract: number;
    allowCustomTypes: boolean;
    syncInterval: number;
    validationRules: PaymentMethodValidationRules;
}

// Правила валидации
export interface PaymentMethodValidationRules {
    codeRequired: boolean;
    nameRequired: boolean;
    nameMinLength: number;
    nameMaxLength: number;
    descriptionMaxLength: number;
    allowDuplicateCodes: boolean;
    allowedTypes: PaymentMethodType[];
}

// Действие над средством оплаты (для аудита)
export interface PaymentMethodAction {
    id: string;
    paymentMethodId: string;
    action: 'create' | 'update' | 'delete' | 'sync';
    userId: string;
    timestamp: string;
    changes?: Record<string, any>;
    metadata?: Record<string, any>;
}

// Уведомление о средстве оплаты
export interface PaymentMethodNotification {
    id: string;
    type: 'sync_error' | 'validation_error' | 'status_change' | 'expiration_warning';
    paymentMethodId: string;
    contractId: string;
    title: string;
    message: string;
    severity: 'info' | 'warning' | 'error';
    timestamp: string;
    isRead: boolean;
    metadata?: Record<string, any>;
}

// Экспорт средств оплаты
export interface PaymentMethodExportOptions {
    format: 'csv' | 'xlsx' | 'json';
    includeInactive: boolean;
    includeDeleted: boolean;
    fields: (keyof PaymentMethod)[];
    filter?: PaymentMethodFilter;
}

// Импорт средств оплаты
export interface PaymentMethodImportOptions {
    format: 'csv' | 'xlsx' | 'json';
    validateOnly: boolean;
    skipDuplicates: boolean;
    updateExisting: boolean;
    defaultContractId?: string;
}

// Результат импорта
export interface PaymentMethodImportResult {
    success: boolean;
    totalRows: number;
    successCount: number;
    errorCount: number;
    errors: Array<{
        row: number;
        field?: string;
        message: string;
    }>;
    warnings: Array<{
        row: number;
        message: string;
    }>;
}

// Шаблон средства оплаты
export interface PaymentMethodTemplate {
    id: string;
    name: string;
    description?: string;
    methodType: PaymentMethodType;
    defaultName: string;
    defaultDescription?: string;
    isActive: boolean;
    category: PaymentMethodCategory;
    metadata?: Record<string, any>;
}

// Группировка средств оплаты
export interface PaymentMethodGroup {
    category: PaymentMethodCategory;
    name: string;
    description?: string;
    methods: PaymentMethod[];
    count: number;
    activeCount: number;
}

// Сравнение средств оплаты
export interface PaymentMethodComparison {
    added: PaymentMethod[];
    removed: PaymentMethod[];
    modified: Array<{
        id: string;
        field: keyof PaymentMethod;
        oldValue: any;
        newValue: any;
    }>;
}

// Настройки отображения
export interface PaymentMethodDisplaySettings {
    showInactive: boolean;
    showDeleted: boolean;
    groupByCategory: boolean;
    sortBy: keyof PaymentMethod;
    sortOrder: 'asc' | 'desc';
    itemsPerPage: number;
    showStatistics: boolean;
    showFilters: boolean;
}

// Права доступа к средствам оплаты
export interface PaymentMethodPermissions {
    canView: boolean;
    canCreate: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canSync: boolean;
    canExport: boolean;
    canImport: boolean;
    canViewStatistics: boolean;
    restrictedTypes?: PaymentMethodType[];
    restrictedContracts?: string[];
}
