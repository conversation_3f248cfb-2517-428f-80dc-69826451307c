/**
 * Типы для Vue компонентов и глобальных объектов
 */

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Универсальные типы для всех Vue компонентов
declare module '@/*' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Типы для переменных окружения Vite
interface ImportMetaEnv {
  readonly VITE_PASIV_GATE_PRIVATE_URL: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  // добавьте другие переменные окружения по мере необходимости
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Глобальные типы для window
declare global {
  interface Window {
    // Добавьте глобальные объекты window если нужно
  }
}

export {}
