/**
 * Типы для работы с gRPC PASIV Gate Private API
 */

// Импорт типов из сгенерированных файлов
import type { PASIVGatePrivateServiceClient } from '@/generated/pasiv-gate-private_grpc_web_pb';
import type {
    Organization,
    OrganizationWithAddresses,
    OrganizationListRequest,
    OrganizationFilter,
    ByIdRequest,
    Address,
    OrganizationHintRequest,
    OrganizationResponse,
    OrganizationListResponse,
    AddressResponse,
    AddressListRequest,
    AddressListResponse
} from '@/generated/pasiv-gate-private_pb';
import type { PaginationRequest, EmptyResponse } from '@/generated/common_pb';

// Экспорт типов для использования в приложении
export type {
    PASIVGatePrivateServiceClient,
    Organization,
    OrganizationWithAddresses,
    OrganizationListRequest,
    OrganizationFilter,
    ByIdRequest,
    Address,
    OrganizationHintRequest,
    OrganizationResponse,
    OrganizationListResponse,
    AddressResponse,
    AddressListRequest,
    AddressListResponse,
    PaginationRequest,
    EmptyResponse
};

// Интерфейсы для фронтенда
export interface FrontendOrganization {
    id?: string;
    name: string;
    shortName?: string;
    fullName?: string;
    inn: string;
    kpp?: string;
    ogrn?: string;
    okpo?: string;
    oktmo?: string;
    okved?: string;
    note?: string;
    fioDirector?: string;
    generalDirector?: string;
    managerActionReason?: string;
    legalAddress?: string;
    actualAddress?: string;
    mailingAddress?: string;
    isDeleted?: boolean;
    parent?: FrontendOrganization | null;
    status?: 'active' | 'inactive';
    type?: string;
    ownershipForm?: string;
    syncStatus?: 'synced' | 'pending' | 'error';
    lastSyncDate?: string;
}

export interface FrontendAddress {
    id?: string;
    name: string;
    index?: string;
    country?: string;
    region: string;
    district?: string;
    city: string;
    street?: string;
    house: string;
    buildingOrHousing?: string;
    officeOrRoom?: string;
    longitude?: number;
    latitude?: number;
    comment?: string;
    oktmo?: string;
    fiac?: string;
    isDeleted?: boolean;
}

// Конфигурация gRPC клиента
export interface GrpcConfig {
    url: string;
    options: Record<string, any>;
}

// Типы для ошибок
export interface GrpcError {
    code: number;
    message: string;
    details?: any;
}

// Типы для пагинации
export interface PaginationParams {
    page: number;
    limit: number;
}

export interface PaginationResponse {
    page: number;
    limit: number;
    totalPage: number;
    totalCount: number;
}

// Типы для фильтрации
export interface OrganizationFilterParams {
    name?: string;
    inn?: string;
    kpp?: string;
    isDeleted?: boolean;
    parentId?: string;
}

// Типы для запросов списков
export interface OrganizationListParams {
    pagination?: PaginationParams;
    filter?: OrganizationFilterParams;
    sorted?: Array<{
        column: string;
        type: 'ASC' | 'DESC';
    }>;
}

// Типы для ответов API
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: GrpcError;
    pagination?: PaginationResponse;
}

// Типы для методов сервиса
export interface PasivGatePrivateServiceMethods {
    // Методы для организаций
    createOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>>;
    updateOrganization(orgData: FrontendOrganization): Promise<ApiResponse<void>>;
    organizationList(params?: OrganizationListParams): Promise<ApiResponse<FrontendOrganization[]>>;
    organizationById(id: string): Promise<ApiResponse<FrontendOrganization>>;
    deleteOrganization(id: string): Promise<ApiResponse<void>>;
    recoverOrganization(id: string): Promise<ApiResponse<void>>;
    
    // Методы для адресов
    createAddress(addressData: FrontendAddress): Promise<ApiResponse<void>>;
    updateAddress(addressData: FrontendAddress): Promise<ApiResponse<void>>;
    addressById(id: string): Promise<ApiResponse<FrontendAddress>>;
    addressList(params?: { pagination?: PaginationParams }): Promise<ApiResponse<FrontendAddress[]>>;
    deleteAddress(id: string): Promise<ApiResponse<void>>;
    recoverAddress(id: string): Promise<ApiResponse<void>>;
    
    // Утилитарные методы
    mapOrganizationFromProto(protoOrg: Organization): FrontendOrganization;
    mapOrganizationToProto(orgData: FrontendOrganization): Organization;
    mapAddressToProto(addressData: FrontendAddress | string): Address;
    extractOwnershipForm(name: string): string;
}
