/**
 * TypeScript типы для работы с договорами
 */

// Основные типы договоров
export type ContractStatus = 'draft' | 'active' | 'expiring' | 'completed' | 'terminated';
export type ContractType = 'system_rules' | 'service' | 'transport' | 'processing';
export type ProjectType = 'transport_system' | 'metro_system' | 'bus_system' | 'taxi_system';
export type OrganizationRole = 'operator' | 'carrier' | 'processing_center' | 'contractor' | 'partner';
export type SyncStatus = 'never' | 'pending' | 'synced' | 'error';

// Интерфейс организации в договоре
export interface ContractOrganization {
    id?: string;
    contractId?: string;
    organizationId: string;
    organizationName: string;
    role: OrganizationRole;
    roleDescription?: string;
    activeFrom?: string;
    activeTill?: string;
    isDeleted?: boolean;
}

// Основной интерфейс договора
export interface Contract {
    id?: string;
    projectCode: string;
    projectName: string;
    projectType: ProjectType;
    contractType: ContractType;
    contractName: string;
    contractNumber: string;
    signatureDate?: string;
    conclusionDate?: string;
    completionDate?: string;
    status: ContractStatus;
    externalId1C?: string;
    description?: string;
    totalAmount?: number;
    currency?: string;
    paymentTerms?: number;
    vatRate?: number;
    isDeleted?: boolean;
    createdDate?: string;
    lastSyncDate?: string;
    syncStatus?: SyncStatus;
    contractOrganizations?: ContractOrganization[];
}

// Фильтры для поиска договоров
export interface ContractFilter {
    isDeleted?: boolean;
    projectCode?: string;
    contractNumber?: string;
    contractName?: string;
    status?: ContractStatus;
    contractType?: ContractType;
    projectType?: ProjectType;
    organizationId?: string;
}

// Параметры запроса списка договоров
export interface ContractListRequest {
    pagination?: {
        page: number;
        size: number;
    };
    filter?: ContractFilter;
}

// Результат запроса списка договоров
export interface ContractListResponse {
    contracts: Contract[];
    pagination?: {
        page: number;
        size: number;
        total: number;
    };
}

// Параметры создания/обновления договора
export interface ContractCreateRequest {
    contract: Omit<Contract, 'id' | 'createdDate' | 'lastSyncDate'>;
    organizations?: ContractOrganization[];
}

export interface ContractUpdateRequest {
    contract: Contract;
    organizations?: ContractOrganization[];
}

// Результат операций с договорами
export interface ContractOperationResult {
    success: boolean;
    data?: Contract;
    message?: string;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
}

// Статистика по договорам
export interface ContractStats {
    total: number;
    byStatus: Record<ContractStatus, number>;
    byType: Record<ContractType, number>;
    byProject: Record<ProjectType, number>;
    syncStats: {
        synced: number;
        pending: number;
        errors: number;
        never: number;
    };
}

// Параметры синхронизации
export interface ContractSyncRequest {
    contractId?: string;
    projectCode?: string;
    force?: boolean;
}

export interface ContractSyncResult {
    success: boolean;
    totalCount?: number;
    successCount?: number;
    errorCount?: number;
    message: string;
    syncDate: string;
    errors?: Array<{
        contractId: string;
        error: string;
    }>;
}

// Форма для создания/редактирования договора
export interface ContractFormData {
    projectCode: string;
    projectName: string;
    projectType: ProjectType;
    contractType: ContractType;
    contractName: string;
    contractNumber: string;
    signatureDate?: Date | string;
    conclusionDate?: Date | string;
    completionDate?: Date | string;
    status: ContractStatus;
    description?: string;
    totalAmount?: number;
    currency: string;
    paymentTerms?: number;
    vatRate?: number;
    contractOrganizations: ContractOrganization[];
}

// Валидация формы договора
export interface ContractValidationErrors {
    projectCode?: string;
    projectName?: string;
    contractName?: string;
    contractNumber?: string;
    signatureDate?: string;
    conclusionDate?: string;
    completionDate?: string;
    totalAmount?: string;
    description?: string;
    contractOrganizations?: string;
}

// Опции для выпадающих списков
export interface SelectOption<T = string> {
    label: string;
    value: T;
    description?: string;
    disabled?: boolean;
}

export type ContractStatusOption = SelectOption<ContractStatus>;
export type ContractTypeOption = SelectOption<ContractType>;
export type ProjectTypeOption = SelectOption<ProjectType>;
export type OrganizationRoleOption = SelectOption<OrganizationRole>;

// Параметры экспорта договоров
export interface ContractExportParams {
    format: 'excel' | 'pdf' | 'csv';
    filters?: ContractFilter;
    fields?: string[];
    includeOrganizations?: boolean;
}

// Настройки отображения таблицы договоров
export interface ContractTableSettings {
    columns: string[];
    pageSize: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    filters?: Record<string, any>;
}

// Уведомления о договорах
export interface ContractNotification {
    id: string;
    contractId: string;
    type: 'expiring' | 'expired' | 'sync_error' | 'created' | 'updated';
    title: string;
    message: string;
    severity: 'info' | 'warn' | 'error' | 'success';
    date: string;
    read: boolean;
}

// Аудит изменений договора
export interface ContractAuditEntry {
    id: string;
    contractId: string;
    action: 'created' | 'updated' | 'deleted' | 'synced';
    field?: string;
    oldValue?: any;
    newValue?: any;
    userId: string;
    userName: string;
    timestamp: string;
    comment?: string;
}

// Шаблон договора
export interface ContractTemplate {
    id: string;
    name: string;
    description?: string;
    projectType: ProjectType;
    contractType: ContractType;
    template: Partial<Contract>;
    defaultOrganizations?: Partial<ContractOrganization>[];
    isActive: boolean;
    createdBy: string;
    createdDate: string;
}

// Права доступа к договорам
export interface ContractPermissions {
    canView: boolean;
    canCreate: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canSync: boolean;
    canExport: boolean;
    canManageTemplates: boolean;
    restrictedProjects?: string[];
}

// Конфигурация модуля договоров
export interface ContractModuleConfig {
    enableSync: boolean;
    enableTemplates: boolean;
    enableAudit: boolean;
    enableNotifications: boolean;
    defaultCurrency: string;
    defaultVatRate: number;
    maxFileSize: number;
    allowedFileTypes: string[];
    syncInterval: number;
}
