/**
 * Утилиты для работы с договорами
 */

import { 
    CONTRACT_STATUS, 
    CONTRACT_TYPE, 
    PROJECT_TYPE, 
    ORGANIZATION_ROLE,
    CONTRACT_VALIDATION_RULES,
    ERROR_MESSAGES,
    CONTRACT_STATUS_OPTIONS,
    CONTRACT_TYPE_OPTIONS,
    PROJECT_TYPE_OPTIONS,
    ORGANIZATION_ROLE_OPTIONS
} from '@/constants/contract';

/**
 * Валидация данных договора
 * @param {Object} contractData - Данные договора
 * @returns {Object} Объект с ошибками валидации
 */
export function validateContract(contractData) {
    const errors = {};

    // Валидация кода проекта
    if (!contractData.projectCode?.trim()) {
        errors.projectCode = ERROR_MESSAGES.REQUIRED_FIELD;
    } else if (contractData.projectCode.length < CONTRACT_VALIDATION_RULES.projectCode.minLength) {
        errors.projectCode = `Минимальная длина: ${CONTRACT_VALIDATION_RULES.projectCode.minLength} символов`;
    } else if (contractData.projectCode.length > CONTRACT_VALIDATION_RULES.projectCode.maxLength) {
        errors.projectCode = `Максимальная длина: ${CONTRACT_VALIDATION_RULES.projectCode.maxLength} символов`;
    } else if (!CONTRACT_VALIDATION_RULES.projectCode.pattern.test(contractData.projectCode)) {
        errors.projectCode = CONTRACT_VALIDATION_RULES.projectCode.message;
    }

    // Валидация названия проекта
    if (!contractData.projectName?.trim()) {
        errors.projectName = ERROR_MESSAGES.REQUIRED_FIELD;
    } else if (contractData.projectName.length < CONTRACT_VALIDATION_RULES.projectName.minLength) {
        errors.projectName = `Минимальная длина: ${CONTRACT_VALIDATION_RULES.projectName.minLength} символов`;
    } else if (contractData.projectName.length > CONTRACT_VALIDATION_RULES.projectName.maxLength) {
        errors.projectName = `Максимальная длина: ${CONTRACT_VALIDATION_RULES.projectName.maxLength} символов`;
    }

    // Валидация названия договора
    if (!contractData.contractName?.trim()) {
        errors.contractName = ERROR_MESSAGES.REQUIRED_FIELD;
    } else if (contractData.contractName.length < CONTRACT_VALIDATION_RULES.contractName.minLength) {
        errors.contractName = `Минимальная длина: ${CONTRACT_VALIDATION_RULES.contractName.minLength} символов`;
    } else if (contractData.contractName.length > CONTRACT_VALIDATION_RULES.contractName.maxLength) {
        errors.contractName = `Максимальная длина: ${CONTRACT_VALIDATION_RULES.contractName.maxLength} символов`;
    }

    // Валидация номера договора
    if (!contractData.contractNumber?.trim()) {
        errors.contractNumber = ERROR_MESSAGES.REQUIRED_FIELD;
    } else if (contractData.contractNumber.length < CONTRACT_VALIDATION_RULES.contractNumber.minLength) {
        errors.contractNumber = `Минимальная длина: ${CONTRACT_VALIDATION_RULES.contractNumber.minLength} символов`;
    } else if (contractData.contractNumber.length > CONTRACT_VALIDATION_RULES.contractNumber.maxLength) {
        errors.contractNumber = `Максимальная длина: ${CONTRACT_VALIDATION_RULES.contractNumber.maxLength} символов`;
    } else if (!CONTRACT_VALIDATION_RULES.contractNumber.pattern.test(contractData.contractNumber)) {
        errors.contractNumber = CONTRACT_VALIDATION_RULES.contractNumber.message;
    }

    // Валидация дат
    if (contractData.signatureDate && contractData.conclusionDate) {
        const signatureDate = new Date(contractData.signatureDate);
        const conclusionDate = new Date(contractData.conclusionDate);
        if (signatureDate < conclusionDate) {
            errors.signatureDate = 'Дата подписания не может быть раньше даты заключения';
        }
    }

    if (contractData.conclusionDate && contractData.completionDate) {
        const conclusionDate = new Date(contractData.conclusionDate);
        const completionDate = new Date(contractData.completionDate);
        if (completionDate <= conclusionDate) {
            errors.completionDate = ERROR_MESSAGES.INVALID_DATE_RANGE;
        }
    }

    // Валидация суммы договора
    if (contractData.totalAmount !== undefined && contractData.totalAmount !== null) {
        if (contractData.totalAmount < CONTRACT_VALIDATION_RULES.totalAmount.min) {
            errors.totalAmount = CONTRACT_VALIDATION_RULES.totalAmount.message;
        } else if (contractData.totalAmount > CONTRACT_VALIDATION_RULES.totalAmount.max) {
            errors.totalAmount = `Максимальная сумма: ${CONTRACT_VALIDATION_RULES.totalAmount.max}`;
        }
    }

    // Валидация описания
    if (contractData.description && contractData.description.length > CONTRACT_VALIDATION_RULES.description.maxLength) {
        errors.description = CONTRACT_VALIDATION_RULES.description.message;
    }

    // Валидация условий оплаты
    if (contractData.paymentTerms !== undefined && contractData.paymentTerms !== null) {
        if (contractData.paymentTerms < CONTRACT_VALIDATION_RULES.paymentTerms.min || 
            contractData.paymentTerms > CONTRACT_VALIDATION_RULES.paymentTerms.max) {
            errors.paymentTerms = CONTRACT_VALIDATION_RULES.paymentTerms.message;
        }
    }

    // Валидация ставки НДС
    if (contractData.vatRate !== undefined && contractData.vatRate !== null) {
        if (contractData.vatRate < CONTRACT_VALIDATION_RULES.vatRate.min || 
            contractData.vatRate > CONTRACT_VALIDATION_RULES.vatRate.max) {
            errors.vatRate = CONTRACT_VALIDATION_RULES.vatRate.message;
        }
    }

    // Валидация организаций
    if (!contractData.contractOrganizations || contractData.contractOrganizations.length === 0) {
        errors.contractOrganizations = ERROR_MESSAGES.ORGANIZATION_REQUIRED;
    }

    return errors;
}

/**
 * Получить метку статуса договора
 * @param {string} status - Статус договора
 * @returns {string} Метка статуса
 */
export function getContractStatusLabel(status) {
    const option = CONTRACT_STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.label : status;
}

/**
 * Получить severity для статуса договора
 * @param {string} status - Статус договора
 * @returns {string} Severity для PrimeVue компонентов
 */
export function getContractStatusSeverity(status) {
    const option = CONTRACT_STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.severity : 'secondary';
}

/**
 * Получить метку типа договора
 * @param {string} type - Тип договора
 * @returns {string} Метка типа
 */
export function getContractTypeLabel(type) {
    const option = CONTRACT_TYPE_OPTIONS.find(opt => opt.value === type);
    return option ? option.label : type;
}

/**
 * Получить метку типа проекта
 * @param {string} type - Тип проекта
 * @returns {string} Метка типа
 */
export function getProjectTypeLabel(type) {
    const option = PROJECT_TYPE_OPTIONS.find(opt => opt.value === type);
    return option ? option.label : type;
}

/**
 * Получить метку роли организации
 * @param {string} role - Роль организации
 * @returns {string} Метка роли
 */
export function getOrganizationRoleLabel(role) {
    const option = ORGANIZATION_ROLE_OPTIONS.find(opt => opt.value === role);
    return option ? option.label : role;
}

/**
 * Форматировать сумму договора
 * @param {number} amount - Сумма
 * @param {string} currency - Валюта
 * @returns {string} Отформатированная сумма
 */
export function formatContractAmount(amount, currency = 'RUB') {
    if (!amount && amount !== 0) return '—';
    
    const formatter = new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
    
    return formatter.format(amount);
}

/**
 * Форматировать дату
 * @param {string|Date} date - Дата
 * @param {string} format - Формат ('short', 'long', 'datetime')
 * @returns {string} Отформатированная дата
 */
export function formatContractDate(date, format = 'short') {
    if (!date) return '—';
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '—';
    
    const options = {
        short: { day: '2-digit', month: '2-digit', year: 'numeric' },
        long: { day: '2-digit', month: 'long', year: 'numeric' },
        datetime: { 
            day: '2-digit', 
            month: '2-digit', 
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }
    };
    
    return dateObj.toLocaleDateString('ru-RU', options[format] || options.short);
}

/**
 * Проверить, истекает ли договор скоро
 * @param {string} completionDate - Дата завершения договора
 * @param {number} daysThreshold - Порог в днях (по умолчанию 30)
 * @returns {boolean} true, если договор истекает скоро
 */
export function isContractExpiringSoon(completionDate, daysThreshold = 30) {
    if (!completionDate) return false;
    
    const completion = new Date(completionDate);
    const now = new Date();
    const diffTime = completion.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 && diffDays <= daysThreshold;
}

/**
 * Проверить, истек ли договор
 * @param {string} completionDate - Дата завершения договора
 * @returns {boolean} true, если договор истек
 */
export function isContractExpired(completionDate) {
    if (!completionDate) return false;
    
    const completion = new Date(completionDate);
    const now = new Date();
    
    return completion.getTime() < now.getTime();
}

/**
 * Получить количество дней до истечения договора
 * @param {string} completionDate - Дата завершения договора
 * @returns {number} Количество дней (отрицательное, если истек)
 */
export function getDaysUntilExpiration(completionDate) {
    if (!completionDate) return null;
    
    const completion = new Date(completionDate);
    const now = new Date();
    const diffTime = completion.getTime() - now.getTime();
    
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Создать пустой объект договора с значениями по умолчанию
 * @param {string} projectCode - Код проекта
 * @returns {Object} Объект договора
 */
export function createEmptyContract(projectCode = '') {
    return {
        projectCode,
        projectName: '',
        projectType: PROJECT_TYPE.TRANSPORT_SYSTEM,
        contractType: CONTRACT_TYPE.SERVICE,
        contractName: '',
        contractNumber: '',
        signatureDate: null,
        conclusionDate: null,
        completionDate: null,
        status: CONTRACT_STATUS.DRAFT,
        description: '',
        totalAmount: null,
        currency: 'RUB',
        paymentTerms: 30,
        vatRate: 20,
        contractOrganizations: []
    };
}

/**
 * Создать пустую организацию в договоре
 * @param {string} contractId - ID договора
 * @returns {Object} Объект организации
 */
export function createEmptyContractOrganization(contractId = '') {
    return {
        contractId,
        organizationId: '',
        organizationName: '',
        role: ORGANIZATION_ROLE.CONTRACTOR,
        roleDescription: '',
        activeFrom: new Date().toISOString(),
        activeTill: null,
        isDeleted: false
    };
}

/**
 * Фильтровать договоры по тексту
 * @param {Array} contracts - Массив договоров
 * @param {string} searchText - Текст для поиска
 * @returns {Array} Отфильтрованные договоры
 */
export function filterContractsByText(contracts, searchText) {
    if (!searchText || !searchText.trim()) return contracts;
    
    const search = searchText.toLowerCase().trim();
    
    return contracts.filter(contract => 
        contract.contractNumber?.toLowerCase().includes(search) ||
        contract.contractName?.toLowerCase().includes(search) ||
        contract.projectName?.toLowerCase().includes(search) ||
        contract.projectCode?.toLowerCase().includes(search) ||
        contract.externalId1C?.toLowerCase().includes(search) ||
        contract.description?.toLowerCase().includes(search)
    );
}

/**
 * Сортировать договоры
 * @param {Array} contracts - Массив договоров
 * @param {string} field - Поле для сортировки
 * @param {string} order - Порядок сортировки ('asc' | 'desc')
 * @returns {Array} Отсортированные договоры
 */
export function sortContracts(contracts, field, order = 'asc') {
    return [...contracts].sort((a, b) => {
        let aVal = a[field];
        let bVal = b[field];
        
        // Обработка дат
        if (field.includes('Date')) {
            aVal = aVal ? new Date(aVal).getTime() : 0;
            bVal = bVal ? new Date(bVal).getTime() : 0;
        }
        
        // Обработка чисел
        if (field === 'totalAmount') {
            aVal = aVal || 0;
            bVal = bVal || 0;
        }
        
        // Обработка строк
        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }
        
        if (aVal < bVal) return order === 'asc' ? -1 : 1;
        if (aVal > bVal) return order === 'asc' ? 1 : -1;
        return 0;
    });
}

/**
 * Экспортировать договоры в CSV
 * @param {Array} contracts - Массив договоров
 * @param {Array} fields - Поля для экспорта
 * @returns {string} CSV строка
 */
export function exportContractsToCSV(contracts, fields) {
    const headers = fields.map(field => {
        switch (field) {
            case 'projectCode': return 'Код проекта';
            case 'projectName': return 'Название проекта';
            case 'contractNumber': return 'Номер договора';
            case 'contractName': return 'Название договора';
            case 'contractType': return 'Тип договора';
            case 'status': return 'Статус';
            case 'signatureDate': return 'Дата подписания';
            case 'conclusionDate': return 'Дата заключения';
            case 'completionDate': return 'Дата завершения';
            case 'totalAmount': return 'Сумма договора';
            case 'currency': return 'Валюта';
            default: return field;
        }
    });
    
    const rows = contracts.map(contract => 
        fields.map(field => {
            let value = contract[field];
            
            if (field.includes('Date') && value) {
                value = formatContractDate(value);
            } else if (field === 'totalAmount' && value) {
                value = formatContractAmount(value, contract.currency);
            } else if (field === 'contractType') {
                value = getContractTypeLabel(value);
            } else if (field === 'status') {
                value = getContractStatusLabel(value);
            }
            
            return `"${value || ''}"`;
        }).join(',')
    );
    
    return [headers.join(','), ...rows].join('\n');
}
