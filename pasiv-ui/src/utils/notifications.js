/**
 * Утилиты для работы с уведомлениями
 * Обертка над PrimeVue Toast для удобного использования
 */

// Глобальная переменная для toast сервиса
let toastService = null;

/**
 * Инициализация toast сервиса
 * Должна быть вызвана в main.js или в корневом компоненте
 * @param {Object} toast - Экземпляр toast сервиса из PrimeVue
 */
export function initToast(toast) {
    toastService = toast;
}

/**
 * Показать уведомление об успехе
 * @param {string} message - Сообщение
 * @param {string} summary - Заголовок (по умолчанию "Успех")
 * @param {number} life - Время показа в мс (по умолчанию 3000)
 */
export function showSuccess(message, summary = 'Успех', life = 3000) {
    if (toastService) {
        toastService.add({
            severity: 'success',
            summary,
            detail: message,
            life
        });
    } else {
        console.log(`✅ ${summary}: ${message}`);
    }
}

/**
 * Показать уведомление об ошибке
 * @param {string} message - Сообщение
 * @param {string} summary - Заголовок (по умолчанию "Ошибка")
 * @param {number} life - Время показа в мс (по умолчанию 5000)
 */
export function showError(message, summary = 'Ошибка', life = 5000) {
    if (toastService) {
        toastService.add({
            severity: 'error',
            summary,
            detail: message,
            life
        });
    } else {
        console.error(`❌ ${summary}: ${message}`);
    }
}

/**
 * Показать информационное уведомление
 * @param {string} message - Сообщение
 * @param {string} summary - Заголовок (по умолчанию "Информация")
 * @param {number} life - Время показа в мс (по умолчанию 3000)
 */
export function showInfo(message, summary = 'Информация', life = 3000) {
    if (toastService) {
        toastService.add({
            severity: 'info',
            summary,
            detail: message,
            life
        });
    } else {
        console.info(`ℹ️ ${summary}: ${message}`);
    }
}

/**
 * Показать предупреждение
 * @param {string} message - Сообщение
 * @param {string} summary - Заголовок (по умолчанию "Предупреждение")
 * @param {number} life - Время показа в мс (по умолчанию 4000)
 */
export function showWarning(message, summary = 'Предупреждение', life = 4000) {
    if (toastService) {
        toastService.add({
            severity: 'warn',
            summary,
            detail: message,
            life
        });
    } else {
        console.warn(`⚠️ ${summary}: ${message}`);
    }
}

/**
 * Показать уведомление на основе результата API
 * @param {Object} result - Результат API вызова
 * @param {string} successMessage - Сообщение при успехе
 * @param {string} errorMessage - Сообщение при ошибке (по умолчанию из result.error)
 */
export function showApiResult(result, successMessage, errorMessage = null) {
    if (result.success) {
        showSuccess(result.message || successMessage);
    } else {
        const message = errorMessage || result.error?.message || 'Произошла неизвестная ошибка';
        showError(message);
    }
}

/**
 * Показать уведомление о загрузке
 * @param {string} message - Сообщение
 * @param {string} summary - Заголовок (по умолчанию "Загрузка")
 */
export function showLoading(message, summary = 'Загрузка') {
    showInfo(message, summary);
}

/**
 * Очистить все уведомления
 */
export function clearAll() {
    if (toastService) {
        toastService.removeAllGroups();
    }
}

// Экспорт объекта с методами для удобства
export const notifications = {
    init: initToast,
    success: showSuccess,
    error: showError,
    info: showInfo,
    warning: showWarning,
    apiResult: showApiResult,
    loading: showLoading,
    clearAll
};

export default notifications;
