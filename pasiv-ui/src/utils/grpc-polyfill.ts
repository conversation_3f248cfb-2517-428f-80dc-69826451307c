/**
 * Polyfill для CommonJS require в браузерной среде
 * Используется для загрузки сгенерированных gRPC файлов
 */

// Создаем глобальный объект require для браузера
declare global {
  var require: any;
  var module: any;
  var exports: any;
  var grpc: any;
}

// Простая реализация require для браузера
if (typeof globalThis.require === 'undefined') {
  console.log('✅ Creating new require function in TypeScript polyfill');
  const moduleCache: Record<string, any> = {};

  globalThis.require = function(moduleName: string) {
    // Проверяем кэш
    if (moduleCache[moduleName]) {
      return moduleCache[moduleName];
    }

    let moduleExports: any = {};

    // Обработка основных модулей
    // Нормализуем имя модуля для обработки относительных путей
    const normalizedModuleName = moduleName.replace(/^\.\//, '').replace(/\.js$/, '');

    switch (moduleName) {
      case 'grpc-web':
        // КРИТИЧЕСКИ ВАЖНО: возвращаем только grpc.web, а не весь grpc объект
        // Потому что в сгенерированном коде: grpc.web = require('grpc-web');
        moduleExports = globalThis.grpc?.web || {};
        break;

      case 'google-protobuf':
        // Полная заглушка для google-protobuf
        moduleExports = {
          Message: class Message {
            static deserializeBinary(bytes: Uint8Array) {
              return new this();
            }

            serializeBinary(): Uint8Array {
              return new Uint8Array();
            }

            toObject(): any {
              return {};
            }

            static toObject(includeInstance: boolean, msg: any): any {
              return {};
            }
          },

          // Базовые типы
          BinaryReader: class BinaryReader {
            constructor(bytes?: Uint8Array) {}
            readMessage(msg: any, reader: Function) {}
            readString(): string { return ''; }
            readInt32(): number { return 0; }
            readInt64(): number { return 0; }
            readUint32(): number { return 0; }
            readUint64(): number { return 0; }
            readBool(): boolean { return false; }
            readFloat(): number { return 0; }
            readDouble(): number { return 0; }
          },

          BinaryWriter: class BinaryWriter {
            writeMessage(field: number, value: any, writer: Function) {}
            writeString(field: number, value: string) {}
            writeInt32(field: number, value: number) {}
            writeInt64(field: number, value: number) {}
            writeUint32(field: number, value: number) {}
            writeUint64(field: number, value: number) {}
            writeBool(field: number, value: boolean) {}
            writeFloat(field: number, value: number) {}
            writeDouble(field: number, value: number) {}
            getResultBuffer(): Uint8Array { return new Uint8Array(); }
          },

          // Добавляем goog функции
          object: {
            extend: function(target: any, source: any): void {
              console.log('🔧 jspb.object.extend called (TypeScript polyfill):', target, source);
              for (let key in source) {
                if (source.hasOwnProperty(key)) {
                  target[key] = source[key];
                }
              }
            }
          },

          inherits: function(childCtor: any, parentCtor: any): void {
            console.log('🔧 jspb.inherits called (TypeScript polyfill):', childCtor.name, parentCtor.name);
            // Реализация наследования
            function tempCtor() {}
            tempCtor.prototype = parentCtor.prototype;
            childCtor.superClass_ = parentCtor.prototype;
            childCtor.prototype = new tempCtor();
            childCtor.prototype.constructor = childCtor;
          },

          exportSymbol: function(name: string, value: any, context?: any): void {
            console.log('📤 jspb.exportSymbol called (TypeScript polyfill):', name);
          },

          DEBUG: false
        };
        break;

      case 'google-protobuf/google/protobuf/empty_pb.js':
        // Заглушка для empty_pb
        moduleExports = {
          Empty: class Empty {
            static deserializeBinary() { return new Empty(); }
            serializeBinary() { return new Uint8Array(); }
          }
        };
        break;

      case 'google-protobuf/google/protobuf/timestamp_pb.js':
        // Заглушка для timestamp_pb
        moduleExports = {
          Timestamp: class Timestamp {
            static deserializeBinary() { return new Timestamp(); }
            serializeBinary() { return new Uint8Array(); }
            toDate() { return new Date(); }
            fromDate(date: Date) { return this; }
          }
        };
        break;

      // Обработка protobuf модулей
      case './common_pb.js':
      case 'common_pb':
        moduleExports = {
          PaginationRequest: class PaginationRequest {
            constructor() {}
            setPage(page: number) { return this; }
            getPage(): number { return 1; }
            setSize(size: number) { return this; }
            getSize(): number { return 10; }
            setLimit(limit: number) { return this; }
            getLimit(): number { return 10; }
            setTotal(total: number) { return this; }
            getTotal(): number { return 0; }
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) { return new this(); }
          },

          PaginationResponse: class PaginationResponse {
            constructor() {}
            getPage(): number { return 1; }
            getSize(): number { return 10; }
            getTotal(): number { return 0; }
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) { return new this(); }
          },

          EmptyResponse: class EmptyResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 EmptyResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
          },

          HistoryResponse: class HistoryResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 HistoryResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getHistoryList(): any[] { return []; }
            setHistoryList(list: any[]): this { return this; }
          }
        };
        break;

      case './pasiv-gate-private_pb.js':
      case 'pasiv-gate-private_pb':
        moduleExports = {
          // Организации
          Organization: class Organization {
            constructor() {}
            getId(): string { return ''; }
            setId(id: string) { return this; }
            getName(): string { return ''; }
            setName(name: string) { return this; }
            getInn(): string { return ''; }
            setInn(inn: string) { return this; }
            getKpp(): string { return ''; }
            setKpp(kpp: string) { return this; }
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) { return new this(); }
          },

          // Договоры
          Contract: class Contract {
            constructor() {}
            getId(): string { return ''; }
            setId(id: string) { return this; }
            getProjectcode(): string { return ''; }
            setProjectcode(code: string) { return this; }
            getProjectname(): string { return ''; }
            setProjectname(name: string) { return this; }
            getContractname(): string { return ''; }
            setContractname(name: string) { return this; }
            getContractnumber(): string { return ''; }
            setContractnumber(number: string) { return this; }
            getStatus(): any { return 0; }
            setStatus(status: any) { return this; }
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) { return new this(); }
          },

          // Enum значения
          OrganizationListRequest: class OrganizationListRequest {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 OrganizationListRequest.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
          },

          OrganizationListResponse: class OrganizationListResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 OrganizationListResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getOrganizationsList(): any[] { return []; }
            setOrganizationsList(list: any[]): this { return this; }
          },

          OrganizationResponse: class OrganizationResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 OrganizationResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getOrganization(): any { return null; }
            setOrganization(org: any): this { return this; }
          },

          ByIdRequest: class ByIdRequest {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ByIdRequest.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getId(): string { return ''; }
            setId(id: string): this { return this; }
          },

          AddressResponse: class AddressResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 AddressResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getAddress(): any { return null; }
            setAddress(address: any): this { return this; }
          },

          AddressListRequest: class AddressListRequest {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 AddressListRequest.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
            getFilters(): any { return null; }
            setFilters(filters: any): this { return this; }
          },

          AddressListResponse: class AddressListResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 AddressListResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getError(): any { return null; }
            setError(error: any): this { return this; }
            getResult(): any { return null; }
            setResult(result: any): this { return this; }
          },

          AddressListResult: class AddressListResult {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 AddressListResult.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
            getAddressList(): any[] { return []; }
            setAddressList(list: any[]): this { return this; }
          },

          Address: class Address {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 Address.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getId(): string { return ''; }
            setId(id: string): this { return this; }
            getAddress(): string { return ''; }
            setAddress(address: string): this { return this; }
          },

          AddressFilter: class AddressFilter {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 AddressFilter.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getOrganizationId(): string { return ''; }
            setOrganizationId(id: string): this { return this; }
            getIsDeleted(): boolean { return false; }
            setIsDeleted(deleted: boolean): this { return this; }
          },

          Contact: class Contact {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 Contact.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getId(): string { return ''; }
            setId(id: string): this { return this; }
            getName(): string { return ''; }
            setName(name: string): this { return this; }
          },

          ContactResponse: class ContactResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContactResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getError(): any { return null; }
            setError(error: any): this { return this; }
            getResult(): any { return null; }
            setResult(result: any): this { return this; }
          },

          ContactListRequest: class ContactListRequest {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContactListRequest.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
            getFilters(): any { return null; }
            setFilters(filters: any): this { return this; }
          },

          ContactListResponse: class ContactListResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContactListResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getError(): any { return null; }
            setError(error: any): this { return this; }
            getResult(): any { return null; }
            setResult(result: any): this { return this; }
          },

          ContactListResult: class ContactListResult {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContactListResult.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
            getContactsList(): any[] { return []; }
            setContactsList(list: any[]): this { return this; }
          },

          ContactFilter: class ContactFilter {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContactFilter.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getOrganizationId(): string { return ''; }
            setOrganizationId(id: string): this { return this; }
            getIsDeleted(): boolean { return false; }
            setIsDeleted(deleted: boolean): this { return this; }
          },

          ByIdWithPaginationRequest: class ByIdWithPaginationRequest {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ByIdWithPaginationRequest.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getId(): string { return ''; }
            setId(id: string): this { return this; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
          },

          ContractResponse: class ContractResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContractResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getError(): any { return null; }
            setError(error: any): this { return this; }
            getResult(): any { return null; }
            setResult(result: any): this { return this; }
          },

          ContractListRequest: class ContractListRequest {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContractListRequest.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
            getFilter(): any { return null; }
            setFilter(filter: any): this { return this; }
          },

          ContractListResponse: class ContractListResponse {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContractListResponse.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getError(): any { return null; }
            setError(error: any): this { return this; }
            getResult(): any { return null; }
            setResult(result: any): this { return this; }
          },

          ContractResult: class ContractResult {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContractResult.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
            getContractsList(): any[] { return []; }
            setContractsList(list: any[]): this { return this; }
          },

          ContractFilter: class ContractFilter {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContractFilter.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getProjectCode(): string { return ''; }
            setProjectCode(code: string): this { return this; }
            getStatus(): any { return 0; }
            setStatus(status: any): this { return this; }
          },

          ContractsByProjectRequest: class ContractsByProjectRequest {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 ContractsByProjectRequest.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getProjectCode(): string { return ''; }
            setProjectCode(code: string): this { return this; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
          },

          OrganizationFilter: class OrganizationFilter {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 OrganizationFilter.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getIsDeleted(): boolean { return false; }
            setIsDeleted(deleted: boolean): this { return this; }
            getName(): string { return ''; }
            setName(name: string): this { return this; }
            getInn(): string { return ''; }
            setInn(inn: string): this { return this; }
            getKpp(): string { return ''; }
            setKpp(kpp: string): this { return this; }
          },

          OrganizationResult: class OrganizationResult {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 OrganizationResult.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getPagination(): any { return null; }
            setPagination(pagination: any): this { return this; }
            getOrganizationList(): any[] { return []; }
            setOrganizationList(list: any[]): this { return this; }
          },

          OrganizationWithAddresses: class OrganizationWithAddresses {
            constructor() {}
            serializeBinary(): Uint8Array { return new Uint8Array(); }
            static deserializeBinary(bytes: Uint8Array) {
              console.log('🔍 OrganizationWithAddresses.deserializeBinary called (TypeScript polyfill)');
              return new this();
            }
            toObject(): any { return {}; }
            static toObject(): any { return {}; }
            getOrganization(): any { return null; }
            setOrganization(org: any): this { return this; }
            getAddressesList(): any[] { return []; }
            setAddressesList(list: any[]): this { return this; }
          },

          // Enum значения
          AddressType: {
            AT_LEGAL: 0,
            AT_ACTUAL: 1,
            AT_MAILING: 2
          },

          ContactType: {
            CT_PHONE: 0,
            CT_EMAIL: 1,
            CT_FAX: 2
          },

          ContractStatus: {
            CS_DRAFT: 0,
            CS_ACTIVE: 1,
            CS_EXPIRING: 2,
            CS_COMPLETED: 3,
            CS_TERMINATED: 4
          },

          ContractType: {
            CT_SYSTEM_RULES: 0,
            CT_SERVICE: 1,
            CT_TRANSPORT: 2,
            CT_PROCESSING: 3
          },

          ProjectType: {
            PT_TRANSPORT_SYSTEM: 0,
            PT_METRO_SYSTEM: 1,
            PT_BUS_SYSTEM: 2,
            PT_TAXI_SYSTEM: 3
          },

          OrganizationRole: {
            OR_OPERATOR: 0,
            OR_CARRIER: 1,
            OR_PROCESSING_CENTER: 2,
            OR_CONTRACTOR: 3,
            OR_PARTNER: 4
          }
        };
        break;

      default:
        // Проверяем нормализованное имя для относительных путей
        if (normalizedModuleName === 'common_pb' || normalizedModuleName === 'pasiv-gate-private_pb') {
          // Рекурсивно вызываем с нормализованным именем
          return globalThis.require(normalizedModuleName);
        }

        // Для других модулей возвращаем пустой объект
        console.warn(`Module ${moduleName} not found, returning empty object`);
        moduleExports = {};
        break;
    }

    // Кэшируем модуль
    moduleCache[moduleName] = moduleExports;
    return moduleExports;
  };
} else {
  console.log('✅ require function already exists (probably from HTML polyfill)');
}

// Создаем глобальные объекты module и exports если их нет
if (typeof globalThis.module === 'undefined') {
  console.log('✅ Creating module object in TypeScript polyfill');
  globalThis.module = { exports: {} };
} else {
  console.log('✅ module object already exists');
}

if (typeof globalThis.exports === 'undefined') {
  console.log('✅ Creating exports object in TypeScript polyfill');
  globalThis.exports = globalThis.module.exports;
} else {
  console.log('✅ exports object already exists');
}

// Создаем глобальный объект grpc для совместимости НЕМЕДЛЕННО
// Это должно быть доступно до любых импортов gRPC файлов
const grpcWebObject = {
  // Типы методов - КРИТИЧЕСКИ ВАЖНО для UNARY
  MethodType: {
    UNARY: 'unary',
    SERVER_STREAMING: 'server_streaming',
    CLIENT_STREAMING: 'client_streaming',
    BIDI_STREAMING: 'bidi_streaming'
  },

  // Базовые классы клиентов
  GrpcWebClientBase: class GrpcWebClientBase {
    constructor(hostname: string, credentials?: any, options?: any) {
      console.log('GrpcWebClientBase created:', hostname);
    }

    rpcCall(method: string, request: any, metadata: any, callback: Function) {
      console.warn('gRPC call not implemented:', method);
      callback(new Error('gRPC not implemented'), null);
    }
  },

  AbstractClientBase: class AbstractClientBase {
    constructor(hostname: string, credentials?: any, options?: any) {
      console.log('AbstractClientBase created:', hostname);
    }
  },

  MethodDescriptor: class MethodDescriptor {
    constructor(name: string, methodType: any, requestType: any, responseType: any, requestSerializeFn?: any, responseDeserializeFn?: any) {
      this.name = name;
      this.methodType = methodType;
      this.requestType = requestType;
      this.responseType = responseType;
      this.requestSerializeFn = requestSerializeFn;
      this.responseDeserializeFn = responseDeserializeFn;
      console.log('MethodDescriptor created:', name);
    }
  },

  ClientReadableStream: class ClientReadableStream {
    public _container: any;
    constructor() {
      this._container = {};
      console.log('ClientReadableStream created');
    }
    on(event: string, callback: Function) { return this; }
    cancel() {}
  },

  ClientWritableStream: class ClientWritableStream {
    public _container: any;
    constructor() {
      this._container = {};
      console.log('ClientWritableStream created');
    }
    write(message: any) {}
    end() {}
    cancel() {}
  },

  BidirectionalStream: class BidirectionalStream {
    public _container: any;
    constructor() {
      this._container = {};
      console.log('BidirectionalStream created');
    }
    write(message: any) {}
    end() {}
    cancel() {}
    on(event: string, callback: Function) { return this; }
  },

  // Статусы ответов
  StatusCode: {
    OK: 0,
    CANCELLED: 1,
    UNKNOWN: 2,
    INVALID_ARGUMENT: 3,
    DEADLINE_EXCEEDED: 4,
    NOT_FOUND: 5,
    ALREADY_EXISTS: 6,
    PERMISSION_DENIED: 7,
    UNAUTHENTICATED: 16,
    RESOURCE_EXHAUSTED: 8,
    FAILED_PRECONDITION: 9,
    ABORTED: 10,
    OUT_OF_RANGE: 11,
    UNIMPLEMENTED: 12,
    INTERNAL: 13,
    UNAVAILABLE: 14,
    DATA_LOSS: 15
  },

  // Ошибка gRPC
  RpcError: class RpcError extends Error {
    constructor(code: number, message: string, metadata?: any) {
      super(message);
      this.code = code;
      this.metadata = metadata;
    }
  }
};

// Проверяем, не создан ли уже объект grpc (например, в HTML)
if (typeof globalThis.grpc === 'undefined') {
  // Создаем объект только если его еще нет
  globalThis.grpc = {
    web: grpcWebObject
  };

  // Также создаем прямые ссылки для максимальной совместимости
  if (typeof window !== 'undefined' && typeof (window as any).grpc === 'undefined') {
    (window as any).grpc = globalThis.grpc;
  }

  console.log('✅ Created new grpc object in TypeScript polyfill');
} else {
  console.log('✅ grpc object already exists (probably from HTML polyfill)');

  // Проверяем, что существующий объект имеет все необходимые свойства
  if (!globalThis.grpc.web?.MethodType?.UNARY) {
    console.warn('⚠️ Existing grpc object is incomplete, extending it');
    try {
      // Пытаемся расширить существующий объект
      if (globalThis.grpc.web) {
        Object.assign(globalThis.grpc.web, grpcWebObject);
      } else {
        globalThis.grpc.web = grpcWebObject;
      }
    } catch (error) {
      console.warn('⚠️ Could not extend grpc object:', error);
    }
  }
}

// Экспортируем функцию инициализации
export function initGrpcPolyfill() {
  console.log('🚀 gRPC polyfill initialized');
  console.log('✅ Global grpc object available:', !!globalThis.grpc);
  console.log('✅ Global require function available:', typeof globalThis.require === 'function');
  console.log('✅ Module and exports objects available:', !!globalThis.module && !!globalThis.exports);

  // Тестируем основные модули
  try {
    const grpcWeb = globalThis.require('grpc-web');
    console.log('✅ grpc-web module loaded:', !!grpcWeb.grpc?.web?.MethodType?.UNARY);

    const protobuf = globalThis.require('google-protobuf');
    console.log('✅ google-protobuf module loaded:', !!protobuf.Message);

    const commonPb = globalThis.require('./common_pb.js');
    console.log('✅ common_pb module loaded:', !!commonPb.PaginationRequest);

    const pasivPb = globalThis.require('./pasiv-gate-private_pb.js');
    console.log('✅ pasiv-gate-private_pb module loaded:', !!pasivPb.Organization);
  } catch (error) {
    console.warn('⚠️ Error testing modules:', error);
  }
}

// Автоматически инициализируем при импорте
initGrpcPolyfill();
