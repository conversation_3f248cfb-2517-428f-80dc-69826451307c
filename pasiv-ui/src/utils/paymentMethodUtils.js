/**
 * Утилиты для работы со средствами оплаты
 */

import { 
    PAYMENT_METHOD_TYPES, 
    PAYMENT_METHOD_CATEGORIES, 
    SYNC_STATUSES,
    VALIDATION_RULES,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES
} from '@/constants/paymentMethod';

/**
 * Валидация средства оплаты
 */
export class PaymentMethodValidator {
    /**
     * Валидация полей средства оплаты
     * @param {Object} data - Данные для валидации
     * @returns {Object} Результат валидации
     */
    static validate(data) {
        const errors = {};

        // Валидация кода
        if (!data.code || data.code.trim() === '') {
            errors.code = 'Код средства оплаты обязателен';
        } else {
            const code = data.code.trim();
            if (code.length < VALIDATION_RULES.code.minLength) {
                errors.code = `Код должен содержать минимум ${VALIDATION_RULES.code.minLength} символов`;
            } else if (code.length > VALIDATION_RULES.code.maxLength) {
                errors.code = `Код не должен превышать ${VALIDATION_RULES.code.maxLength} символов`;
            } else if (!VALIDATION_RULES.code.pattern.test(code)) {
                errors.code = VALIDATION_RULES.code.message;
            } else if (!PAYMENT_METHOD_TYPES[code]) {
                errors.code = 'Недопустимый код средства оплаты';
            }
        }

        // Валидация названия
        if (!data.name || data.name.trim() === '') {
            errors.name = 'Название средства оплаты обязательно';
        } else {
            const name = data.name.trim();
            if (name.length < VALIDATION_RULES.name.minLength) {
                errors.name = `Название должно содержать минимум ${VALIDATION_RULES.name.minLength} символов`;
            } else if (name.length > VALIDATION_RULES.name.maxLength) {
                errors.name = `Название не должно превышать ${VALIDATION_RULES.name.maxLength} символов`;
            }
        }

        // Валидация описания
        if (data.description && data.description.trim().length > VALIDATION_RULES.description.maxLength) {
            errors.description = `Описание не должно превышать ${VALIDATION_RULES.description.maxLength} символов`;
        }

        // Валидация ID договора
        if (!data.contractId || data.contractId.toString().trim() === '') {
            errors.contractId = 'ID договора обязателен';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * Валидация массива средств оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @returns {Object} Результат валидации
     */
    static validateArray(paymentMethods) {
        const errors = [];
        const warnings = [];

        paymentMethods.forEach((pm, index) => {
            const validation = this.validate(pm);
            if (!validation.isValid) {
                errors.push({
                    index,
                    id: pm.id,
                    errors: validation.errors
                });
            }
        });

        // Проверка на дубликаты кодов в рамках одного договора
        const codesByContract = {};
        paymentMethods.forEach((pm, index) => {
            if (pm.contractId && pm.code) {
                const key = `${pm.contractId}_${pm.code}`;
                if (codesByContract[key]) {
                    warnings.push({
                        index,
                        id: pm.id,
                        message: `Дублирующийся код "${pm.code}" в договоре ${pm.contractId}`
                    });
                } else {
                    codesByContract[key] = true;
                }
            }
        });

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}

/**
 * Форматирование данных средств оплаты
 */
export class PaymentMethodFormatter {
    /**
     * Форматирование даты
     * @param {string|Date} date - Дата
     * @param {Object} options - Опции форматирования
     * @returns {string} Отформатированная дата
     */
    static formatDate(date, options = {}) {
        if (!date) return '-';
        
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        if (isNaN(dateObj.getTime())) return '-';

        const defaultOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        };

        return dateObj.toLocaleDateString('ru-RU', { ...defaultOptions, ...options });
    }

    /**
     * Форматирование средства оплаты для отображения
     * @param {Object} paymentMethod - Средство оплаты
     * @returns {Object} Отформатированные данные
     */
    static formatForDisplay(paymentMethod) {
        const category = PaymentMethodHelper.getCategory(paymentMethod.code);
        const syncStatusInfo = PaymentMethodHelper.getSyncStatusInfo(paymentMethod.syncStatus);

        return {
            ...paymentMethod,
            typeName: PAYMENT_METHOD_TYPES[paymentMethod.code] || paymentMethod.code,
            category,
            syncStatusInfo,
            formattedCreatedDate: this.formatDate(paymentMethod.createdDate),
            formattedLastSyncDate: this.formatDate(paymentMethod.lastSyncDate),
            statusBadge: {
                label: paymentMethod.isActive ? 'Активно' : 'Неактивно',
                severity: paymentMethod.isActive ? 'success' : 'secondary',
                icon: paymentMethod.isActive ? 'pi-check' : 'pi-times'
            },
            syncBadge: {
                label: syncStatusInfo.name,
                severity: syncStatusInfo.severity,
                icon: syncStatusInfo.icon
            }
        };
    }

    /**
     * Форматирование для экспорта
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {string} format - Формат экспорта
     * @returns {string|Object} Данные для экспорта
     */
    static formatForExport(paymentMethods, format = 'csv') {
        const formattedData = paymentMethods.map(pm => ({
            'ID': pm.id || '',
            'ID договора': pm.contractId || '',
            'Код': pm.code || '',
            'Название': pm.name || '',
            'Описание': pm.description || '',
            'Категория': PaymentMethodHelper.getCategory(pm.code)?.name || '',
            'Активно': pm.isActive ? 'Да' : 'Нет',
            'Статус синхронизации': PaymentMethodHelper.getSyncStatusInfo(pm.syncStatus).name,
            'Дата создания': this.formatDate(pm.createdDate, { hour: undefined, minute: undefined }),
            'Дата синхронизации': this.formatDate(pm.lastSyncDate, { hour: undefined, minute: undefined }),
            'Внешний ID': pm.externalId || ''
        }));

        switch (format) {
            case 'csv':
                return this.arrayToCSV(formattedData);
            case 'json':
                return JSON.stringify(formattedData, null, 2);
            default:
                return formattedData;
        }
    }

    /**
     * Преобразование массива в CSV
     * @param {Array} data - Данные
     * @returns {string} CSV строка
     */
    static arrayToCSV(data) {
        if (!data.length) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [headers.join(',')];

        data.forEach(row => {
            const values = headers.map(header => {
                const value = row[header] || '';
                return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
    }
}

/**
 * Помощник для работы со средствами оплаты
 */
export class PaymentMethodHelper {
    /**
     * Получить категорию средства оплаты
     * @param {string} code - Код типа
     * @returns {Object|null} Категория
     */
    static getCategory(code) {
        for (const [categoryKey, category] of Object.entries(PAYMENT_METHOD_CATEGORIES)) {
            if (category.types.includes(code)) {
                return { key: categoryKey, ...category };
            }
        }
        return { key: 'other', ...PAYMENT_METHOD_CATEGORIES.other };
    }

    /**
     * Получить информацию о статусе синхронизации
     * @param {string} status - Статус синхронизации
     * @returns {Object} Информация о статусе
     */
    static getSyncStatusInfo(status) {
        return SYNC_STATUSES[status] || SYNC_STATUSES.never;
    }

    /**
     * Группировка средств оплаты по категориям
     * @param {Array} paymentMethods - Массив средств оплаты
     * @returns {Array} Сгруппированные данные
     */
    static groupByCategory(paymentMethods) {
        const groups = {};

        paymentMethods.forEach(pm => {
            const category = this.getCategory(pm.code);
            const categoryKey = category.key;
            
            if (!groups[categoryKey]) {
                groups[categoryKey] = {
                    category: categoryKey,
                    name: category.name,
                    description: category.description,
                    icon: category.icon,
                    color: category.color,
                    methods: [],
                    count: 0,
                    activeCount: 0
                };
            }

            groups[categoryKey].methods.push(pm);
            groups[categoryKey].count++;
            if (pm.isActive) {
                groups[categoryKey].activeCount++;
            }
        });

        return Object.values(groups).sort((a, b) => b.count - a.count);
    }

    /**
     * Фильтрация средств оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {Object} filters - Фильтры
     * @returns {Array} Отфильтрованный массив
     */
    static applyFilters(paymentMethods, filters = {}) {
        let filtered = [...paymentMethods];

        // Поиск по тексту
        if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            filtered = filtered.filter(pm => 
                (pm.code && pm.code.toLowerCase().includes(searchLower)) ||
                (pm.name && pm.name.toLowerCase().includes(searchLower)) ||
                (pm.description && pm.description.toLowerCase().includes(searchLower))
            );
        }

        // Фильтр по категории
        if (filters.category) {
            filtered = filtered.filter(pm => {
                const category = this.getCategory(pm.code);
                return category.key === filters.category;
            });
        }

        // Фильтр по активности
        if (filters.isActive !== undefined) {
            filtered = filtered.filter(pm => pm.isActive === filters.isActive);
        }

        // Фильтр по статусу синхронизации
        if (filters.syncStatus) {
            filtered = filtered.filter(pm => pm.syncStatus === filters.syncStatus);
        }

        // Фильтр по договору
        if (filters.contractId) {
            filtered = filtered.filter(pm => pm.contractId === filters.contractId);
        }

        // Исключение удаленных
        if (!filters.showDeleted) {
            filtered = filtered.filter(pm => !pm.isDeleted);
        }

        return filtered;
    }

    /**
     * Сортировка средств оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {string} sortBy - Поле для сортировки
     * @param {string} sortOrder - Порядок сортировки
     * @returns {Array} Отсортированный массив
     */
    static applySorting(paymentMethods, sortBy = 'name', sortOrder = 'asc') {
        return [...paymentMethods].sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            // Обработка null/undefined значений
            if (aValue == null && bValue == null) return 0;
            if (aValue == null) return sortOrder === 'asc' ? 1 : -1;
            if (bValue == null) return sortOrder === 'asc' ? -1 : 1;

            // Обработка строк
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            // Обработка дат
            if (sortBy.includes('Date')) {
                aValue = new Date(aValue).getTime();
                bValue = new Date(bValue).getTime();
            }

            if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
            return 0;
        });
    }

    /**
     * Получить статистику по средствам оплаты
     * @param {Array} paymentMethods - Массив средств оплаты
     * @returns {Object} Статистика
     */
    static getStatistics(paymentMethods) {
        const total = paymentMethods.length;
        const active = paymentMethods.filter(pm => pm.isActive && !pm.isDeleted).length;
        const inactive = paymentMethods.filter(pm => !pm.isActive && !pm.isDeleted).length;
        const deleted = paymentMethods.filter(pm => pm.isDeleted).length;

        // Статистика по типам
        const byType = {};
        Object.keys(PAYMENT_METHOD_TYPES).forEach(type => {
            byType[type] = paymentMethods.filter(pm => pm.code === type && !pm.isDeleted).length;
        });

        // Статистика по категориям
        const byCategory = {};
        Object.keys(PAYMENT_METHOD_CATEGORIES).forEach(categoryKey => {
            const category = PAYMENT_METHOD_CATEGORIES[categoryKey];
            byCategory[categoryKey] = paymentMethods.filter(pm => 
                category.types.includes(pm.code) && !pm.isDeleted
            ).length;
        });

        // Статистика по статусам синхронизации
        const bySyncStatus = {};
        Object.keys(SYNC_STATUSES).forEach(status => {
            bySyncStatus[status] = paymentMethods.filter(pm => 
                (pm.syncStatus || 'never') === status && !pm.isDeleted
            ).length;
        });

        return {
            total,
            active,
            inactive,
            deleted,
            byType,
            byCategory,
            bySyncStatus,
            lastUpdated: new Date().toISOString()
        };
    }

    /**
     * Создать опции для выпадающего списка
     * @param {Array} paymentMethods - Массив средств оплаты
     * @param {Object} options - Опции
     * @returns {Array} Опции для селекта
     */
    static createSelectOptions(paymentMethods, options = {}) {
        const { 
            includeInactive = false, 
            groupByCategory = false,
            addEmptyOption = false 
        } = options;

        let filtered = paymentMethods.filter(pm => 
            !pm.isDeleted && (includeInactive || pm.isActive)
        );

        const selectOptions = filtered.map(pm => ({
            label: pm.name,
            value: pm.id,
            code: pm.code,
            category: this.getCategory(pm.code).name,
            disabled: !pm.isActive
        }));

        if (addEmptyOption) {
            selectOptions.unshift({
                label: '-- Выберите средство оплаты --',
                value: '',
                disabled: false
            });
        }

        if (groupByCategory) {
            const grouped = {};
            selectOptions.forEach(option => {
                const category = option.category || 'Прочие';
                if (!grouped[category]) {
                    grouped[category] = [];
                }
                grouped[category].push(option);
            });

            return Object.entries(grouped).map(([category, items]) => ({
                label: category,
                items: items.sort((a, b) => a.label.localeCompare(b.label))
            }));
        }

        return selectOptions.sort((a, b) => a.label.localeCompare(b.label));
    }
}

export default {
    PaymentMethodValidator,
    PaymentMethodFormatter,
    PaymentMethodHelper
};
