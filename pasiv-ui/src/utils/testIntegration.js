/**
 * Утилиты для тестирования интеграции с gRPC API
 * Используется для проверки работоспособности API в development режиме
 */

import { OrganizationService } from '@/service/OrganizationService';
import { ContractService } from '@/service/ContractService';
import { PaymentMethodService } from '@/service/PaymentMethodService';
import pasivGatePrivateService from '@/service/PasivGatePrivateService';
import contractGatePrivateService from '@/service/ContractGatePrivateService';
import paymentMethodGatePrivateService from '@/service/PaymentMethodGatePrivateService';

/**
 * Тестирование основных операций с организациями
 */
export class IntegrationTester {
    constructor() {
        this.results = [];
        this.errors = [];
    }

    /**
     * Запустить все тесты
     */
    async runAllTests() {
        console.group('🧪 Тестирование интеграции PASIV API');

        this.results = [];
        this.errors = [];

        try {
            await this.testConnection();
            await this.testGetOrganizations();
            await this.testCreateOrganization();
            await this.testUpdateOrganization();
            await this.testDeleteOrganization();
            await this.testSearchOrganizations();
            await this.testGetHints();

            // Тесты договоров
            await this.testGetContracts();
            await this.testCreateContract();
            await this.testUpdateContract();
            await this.testDeleteContract();
            await this.testSearchContracts();

            // Тесты средств оплаты
            await this.testGetPaymentMethods();
            await this.testCreatePaymentMethod();
            await this.testUpdatePaymentMethod();
            await this.testDeletePaymentMethod();
            await this.testSearchPaymentMethods();
            await this.testPaymentMethodStatistics();
        } catch (error) {
            console.error('❌ Критическая ошибка тестирования:', error);
            this.errors.push({ test: 'general', error });
        }

        this.printResults();
        console.groupEnd();

        return {
            success: this.errors.length === 0,
            results: this.results,
            errors: this.errors
        };
    }

    /**
     * Тест подключения к API
     */
    async testConnection() {
        try {
            console.log('🔌 Тестирование подключения к API...');

            const result = await pasivGatePrivateService.getOrganizationList({
                pagination: { page: 1, size: 1 }
            });

            if (result.success) {
                this.addResult('connection', '✅ Подключение к API работает');
            } else {
                this.addError('connection', 'Ошибка подключения к API', result.error);
            }
        } catch (error) {
            this.addError('connection', 'Исключение при подключении к API', error);
        }
    }

    /**
     * Тест получения списка организаций
     */
    async testGetOrganizations() {
        try {
            console.log('📋 Тестирование получения списка организаций...');

            const organizations = await OrganizationService.getOrganizations();

            if (Array.isArray(organizations)) {
                this.addResult('getOrganizations', `✅ Получен список из ${organizations.length} организаций`);

                if (organizations.length > 0) {
                    const org = organizations[0];
                    console.log('📄 Пример организации:', {
                        id: org.id,
                        name: org.name,
                        inn: org.inn,
                        status: org.status
                    });
                }
            } else {
                this.addError('getOrganizations', 'Получен некорректный формат данных', organizations);
            }
        } catch (error) {
            this.addError('getOrganizations', 'Ошибка получения списка организаций', error);
        }
    }

    /**
     * Тест создания организации
     */
    async testCreateOrganization() {
        try {
            console.log('➕ Тестирование создания организации...');

            const testOrg = {
                name: 'Тестовая организация ' + Date.now(),
                shortName: 'Тест',
                inn: '1234567890',
                kpp: '123456789',
                ogrn: '1234567890123',
                legalAddress: 'г. Москва, ул. Тестовая, д. 1',
                fioDirector: 'Тестов Тест Тестович'
            };

            const result = await OrganizationService.createOrganization(testOrg);

            if (result.success) {
                this.addResult('createOrganization', '✅ Организация успешно создана');
                this.testOrgId = result.data?.id;
            } else {
                this.addError('createOrganization', 'Ошибка создания организации', result.error);
            }
        } catch (error) {
            this.addError('createOrganization', 'Исключение при создании организации', error);
        }
    }

    /**
     * Тест обновления организации
     */
    async testUpdateOrganization() {
        if (!this.testOrgId) {
            this.addError('updateOrganization', 'Нет ID тестовой организации для обновления');
            return;
        }

        try {
            console.log('✏️ Тестирование обновления организации...');

            const updateData = {
                name: 'Обновленная тестовая организация',
                note: 'Обновлено в тесте ' + new Date().toISOString()
            };

            const result = await OrganizationService.updateOrganization(this.testOrgId, updateData);

            if (result.success) {
                this.addResult('updateOrganization', '✅ Организация успешно обновлена');
            } else {
                this.addError('updateOrganization', 'Ошибка обновления организации', result.error);
            }
        } catch (error) {
            this.addError('updateOrganization', 'Исключение при обновлении организации', error);
        }
    }

    /**
     * Тест удаления организации
     */
    async testDeleteOrganization() {
        if (!this.testOrgId) {
            this.addError('deleteOrganization', 'Нет ID тестовой организации для удаления');
            return;
        }

        try {
            console.log('🗑️ Тестирование удаления организации...');

            const result = await OrganizationService.deleteOrganization(this.testOrgId);

            if (result.success) {
                this.addResult('deleteOrganization', '✅ Организация успешно удалена');
            } else {
                this.addError('deleteOrganization', 'Ошибка удаления организации', result.error);
            }
        } catch (error) {
            this.addError('deleteOrganization', 'Исключение при удалении организации', error);
        }
    }

    /**
     * Тест поиска организаций
     */
    async testSearchOrganizations() {
        try {
            console.log('🔍 Тестирование поиска организаций...');

            const results = await OrganizationService.searchOrganizations({
                name: 'ООО'
            });

            if (Array.isArray(results)) {
                this.addResult('searchOrganizations', `✅ Поиск работает, найдено ${results.length} организаций`);
            } else {
                this.addError('searchOrganizations', 'Поиск вернул некорректный формат', results);
            }
        } catch (error) {
            this.addError('searchOrganizations', 'Ошибка поиска организаций', error);
        }
    }

    /**
     * Тест получения подсказок по ИНН
     */
    async testGetHints() {
        try {
            console.log('💡 Тестирование получения подсказок по ИНН...');

            const result = await OrganizationService.getOrganizationHintByINN('1234567890');

            if (result.success || result.error?.code === 'NOT_FOUND') {
                this.addResult('getHints', '✅ Сервис подсказок работает');
            } else {
                this.addError('getHints', 'Ошибка получения подсказок', result.error);
            }
        } catch (error) {
            this.addError('getHints', 'Исключение при получении подсказок', error);
        }
    }

    /**
     * Тест получения списка договоров
     */
    async testGetContracts() {
        try {
            console.log('📋 Тестирование получения списка договоров...');

            const contracts = await ContractService.getContracts();

            if (Array.isArray(contracts)) {
                this.addResult('getContracts', `✅ Получен список из ${contracts.length} договоров`);

                if (contracts.length > 0) {
                    const contract = contracts[0];
                    console.log('📄 Пример договора:', {
                        id: contract.id,
                        contractNumber: contract.contractNumber,
                        contractName: contract.contractName,
                        status: contract.status
                    });
                }
            } else {
                this.addError('getContracts', 'Получен некорректный формат данных', contracts);
            }
        } catch (error) {
            this.addError('getContracts', 'Ошибка получения списка договоров', error);
        }
    }

    /**
     * Тест создания договора
     */
    async testCreateContract() {
        try {
            console.log('➕ Тестирование создания договора...');

            const testContract = {
                projectCode: 'TEST-' + Date.now(),
                projectName: 'Тестовый проект ' + Date.now(),
                projectType: 'transport_system',
                contractType: 'service',
                contractName: 'Тестовый договор ' + Date.now(),
                contractNumber: 'TEST-CONTRACT-' + Date.now(),
                signatureDate: new Date().toISOString(),
                conclusionDate: new Date().toISOString(),
                completionDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'draft',
                description: 'Тестовый договор для проверки API',
                totalAmount: 1000000,
                currency: 'RUB',
                paymentTerms: 30,
                vatRate: 20,
                contractOrganizations: []
            };

            const result = await ContractService.createContract(testContract.projectCode, testContract);

            if (result.success) {
                this.addResult('createContract', '✅ Договор успешно создан');
                this.testContractId = result.data?.id;
            } else {
                this.addError('createContract', 'Ошибка создания договора', result.error);
            }
        } catch (error) {
            this.addError('createContract', 'Исключение при создании договора', error);
        }
    }

    /**
     * Тест обновления договора
     */
    async testUpdateContract() {
        if (!this.testContractId) {
            this.addError('updateContract', 'Нет ID тестового договора для обновления');
            return;
        }

        try {
            console.log('✏️ Тестирование обновления договора...');

            const updateData = {
                id: this.testContractId,
                contractName: 'Обновленный тестовый договор',
                description: 'Обновлено в тесте ' + new Date().toISOString(),
                totalAmount: 1500000
            };

            const result = await ContractService.updateContract(this.testContractId, updateData);

            if (result.success) {
                this.addResult('updateContract', '✅ Договор успешно обновлен');
            } else {
                this.addError('updateContract', 'Ошибка обновления договора', result.error);
            }
        } catch (error) {
            this.addError('updateContract', 'Исключение при обновлении договора', error);
        }
    }

    /**
     * Тест удаления договора
     */
    async testDeleteContract() {
        if (!this.testContractId) {
            this.addError('deleteContract', 'Нет ID тестового договора для удаления');
            return;
        }

        try {
            console.log('🗑️ Тестирование удаления договора...');

            const result = await ContractService.deleteContract(this.testContractId);

            if (result.success) {
                this.addResult('deleteContract', '✅ Договор успешно удален');
            } else {
                this.addError('deleteContract', 'Ошибка удаления договора', result.error);
            }
        } catch (error) {
            this.addError('deleteContract', 'Исключение при удалении договора', error);
        }
    }

    /**
     * Тест поиска договоров
     */
    async testSearchContracts() {
        try {
            console.log('🔍 Тестирование поиска договоров...');

            const results = await ContractService.searchContracts({
                contractType: 'service'
            });

            if (Array.isArray(results)) {
                this.addResult('searchContracts', `✅ Поиск работает, найдено ${results.length} договоров`);
            } else {
                this.addError('searchContracts', 'Поиск вернул некорректный формат', results);
            }
        } catch (error) {
            this.addError('searchContracts', 'Ошибка поиска договоров', error);
        }
    }

    /**
     * Добавить результат теста
     */
    addResult(test, message) {
        this.results.push({ test, message, timestamp: new Date() });
        console.log(message);
    }

    /**
     * Добавить ошибку теста
     */
    addError(test, message, error = null) {
        this.errors.push({ test, message, error, timestamp: new Date() });
        console.error(`❌ ${message}`, error);
    }

    /**
     * Вывести итоговые результаты
     */
    printResults() {
        console.log('\n📊 Результаты тестирования:');
        console.log(`✅ Успешных тестов: ${this.results.length}`);
        console.log(`❌ Ошибок: ${this.errors.length}`);

        if (this.errors.length > 0) {
            console.group('❌ Детали ошибок:');
            this.errors.forEach(({ test, message, error }) => {
                console.error(`${test}: ${message}`, error);
            });
            console.groupEnd();
        }
    }
}

/**
 * Быстрый тест подключения
 */
export async function quickConnectionTest() {
    console.log('🚀 Быстрый тест подключения к PASIV API...');

    try {
        const result = await pasivGatePrivateService.getOrganizationList({
            pagination: { page: 1, size: 1 }
        });

        if (result.success) {
            console.log('✅ API доступен');
            return true;
        } else {
            console.error('❌ API недоступен:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Ошибка подключения:', error);
        return false;
    }
}

/**
 * Быстрый тест API договоров
 */
export async function quickContractTest() {
    console.log('🚀 Быстрый тест API договоров...');

    try {
        const result = await contractGatePrivateService.getContractList({
            pagination: { page: 1, size: 1 }
        });

        if (result.success) {
            console.log('✅ API договоров доступен');
            return true;
        } else {
            console.error('❌ API договоров недоступен:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Ошибка подключения к API договоров:', error);
        return false;
    }
}

/**
 * Тест производительности
 */
export async function performanceTest() {
    console.log('⚡ Тест производительности API...');

    const startTime = performance.now();

    try {
        const result = await pasivGatePrivateService.getOrganizationList({
            pagination: { page: 1, size: 100 }
        });

        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log(`⏱️ Время выполнения: ${duration.toFixed(2)}ms`);

        if (result.success) {
            console.log(`📊 Загружено ${result.data.organizations.length} организаций`);
        }

        return { duration, success: result.success };
    } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        console.error(`❌ Ошибка за ${duration.toFixed(2)}ms:`, error);
        return { duration, success: false, error };
    }
}

// Экспорт для использования в консоли браузера
if (typeof window !== 'undefined') {
    window.PasivIntegrationTester = IntegrationTester;
    window.quickConnectionTest = quickConnectionTest;
    window.quickContractTest = quickContractTest;
    window.performanceTest = performanceTest;
}
