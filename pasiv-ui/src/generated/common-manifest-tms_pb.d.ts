import * as jspb from 'google-protobuf'

import * as common$manifest$core_pb from './common-manifest-core_pb'; // proto import: "common-manifest-core.proto"


export class ManifestTmsDict extends jspb.Message {
  getUserList(): Array<TerminalUser>;
  setUserList(value: Array<TerminalUser>): ManifestTmsDict;
  clearUserList(): ManifestTmsDict;
  addUser(value?: TerminalUser, index?: number): TerminalUser;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestTmsDict.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestTmsDict): ManifestTmsDict.AsObject;
  static serializeBinaryToWriter(message: ManifestTmsDict, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestTmsDict;
  static deserializeBinaryFromReader(message: ManifestTmsDict, reader: jspb.BinaryReader): ManifestTmsDict;
}

export namespace ManifestTmsDict {
  export type AsObject = {
    userList: Array<TerminalUser.AsObject>,
  }
}

export class ManifestTms extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestTms;
  clearFeaturesList(): ManifestTms;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  getDict(): ManifestTmsDict | undefined;
  setDict(value?: ManifestTmsDict): ManifestTms;
  hasDict(): boolean;
  clearDict(): ManifestTms;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestTms.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestTms): ManifestTms.AsObject;
  static serializeBinaryToWriter(message: ManifestTms, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestTms;
  static deserializeBinaryFromReader(message: ManifestTms, reader: jspb.BinaryReader): ManifestTms;
}

export namespace ManifestTms {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
    dict?: ManifestTmsDict.AsObject,
  }
}

export class TerminalUser extends jspb.Message {
  getId(): string;
  setId(value: string): TerminalUser;

  getRole(): string;
  setRole(value: string): TerminalUser;

  getSurname(): string;
  setSurname(value: string): TerminalUser;

  getName(): string;
  setName(value: string): TerminalUser;

  getMiddlename(): string;
  setMiddlename(value: string): TerminalUser;

  getPersonalnumber(): string;
  setPersonalnumber(value: string): TerminalUser;

  getPinhash(): string;
  setPinhash(value: string): TerminalUser;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): TerminalUser.AsObject;
  static toObject(includeInstance: boolean, msg: TerminalUser): TerminalUser.AsObject;
  static serializeBinaryToWriter(message: TerminalUser, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): TerminalUser;
  static deserializeBinaryFromReader(message: TerminalUser, reader: jspb.BinaryReader): TerminalUser;
}

export namespace TerminalUser {
  export type AsObject = {
    id: string,
    role: string,
    surname: string,
    name: string,
    middlename: string,
    personalnumber: string,
    pinhash: string,
  }
}

