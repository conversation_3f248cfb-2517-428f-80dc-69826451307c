// source: pasiv-gate-private.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js');
goog.object.extend(proto, google_protobuf_empty_pb);
var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
var common_pb = require('./common_pb.js');
goog.object.extend(proto, common_pb);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.Address', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressFilter', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressHint', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressListResult', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.AddressType', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.Contact', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactFilter', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactHint', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactListResult', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContactType', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.Contract', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractFilter', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractResult', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractStatus', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractType', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.Organization', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationRole', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ProjectType', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.Organization, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.Organization.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.Organization';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.Address = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.Address, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.Address.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.Address';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.Contact, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.Contact.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.Contact';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.AddressFilter, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.AddressFilter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.AddressListResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.AddressListResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContactFilter, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContactFilter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContactListResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContactListResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.AddressResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.AddressResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContactResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContactResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContactHint, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContactHint.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContactHint';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.AddressHint, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.AddressHint.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.AddressHint';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.Contract, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.Contract.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.Contract';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractFilter, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractFilter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.ContractResult.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractResult.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest';
}

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.Organization.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Organization;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Organization.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Organization.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional Organization result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Organization}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Organization} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Organization, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Organization|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.toObject = function(includeInstance, msg) {
  var f, obj = {
    organization: (f = msg.getOrganization()) && proto.ru.sbertroika.pasiv.gate.v1.Organization.toObject(includeInstance, f),
    addresslegal: (f = msg.getAddresslegal()) && proto.ru.sbertroika.pasiv.gate.v1.Address.toObject(includeInstance, f),
    addressactual: (f = msg.getAddressactual()) && proto.ru.sbertroika.pasiv.gate.v1.Address.toObject(includeInstance, f),
    addressmailing: (f = msg.getAddressmailing()) && proto.ru.sbertroika.pasiv.gate.v1.Address.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Organization;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Organization.deserializeBinaryFromReader);
      msg.setOrganization(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Address;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader);
      msg.setAddresslegal(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Address;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader);
      msg.setAddressactual(value);
      break;
    case 4:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Address;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader);
      msg.setAddressmailing(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrganization();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Organization.serializeBinaryToWriter
    );
  }
  f = message.getAddresslegal();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter
    );
  }
  f = message.getAddressactual();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter
    );
  }
  f = message.getAddressmailing();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter
    );
  }
};


/**
 * optional Organization organization = 1;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Organization}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.getOrganization = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Organization} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Organization, 1));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Organization|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.setOrganization = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.clearOrganization = function() {
  return this.setOrganization(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.hasOrganization = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional Address addressLegal = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.getAddresslegal = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Address} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Address, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Address|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.setAddresslegal = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.clearAddresslegal = function() {
  return this.setAddresslegal(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.hasAddresslegal = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional Address addressActual = 3;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.getAddressactual = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Address} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Address, 3));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Address|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.setAddressactual = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.clearAddressactual = function() {
  return this.setAddressactual(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.hasAddressactual = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional Address addressMailing = 4;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.getAddressmailing = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Address} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Address, 4));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Address|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.setAddressmailing = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.clearAddressmailing = function() {
  return this.setAddressmailing(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses.prototype.hasAddressmailing = function() {
  return jspb.Message.getField(this, 4) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.Organization.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Organization} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    parent: (f = msg.getParent()) && proto.ru.sbertroika.pasiv.gate.v1.Organization.toObject(includeInstance, f),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    shortname: jspb.Message.getFieldWithDefault(msg, 4, ""),
    kpp: jspb.Message.getFieldWithDefault(msg, 5, ""),
    inn: jspb.Message.getFieldWithDefault(msg, 6, ""),
    note: jspb.Message.getFieldWithDefault(msg, 7, ""),
    okpo: jspb.Message.getFieldWithDefault(msg, 8, ""),
    oktmo: jspb.Message.getFieldWithDefault(msg, 9, ""),
    okved: jspb.Message.getFieldWithDefault(msg, 10, ""),
    fiodirector: jspb.Message.getFieldWithDefault(msg, 11, ""),
    addresslegal: jspb.Message.getFieldWithDefault(msg, 12, ""),
    addressactual: jspb.Message.getFieldWithDefault(msg, 13, ""),
    addressmailing: jspb.Message.getFieldWithDefault(msg, 14, ""),
    manageractionreason: jspb.Message.getFieldWithDefault(msg, 15, ""),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 16, false),
    ogrn: jspb.Message.getFieldWithDefault(msg, 17, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.Organization;
  return proto.ru.sbertroika.pasiv.gate.v1.Organization.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Organization} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Organization;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Organization.deserializeBinaryFromReader);
      msg.setParent(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setShortname(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setKpp(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setInn(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setNote(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setOkpo(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setOktmo(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setOkved(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setFiodirector(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setAddresslegal(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setAddressactual(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setAddressmailing(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setManageractionreason(value);
      break;
    case 16:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    case 17:
      var value = /** @type {string} */ (reader.readString());
      msg.setOgrn(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.Organization.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Organization} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getParent();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Organization.serializeBinaryToWriter
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getShortname();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getKpp();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getInn();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeString(
      8,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeString(
      9,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 10));
  if (f != null) {
    writer.writeString(
      10,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 11));
  if (f != null) {
    writer.writeString(
      11,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 12));
  if (f != null) {
    writer.writeString(
      12,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 13));
  if (f != null) {
    writer.writeString(
      13,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 14));
  if (f != null) {
    writer.writeString(
      14,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 15));
  if (f != null) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getIsdeleted();
  if (f) {
    writer.writeBool(
      16,
      f
    );
  }
  f = message.getOgrn();
  if (f.length > 0) {
    writer.writeString(
      17,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional Organization parent = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Organization}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getParent = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Organization} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Organization, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Organization|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setParent = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearParent = function() {
  return this.setParent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasParent = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string name = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string shortName = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getShortname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setShortname = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string kpp = 5;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getKpp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setKpp = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string inn = 6;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getInn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setInn = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string note = 7;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getNote = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setNote = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearNote = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasNote = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional string okpo = 8;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getOkpo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setOkpo = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearOkpo = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasOkpo = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string oktmo = 9;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getOktmo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setOktmo = function(value) {
  return jspb.Message.setField(this, 9, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearOktmo = function() {
  return jspb.Message.setField(this, 9, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasOktmo = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional string okved = 10;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getOkved = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setOkved = function(value) {
  return jspb.Message.setField(this, 10, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearOkved = function() {
  return jspb.Message.setField(this, 10, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasOkved = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional string fioDirector = 11;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getFiodirector = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setFiodirector = function(value) {
  return jspb.Message.setField(this, 11, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearFiodirector = function() {
  return jspb.Message.setField(this, 11, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasFiodirector = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional string addressLegal = 12;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getAddresslegal = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setAddresslegal = function(value) {
  return jspb.Message.setField(this, 12, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearAddresslegal = function() {
  return jspb.Message.setField(this, 12, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasAddresslegal = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional string addressActual = 13;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getAddressactual = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setAddressactual = function(value) {
  return jspb.Message.setField(this, 13, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearAddressactual = function() {
  return jspb.Message.setField(this, 13, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasAddressactual = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional string addressMailing = 14;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getAddressmailing = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setAddressmailing = function(value) {
  return jspb.Message.setField(this, 14, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearAddressmailing = function() {
  return jspb.Message.setField(this, 14, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasAddressmailing = function() {
  return jspb.Message.getField(this, 14) != null;
};


/**
 * optional string managerActionReason = 15;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getManageractionreason = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setManageractionreason = function(value) {
  return jspb.Message.setField(this, 15, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.clearManageractionreason = function() {
  return jspb.Message.setField(this, 15, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.hasManageractionreason = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional bool isDeleted = 16;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 16, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setIsdeleted = function(value) {
  return jspb.Message.setProto3BooleanField(this, 16, value);
};


/**
 * optional string ogrn = 17;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.getOgrn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 17, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Organization.prototype.setOgrn = function(value) {
  return jspb.Message.setProto3StringField(this, 17, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.toObject = function(includeInstance, msg) {
  var f, obj = {
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    inn: jspb.Message.getFieldWithDefault(msg, 3, ""),
    kpp: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setInn(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setKpp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {boolean} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeBool(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional bool isDeleted = 1;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.setIsdeleted = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.clearIsdeleted = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.hasIsdeleted = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.setName = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.clearName = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.hasName = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string inn = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.getInn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.setInn = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.clearInn = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.hasInn = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string kpp = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.getKpp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.setKpp = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.clearKpp = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.prototype.hasKpp = function() {
  return jspb.Message.getField(this, 4) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f),
    filter: (f = msg.getFilter()) && proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.deserializeBinaryFromReader);
      msg.setFilter(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
  f = message.getFilter();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional OrganizationFilter filter = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.getFilter = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.setFilter = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.clearFilter = function() {
  return this.setFilter(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest.prototype.hasFilter = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationResponse.toObject(includeInstance, f),
    filter: (f = msg.getFilter()) && proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.toObject(includeInstance, f),
    organizationList: jspb.Message.toObjectList(msg.getOrganizationList(),
    proto.ru.sbertroika.pasiv.gate.v1.Organization.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationResponse;
      reader.readMessage(value,common_pb.PaginationResponse.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.deserializeBinaryFromReader);
      msg.setFilter(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Organization;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Organization.deserializeBinaryFromReader);
      msg.addOrganization(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationResponse.serializeBinaryToWriter
    );
  }
  f = message.getFilter();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter.serializeBinaryToWriter
    );
  }
  f = message.getOrganizationList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Organization.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationResponse} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationResponse, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationResponse|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional OrganizationFilter filter = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.getFilter = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.setFilter = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.clearFilter = function() {
  return this.setFilter(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.hasFilter = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * repeated Organization organization = 3;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Organization>}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.getOrganizationList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.Organization>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Organization, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Organization>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.setOrganizationList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Organization=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Organization}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.addOrganization = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.pasiv.gate.v1.Organization, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.prototype.clearOrganizationList = function() {
  return this.setOrganizationList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional OrganizationResult result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationResult|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f),
    projectid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setProjectid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
  f = message.getProjectid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string projectId = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.getProjectid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest.prototype.setProjectid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    organizationid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    projectid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setProjectid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrganizationid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getProjectid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string organizationId = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.prototype.getOrganizationid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.prototype.setOrganizationid = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string projectId = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.prototype.getProjectid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest.prototype.setProjectid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.Address.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Address} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    index: jspb.Message.getFieldWithDefault(msg, 3, 0),
    country: jspb.Message.getFieldWithDefault(msg, 4, ""),
    region: jspb.Message.getFieldWithDefault(msg, 5, ""),
    district: jspb.Message.getFieldWithDefault(msg, 6, ""),
    city: jspb.Message.getFieldWithDefault(msg, 7, ""),
    street: jspb.Message.getFieldWithDefault(msg, 8, ""),
    house: jspb.Message.getFieldWithDefault(msg, 9, ""),
    buildingorhousing: jspb.Message.getFieldWithDefault(msg, 10, ""),
    officeorroom: jspb.Message.getFieldWithDefault(msg, 11, ""),
    longitude: jspb.Message.getFloatingPointFieldWithDefault(msg, 12, 0.0),
    latitude: jspb.Message.getFloatingPointFieldWithDefault(msg, 13, 0.0),
    comment: jspb.Message.getFieldWithDefault(msg, 14, ""),
    oktmo: jspb.Message.getFieldWithDefault(msg, 15, 0),
    fiac: jspb.Message.getFieldWithDefault(msg, 16, ""),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 17, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.Address;
  return proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Address} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIndex(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCountry(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setRegion(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDistrict(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setCity(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setStreet(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setHouse(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setBuildingorhousing(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setOfficeorroom(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLongitude(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLatitude(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setComment(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setOktmo(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setFiac(value);
      break;
    case 17:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Address} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getRegion();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getCity();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getHouse();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 10));
  if (f != null) {
    writer.writeString(
      10,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 11));
  if (f != null) {
    writer.writeString(
      11,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 12));
  if (f != null) {
    writer.writeDouble(
      12,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 13));
  if (f != null) {
    writer.writeDouble(
      13,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 14));
  if (f != null) {
    writer.writeString(
      14,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 15));
  if (f != null) {
    writer.writeUint64(
      15,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 16));
  if (f != null) {
    writer.writeString(
      16,
      f
    );
  }
  f = message.getIsdeleted();
  if (f) {
    writer.writeBool(
      17,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional uint32 index = 3;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getIndex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setIndex = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearIndex = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasIndex = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string country = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getCountry = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setCountry = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearCountry = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasCountry = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string region = 5;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getRegion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setRegion = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string district = 6;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getDistrict = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setDistrict = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearDistrict = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasDistrict = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string city = 7;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getCity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setCity = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string street = 8;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getStreet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setStreet = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearStreet = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasStreet = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string house = 9;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getHouse = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setHouse = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string buildingOrHousing = 10;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getBuildingorhousing = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setBuildingorhousing = function(value) {
  return jspb.Message.setField(this, 10, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearBuildingorhousing = function() {
  return jspb.Message.setField(this, 10, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasBuildingorhousing = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional string officeOrRoom = 11;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getOfficeorroom = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setOfficeorroom = function(value) {
  return jspb.Message.setField(this, 11, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearOfficeorroom = function() {
  return jspb.Message.setField(this, 11, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasOfficeorroom = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional double longitude = 12;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getLongitude = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 12, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setLongitude = function(value) {
  return jspb.Message.setField(this, 12, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearLongitude = function() {
  return jspb.Message.setField(this, 12, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasLongitude = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional double latitude = 13;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getLatitude = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 13, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setLatitude = function(value) {
  return jspb.Message.setField(this, 13, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearLatitude = function() {
  return jspb.Message.setField(this, 13, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasLatitude = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional string comment = 14;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getComment = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setComment = function(value) {
  return jspb.Message.setField(this, 14, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearComment = function() {
  return jspb.Message.setField(this, 14, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasComment = function() {
  return jspb.Message.getField(this, 14) != null;
};


/**
 * optional uint64 oktmo = 15;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getOktmo = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 15, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setOktmo = function(value) {
  return jspb.Message.setField(this, 15, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearOktmo = function() {
  return jspb.Message.setField(this, 15, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasOktmo = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional string fiac = 16;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getFiac = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setFiac = function(value) {
  return jspb.Message.setField(this, 16, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.clearFiac = function() {
  return jspb.Message.setField(this, 16, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.hasFiac = function() {
  return jspb.Message.getField(this, 16) != null;
};


/**
 * optional bool isDeleted = 17;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 17, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Address.prototype.setIsdeleted = function(value) {
  return jspb.Message.setProto3BooleanField(this, 17, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.Contact.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    organizationid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    type: jspb.Message.getFieldWithDefault(msg, 3, 0),
    value: jspb.Message.getFieldWithDefault(msg, 4, ""),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 5, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.Contact;
  return proto.ru.sbertroika.pasiv.gate.v1.Contact.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationid(value);
      break;
    case 3:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContactType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setValue(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.Contact.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getOrganizationid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getValue();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getIsdeleted();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string organizationId = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.getOrganizationid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.setOrganizationid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional ContactType type = 3;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactType}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContactType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional string value = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.getValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.setValue = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional bool isDeleted = 5;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contact.prototype.setIsdeleted = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.toObject = function(includeInstance, msg) {
  var f, obj = {
    organizationid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    city: jspb.Message.getFieldWithDefault(msg, 2, ""),
    street: jspb.Message.getFieldWithDefault(msg, 3, ""),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 4, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.AddressFilter;
  return proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCity(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setStreet(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeBool(
      4,
      f
    );
  }
};


/**
 * optional string organizationId = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.getOrganizationid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.setOrganizationid = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.clearOrganizationid = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.hasOrganizationid = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string city = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.getCity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.setCity = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.clearCity = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.hasCity = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string street = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.getStreet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.setStreet = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.clearStreet = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.hasStreet = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional bool isDeleted = 4;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.setIsdeleted = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.clearIsdeleted = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.prototype.hasIsdeleted = function() {
  return jspb.Message.getField(this, 4) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f),
    filters: (f = msg.getFilters()) && proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.AddressFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.deserializeBinaryFromReader);
      msg.setFilters(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
  f = message.getFilters();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional AddressFilter filters = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.AddressFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.getFilters = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.AddressFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.AddressFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.setFilters = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.clearFilters = function() {
  return this.setFilters(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest.prototype.hasFilters = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationResponse.toObject(includeInstance, f),
    filters: (f = msg.getFilters()) && proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.toObject(includeInstance, f),
    addressList: jspb.Message.toObjectList(msg.getAddressList(),
    proto.ru.sbertroika.pasiv.gate.v1.Address.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.AddressListResult;
  return proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationResponse;
      reader.readMessage(value,common_pb.PaginationResponse.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.AddressFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.deserializeBinaryFromReader);
      msg.setFilters(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Address;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader);
      msg.addAddress(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationResponse.serializeBinaryToWriter
    );
  }
  f = message.getFilters();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.AddressFilter.serializeBinaryToWriter
    );
  }
  f = message.getAddressList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationResponse} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationResponse, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationResponse|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional AddressFilter filters = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.AddressFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.getFilters = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.AddressFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.AddressFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.AddressFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.setFilters = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.clearFilters = function() {
  return this.setFilters(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.hasFilters = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * repeated Address address = 3;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Address>}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.getAddressList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.Address>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Address, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Address>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.setAddressList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Address=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.addAddress = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.pasiv.gate.v1.Address, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.prototype.clearAddressList = function() {
  return this.setAddressList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.AddressListResult;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.AddressListResult.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional AddressListResult result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.AddressListResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.AddressListResult} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.AddressListResult, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.AddressListResult|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.toObject = function(includeInstance, msg) {
  var f, obj = {
    organizationid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContactFilter;
  return proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationid(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional string organizationId = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.getOrganizationid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.setOrganizationid = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.clearOrganizationid = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.hasOrganizationid = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional bool isDeleted = 2;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.setIsdeleted = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.clearIsdeleted = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.prototype.hasIsdeleted = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f),
    filters: (f = msg.getFilters()) && proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContactFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.deserializeBinaryFromReader);
      msg.setFilters(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
  f = message.getFilters();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ContactFilter filters = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.ContactFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.getFilters = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContactFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.ContactFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.setFilters = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.clearFilters = function() {
  return this.setFilters(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest.prototype.hasFilters = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationResponse.toObject(includeInstance, f),
    filters: (f = msg.getFilters()) && proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.toObject(includeInstance, f),
    contactsList: jspb.Message.toObjectList(msg.getContactsList(),
    proto.ru.sbertroika.pasiv.gate.v1.Contact.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContactListResult;
  return proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationResponse;
      reader.readMessage(value,common_pb.PaginationResponse.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContactFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.deserializeBinaryFromReader);
      msg.setFilters(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Contact;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Contact.deserializeBinaryFromReader);
      msg.addContacts(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationResponse.serializeBinaryToWriter
    );
  }
  f = message.getFilters();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContactFilter.serializeBinaryToWriter
    );
  }
  f = message.getContactsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Contact.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationResponse} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationResponse, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationResponse|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ContactFilter filters = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.ContactFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.getFilters = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.ContactFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContactFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.ContactFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.setFilters = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.clearFilters = function() {
  return this.setFilters(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.hasFilters = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * repeated Contact contacts = 3;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Contact>}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.getContactsList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.Contact>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Contact, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Contact>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.setContactsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contact}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.addContacts = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.pasiv.gate.v1.Contact, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.prototype.clearContactsList = function() {
  return this.setContactsList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContactListResult;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContactListResult.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ContactListResult result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.ContactListResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.ContactListResult} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContactListResult, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.ContactListResult|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    version: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeInt64(
      2,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int64 version = 2;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.setVersion = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.clearVersion = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest.prototype.hasVersion = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.toObject = function(includeInstance, msg) {
  var f, obj = {
    address: (f = msg.getAddress()) && proto.ru.sbertroika.pasiv.gate.v1.Address.toObject(includeInstance, f),
    type: jspb.Message.getFieldWithDefault(msg, 2, 0),
    organizationid: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete;
  return proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Address;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader);
      msg.setAddress(value);
      break;
    case 2:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.AddressType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAddress();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getOrganizationid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional Address address = 1;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.getAddress = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Address} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Address, 1));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Address|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.setAddress = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.clearAddress = function() {
  return this.setAddress(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.hasAddress = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional AddressType type = 2;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressType}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.AddressType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional string organizationId = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.getOrganizationid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete.prototype.setOrganizationid = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.Address.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.AddressResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Address;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Address.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Address.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional Address result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Address}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Address} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Address, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Address|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.Contact.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContactResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Contact;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Contact.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Contact.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional Contact result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Contact}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Contact} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Contact, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Contact|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 2;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    inn: jspb.Message.getFieldWithDefault(msg, 1, ""),
    kpp: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setInn(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setKpp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getInn();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string inn = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.getInn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.setInn = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string kpp = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.getKpp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.setKpp = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.clearKpp = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest.prototype.hasKpp = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional OrganizationHintList result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.toObject = function(includeInstance, msg) {
  var f, obj = {
    organizationhintList: jspb.Message.toObjectList(msg.getOrganizationhintList(),
    proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.deserializeBinaryFromReader);
      msg.addOrganizationhint(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrganizationhintList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.serializeBinaryToWriter
    );
  }
};


/**
 * repeated OrganizationHint organizationHint = 1;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint>}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.prototype.getOrganizationhintList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.prototype.setOrganizationhintList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.prototype.addOrganizationhint = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintList.prototype.clearOrganizationhintList = function() {
  return this.setOrganizationhintList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.repeatedFields_ = [13];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    shortname: jspb.Message.getFieldWithDefault(msg, 2, ""),
    kpp: jspb.Message.getFieldWithDefault(msg, 3, ""),
    inn: jspb.Message.getFieldWithDefault(msg, 4, ""),
    note: jspb.Message.getFieldWithDefault(msg, 5, ""),
    okpo: jspb.Message.getFieldWithDefault(msg, 6, ""),
    oktmo: jspb.Message.getFieldWithDefault(msg, 7, ""),
    okved: jspb.Message.getFieldWithDefault(msg, 8, ""),
    fiodirector: jspb.Message.getFieldWithDefault(msg, 9, ""),
    manageractionreason: jspb.Message.getFieldWithDefault(msg, 10, ""),
    ogrn: jspb.Message.getFieldWithDefault(msg, 11, ""),
    addresslegalhint: (f = msg.getAddresslegalhint()) && proto.ru.sbertroika.pasiv.gate.v1.AddressHint.toObject(includeInstance, f),
    contacthintsList: jspb.Message.toObjectList(msg.getContacthintsList(),
    proto.ru.sbertroika.pasiv.gate.v1.ContactHint.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint;
  return proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setShortname(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setKpp(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setInn(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setNote(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setOkpo(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setOktmo(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setOkved(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setFiodirector(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setManageractionreason(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setOgrn(value);
      break;
    case 12:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.AddressHint;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.AddressHint.deserializeBinaryFromReader);
      msg.setAddresslegalhint(value);
      break;
    case 13:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContactHint;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContactHint.deserializeBinaryFromReader);
      msg.addContacthints(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getShortname();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getKpp();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getInn();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeString(
      8,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeString(
      9,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 10));
  if (f != null) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getOgrn();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getAddresslegalhint();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.AddressHint.serializeBinaryToWriter
    );
  }
  f = message.getContacthintsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      13,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContactHint.serializeBinaryToWriter
    );
  }
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string shortName = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getShortname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setShortname = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string kpp = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getKpp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setKpp = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string inn = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getInn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setInn = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string note = 5;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getNote = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setNote = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearNote = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.hasNote = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional string okpo = 6;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getOkpo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setOkpo = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearOkpo = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.hasOkpo = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string oktmo = 7;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getOktmo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setOktmo = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearOktmo = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.hasOktmo = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional string okved = 8;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getOkved = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setOkved = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearOkved = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.hasOkved = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string fioDirector = 9;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getFiodirector = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setFiodirector = function(value) {
  return jspb.Message.setField(this, 9, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearFiodirector = function() {
  return jspb.Message.setField(this, 9, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.hasFiodirector = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional string managerActionReason = 10;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getManageractionreason = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setManageractionreason = function(value) {
  return jspb.Message.setField(this, 10, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearManageractionreason = function() {
  return jspb.Message.setField(this, 10, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.hasManageractionreason = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional string ogrn = 11;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getOgrn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setOgrn = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional AddressHint addressLegalHint = 12;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.AddressHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getAddresslegalhint = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.AddressHint} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.AddressHint, 12));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.AddressHint|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setAddresslegalhint = function(value) {
  return jspb.Message.setWrapperField(this, 12, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearAddresslegalhint = function() {
  return this.setAddresslegalhint(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.hasAddresslegalhint = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * repeated ContactHint contactHints = 13;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContactHint>}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.getContacthintsList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContactHint>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContactHint, 13));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContactHint>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.setContacthintsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 13, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.addContacthints = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 13, opt_value, proto.ru.sbertroika.pasiv.gate.v1.ContactHint, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationHint.prototype.clearContacthintsList = function() {
  return this.setContacthintsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContactHint.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.toObject = function(includeInstance, msg) {
  var f, obj = {
    type: jspb.Message.getFieldWithDefault(msg, 3, 0),
    value: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContactHint;
  return proto.ru.sbertroika.pasiv.gate.v1.ContactHint.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 3:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContactType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContactHint.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getValue();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional ContactType type = 3;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactType}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContactType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional string value = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.prototype.getValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContactHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactHint.prototype.setValue = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.AddressHint.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.toObject = function(includeInstance, msg) {
  var f, obj = {
    index: jspb.Message.getFieldWithDefault(msg, 1, 0),
    country: jspb.Message.getFieldWithDefault(msg, 2, ""),
    region: jspb.Message.getFieldWithDefault(msg, 3, ""),
    district: jspb.Message.getFieldWithDefault(msg, 4, ""),
    city: jspb.Message.getFieldWithDefault(msg, 5, ""),
    street: jspb.Message.getFieldWithDefault(msg, 6, ""),
    house: jspb.Message.getFieldWithDefault(msg, 7, ""),
    buildingorhousing: jspb.Message.getFieldWithDefault(msg, 8, ""),
    officeorroom: jspb.Message.getFieldWithDefault(msg, 9, ""),
    longitude: jspb.Message.getFloatingPointFieldWithDefault(msg, 10, 0.0),
    latitude: jspb.Message.getFloatingPointFieldWithDefault(msg, 11, 0.0),
    oktmo: jspb.Message.getFieldWithDefault(msg, 12, 0),
    fiac: jspb.Message.getFieldWithDefault(msg, 14, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.AddressHint;
  return proto.ru.sbertroika.pasiv.gate.v1.AddressHint.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIndex(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCountry(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setRegion(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDistrict(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setCity(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setStreet(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setHouse(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setBuildingorhousing(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setOfficeorroom(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLongitude(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLatitude(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setOktmo(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setFiac(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.AddressHint.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {number} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getRegion();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getCity();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getHouse();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeString(
      8,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeString(
      9,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 10));
  if (f != null) {
    writer.writeDouble(
      10,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 11));
  if (f != null) {
    writer.writeDouble(
      11,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 12));
  if (f != null) {
    writer.writeUint64(
      12,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 14));
  if (f != null) {
    writer.writeString(
      14,
      f
    );
  }
};


/**
 * optional uint32 index = 1;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getIndex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setIndex = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearIndex = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasIndex = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string country = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getCountry = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setCountry = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearCountry = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasCountry = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string region = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getRegion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setRegion = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string district = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getDistrict = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setDistrict = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearDistrict = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasDistrict = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string city = 5;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getCity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setCity = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string street = 6;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getStreet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setStreet = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearStreet = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasStreet = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string house = 7;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getHouse = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setHouse = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string buildingOrHousing = 8;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getBuildingorhousing = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setBuildingorhousing = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearBuildingorhousing = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasBuildingorhousing = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string officeOrRoom = 9;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getOfficeorroom = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setOfficeorroom = function(value) {
  return jspb.Message.setField(this, 9, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearOfficeorroom = function() {
  return jspb.Message.setField(this, 9, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasOfficeorroom = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional double longitude = 10;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getLongitude = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 10, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setLongitude = function(value) {
  return jspb.Message.setField(this, 10, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearLongitude = function() {
  return jspb.Message.setField(this, 10, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasLongitude = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional double latitude = 11;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getLatitude = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 11, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setLatitude = function(value) {
  return jspb.Message.setField(this, 11, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearLatitude = function() {
  return jspb.Message.setField(this, 11, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasLatitude = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional uint64 oktmo = 12;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getOktmo = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setOktmo = function(value) {
  return jspb.Message.setField(this, 12, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearOktmo = function() {
  return jspb.Message.setField(this, 12, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasOktmo = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional string fiac = 14;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.getFiac = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.setFiac = function(value) {
  return jspb.Message.setField(this, 14, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.AddressHint} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.clearFiac = function() {
  return jspb.Message.setField(this, 14, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressHint.prototype.hasFiac = function() {
  return jspb.Message.getField(this, 14) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.Contract.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contract} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    projectcode: jspb.Message.getFieldWithDefault(msg, 2, ""),
    projectname: jspb.Message.getFieldWithDefault(msg, 3, ""),
    projecttype: jspb.Message.getFieldWithDefault(msg, 4, 0),
    contracttype: jspb.Message.getFieldWithDefault(msg, 5, 0),
    contractname: jspb.Message.getFieldWithDefault(msg, 6, ""),
    contractnumber: jspb.Message.getFieldWithDefault(msg, 7, ""),
    signaturedate: (f = msg.getSignaturedate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    conclusiondate: (f = msg.getConclusiondate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    completiondate: (f = msg.getCompletiondate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    status: jspb.Message.getFieldWithDefault(msg, 11, 0),
    externalid1c: jspb.Message.getFieldWithDefault(msg, 12, ""),
    description: jspb.Message.getFieldWithDefault(msg, 13, ""),
    totalamount: jspb.Message.getFloatingPointFieldWithDefault(msg, 14, 0.0),
    currency: jspb.Message.getFieldWithDefault(msg, 15, ""),
    paymentterms: jspb.Message.getFieldWithDefault(msg, 16, 0),
    vatrate: jspb.Message.getFloatingPointFieldWithDefault(msg, 17, 0.0),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 18, false),
    createddate: (f = msg.getCreateddate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    lastsyncdate: (f = msg.getLastsyncdate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.Contract;
  return proto.ru.sbertroika.pasiv.gate.v1.Contract.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contract} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setProjectcode(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setProjectname(value);
      break;
    case 4:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType} */ (reader.readEnum());
      msg.setProjecttype(value);
      break;
    case 5:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractType} */ (reader.readEnum());
      msg.setContracttype(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractname(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractnumber(value);
      break;
    case 8:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setSignaturedate(value);
      break;
    case 9:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setConclusiondate(value);
      break;
    case 10:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCompletiondate(value);
      break;
    case 11:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setExternalid1c(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setTotalamount(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setCurrency(value);
      break;
    case 16:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPaymentterms(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setVatrate(value);
      break;
    case 18:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    case 19:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreateddate(value);
      break;
    case 20:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setLastsyncdate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.Contract.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contract} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getProjectcode();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getProjectname();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getProjecttype();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getContracttype();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getContractname();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getContractnumber();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getSignaturedate();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getConclusiondate();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getCompletiondate();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      11,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 12));
  if (f != null) {
    writer.writeString(
      12,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 13));
  if (f != null) {
    writer.writeString(
      13,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 14));
  if (f != null) {
    writer.writeDouble(
      14,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 15));
  if (f != null) {
    writer.writeString(
      15,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 16));
  if (f != null) {
    writer.writeInt32(
      16,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 17));
  if (f != null) {
    writer.writeDouble(
      17,
      f
    );
  }
  f = message.getIsdeleted();
  if (f) {
    writer.writeBool(
      18,
      f
    );
  }
  f = message.getCreateddate();
  if (f != null) {
    writer.writeMessage(
      19,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getLastsyncdate();
  if (f != null) {
    writer.writeMessage(
      20,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string projectCode = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getProjectcode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setProjectcode = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string projectName = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getProjectname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setProjectname = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional ProjectType projectType = 4;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getProjecttype = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setProjecttype = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional ContractType contractType = 5;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractType}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getContracttype = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractType} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setContracttype = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional string contractName = 6;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getContractname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setContractname = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string contractNumber = 7;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getContractnumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setContractnumber = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional google.protobuf.Timestamp signatureDate = 8;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getSignaturedate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 8));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setSignaturedate = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearSignaturedate = function() {
  return this.setSignaturedate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasSignaturedate = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional google.protobuf.Timestamp conclusionDate = 9;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getConclusiondate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 9));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setConclusiondate = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearConclusiondate = function() {
  return this.setConclusiondate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasConclusiondate = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional google.protobuf.Timestamp completionDate = 10;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getCompletiondate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 10));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setCompletiondate = function(value) {
  return jspb.Message.setWrapperField(this, 10, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearCompletiondate = function() {
  return this.setCompletiondate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasCompletiondate = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional ContractStatus status = 11;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getStatus = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 11, value);
};


/**
 * optional string externalId1C = 12;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getExternalid1c = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setExternalid1c = function(value) {
  return jspb.Message.setField(this, 12, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearExternalid1c = function() {
  return jspb.Message.setField(this, 12, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasExternalid1c = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional string description = 13;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setDescription = function(value) {
  return jspb.Message.setField(this, 13, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearDescription = function() {
  return jspb.Message.setField(this, 13, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasDescription = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional double totalAmount = 14;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getTotalamount = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 14, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setTotalamount = function(value) {
  return jspb.Message.setField(this, 14, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearTotalamount = function() {
  return jspb.Message.setField(this, 14, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasTotalamount = function() {
  return jspb.Message.getField(this, 14) != null;
};


/**
 * optional string currency = 15;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getCurrency = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setCurrency = function(value) {
  return jspb.Message.setField(this, 15, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearCurrency = function() {
  return jspb.Message.setField(this, 15, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasCurrency = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional int32 paymentTerms = 16;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getPaymentterms = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 16, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setPaymentterms = function(value) {
  return jspb.Message.setField(this, 16, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearPaymentterms = function() {
  return jspb.Message.setField(this, 16, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasPaymentterms = function() {
  return jspb.Message.getField(this, 16) != null;
};


/**
 * optional double vatRate = 17;
 * @return {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getVatrate = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 17, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setVatrate = function(value) {
  return jspb.Message.setField(this, 17, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearVatrate = function() {
  return jspb.Message.setField(this, 17, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasVatrate = function() {
  return jspb.Message.getField(this, 17) != null;
};


/**
 * optional bool isDeleted = 18;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 18, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setIsdeleted = function(value) {
  return jspb.Message.setProto3BooleanField(this, 18, value);
};


/**
 * optional google.protobuf.Timestamp createdDate = 19;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getCreateddate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 19));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setCreateddate = function(value) {
  return jspb.Message.setWrapperField(this, 19, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearCreateddate = function() {
  return this.setCreateddate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasCreateddate = function() {
  return jspb.Message.getField(this, 19) != null;
};


/**
 * optional google.protobuf.Timestamp lastSyncDate = 20;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.getLastsyncdate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 20));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.setLastsyncdate = function(value) {
  return jspb.Message.setWrapperField(this, 20, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.clearLastsyncdate = function() {
  return this.setLastsyncdate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.Contract.prototype.hasLastsyncdate = function() {
  return jspb.Message.getField(this, 20) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    contractid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    organizationid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    organizationname: jspb.Message.getFieldWithDefault(msg, 4, ""),
    role: jspb.Message.getFieldWithDefault(msg, 5, 0),
    roledescription: jspb.Message.getFieldWithDefault(msg, 6, ""),
    activefrom: (f = msg.getActivefrom()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    activetill: (f = msg.getActivetill()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 9, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationname(value);
      break;
    case 5:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationRole} */ (reader.readEnum());
      msg.setRole(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setRoledescription(value);
      break;
    case 7:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setActivefrom(value);
      break;
    case 8:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setActivetill(value);
      break;
    case 9:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getContractid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrganizationid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getOrganizationname();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getRole();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getActivefrom();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getActivetill();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getIsdeleted();
  if (f) {
    writer.writeBool(
      9,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string contractId = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getContractid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setContractid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string organizationId = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getOrganizationid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setOrganizationid = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string organizationName = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getOrganizationname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setOrganizationname = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional OrganizationRole role = 5;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationRole}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getRole = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationRole} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationRole} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setRole = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional string roleDescription = 6;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getRoledescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setRoledescription = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.clearRoledescription = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.hasRoledescription = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional google.protobuf.Timestamp activeFrom = 7;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getActivefrom = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 7));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setActivefrom = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.clearActivefrom = function() {
  return this.setActivefrom(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.hasActivefrom = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional google.protobuf.Timestamp activeTill = 8;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getActivetill = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 8));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setActivetill = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.clearActivetill = function() {
  return this.setActivetill(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.hasActivetill = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional bool isDeleted = 9;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 9, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.prototype.setIsdeleted = function(value) {
  return jspb.Message.setProto3BooleanField(this, 9, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.toObject = function(includeInstance, msg) {
  var f, obj = {
    contract: (f = msg.getContract()) && proto.ru.sbertroika.pasiv.gate.v1.Contract.toObject(includeInstance, f),
    organizationsList: jspb.Message.toObjectList(msg.getOrganizationsList(),
    proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Contract;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Contract.deserializeBinaryFromReader);
      msg.setContract(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.deserializeBinaryFromReader);
      msg.addOrganizations(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getContract();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Contract.serializeBinaryToWriter
    );
  }
  f = message.getOrganizationsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization.serializeBinaryToWriter
    );
  }
};


/**
 * optional Contract contract = 1;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Contract}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.getContract = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Contract} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Contract, 1));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Contract|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.setContract = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.clearContract = function() {
  return this.setContract(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.hasContract = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * repeated ContractOrganization organizations = 2;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization>}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.getOrganizationsList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization, 2));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.setOrganizationsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.addOrganizations = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.ru.sbertroika.pasiv.gate.v1.ContractOrganization, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations.prototype.clearOrganizationsList = function() {
  return this.setOrganizationsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.toObject = function(includeInstance, msg) {
  var f, obj = {
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
    projectcode: jspb.Message.getFieldWithDefault(msg, 2, ""),
    contractnumber: jspb.Message.getFieldWithDefault(msg, 3, ""),
    contractname: jspb.Message.getFieldWithDefault(msg, 4, ""),
    status: jspb.Message.getFieldWithDefault(msg, 5, 0),
    contracttype: jspb.Message.getFieldWithDefault(msg, 6, 0),
    projecttype: jspb.Message.getFieldWithDefault(msg, 7, 0),
    organizationid: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractFilter;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setProjectcode(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractnumber(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractname(value);
      break;
    case 5:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 6:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractType} */ (reader.readEnum());
      msg.setContracttype(value);
      break;
    case 7:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType} */ (reader.readEnum());
      msg.setProjecttype(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {boolean} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeBool(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractType} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeEnum(
      6,
      f
    );
  }
  f = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeEnum(
      7,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional bool isDeleted = 1;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setIsdeleted = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearIsdeleted = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasIsdeleted = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string projectCode = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getProjectcode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setProjectcode = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearProjectcode = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasProjectcode = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string contractNumber = 3;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getContractnumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setContractnumber = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearContractnumber = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasContractnumber = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string contractName = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getContractname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setContractname = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearContractname = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasContractname = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional ContractStatus status = 5;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getStatus = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractStatus} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setStatus = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearStatus = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasStatus = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional ContractType contractType = 6;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractType}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getContracttype = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ContractType} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setContracttype = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearContracttype = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasContracttype = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional ProjectType projectType = 7;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getProjecttype = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ProjectType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setProjecttype = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearProjecttype = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasProjecttype = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional string organizationId = 8;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.getOrganizationid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.setOrganizationid = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.clearOrganizationid = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.prototype.hasOrganizationid = function() {
  return jspb.Message.getField(this, 8) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f),
    filter: (f = msg.getFilter()) && proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContractFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.deserializeBinaryFromReader);
      msg.setFilter(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
  f = message.getFilter();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ContractFilter filter = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.ContractFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.getFilter = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContractFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.ContractFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.setFilter = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.clearFilter = function() {
  return this.setFilter(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest.prototype.hasFilter = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationResponse.toObject(includeInstance, f),
    filter: (f = msg.getFilter()) && proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.toObject(includeInstance, f),
    contractsList: jspb.Message.toObjectList(msg.getContractsList(),
    proto.ru.sbertroika.pasiv.gate.v1.Contract.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractResult;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationResponse;
      reader.readMessage(value,common_pb.PaginationResponse.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContractFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.deserializeBinaryFromReader);
      msg.setFilter(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Contract;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Contract.deserializeBinaryFromReader);
      msg.addContracts(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationResponse.serializeBinaryToWriter
    );
  }
  f = message.getFilter();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContractFilter.serializeBinaryToWriter
    );
  }
  f = message.getContractsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Contract.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationResponse} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationResponse, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationResponse|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ContractFilter filter = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.ContractFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.getFilter = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.ContractFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContractFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.ContractFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.setFilter = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.clearFilter = function() {
  return this.setFilter(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.hasFilter = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * repeated Contract contracts = 3;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Contract>}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.getContractsList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.Contract>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Contract, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.Contract>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.setContractsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contract=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.Contract}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.addContracts = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.pasiv.gate.v1.Contract, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResult.prototype.clearContractsList = function() {
  return this.setContractsList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.ContractResult.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContractResult;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContractResult.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContractResult.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ContractResult result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.ContractResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.ContractResult} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContractResult, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.ContractResult|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.Contract.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.Contract;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.Contract.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.Contract.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional Contract result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.Contract}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.Contract} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.Contract, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.Contract|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    projectcode: jspb.Message.getFieldWithDefault(msg, 1, ""),
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setProjectcode(value);
      break;
    case 2:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getProjectcode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
};


/**
 * optional string projectCode = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.getProjectcode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.setProjectcode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 2;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    contractid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    methodtype: jspb.Message.getFieldWithDefault(msg, 3, 0),
    code: jspb.Message.getFieldWithDefault(msg, 4, ""),
    name: jspb.Message.getFieldWithDefault(msg, 5, ""),
    description: jspb.Message.getFieldWithDefault(msg, 6, ""),
    isactive: jspb.Message.getBooleanFieldWithDefault(msg, 7, false),
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 8, false),
    createddate: (f = msg.getCreateddate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    lastsyncdate: (f = msg.getLastsyncdate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    externalid: jspb.Message.getFieldWithDefault(msg, 11, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod;
  return proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractid(value);
      break;
    case 3:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType} */ (reader.readEnum());
      msg.setMethodtype(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCode(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsactive(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    case 9:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreateddate(value);
      break;
    case 10:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setLastsyncdate(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setExternalid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getContractid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getMethodtype();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getIsactive();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getIsdeleted();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
  f = message.getCreateddate();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getLastsyncdate();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 11));
  if (f != null) {
    writer.writeString(
      11,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string contractId = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getContractid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setContractid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional PaymentMethodType methodType = 3;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getMethodtype = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setMethodtype = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional string code = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setCode = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string name = 5;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string description = 6;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setDescription = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.clearDescription = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.hasDescription = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional bool isActive = 7;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getIsactive = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setIsactive = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * optional bool isDeleted = 8;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setIsdeleted = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};


/**
 * optional google.protobuf.Timestamp createdDate = 9;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getCreateddate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 9));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setCreateddate = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.clearCreateddate = function() {
  return this.setCreateddate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.hasCreateddate = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional google.protobuf.Timestamp lastSyncDate = 10;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getLastsyncdate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 10));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setLastsyncdate = function(value) {
  return jspb.Message.setWrapperField(this, 10, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.clearLastsyncdate = function() {
  return this.setLastsyncdate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.hasLastsyncdate = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional string externalId = 11;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.getExternalid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.setExternalid = function(value) {
  return jspb.Message.setField(this, 11, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.clearExternalid = function() {
  return jspb.Message.setField(this, 11, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.prototype.hasExternalid = function() {
  return jspb.Message.getField(this, 11) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.toObject = function(includeInstance, msg) {
  var f, obj = {
    isdeleted: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
    contractid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    methodtype: jspb.Message.getFieldWithDefault(msg, 3, 0),
    code: jspb.Message.getFieldWithDefault(msg, 4, ""),
    isactive: jspb.Message.getBooleanFieldWithDefault(msg, 5, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter;
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsdeleted(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractid(value);
      break;
    case 3:
      var value = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType} */ (reader.readEnum());
      msg.setMethodtype(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCode(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsactive(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {boolean} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeBool(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeBool(
      5,
      f
    );
  }
};


/**
 * optional bool isDeleted = 1;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.getIsdeleted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.setIsdeleted = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.clearIsdeleted = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.hasIsdeleted = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string contractId = 2;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.getContractid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.setContractid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.clearContractid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.hasContractid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional PaymentMethodType methodType = 3;
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.getMethodtype = function() {
  return /** @type {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.setMethodtype = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.clearMethodtype = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.hasMethodtype = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string code = 4;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.getCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.setCode = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.clearCode = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.hasCode = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional bool isActive = 5;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.getIsactive = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.setIsactive = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.clearIsactive = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.prototype.hasIsactive = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f),
    filter: (f = msg.getFilter()) && proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.deserializeBinaryFromReader);
      msg.setFilter(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
  f = message.getFilter();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional PaymentMethodFilter filter = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.getFilter = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.setFilter = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.clearFilter = function() {
  return this.setFilter(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest.prototype.hasFilter = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && common_pb.PaginationResponse.toObject(includeInstance, f),
    filter: (f = msg.getFilter()) && proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.toObject(includeInstance, f),
    paymentmethodsList: jspb.Message.toObjectList(msg.getPaymentmethodsList(),
    proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult;
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.PaginationResponse;
      reader.readMessage(value,common_pb.PaginationResponse.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.deserializeBinaryFromReader);
      msg.setFilter(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.deserializeBinaryFromReader);
      msg.addPaymentmethods(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.PaginationResponse.serializeBinaryToWriter
    );
  }
  f = message.getFilter();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter.serializeBinaryToWriter
    );
  }
  f = message.getPaymentmethodsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationResponse} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationResponse, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationResponse|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional PaymentMethodFilter filter = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.getFilter = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodFilter|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.setFilter = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.clearFilter = function() {
  return this.setFilter(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.hasFilter = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * repeated ContractPaymentMethod paymentMethods = 3;
 * @return {!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod>}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.getPaymentmethodsList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod>} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.setPaymentmethodsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.addPaymentmethods = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.prototype.clearPaymentmethodsList = function() {
  return this.setPaymentmethodsList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional PaymentMethodResult result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResult|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod;
      reader.readMessage(value,proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ContractPaymentMethod result = 2;
 * @return {?proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod, 2));
};


/**
 * @param {?proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    contractid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    pagination: (f = msg.getPagination()) && common_pb.PaginationRequest.toObject(includeInstance, f),
    includeinactive: jspb.Message.getBooleanFieldWithDefault(msg, 3, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest;
  return proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setContractid(value);
      break;
    case 2:
      var value = new common_pb.PaginationRequest;
      reader.readMessage(value,common_pb.PaginationRequest.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIncludeinactive(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getContractid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      common_pb.PaginationRequest.serializeBinaryToWriter
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeBool(
      3,
      f
    );
  }
};


/**
 * optional string contractId = 1;
 * @return {string}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.getContractid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.setContractid = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional ru.sbertroika.common.v1.PaginationRequest pagination = 2;
 * @return {?proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationRequest} */ (
    jspb.Message.getWrapperField(this, common_pb.PaginationRequest, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationRequest|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional bool includeInactive = 3;
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.getIncludeinactive = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.setIncludeinactive = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.clearIncludeinactive = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest.prototype.hasIncludeinactive = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContactType = {
  CT_PHONE: 0,
  CT_EMAIL: 1
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.AddressType = {
  AT_LEGAL: 0,
  AT_ACTUAL: 1,
  AT_MAILING: 2
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractStatus = {
  CS_DRAFT: 0,
  CS_ACTIVE: 1,
  CS_EXPIRING: 2,
  CS_COMPLETED: 3,
  CS_TERMINATED: 4
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ContractType = {
  CT_SYSTEM_RULES: 0,
  CT_SERVICE: 1,
  CT_TRANSPORT: 2,
  CT_PROCESSING: 3
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ProjectType = {
  PT_TRANSPORT_SYSTEM: 0,
  PT_METRO_SYSTEM: 1,
  PT_BUS_SYSTEM: 2,
  PT_TAXI_SYSTEM: 3
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.OrganizationRole = {
  OR_OPERATOR: 0,
  OR_CARRIER: 1,
  OR_PROCESSING_CENTER: 2,
  OR_CONTRACTOR: 3,
  OR_PARTNER: 4
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodType = {
  PMT_BANK_CARD: 0,
  PMT_CASH: 1,
  PMT_TROIKA_SINGLE: 2,
  PMT_TROIKA_SUBSCRIPTION: 3,
  PMT_MPC_DISCOUNT: 4,
  PMT_MPC_SOCIAL: 5,
  PMT_MPC_SCHOOL: 6,
  PMT_MPC_STUDENT_SINGLE: 7,
  PMT_MPC_STUDENT_SUBSCRIPTION: 8,
  PMT_TC_RESIDENT: 9,
  PMT_MOBILE_BC: 10,
  PMT_MOBILE_VIRTUAL_TC: 11,
  PMT_MOBILE_SBP: 12,
  PMT_REGIONAL_TC: 13,
  PMT_SOCIAL_TC: 14,
  PMT_OTHER_CARDS: 15
};

goog.object.extend(exports, proto.ru.sbertroika.pasiv.gate.v1);
