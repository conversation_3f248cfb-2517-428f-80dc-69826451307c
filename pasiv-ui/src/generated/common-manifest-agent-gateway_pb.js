// source: common-manifest-agent-gateway.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var common$manifest$core_pb = require('./common-manifest-core_pb.js');
goog.object.extend(proto, common$manifest$core_pb);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.displayName = 'proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate}
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate;
  return proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate}
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};


goog.object.extend(exports, proto.ru.sbertroika.common.manifest.v1.agent.gateway);
