import * as jspb from 'google-protobuf'

import * as google_protobuf_empty_pb from 'google-protobuf/google/protobuf/empty_pb'; // proto import: "google/protobuf/empty.proto"
import * as google_protobuf_timestamp_pb from 'google-protobuf/google/protobuf/timestamp_pb'; // proto import: "google/protobuf/timestamp.proto"


export class OperationError extends jspb.Message {
  getType(): ErrorType;
  setType(value: ErrorType): OperationError;

  getMessage(): string;
  setMessage(value: string): OperationError;

  getCode(): number;
  setCode(value: number): OperationError;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OperationError.AsObject;
  static toObject(includeInstance: boolean, msg: OperationError): OperationError.AsObject;
  static serializeBinaryToWriter(message: OperationError, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OperationError;
  static deserializeBinaryFromReader(message: OperationError, reader: jspb.BinaryReader): OperationError;
}

export namespace OperationError {
  export type AsObject = {
    type: ErrorType,
    message: string,
    code: number,
  }
}

export class PaginationRequest extends jspb.Message {
  getPage(): number;
  setPage(value: number): PaginationRequest;

  getLimit(): number;
  setLimit(value: number): PaginationRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaginationRequest.AsObject;
  static toObject(includeInstance: boolean, msg: PaginationRequest): PaginationRequest.AsObject;
  static serializeBinaryToWriter(message: PaginationRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaginationRequest;
  static deserializeBinaryFromReader(message: PaginationRequest, reader: jspb.BinaryReader): PaginationRequest;
}

export namespace PaginationRequest {
  export type AsObject = {
    page: number,
    limit: number,
  }
}

export class PaginationResponse extends jspb.Message {
  getPage(): number;
  setPage(value: number): PaginationResponse;

  getLimit(): number;
  setLimit(value: number): PaginationResponse;

  getTotalpage(): number;
  setTotalpage(value: number): PaginationResponse;

  getTotalcount(): number;
  setTotalcount(value: number): PaginationResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaginationResponse.AsObject;
  static toObject(includeInstance: boolean, msg: PaginationResponse): PaginationResponse.AsObject;
  static serializeBinaryToWriter(message: PaginationResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaginationResponse;
  static deserializeBinaryFromReader(message: PaginationResponse, reader: jspb.BinaryReader): PaginationResponse;
}

export namespace PaginationResponse {
  export type AsObject = {
    page: number,
    limit: number,
    totalpage: number,
    totalcount: number,
  }
}

export class PaymentMethod extends jspb.Message {
  getPaymentid(): string;
  setPaymentid(value: string): PaymentMethod;

  getType(): PaymentType;
  setType(value: PaymentType): PaymentMethod;

  getName(): string;
  setName(value: string): PaymentMethod;

  getLabel(): string;
  setLabel(value: string): PaymentMethod;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaymentMethod.AsObject;
  static toObject(includeInstance: boolean, msg: PaymentMethod): PaymentMethod.AsObject;
  static serializeBinaryToWriter(message: PaymentMethod, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaymentMethod;
  static deserializeBinaryFromReader(message: PaymentMethod, reader: jspb.BinaryReader): PaymentMethod;
}

export namespace PaymentMethod {
  export type AsObject = {
    paymentid: string,
    type: PaymentType,
    name: string,
    label: string,
  }
}

export class Card extends jspb.Message {
  getCardnum(): string;
  setCardnum(value: string): Card;

  getCarddate(): string;
  setCarddate(value: string): Card;

  getCardholder(): string;
  setCardholder(value: string): Card;

  getCvc(): number;
  setCvc(value: number): Card;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Card.AsObject;
  static toObject(includeInstance: boolean, msg: Card): Card.AsObject;
  static serializeBinaryToWriter(message: Card, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Card;
  static deserializeBinaryFromReader(message: Card, reader: jspb.BinaryReader): Card;
}

export namespace Card {
  export type AsObject = {
    cardnum: string,
    carddate: string,
    cardholder: string,
    cvc: number,
  }
}

export class Sorted extends jspb.Message {
  getColumn(): string;
  setColumn(value: string): Sorted;

  getType(): SortedType;
  setType(value: SortedType): Sorted;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Sorted.AsObject;
  static toObject(includeInstance: boolean, msg: Sorted): Sorted.AsObject;
  static serializeBinaryToWriter(message: Sorted, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Sorted;
  static deserializeBinaryFromReader(message: Sorted, reader: jspb.BinaryReader): Sorted;
}

export namespace Sorted {
  export type AsObject = {
    column: string,
    type: SortedType,
  }
}

export class Filter extends jspb.Message {
  getColumn(): string;
  setColumn(value: string): Filter;

  getValue(): string;
  setValue(value: string): Filter;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Filter.AsObject;
  static toObject(includeInstance: boolean, msg: Filter): Filter.AsObject;
  static serializeBinaryToWriter(message: Filter, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Filter;
  static deserializeBinaryFromReader(message: Filter, reader: jspb.BinaryReader): Filter;
}

export namespace Filter {
  export type AsObject = {
    column: string,
    value: string,
  }
}

export class Position extends jspb.Message {
  getLatitude(): number;
  setLatitude(value: number): Position;

  getLongitude(): number;
  setLongitude(value: number): Position;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Position.AsObject;
  static toObject(includeInstance: boolean, msg: Position): Position.AsObject;
  static serializeBinaryToWriter(message: Position, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Position;
  static deserializeBinaryFromReader(message: Position, reader: jspb.BinaryReader): Position;
}

export namespace Position {
  export type AsObject = {
    latitude: number,
    longitude: number,
  }
}

export class EmptyResponse extends jspb.Message {
  getError(): OperationError | undefined;
  setError(value?: OperationError): EmptyResponse;
  hasError(): boolean;
  clearError(): EmptyResponse;

  getEmpty(): google_protobuf_empty_pb.Empty | undefined;
  setEmpty(value?: google_protobuf_empty_pb.Empty): EmptyResponse;
  hasEmpty(): boolean;
  clearEmpty(): EmptyResponse;

  getResponseCase(): EmptyResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): EmptyResponse.AsObject;
  static toObject(includeInstance: boolean, msg: EmptyResponse): EmptyResponse.AsObject;
  static serializeBinaryToWriter(message: EmptyResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): EmptyResponse;
  static deserializeBinaryFromReader(message: EmptyResponse, reader: jspb.BinaryReader): EmptyResponse;
}

export namespace EmptyResponse {
  export type AsObject = {
    error?: OperationError.AsObject,
    empty?: google_protobuf_empty_pb.Empty.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    EMPTY = 2,
  }
}

export class CreateResponse extends jspb.Message {
  getError(): OperationError | undefined;
  setError(value?: OperationError): CreateResponse;
  hasError(): boolean;
  clearError(): CreateResponse;

  getId(): string;
  setId(value: string): CreateResponse;

  getResponseCase(): CreateResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CreateResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CreateResponse): CreateResponse.AsObject;
  static serializeBinaryToWriter(message: CreateResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CreateResponse;
  static deserializeBinaryFromReader(message: CreateResponse, reader: jspb.BinaryReader): CreateResponse;
}

export namespace CreateResponse {
  export type AsObject = {
    error?: OperationError.AsObject,
    id: string,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    ID = 2,
  }
}

export class HistoryChange extends jspb.Message {
  getField(): string;
  setField(value: string): HistoryChange;

  getOldvalue(): string;
  setOldvalue(value: string): HistoryChange;
  hasOldvalue(): boolean;
  clearOldvalue(): HistoryChange;

  getValue(): string;
  setValue(value: string): HistoryChange;
  hasValue(): boolean;
  clearValue(): HistoryChange;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): HistoryChange.AsObject;
  static toObject(includeInstance: boolean, msg: HistoryChange): HistoryChange.AsObject;
  static serializeBinaryToWriter(message: HistoryChange, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): HistoryChange;
  static deserializeBinaryFromReader(message: HistoryChange, reader: jspb.BinaryReader): HistoryChange;
}

export namespace HistoryChange {
  export type AsObject = {
    field: string,
    oldvalue?: string,
    value?: string,
  }

  export enum OldvalueCase { 
    _OLDVALUE_NOT_SET = 0,
    OLDVALUE = 2,
  }

  export enum ValueCase { 
    _VALUE_NOT_SET = 0,
    VALUE = 3,
  }
}

export class History extends jspb.Message {
  getVersion(): number;
  setVersion(value: number): History;

  getVersioncreateby(): string;
  setVersioncreateby(value: string): History;

  getVersioncreateat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setVersioncreateat(value?: google_protobuf_timestamp_pb.Timestamp): History;
  hasVersioncreateat(): boolean;
  clearVersioncreateat(): History;

  getChangeList(): Array<HistoryChange>;
  setChangeList(value: Array<HistoryChange>): History;
  clearChangeList(): History;
  addChange(value?: HistoryChange, index?: number): HistoryChange;

  getStatus(): ModelStatus;
  setStatus(value: ModelStatus): History;
  hasStatus(): boolean;
  clearStatus(): History;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): History.AsObject;
  static toObject(includeInstance: boolean, msg: History): History.AsObject;
  static serializeBinaryToWriter(message: History, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): History;
  static deserializeBinaryFromReader(message: History, reader: jspb.BinaryReader): History;
}

export namespace History {
  export type AsObject = {
    version: number,
    versioncreateby: string,
    versioncreateat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    changeList: Array<HistoryChange.AsObject>,
    status?: ModelStatus,
  }

  export enum StatusCase { 
    _STATUS_NOT_SET = 0,
    STATUS = 5,
  }
}

export class HistoryResult extends jspb.Message {
  getPagination(): PaginationResponse | undefined;
  setPagination(value?: PaginationResponse): HistoryResult;
  hasPagination(): boolean;
  clearPagination(): HistoryResult;

  getHistoryList(): Array<History>;
  setHistoryList(value: Array<History>): HistoryResult;
  clearHistoryList(): HistoryResult;
  addHistory(value?: History, index?: number): History;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): HistoryResult.AsObject;
  static toObject(includeInstance: boolean, msg: HistoryResult): HistoryResult.AsObject;
  static serializeBinaryToWriter(message: HistoryResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): HistoryResult;
  static deserializeBinaryFromReader(message: HistoryResult, reader: jspb.BinaryReader): HistoryResult;
}

export namespace HistoryResult {
  export type AsObject = {
    pagination?: PaginationResponse.AsObject,
    historyList: Array<History.AsObject>,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }
}

export class HistoryResponse extends jspb.Message {
  getError(): OperationError | undefined;
  setError(value?: OperationError): HistoryResponse;
  hasError(): boolean;
  clearError(): HistoryResponse;

  getResult(): HistoryResult | undefined;
  setResult(value?: HistoryResult): HistoryResponse;
  hasResult(): boolean;
  clearResult(): HistoryResponse;

  getResponseCase(): HistoryResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): HistoryResponse.AsObject;
  static toObject(includeInstance: boolean, msg: HistoryResponse): HistoryResponse.AsObject;
  static serializeBinaryToWriter(message: HistoryResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): HistoryResponse;
  static deserializeBinaryFromReader(message: HistoryResponse, reader: jspb.BinaryReader): HistoryResponse;
}

export namespace HistoryResponse {
  export type AsObject = {
    error?: OperationError.AsObject,
    result?: HistoryResult.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class ByIdRequest extends jspb.Message {
  getId(): string;
  setId(value: string): ByIdRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ByIdRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ByIdRequest): ByIdRequest.AsObject;
  static serializeBinaryToWriter(message: ByIdRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ByIdRequest;
  static deserializeBinaryFromReader(message: ByIdRequest, reader: jspb.BinaryReader): ByIdRequest;
}

export namespace ByIdRequest {
  export type AsObject = {
    id: string,
  }
}

export enum ErrorType { 
  UNKNOWN_ERROR = 0,
  SERVICE_ERROR = 1,
  UNSUPPORTED_OPERATION = 2,
  BAD_REQUEST = 3,
  AUTHENTICATION_ERROR = 4,
  NOT_FOUND = 5,
}
export enum TransportType { 
  BUS = 0,
  TROLLEYBUS = 1,
  TRAM = 2,
  METRO = 3,
}
export enum OperationStatus { 
  NEW = 0,
  SUCCESS = 1,
  FAILED = 2,
}
export enum AbonementType { 
  WALLET = 0,
  TRAVEL = 1,
  UNLIMITED = 2,
}
export enum SubscriptionCounterType { 
  SCT_ALL = 0,
  SCT_ALLOW_LIST = 1,
  SCT_SINGLE = 2,
  SCT_ALL_UNLIMITED = 3,
  SCT_ALLOW_LIST_UNLIMITED = 4,
}
export enum PaymentType { 
  EMV = 0,
  CARRIER = 1,
  SBER_PAY = 2,
  SBP = 3,
  BINDING = 4,
}
export enum ProlongType { 
  PT_PERIOD = 0,
  PT_END_DATE = 1,
  PT_ACTIVE_DATE = 2,
}
export enum PaymentAttribute { 
  PAYMENT_TYPE = 0,
  CLIENT_ID = 1,
  USER_ID = 3,
  ORDER_ID = 4,
  PAYMENT_DATE = 5,
  RETURN_URL = 6,
  FAIL_URL = 7,
  ITEMS = 8,
  CONFIG = 9,
  JSON_PARAMS = 10,
  USER_EMAIL = 11,
  USER_PHONE = 12,
}
export enum AuthType { 
  OPEN_ID = 0,
  MTLS = 1,
}
export enum SortedType { 
  ASC = 0,
  DESC = 1,
}
export enum ConstraintType { 
  TARIFF = 0,
  ROUTE = 1,
  TRANSPORT = 2,
  SERVICE = 3,
  ORGANIZATION = 4,
}
export enum ConstraintBaseRule { 
  ALLOW = 0,
  DENY = 1,
}
export enum RouteScheme { 
  DIRECTIONAL = 0,
  CIRCLE = 1,
}
export enum ModelStatus { 
  ACTIVE = 0,
  DISABLED = 1,
  BLOCKED = 2,
  IS_DELETED = 3,
}
