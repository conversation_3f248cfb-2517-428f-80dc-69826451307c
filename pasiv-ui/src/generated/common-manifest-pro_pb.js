// source: common-manifest-pro.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
var common_pb = require('./common_pb.js');
goog.object.extend(proto, common_pb);
var common$manifest$core_pb = require('./common-manifest-core_pb.js');
goog.object.extend(proto, common$manifest$core_pb);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.Employee', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.PriceRule', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.TPaymentType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.Product', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.Route', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.RouteStation', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.Station', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.Tariff', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.Transport', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pro.ValidTimeType', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.Product, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.Product.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.Product';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.Station, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.Station.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.Station';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.RouteStation, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.RouteStation';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.Transport.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.Transport, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.Transport.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.Transport';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.Route.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.Route, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.Route.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.Route';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.Tariff.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.Tariff, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.Tariff.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.Tariff';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.PriceRule, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.PriceRule';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.Employee, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.Employee.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.Employee';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.displayName = 'proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.repeatedFields_ = [1,2,3,4,5,6,7];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.toObject = function(includeInstance, msg) {
  var f, obj = {
    stationList: jspb.Message.toObjectList(msg.getStationList(),
    proto.ru.sbertroika.common.manifest.v1.pro.Station.toObject, includeInstance),
    routeList: jspb.Message.toObjectList(msg.getRouteList(),
    proto.ru.sbertroika.common.manifest.v1.pro.Route.toObject, includeInstance),
    transportList: jspb.Message.toObjectList(msg.getTransportList(),
    proto.ru.sbertroika.common.manifest.v1.pro.Transport.toObject, includeInstance),
    productList: jspb.Message.toObjectList(msg.getProductList(),
    proto.ru.sbertroika.common.manifest.v1.pro.Product.toObject, includeInstance),
    tariffList: jspb.Message.toObjectList(msg.getTariffList(),
    proto.ru.sbertroika.common.manifest.v1.pro.Tariff.toObject, includeInstance),
    menuList: jspb.Message.toObjectList(msg.getMenuList(),
    proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.toObject, includeInstance),
    employeeList: jspb.Message.toObjectList(msg.getEmployeeList(),
    proto.ru.sbertroika.common.manifest.v1.pro.Employee.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.Station;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.Station.deserializeBinaryFromReader);
      msg.addStation(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.Route;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.Route.deserializeBinaryFromReader);
      msg.addRoute(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.Transport;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.Transport.deserializeBinaryFromReader);
      msg.addTransport(value);
      break;
    case 4:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.Product;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.Product.deserializeBinaryFromReader);
      msg.addProduct(value);
      break;
    case 5:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.Tariff;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.Tariff.deserializeBinaryFromReader);
      msg.addTariff(value);
      break;
    case 6:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.deserializeBinaryFromReader);
      msg.addMenu(value);
      break;
    case 7:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.Employee;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.Employee.deserializeBinaryFromReader);
      msg.addEmployee(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStationList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.Station.serializeBinaryToWriter
    );
  }
  f = message.getRouteList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.Route.serializeBinaryToWriter
    );
  }
  f = message.getTransportList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.Transport.serializeBinaryToWriter
    );
  }
  f = message.getProductList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.Product.serializeBinaryToWriter
    );
  }
  f = message.getTariffList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.Tariff.serializeBinaryToWriter
    );
  }
  f = message.getMenuList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      6,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.serializeBinaryToWriter
    );
  }
  f = message.getEmployeeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      7,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.Employee.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Station station = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Station>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.getStationList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Station>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.Station, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Station>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.setStationList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Station=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.addStation = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.Station, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.clearStationList = function() {
  return this.setStationList([]);
};


/**
 * repeated Route route = 2;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Route>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.getRouteList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Route>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.Route, 2));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Route>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.setRouteList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Route=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.addRoute = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.Route, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.clearRouteList = function() {
  return this.setRouteList([]);
};


/**
 * repeated Transport transport = 3;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Transport>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.getTransportList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Transport>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.Transport, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Transport>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.setTransportList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Transport=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.addTransport = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.Transport, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.clearTransportList = function() {
  return this.setTransportList([]);
};


/**
 * repeated Product product = 4;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Product>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.getProductList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Product>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.Product, 4));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Product>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.setProductList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Product=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Product}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.addProduct = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.Product, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.clearProductList = function() {
  return this.setProductList([]);
};


/**
 * repeated Tariff tariff = 5;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Tariff>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.getTariffList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Tariff>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.Tariff, 5));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Tariff>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.setTariffList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.addTariff = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.Tariff, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.clearTariffList = function() {
  return this.setTariffList([]);
};


/**
 * repeated ProductMenu menu = 6;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.getMenuList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu, 6));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.setMenuList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 6, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.addMenu = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 6, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.clearMenuList = function() {
  return this.setMenuList([]);
};


/**
 * repeated Employee employee = 7;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Employee>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.getEmployeeList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Employee>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.Employee, 7));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.Employee>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.setEmployeeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 7, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Employee=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.addEmployee = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 7, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.Employee, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.prototype.clearEmployeeList = function() {
  return this.setEmployeeList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance),
    dict: (f = msg.getDict()) && proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.deserializeBinaryFromReader);
      msg.setDict(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
  f = message.getDict();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};


/**
 * optional ManifestProDict dict = 2;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.getDict = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProDict|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.setDict = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.clearDict = function() {
  return this.setDict(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro.prototype.hasDict = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.Product.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Product} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    version: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Product}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.Product;
  return proto.ru.sbertroika.common.manifest.v1.pro.Product.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Product} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Product}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.Product.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Product} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Product} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Product} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional uint32 version = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Product} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Product.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.Station.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Station} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    lat: jspb.Message.getFloatingPointFieldWithDefault(msg, 3, 0.0),
    lon: jspb.Message.getFloatingPointFieldWithDefault(msg, 4, 0.0),
    version: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.Station;
  return proto.ru.sbertroika.common.manifest.v1.pro.Station.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Station} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLat(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLon(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.Station.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Station} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getLat();
  if (f !== 0.0) {
    writer.writeDouble(
      3,
      f
    );
  }
  f = message.getLon();
  if (f !== 0.0) {
    writer.writeDouble(
      4,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional double lat = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.getLat = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.setLat = function(value) {
  return jspb.Message.setProto3FloatField(this, 3, value);
};


/**
 * optional double lon = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.getLon = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 4, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.setLon = function(value) {
  return jspb.Message.setProto3FloatField(this, 4, value);
};


/**
 * optional uint32 version = 5;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Station} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Station.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    pos: jspb.Message.getFieldWithDefault(msg, 2, 0),
    version: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation}
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.RouteStation;
  return proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation}
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPos(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPos();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 pos = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.getPos = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.setPos = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 version = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.repeatedFields_ = [4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.Transport.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    number: jspb.Message.getFieldWithDefault(msg, 2, ""),
    type: jspb.Message.getFieldWithDefault(msg, 3, 0),
    constraintList: jspb.Message.toObjectList(msg.getConstraintList(),
    common$manifest$core_pb.Constraint.toObject, includeInstance),
    version: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.Transport;
  return proto.ru.sbertroika.common.manifest.v1.pro.Transport.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setNumber(value);
      break;
    case 3:
      var value = /** @type {!proto.ru.sbertroika.common.v1.TransportType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 4:
      var value = new common$manifest$core_pb.Constraint;
      reader.readMessage(value,common$manifest$core_pb.Constraint.deserializeBinaryFromReader);
      msg.addConstraint(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.Transport.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getNumber();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getConstraintList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      common$manifest$core_pb.Constraint.serializeBinaryToWriter
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string number = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.getNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.setNumber = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional ru.sbertroika.common.v1.TransportType type = 3;
 * @return {!proto.ru.sbertroika.common.v1.TransportType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.TransportType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.TransportType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.Constraint constraint = 4;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.getConstraintList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.Constraint, 4));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.setConstraintList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.Constraint=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.addConstraint = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.ru.sbertroika.common.manifest.v1.core.Constraint, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.clearConstraintList = function() {
  return this.setConstraintList([]);
};


/**
 * optional uint32 version = 5;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Transport} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Transport.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization}
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization;
  return proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization}
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.repeatedFields_ = [4,5,8];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.Route.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Route} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    scheme: jspb.Message.getFieldWithDefault(msg, 3, 0),
    stationList: jspb.Message.toObjectList(msg.getStationList(),
    proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.toObject, includeInstance),
    constraintList: jspb.Message.toObjectList(msg.getConstraintList(),
    common$manifest$core_pb.Constraint.toObject, includeInstance),
    routeindex: jspb.Message.getFieldWithDefault(msg, 6, 0),
    number: jspb.Message.getFieldWithDefault(msg, 7, ""),
    dispatchingorganizationList: jspb.Message.toObjectList(msg.getDispatchingorganizationList(),
    proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.toObject, includeInstance),
    version: jspb.Message.getFieldWithDefault(msg, 9, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.Route;
  return proto.ru.sbertroika.common.manifest.v1.pro.Route.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Route} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {!proto.ru.sbertroika.common.v1.RouteScheme} */ (reader.readEnum());
      msg.setScheme(value);
      break;
    case 4:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.RouteStation;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.deserializeBinaryFromReader);
      msg.addStation(value);
      break;
    case 5:
      var value = new common$manifest$core_pb.Constraint;
      reader.readMessage(value,common$manifest$core_pb.Constraint.deserializeBinaryFromReader);
      msg.addConstraint(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRouteindex(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setNumber(value);
      break;
    case 8:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.deserializeBinaryFromReader);
      msg.addDispatchingorganization(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.Route.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Route} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getScheme();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getStationList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.RouteStation.serializeBinaryToWriter
    );
  }
  f = message.getConstraintList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      common$manifest$core_pb.Constraint.serializeBinaryToWriter
    );
  }
  f = message.getRouteindex();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getNumber();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getDispatchingorganizationList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      8,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization.serializeBinaryToWriter
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      9,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional ru.sbertroika.common.v1.RouteScheme scheme = 3;
 * @return {!proto.ru.sbertroika.common.v1.RouteScheme}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getScheme = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.RouteScheme} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.RouteScheme} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setScheme = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * repeated RouteStation station = 4;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getStationList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.RouteStation, 4));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setStationList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.RouteStation}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.addStation = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.RouteStation, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.clearStationList = function() {
  return this.setStationList([]);
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.Constraint constraint = 5;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getConstraintList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.Constraint, 5));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setConstraintList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.Constraint=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.addConstraint = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.ru.sbertroika.common.manifest.v1.core.Constraint, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.clearConstraintList = function() {
  return this.setConstraintList([]);
};


/**
 * optional uint32 routeIndex = 6;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getRouteindex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setRouteindex = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string number = 7;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setNumber = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * repeated DispatchingRouteOrganization dispatchingOrganization = 8;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getDispatchingorganizationList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization, 8));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setDispatchingorganizationList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 8, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.addDispatchingorganization = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 8, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.DispatchingRouteOrganization, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.clearDispatchingorganizationList = function() {
  return this.setDispatchingorganizationList([]);
};


/**
 * optional uint32 version = 9;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Route} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Route.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.Tariff.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    constraintList: jspb.Message.toObjectList(msg.getConstraintList(),
    common$manifest$core_pb.Constraint.toObject, includeInstance),
    version: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.Tariff;
  return proto.ru.sbertroika.common.manifest.v1.pro.Tariff.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = new common$manifest$core_pb.Constraint;
      reader.readMessage(value,common$manifest$core_pb.Constraint.deserializeBinaryFromReader);
      msg.addConstraint(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.Tariff.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getConstraintList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      common$manifest$core_pb.Constraint.serializeBinaryToWriter
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.Constraint constraint = 3;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.getConstraintList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.Constraint, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.Constraint>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.setConstraintList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.Constraint=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.addConstraint = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.common.manifest.v1.core.Constraint, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.clearConstraintList = function() {
  return this.setConstraintList([]);
};


/**
 * optional uint32 version = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Tariff} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Tariff.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.toObject = function(includeInstance, msg) {
  var f, obj = {
    stationfrom: jspb.Message.getFieldWithDefault(msg, 1, ""),
    stationto: jspb.Message.getFieldWithDefault(msg, 2, ""),
    price: jspb.Message.getFieldWithDefault(msg, 3, 0),
    version: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem;
  return proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setStationfrom(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setStationto(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPrice(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStationfrom();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getStationto();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getPrice();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional string stationFrom = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.getStationfrom = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.setStationfrom = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string stationTo = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.getStationto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.setStationto = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional uint32 price = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.getPrice = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.setPrice = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 version = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.toObject = function(includeInstance, msg) {
  var f, obj = {
    paymenttype: jspb.Message.getFieldWithDefault(msg, 1, 0),
    price: jspb.Message.getFieldWithDefault(msg, 2, 0),
    matrixList: jspb.Message.toObjectList(msg.getMatrixList(),
    proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.toObject, includeInstance),
    version: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.PriceRule;
  return proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.TPaymentType} */ (reader.readEnum());
      msg.setPaymenttype(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPrice(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.deserializeBinaryFromReader);
      msg.addMatrix(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPaymenttype();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getPrice();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getMatrixList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem.serializeBinaryToWriter
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.TPaymentType = {
  CASH: 0,
  EMV: 1,
  TROIKA_TICKET: 2,
  TROIKA_WALLET: 3,
  ABT_TICKET: 4,
  ABT_WALLET: 5,
  PROSTOR_TICKET: 6,
  QR_TICKET: 7,
  QR_WALLET: 8
};

/**
 * optional TPaymentType paymentType = 1;
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.TPaymentType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.getPaymenttype = function() {
  return /** @type {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.TPaymentType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.TPaymentType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.setPaymenttype = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 price = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.getPrice = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.setPrice = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * repeated PriceRuleMatrixItem matrix = 3;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.getMatrixList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.setMatrixList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.addMatrix = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.PriceRuleMatrixItem, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.clearMatrixList = function() {
  return this.setMatrixList([]);
};


/**
 * optional uint32 version = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.toObject = function(includeInstance, msg) {
  var f, obj = {
    productid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    tariffid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    pricerulesList: jspb.Message.toObjectList(msg.getPricerulesList(),
    proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.toObject, includeInstance),
    version: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu;
  return proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setProductid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTariffid(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.PriceRule;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.deserializeBinaryFromReader);
      msg.addPricerules(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getProductid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTariffid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getPricerulesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.PriceRule.serializeBinaryToWriter
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional string productId = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.getProductid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.setProductid = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string tariffId = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.getTariffid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.setTariffid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated PriceRule priceRules = 3;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.getPricerulesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.PriceRule, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.setPricerulesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.PriceRule}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.addPricerules = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.PriceRule, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.clearPricerulesList = function() {
  return this.setPricerulesList([]);
};


/**
 * optional uint32 version = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ProductMenu.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.Employee.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    role: jspb.Message.getFieldWithDefault(msg, 2, ""),
    surname: jspb.Message.getFieldWithDefault(msg, 3, ""),
    name: jspb.Message.getFieldWithDefault(msg, 4, ""),
    middlename: jspb.Message.getFieldWithDefault(msg, 5, ""),
    version: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.Employee;
  return proto.ru.sbertroika.common.manifest.v1.pro.Employee.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setRole(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSurname(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setMiddlename(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.Employee.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getRole();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSurname();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getMiddlename();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string role = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.getRole = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.setRole = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string surname = 3;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.getSurname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.setSurname = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string name = 4;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string middleName = 5;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.getMiddlename = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.setMiddlename = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional uint32 version = 6;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.Employee} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.Employee.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance),
    dict: (f = msg.getDict()) && proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.deserializeBinaryFromReader);
      msg.setDict(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
  f = message.getDict();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};


/**
 * optional ManifestProTroikaDict dict = 2;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.getDict = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.setDict = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.clearDict = function() {
  return this.setDict(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika.prototype.hasDict = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.toObject = function(includeInstance, msg) {
  var f, obj = {
    templatesList: jspb.Message.toObjectList(msg.getTemplatesList(),
    proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.deserializeBinaryFromReader);
      msg.addTemplates(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTemplatesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.serializeBinaryToWriter
    );
  }
};


/**
 * repeated TroikaTemplate templates = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.prototype.getTemplatesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.prototype.setTemplatesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.prototype.addTemplates = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroikaDict.prototype.clearTemplatesList = function() {
  return this.setTemplatesList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    appcode: jspb.Message.getFieldWithDefault(msg, 2, 0),
    crdcode: jspb.Message.getFieldWithDefault(msg, 3, 0),
    name: jspb.Message.getFieldWithDefault(msg, 4, ""),
    type: jspb.Message.getFieldWithDefault(msg, 5, 0),
    limit: jspb.Message.getFieldWithDefault(msg, 6, 0),
    prolongtype: jspb.Message.getFieldWithDefault(msg, 7, 0),
    prolongstartdate: (f = msg.getProlongstartdate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    prolongenddate: (f = msg.getProlongenddate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    prolongdays: jspb.Message.getFieldWithDefault(msg, 10, 0),
    prolongcardcode: jspb.Message.getFieldWithDefault(msg, 11, 0),
    version: jspb.Message.getFieldWithDefault(msg, 12, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate;
  return proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAppcode(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCrdcode(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 5:
      var value = /** @type {!proto.ru.sbertroika.common.v1.AbonementType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLimit(value);
      break;
    case 7:
      var value = /** @type {!proto.ru.sbertroika.common.v1.ProlongType} */ (reader.readEnum());
      msg.setProlongtype(value);
      break;
    case 8:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setProlongstartdate(value);
      break;
    case 9:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setProlongenddate(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setProlongdays(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setProlongcardcode(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAppcode();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getCrdcode();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getLimit();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getProlongtype();
  if (f !== 0.0) {
    writer.writeEnum(
      7,
      f
    );
  }
  f = message.getProlongstartdate();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getProlongenddate();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getProlongdays();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getProlongcardcode();
  if (f !== 0) {
    writer.writeUint32(
      11,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      12,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 appCode = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getAppcode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setAppcode = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 crdCode = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getCrdcode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setCrdcode = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string name = 4;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional ru.sbertroika.common.v1.AbonementType type = 5;
 * @return {!proto.ru.sbertroika.common.v1.AbonementType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.AbonementType} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.AbonementType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional uint32 limit = 6;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getLimit = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setLimit = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional ru.sbertroika.common.v1.ProlongType prolongType = 7;
 * @return {!proto.ru.sbertroika.common.v1.ProlongType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getProlongtype = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.ProlongType} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.ProlongType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setProlongtype = function(value) {
  return jspb.Message.setProto3EnumField(this, 7, value);
};


/**
 * optional google.protobuf.Timestamp prolongStartDate = 8;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getProlongstartdate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 8));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setProlongstartdate = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.clearProlongstartdate = function() {
  return this.setProlongstartdate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.hasProlongstartdate = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional google.protobuf.Timestamp prolongEndDate = 9;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getProlongenddate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 9));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setProlongenddate = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.clearProlongenddate = function() {
  return this.setProlongenddate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.hasProlongenddate = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional uint32 prolongDays = 10;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getProlongdays = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setProlongdays = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional uint32 prolongCardCode = 11;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getProlongcardcode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setProlongcardcode = function(value) {
  return jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional uint32 version = 12;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.TroikaTemplate.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance),
    dict: (f = msg.getDict()) && proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.deserializeBinaryFromReader);
      msg.setDict(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
  f = message.getDict();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};


/**
 * optional ManifestProAbtDict dict = 2;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.getDict = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.setDict = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.clearDict = function() {
  return this.setDict(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt.prototype.hasDict = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.toObject = function(includeInstance, msg) {
  var f, obj = {
    templateList: jspb.Message.toObjectList(msg.getTemplateList(),
    proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict;
  return proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.deserializeBinaryFromReader);
      msg.addTemplate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTemplateList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.serializeBinaryToWriter
    );
  }
};


/**
 * repeated SubscriptionTemplate template = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.prototype.getTemplateList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.prototype.setTemplateList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.prototype.addTemplate = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbtDict.prototype.clearTemplateList = function() {
  return this.setTemplateList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.repeatedFields_ = [7,9,10];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    version: jspb.Message.getFieldWithDefault(msg, 2, 0),
    appcode: jspb.Message.getFieldWithDefault(msg, 3, 0),
    crdcode: jspb.Message.getFieldWithDefault(msg, 4, 0),
    name: jspb.Message.getFieldWithDefault(msg, 5, ""),
    type: jspb.Message.getFieldWithDefault(msg, 6, 0),
    rulesList: jspb.Message.toObjectList(msg.getRulesList(),
    proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.toObject, includeInstance),
    issocial: jspb.Message.getBooleanFieldWithDefault(msg, 8, false),
    counterList: jspb.Message.toObjectList(msg.getCounterList(),
    proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.toObject, includeInstance),
    cardtypeList: jspb.Message.toObjectList(msg.getCardtypeList(),
    proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.toObject, includeInstance),
    validtimetype: jspb.Message.getFieldWithDefault(msg, 11, 0),
    validtimestart: (f = msg.getValidtimestart()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    validtimedays: jspb.Message.getFieldWithDefault(msg, 13, 0),
    validtimeend: (f = msg.getValidtimeend()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate;
  return proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAppcode(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCrdcode(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 6:
      var value = /** @type {!proto.ru.sbertroika.common.v1.AbonementType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 7:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.deserializeBinaryFromReader);
      msg.addRules(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIssocial(value);
      break;
    case 9:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.deserializeBinaryFromReader);
      msg.addCounter(value);
      break;
    case 10:
      var value = new proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.deserializeBinaryFromReader);
      msg.addCardtype(value);
      break;
    case 11:
      var value = /** @type {!proto.ru.sbertroika.common.manifest.v1.pro.ValidTimeType} */ (reader.readEnum());
      msg.setValidtimetype(value);
      break;
    case 12:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setValidtimestart(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setValidtimedays(value);
      break;
    case 14:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setValidtimeend(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getAppcode();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getCrdcode();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      6,
      f
    );
  }
  f = message.getRulesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      7,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.serializeBinaryToWriter
    );
  }
  f = message.getIssocial();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
  f = message.getCounterList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      9,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.serializeBinaryToWriter
    );
  }
  f = message.getCardtypeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      10,
      f,
      proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.serializeBinaryToWriter
    );
  }
  f = message.getValidtimetype();
  if (f !== 0.0) {
    writer.writeEnum(
      11,
      f
    );
  }
  f = message.getValidtimestart();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 13));
  if (f != null) {
    writer.writeInt32(
      13,
      f
    );
  }
  f = message.getValidtimeend();
  if (f != null) {
    writer.writeMessage(
      14,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 version = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 appCode = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getAppcode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setAppcode = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 crdCode = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getCrdcode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setCrdcode = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string name = 5;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional ru.sbertroika.common.v1.AbonementType type = 6;
 * @return {!proto.ru.sbertroika.common.v1.AbonementType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.AbonementType} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.AbonementType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 6, value);
};


/**
 * repeated SubscriptionTemplatePassRule rules = 7;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getRulesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule, 7));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setRulesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 7, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.addRules = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 7, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.clearRulesList = function() {
  return this.setRulesList([]);
};


/**
 * optional bool isSocial = 8;
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getIssocial = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setIssocial = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};


/**
 * repeated SubscriptionTemplateCounter counter = 9;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getCounterList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter, 9));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setCounterList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 9, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.addCounter = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 9, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.clearCounterList = function() {
  return this.setCounterList([]);
};


/**
 * repeated AbtCardType cardType = 10;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType>}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getCardtypeList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType, 10));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setCardtypeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 10, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.addCardtype = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 10, opt_value, proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.clearCardtypeList = function() {
  return this.setCardtypeList([]);
};


/**
 * optional ValidTimeType validTimeType = 11;
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.ValidTimeType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getValidtimetype = function() {
  return /** @type {!proto.ru.sbertroika.common.manifest.v1.pro.ValidTimeType} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.ValidTimeType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setValidtimetype = function(value) {
  return jspb.Message.setProto3EnumField(this, 11, value);
};


/**
 * optional google.protobuf.Timestamp validTimeStart = 12;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getValidtimestart = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 12));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setValidtimestart = function(value) {
  return jspb.Message.setWrapperField(this, 12, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.clearValidtimestart = function() {
  return this.setValidtimestart(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.hasValidtimestart = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional int32 validTimeDays = 13;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getValidtimedays = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setValidtimedays = function(value) {
  return jspb.Message.setField(this, 13, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.clearValidtimedays = function() {
  return jspb.Message.setField(this, 13, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.hasValidtimedays = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional google.protobuf.Timestamp validTimeEnd = 14;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.getValidtimeend = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 14));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.setValidtimeend = function(value) {
  return jspb.Message.setWrapperField(this, 14, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.clearValidtimeend = function() {
  return this.setValidtimeend(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplate.prototype.hasValidtimeend = function() {
  return jspb.Message.getField(this, 14) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    version: jspb.Message.getFieldWithDefault(msg, 2, 0),
    index: jspb.Message.getFieldWithDefault(msg, 3, 0),
    action: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule;
  return proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIndex(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setAction(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getIndex();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getAction();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 version = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 index = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.getIndex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.setIndex = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string action = 4;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.getAction = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplatePassRule.prototype.setAction = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    version: jspb.Message.getFieldWithDefault(msg, 2, 0),
    type: jspb.Message.getFieldWithDefault(msg, 3, 0),
    value: jspb.Message.getFieldWithDefault(msg, 4, 0),
    isbus: jspb.Message.getBooleanFieldWithDefault(msg, 51, false),
    istrolleybus: jspb.Message.getBooleanFieldWithDefault(msg, 52, false),
    istram: jspb.Message.getBooleanFieldWithDefault(msg, 53, false),
    ismetro: jspb.Message.getBooleanFieldWithDefault(msg, 54, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter;
  return proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 3:
      var value = /** @type {!proto.ru.sbertroika.common.v1.SubscriptionCounterType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setValue(value);
      break;
    case 51:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsbus(value);
      break;
    case 52:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIstrolleybus(value);
      break;
    case 53:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIstram(value);
      break;
    case 54:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsmetro(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getValue();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getIsbus();
  if (f) {
    writer.writeBool(
      51,
      f
    );
  }
  f = message.getIstrolleybus();
  if (f) {
    writer.writeBool(
      52,
      f
    );
  }
  f = message.getIstram();
  if (f) {
    writer.writeBool(
      53,
      f
    );
  }
  f = message.getIsmetro();
  if (f) {
    writer.writeBool(
      54,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 version = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional ru.sbertroika.common.v1.SubscriptionCounterType type = 3;
 * @return {!proto.ru.sbertroika.common.v1.SubscriptionCounterType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.SubscriptionCounterType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.SubscriptionCounterType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional uint32 value = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getValue = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setValue = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional bool isBus = 51;
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getIsbus = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 51, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setIsbus = function(value) {
  return jspb.Message.setProto3BooleanField(this, 51, value);
};


/**
 * optional bool isTrolleybus = 52;
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getIstrolleybus = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 52, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setIstrolleybus = function(value) {
  return jspb.Message.setProto3BooleanField(this, 52, value);
};


/**
 * optional bool isTram = 53;
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getIstram = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 53, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setIstram = function(value) {
  return jspb.Message.setProto3BooleanField(this, 53, value);
};


/**
 * optional bool isMetro = 54;
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.getIsmetro = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 54, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.SubscriptionTemplateCounter.prototype.setIsmetro = function(value) {
  return jspb.Message.setProto3BooleanField(this, 54, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType;
  return proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType}
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pro.AbtCardType.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * @enum {number}
 */
proto.ru.sbertroika.common.manifest.v1.pro.ValidTimeType = {
  INTERVAL: 0,
  DAYS: 1,
  INTERVAL_AND_DAYS: 2
};

goog.object.extend(exports, proto.ru.sbertroika.common.manifest.v1.pro);
