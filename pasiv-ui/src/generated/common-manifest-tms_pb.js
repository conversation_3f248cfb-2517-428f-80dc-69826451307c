// source: common-manifest-tms.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var common$manifest$core_pb = require('./common-manifest-core_pb.js');
goog.object.extend(proto, common$manifest$core_pb);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.displayName = 'proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.displayName = 'proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.displayName = 'proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.toObject = function(includeInstance, msg) {
  var f, obj = {
    userList: jspb.Message.toObjectList(msg.getUserList(),
    proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict;
  return proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.deserializeBinaryFromReader);
      msg.addUser(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getUserList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.serializeBinaryToWriter
    );
  }
};


/**
 * repeated TerminalUser user = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser>}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.prototype.getUserList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.prototype.setUserList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.prototype.addUser = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.prototype.clearUserList = function() {
  return this.setUserList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance),
    dict: (f = msg.getDict()) && proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms;
  return proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.deserializeBinaryFromReader);
      msg.setDict(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
  f = message.getDict();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} returns this
*/
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};


/**
 * optional ManifestTmsDict dict = 2;
 * @return {?proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.getDict = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.tms.ManifestTmsDict|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} returns this
*/
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.setDict = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.clearDict = function() {
  return this.setDict(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms.prototype.hasDict = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    role: jspb.Message.getFieldWithDefault(msg, 2, ""),
    surname: jspb.Message.getFieldWithDefault(msg, 3, ""),
    name: jspb.Message.getFieldWithDefault(msg, 4, ""),
    middlename: jspb.Message.getFieldWithDefault(msg, 5, ""),
    personalnumber: jspb.Message.getFieldWithDefault(msg, 6, ""),
    pinhash: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser;
  return proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setRole(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSurname(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setMiddlename(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setPersonalnumber(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPinhash(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getRole();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSurname();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getMiddlename();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getPersonalnumber();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getPinhash();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string role = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.getRole = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.setRole = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string surname = 3;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.getSurname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.setSurname = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string name = 4;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string middleName = 5;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.getMiddlename = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.setMiddlename = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string personalNumber = 6;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.getPersonalnumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.setPersonalnumber = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string pinHash = 7;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.getPinhash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser} returns this
 */
proto.ru.sbertroika.common.manifest.v1.tms.TerminalUser.prototype.setPinhash = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


goog.object.extend(exports, proto.ru.sbertroika.common.manifest.v1.tms);
