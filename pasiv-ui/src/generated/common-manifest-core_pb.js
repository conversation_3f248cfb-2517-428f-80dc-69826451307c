// source: common-manifest-core.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var common_pb = require('./common_pb.js');
goog.object.extend(proto, common_pb);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.core.Constraint', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.core.ConstraintException', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.core.TkpFeature', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.FeatureState', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.core.ConstraintException, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.displayName = 'proto.ru.sbertroika.common.manifest.v1.core.ConstraintException';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.core.Constraint.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.core.Constraint, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.core.Constraint.displayName = 'proto.ru.sbertroika.common.manifest.v1.core.Constraint';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.displayName = 'proto.ru.sbertroika.common.manifest.v1.core.TkpFeature';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException}
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.core.ConstraintException;
  return proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException}
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException} returns this
 */
proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.core.Constraint.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.Constraint} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.toObject = function(includeInstance, msg) {
  var f, obj = {
    type: jspb.Message.getFieldWithDefault(msg, 1, 0),
    baserule: jspb.Message.getFieldWithDefault(msg, 2, 0),
    exceptionList: jspb.Message.toObjectList(msg.getExceptionList(),
    proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.core.Constraint;
  return proto.ru.sbertroika.common.manifest.v1.core.Constraint.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.Constraint} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.ru.sbertroika.common.v1.ConstraintType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 2:
      var value = /** @type {!proto.ru.sbertroika.common.v1.ConstraintBaseRule} */ (reader.readEnum());
      msg.setBaserule(value);
      break;
    case 3:
      var value = new proto.ru.sbertroika.common.manifest.v1.core.ConstraintException;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.deserializeBinaryFromReader);
      msg.addException(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.core.Constraint.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.Constraint} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getBaserule();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getExceptionList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.ru.sbertroika.common.manifest.v1.core.ConstraintException.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.ConstraintType type = 1;
 * @return {!proto.ru.sbertroika.common.v1.ConstraintType}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.ConstraintType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.ConstraintType} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint} returns this
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional ru.sbertroika.common.v1.ConstraintBaseRule baseRule = 2;
 * @return {!proto.ru.sbertroika.common.v1.ConstraintBaseRule}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.getBaserule = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.ConstraintBaseRule} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.ConstraintBaseRule} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint} returns this
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.setBaserule = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * repeated ConstraintException exception = 3;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException>}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.getExceptionList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.core.ConstraintException, 3));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint} returns this
*/
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.setExceptionList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.ConstraintException}
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.addException = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.ru.sbertroika.common.manifest.v1.core.ConstraintException, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.Constraint} returns this
 */
proto.ru.sbertroika.common.manifest.v1.core.Constraint.prototype.clearExceptionList = function() {
  return this.setExceptionList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    state: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.core.TkpFeature;
  return proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.FeatureState} */ (reader.readEnum());
      msg.setState(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getState();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.FeatureState = {
  FS_ACTIVE: 0
};

/**
 * optional string name = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature} returns this
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional FeatureState state = 2;
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.FeatureState}
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.prototype.getState = function() {
  return /** @type {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.FeatureState} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.FeatureState} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature} returns this
 */
proto.ru.sbertroika.common.manifest.v1.core.TkpFeature.prototype.setState = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


goog.object.extend(exports, proto.ru.sbertroika.common.manifest.v1.core);
