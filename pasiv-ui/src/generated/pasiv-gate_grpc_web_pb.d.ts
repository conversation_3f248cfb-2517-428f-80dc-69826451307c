import * as grpcWeb from 'grpc-web';

import * as common$manifest_pb from './common-manifest_pb'; // proto import: "common-manifest.proto"
import * as pasiv$gate_pb from './pasiv-gate_pb'; // proto import: "pasiv-gate.proto"


export class PASIVGateServiceClient {
  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; });

  getManifest(
    request: common$manifest_pb.ManifestRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate_pb.ManifestResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate_pb.ManifestResponse>;

}

export class PASIVGateServicePromiseClient {
  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; });

  getManifest(
    request: common$manifest_pb.ManifestRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate_pb.ManifestResponse>;

}

