// source: common-manifest-pasiv.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var common$manifest$core_pb = require('./common-manifest-core_pb.js');
goog.object.extend(proto, common$manifest$core_pb);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.pasiv.Organization', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.displayName = 'proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.displayName = 'proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.pasiv.Organization, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.displayName = 'proto.ru.sbertroika.common.manifest.v1.pasiv.Organization';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.toObject = function(includeInstance, msg) {
  var f, obj = {
    organizationList: jspb.Message.toObjectList(msg.getOrganizationList(),
    proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict;
  return proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.manifest.v1.pasiv.Organization;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.deserializeBinaryFromReader);
      msg.addOrganization(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrganizationList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Organization organization = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization>}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.prototype.getOrganizationList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pasiv.Organization, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.prototype.setOrganizationList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.prototype.addOrganization = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.pasiv.Organization, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.prototype.clearOrganizationList = function() {
  return this.setOrganizationList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.toObject = function(includeInstance, msg) {
  var f, obj = {
    featuresList: jspb.Message.toObjectList(msg.getFeaturesList(),
    common$manifest$core_pb.TkpFeature.toObject, includeInstance),
    dict: (f = msg.getDict()) && proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv;
  return proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$core_pb.TkpFeature;
      reader.readMessage(value,common$manifest$core_pb.TkpFeature.deserializeBinaryFromReader);
      msg.addFeatures(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.deserializeBinaryFromReader);
      msg.setDict(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFeaturesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      common$manifest$core_pb.TkpFeature.serializeBinaryToWriter
    );
  }
  f = message.getDict();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ru.sbertroika.common.manifest.v1.core.TkpFeature features = 1;
 * @return {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.getFeaturesList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} */ (
    jspb.Message.getRepeatedWrapperField(this, common$manifest$core_pb.TkpFeature, 1));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature>} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.setFeaturesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.manifest.v1.core.TkpFeature}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.addFeatures = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.ru.sbertroika.common.manifest.v1.core.TkpFeature, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.clearFeaturesList = function() {
  return this.setFeaturesList([]);
};


/**
 * optional ManifestPasivDict dict = 2;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.getDict = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasivDict|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} returns this
*/
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.setDict = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.clearDict = function() {
  return this.setDict(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv.prototype.hasDict = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    shortname: jspb.Message.getFieldWithDefault(msg, 3, ""),
    inn: jspb.Message.getFieldWithDefault(msg, 4, ""),
    kpp: jspb.Message.getFieldWithDefault(msg, 5, ""),
    address: jspb.Message.getFieldWithDefault(msg, 6, ""),
    paymentplace: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.pasiv.Organization;
  return proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setShortname(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setInn(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setKpp(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setAddress(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPaymentplace(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getShortname();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getInn();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getKpp();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getAddress();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getPaymentplace();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string shortName = 3;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.getShortname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.setShortname = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string inn = 4;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.getInn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.setInn = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string kpp = 5;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.getKpp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.setKpp = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string address = 6;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.getAddress = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.setAddress = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string paymentPlace = 7;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.getPaymentplace = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.pasiv.Organization} returns this
 */
proto.ru.sbertroika.common.manifest.v1.pasiv.Organization.prototype.setPaymentplace = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


goog.object.extend(exports, proto.ru.sbertroika.common.manifest.v1.pasiv);
