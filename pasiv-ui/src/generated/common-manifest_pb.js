// source: common-manifest.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
var common$manifest$core_pb = require('./common-manifest-core_pb.js');
goog.object.extend(proto, common$manifest$core_pb);
var common$manifest$pro_pb = require('./common-manifest-pro_pb.js');
goog.object.extend(proto, common$manifest$pro_pb);
var common$manifest$pasiv_pb = require('./common-manifest-pasiv_pb.js');
goog.object.extend(proto, common$manifest$pasiv_pb);
var common$manifest$tms_pb = require('./common-manifest-tms_pb.js');
goog.object.extend(proto, common$manifest$tms_pb);
var common$manifest$agent$gateway_pb = require('./common-manifest-agent-gateway_pb.js');
goog.object.extend(proto, common$manifest$agent$gateway_pb);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.Manifest', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.Manifest.Service', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.manifest.v1.ManifestRequest', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.ManifestRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.ManifestRequest.displayName = 'proto.ru.sbertroika.common.manifest.v1.ManifestRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.Manifest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.Manifest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.Manifest.displayName = 'proto.ru.sbertroika.common.manifest.v1.Manifest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.manifest.v1.Manifest.Service, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.manifest.v1.Manifest.Service.displayName = 'proto.ru.sbertroika.common.manifest.v1.Manifest.Service';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.ManifestRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    projectid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    startdate: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest}
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.ManifestRequest;
  return proto.ru.sbertroika.common.manifest.v1.ManifestRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest}
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setProjectid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setStartdate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.ManifestRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getProjectid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string projectId = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.getProjectid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.setProjectid = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string startDate = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.getStartdate = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.setStartdate = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.clearStartdate = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.ManifestRequest.prototype.hasStartdate = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.Manifest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.Manifest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    version: jspb.Message.getFieldWithDefault(msg, 2, 0),
    validfrom: (f = msg.getValidfrom()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    validtill: (f = msg.getValidtill()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    projectindex: jspb.Message.getFieldWithDefault(msg, 5, 0),
    service: (f = msg.getService()) && proto.ru.sbertroika.common.manifest.v1.Manifest.Service.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.Manifest;
  return proto.ru.sbertroika.common.manifest.v1.Manifest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.Manifest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 3:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setValidfrom(value);
      break;
    case 4:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setValidtill(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setProjectindex(value);
      break;
    case 6:
      var value = new proto.ru.sbertroika.common.manifest.v1.Manifest.Service;
      reader.readMessage(value,proto.ru.sbertroika.common.manifest.v1.Manifest.Service.deserializeBinaryFromReader);
      msg.setService(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.Manifest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.Manifest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getValidfrom();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getValidtill();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getService();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.ru.sbertroika.common.manifest.v1.Manifest.Service.serializeBinaryToWriter
    );
  }
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.manifest.v1.Manifest.Service.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.toObject = function(includeInstance, msg) {
  var f, obj = {
    pasiv: (f = msg.getPasiv()) && common$manifest$pasiv_pb.ManifestPasiv.toObject(includeInstance, f),
    pro: (f = msg.getPro()) && common$manifest$pro_pb.ManifestPro.toObject(includeInstance, f),
    tms: (f = msg.getTms()) && common$manifest$tms_pb.ManifestTms.toObject(includeInstance, f),
    agentgate: (f = msg.getAgentgate()) && common$manifest$agent$gateway_pb.ManifestAgentGate.toObject(includeInstance, f),
    proemv: (f = msg.getProemv()) && common$manifest$pro_pb.ManifestProEmv.toObject(includeInstance, f),
    procash: (f = msg.getProcash()) && common$manifest$pro_pb.ManifestProCash.toObject(includeInstance, f),
    protroika: (f = msg.getProtroika()) && common$manifest$pro_pb.ManifestProTroika.toObject(includeInstance, f),
    proabt: (f = msg.getProabt()) && common$manifest$pro_pb.ManifestProAbt.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.manifest.v1.Manifest.Service;
  return proto.ru.sbertroika.common.manifest.v1.Manifest.Service.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common$manifest$pasiv_pb.ManifestPasiv;
      reader.readMessage(value,common$manifest$pasiv_pb.ManifestPasiv.deserializeBinaryFromReader);
      msg.setPasiv(value);
      break;
    case 2:
      var value = new common$manifest$pro_pb.ManifestPro;
      reader.readMessage(value,common$manifest$pro_pb.ManifestPro.deserializeBinaryFromReader);
      msg.setPro(value);
      break;
    case 3:
      var value = new common$manifest$tms_pb.ManifestTms;
      reader.readMessage(value,common$manifest$tms_pb.ManifestTms.deserializeBinaryFromReader);
      msg.setTms(value);
      break;
    case 4:
      var value = new common$manifest$agent$gateway_pb.ManifestAgentGate;
      reader.readMessage(value,common$manifest$agent$gateway_pb.ManifestAgentGate.deserializeBinaryFromReader);
      msg.setAgentgate(value);
      break;
    case 101:
      var value = new common$manifest$pro_pb.ManifestProEmv;
      reader.readMessage(value,common$manifest$pro_pb.ManifestProEmv.deserializeBinaryFromReader);
      msg.setProemv(value);
      break;
    case 102:
      var value = new common$manifest$pro_pb.ManifestProCash;
      reader.readMessage(value,common$manifest$pro_pb.ManifestProCash.deserializeBinaryFromReader);
      msg.setProcash(value);
      break;
    case 103:
      var value = new common$manifest$pro_pb.ManifestProTroika;
      reader.readMessage(value,common$manifest$pro_pb.ManifestProTroika.deserializeBinaryFromReader);
      msg.setProtroika(value);
      break;
    case 104:
      var value = new common$manifest$pro_pb.ManifestProAbt;
      reader.readMessage(value,common$manifest$pro_pb.ManifestProAbt.deserializeBinaryFromReader);
      msg.setProabt(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.manifest.v1.Manifest.Service.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPasiv();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common$manifest$pasiv_pb.ManifestPasiv.serializeBinaryToWriter
    );
  }
  f = message.getPro();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      common$manifest$pro_pb.ManifestPro.serializeBinaryToWriter
    );
  }
  f = message.getTms();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      common$manifest$tms_pb.ManifestTms.serializeBinaryToWriter
    );
  }
  f = message.getAgentgate();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      common$manifest$agent$gateway_pb.ManifestAgentGate.serializeBinaryToWriter
    );
  }
  f = message.getProemv();
  if (f != null) {
    writer.writeMessage(
      101,
      f,
      common$manifest$pro_pb.ManifestProEmv.serializeBinaryToWriter
    );
  }
  f = message.getProcash();
  if (f != null) {
    writer.writeMessage(
      102,
      f,
      common$manifest$pro_pb.ManifestProCash.serializeBinaryToWriter
    );
  }
  f = message.getProtroika();
  if (f != null) {
    writer.writeMessage(
      103,
      f,
      common$manifest$pro_pb.ManifestProTroika.serializeBinaryToWriter
    );
  }
  f = message.getProabt();
  if (f != null) {
    writer.writeMessage(
      104,
      f,
      common$manifest$pro_pb.ManifestProAbt.serializeBinaryToWriter
    );
  }
};


/**
 * optional pasiv.ManifestPasiv pasiv = 1;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getPasiv = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} */ (
    jspb.Message.getWrapperField(this, common$manifest$pasiv_pb.ManifestPasiv, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setPasiv = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearPasiv = function() {
  return this.setPasiv(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasPasiv = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional pro.ManifestPro pro = 2;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getPro = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro} */ (
    jspb.Message.getWrapperField(this, common$manifest$pro_pb.ManifestPro, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestPro|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setPro = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearPro = function() {
  return this.setPro(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasPro = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional tms.ManifestTms tms = 3;
 * @return {?proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getTms = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms} */ (
    jspb.Message.getWrapperField(this, common$manifest$tms_pb.ManifestTms, 3));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.tms.ManifestTms|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setTms = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearTms = function() {
  return this.setTms(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasTms = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional agent.gateway.ManifestAgentGate agentGate = 4;
 * @return {?proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getAgentgate = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate} */ (
    jspb.Message.getWrapperField(this, common$manifest$agent$gateway_pb.ManifestAgentGate, 4));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.agent.gateway.ManifestAgentGate|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setAgentgate = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearAgentgate = function() {
  return this.setAgentgate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasAgentgate = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional pro.ManifestProEmv proEmv = 101;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getProemv = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv} */ (
    jspb.Message.getWrapperField(this, common$manifest$pro_pb.ManifestProEmv, 101));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProEmv|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setProemv = function(value) {
  return jspb.Message.setWrapperField(this, 101, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearProemv = function() {
  return this.setProemv(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasProemv = function() {
  return jspb.Message.getField(this, 101) != null;
};


/**
 * optional pro.ManifestProCash proCash = 102;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getProcash = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash} */ (
    jspb.Message.getWrapperField(this, common$manifest$pro_pb.ManifestProCash, 102));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProCash|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setProcash = function(value) {
  return jspb.Message.setWrapperField(this, 102, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearProcash = function() {
  return this.setProcash(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasProcash = function() {
  return jspb.Message.getField(this, 102) != null;
};


/**
 * optional pro.ManifestProTroika proTroika = 103;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getProtroika = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika} */ (
    jspb.Message.getWrapperField(this, common$manifest$pro_pb.ManifestProTroika, 103));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProTroika|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setProtroika = function(value) {
  return jspb.Message.setWrapperField(this, 103, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearProtroika = function() {
  return this.setProtroika(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasProtroika = function() {
  return jspb.Message.getField(this, 103) != null;
};


/**
 * optional pro.ManifestProAbt proAbt = 104;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.getProabt = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt} */ (
    jspb.Message.getWrapperField(this, common$manifest$pro_pb.ManifestProAbt, 104));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pro.ManifestProAbt|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.setProabt = function(value) {
  return jspb.Message.setWrapperField(this, 104, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest.Service} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.clearProabt = function() {
  return this.setProabt(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.Service.prototype.hasProabt = function() {
  return jspb.Message.getField(this, 104) != null;
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 version = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional google.protobuf.Timestamp validFrom = 3;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.getValidfrom = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 3));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.setValidfrom = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.clearValidfrom = function() {
  return this.setValidfrom(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.hasValidfrom = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional google.protobuf.Timestamp validTill = 4;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.getValidtill = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.setValidtill = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.clearValidtill = function() {
  return this.setValidtill(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.hasValidtill = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional uint32 projectIndex = 5;
 * @return {number}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.getProjectindex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.setProjectindex = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.clearProjectindex = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.hasProjectindex = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional Service service = 6;
 * @return {?proto.ru.sbertroika.common.manifest.v1.Manifest.Service}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.getService = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.Manifest.Service} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.manifest.v1.Manifest.Service, 6));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.Manifest.Service|undefined} value
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
*/
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.setService = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.manifest.v1.Manifest} returns this
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.clearService = function() {
  return this.setService(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.manifest.v1.Manifest.prototype.hasService = function() {
  return jspb.Message.getField(this, 6) != null;
};


goog.object.extend(exports, proto.ru.sbertroika.common.manifest.v1);
