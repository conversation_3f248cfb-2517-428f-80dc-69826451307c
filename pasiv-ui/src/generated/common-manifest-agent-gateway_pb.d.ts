import * as jspb from 'google-protobuf'

import * as common$manifest$core_pb from './common-manifest-core_pb'; // proto import: "common-manifest-core.proto"


export class ManifestAgentGate extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestAgentGate;
  clearFeaturesList(): ManifestAgentGate;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestAgentGate.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestAgentGate): ManifestAgentGate.AsObject;
  static serializeBinaryToWriter(message: ManifestAgentG<PERSON>, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestAgentGate;
  static deserializeBinaryFromReader(message: ManifestAgentGate, reader: jspb.BinaryReader): ManifestAgentGate;
}

export namespace ManifestAgentGate {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
  }
}

