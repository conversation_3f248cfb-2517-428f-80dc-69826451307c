import * as jspb from 'google-protobuf'

import * as google_protobuf_timestamp_pb from 'google-protobuf/google/protobuf/timestamp_pb'; // proto import: "google/protobuf/timestamp.proto"
import * as common$manifest$core_pb from './common-manifest-core_pb'; // proto import: "common-manifest-core.proto"
import * as common$manifest$pro_pb from './common-manifest-pro_pb'; // proto import: "common-manifest-pro.proto"
import * as common$manifest$pasiv_pb from './common-manifest-pasiv_pb'; // proto import: "common-manifest-pasiv.proto"
import * as common$manifest$tms_pb from './common-manifest-tms_pb'; // proto import: "common-manifest-tms.proto"
import * as common$manifest$agent$gateway_pb from './common-manifest-agent-gateway_pb'; // proto import: "common-manifest-agent-gateway.proto"


export class ManifestRequest extends jspb.Message {
  getProjectid(): string;
  setProjectid(value: string): ManifestRequest;

  getStartdate(): string;
  setStartdate(value: string): ManifestRequest;
  hasStartdate(): boolean;
  clearStartdate(): ManifestRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestRequest): ManifestRequest.AsObject;
  static serializeBinaryToWriter(message: ManifestRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestRequest;
  static deserializeBinaryFromReader(message: ManifestRequest, reader: jspb.BinaryReader): ManifestRequest;
}

export namespace ManifestRequest {
  export type AsObject = {
    projectid: string,
    startdate?: string,
  }

  export enum StartdateCase { 
    _STARTDATE_NOT_SET = 0,
    STARTDATE = 2,
  }
}

export class Manifest extends jspb.Message {
  getId(): string;
  setId(value: string): Manifest;

  getVersion(): number;
  setVersion(value: number): Manifest;

  getValidfrom(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setValidfrom(value?: google_protobuf_timestamp_pb.Timestamp): Manifest;
  hasValidfrom(): boolean;
  clearValidfrom(): Manifest;

  getValidtill(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setValidtill(value?: google_protobuf_timestamp_pb.Timestamp): Manifest;
  hasValidtill(): boolean;
  clearValidtill(): Manifest;

  getProjectindex(): number;
  setProjectindex(value: number): Manifest;
  hasProjectindex(): boolean;
  clearProjectindex(): Manifest;

  getService(): Manifest.Service | undefined;
  setService(value?: Manifest.Service): Manifest;
  hasService(): boolean;
  clearService(): Manifest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Manifest.AsObject;
  static toObject(includeInstance: boolean, msg: Manifest): Manifest.AsObject;
  static serializeBinaryToWriter(message: Manifest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Manifest;
  static deserializeBinaryFromReader(message: Manifest, reader: jspb.BinaryReader): Manifest;
}

export namespace Manifest {
  export type AsObject = {
    id: string,
    version: number,
    validfrom?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    validtill?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    projectindex?: number,
    service?: Manifest.Service.AsObject,
  }

  export class Service extends jspb.Message {
    getPasiv(): common$manifest$pasiv_pb.ManifestPasiv | undefined;
    setPasiv(value?: common$manifest$pasiv_pb.ManifestPasiv): Service;
    hasPasiv(): boolean;
    clearPasiv(): Service;

    getPro(): common$manifest$pro_pb.ManifestPro | undefined;
    setPro(value?: common$manifest$pro_pb.ManifestPro): Service;
    hasPro(): boolean;
    clearPro(): Service;

    getTms(): common$manifest$tms_pb.ManifestTms | undefined;
    setTms(value?: common$manifest$tms_pb.ManifestTms): Service;
    hasTms(): boolean;
    clearTms(): Service;

    getAgentgate(): common$manifest$agent$gateway_pb.ManifestAgentGate | undefined;
    setAgentgate(value?: common$manifest$agent$gateway_pb.ManifestAgentGate): Service;
    hasAgentgate(): boolean;
    clearAgentgate(): Service;

    getProemv(): common$manifest$pro_pb.ManifestProEmv | undefined;
    setProemv(value?: common$manifest$pro_pb.ManifestProEmv): Service;
    hasProemv(): boolean;
    clearProemv(): Service;

    getProcash(): common$manifest$pro_pb.ManifestProCash | undefined;
    setProcash(value?: common$manifest$pro_pb.ManifestProCash): Service;
    hasProcash(): boolean;
    clearProcash(): Service;

    getProtroika(): common$manifest$pro_pb.ManifestProTroika | undefined;
    setProtroika(value?: common$manifest$pro_pb.ManifestProTroika): Service;
    hasProtroika(): boolean;
    clearProtroika(): Service;

    getProabt(): common$manifest$pro_pb.ManifestProAbt | undefined;
    setProabt(value?: common$manifest$pro_pb.ManifestProAbt): Service;
    hasProabt(): boolean;
    clearProabt(): Service;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Service.AsObject;
    static toObject(includeInstance: boolean, msg: Service): Service.AsObject;
    static serializeBinaryToWriter(message: Service, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Service;
    static deserializeBinaryFromReader(message: Service, reader: jspb.BinaryReader): Service;
  }

  export namespace Service {
    export type AsObject = {
      pasiv?: common$manifest$pasiv_pb.ManifestPasiv.AsObject,
      pro?: common$manifest$pro_pb.ManifestPro.AsObject,
      tms?: common$manifest$tms_pb.ManifestTms.AsObject,
      agentgate?: common$manifest$agent$gateway_pb.ManifestAgentGate.AsObject,
      proemv?: common$manifest$pro_pb.ManifestProEmv.AsObject,
      procash?: common$manifest$pro_pb.ManifestProCash.AsObject,
      protroika?: common$manifest$pro_pb.ManifestProTroika.AsObject,
      proabt?: common$manifest$pro_pb.ManifestProAbt.AsObject,
    }

    export enum PasivCase { 
      _PASIV_NOT_SET = 0,
      PASIV = 1,
    }

    export enum ProCase { 
      _PRO_NOT_SET = 0,
      PRO = 2,
    }

    export enum TmsCase { 
      _TMS_NOT_SET = 0,
      TMS = 3,
    }

    export enum AgentgateCase { 
      _AGENTGATE_NOT_SET = 0,
      AGENTGATE = 4,
    }

    export enum ProemvCase { 
      _PROEMV_NOT_SET = 0,
      PROEMV = 101,
    }

    export enum ProcashCase { 
      _PROCASH_NOT_SET = 0,
      PROCASH = 102,
    }

    export enum ProtroikaCase { 
      _PROTROIKA_NOT_SET = 0,
      PROTROIKA = 103,
    }

    export enum ProabtCase { 
      _PROABT_NOT_SET = 0,
      PROABT = 104,
    }
  }


  export enum ValidfromCase { 
    _VALIDFROM_NOT_SET = 0,
    VALIDFROM = 3,
  }

  export enum ValidtillCase { 
    _VALIDTILL_NOT_SET = 0,
    VALIDTILL = 4,
  }

  export enum ProjectindexCase { 
    _PROJECTINDEX_NOT_SET = 0,
    PROJECTINDEX = 5,
  }
}

