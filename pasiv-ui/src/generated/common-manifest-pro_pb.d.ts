import * as jspb from 'google-protobuf'

import * as google_protobuf_timestamp_pb from 'google-protobuf/google/protobuf/timestamp_pb'; // proto import: "google/protobuf/timestamp.proto"
import * as common_pb from './common_pb'; // proto import: "common.proto"
import * as common$manifest$core_pb from './common-manifest-core_pb'; // proto import: "common-manifest-core.proto"


export class ManifestProDict extends jspb.Message {
  getStationList(): Array<Station>;
  setStationList(value: Array<Station>): ManifestProDict;
  clearStationList(): ManifestProDict;
  addStation(value?: Station, index?: number): Station;

  getRouteList(): Array<Route>;
  setRouteList(value: Array<Route>): ManifestProDict;
  clearRouteList(): ManifestProDict;
  addRoute(value?: Route, index?: number): Route;

  getTransportList(): Array<Transport>;
  setTransportList(value: Array<Transport>): ManifestProDict;
  clearTransportList(): ManifestProDict;
  addTransport(value?: Transport, index?: number): Transport;

  getProductList(): Array<Product>;
  setProductList(value: Array<Product>): ManifestProDict;
  clearProductList(): ManifestProDict;
  addProduct(value?: Product, index?: number): Product;

  getTariffList(): Array<Tariff>;
  setTariffList(value: Array<Tariff>): ManifestProDict;
  clearTariffList(): ManifestProDict;
  addTariff(value?: Tariff, index?: number): Tariff;

  getMenuList(): Array<ProductMenu>;
  setMenuList(value: Array<ProductMenu>): ManifestProDict;
  clearMenuList(): ManifestProDict;
  addMenu(value?: ProductMenu, index?: number): ProductMenu;

  getEmployeeList(): Array<Employee>;
  setEmployeeList(value: Array<Employee>): ManifestProDict;
  clearEmployeeList(): ManifestProDict;
  addEmployee(value?: Employee, index?: number): Employee;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestProDict.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestProDict): ManifestProDict.AsObject;
  static serializeBinaryToWriter(message: ManifestProDict, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestProDict;
  static deserializeBinaryFromReader(message: ManifestProDict, reader: jspb.BinaryReader): ManifestProDict;
}

export namespace ManifestProDict {
  export type AsObject = {
    stationList: Array<Station.AsObject>,
    routeList: Array<Route.AsObject>,
    transportList: Array<Transport.AsObject>,
    productList: Array<Product.AsObject>,
    tariffList: Array<Tariff.AsObject>,
    menuList: Array<ProductMenu.AsObject>,
    employeeList: Array<Employee.AsObject>,
  }
}

export class ManifestPro extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestPro;
  clearFeaturesList(): ManifestPro;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  getDict(): ManifestProDict | undefined;
  setDict(value?: ManifestProDict): ManifestPro;
  hasDict(): boolean;
  clearDict(): ManifestPro;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestPro.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestPro): ManifestPro.AsObject;
  static serializeBinaryToWriter(message: ManifestPro, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestPro;
  static deserializeBinaryFromReader(message: ManifestPro, reader: jspb.BinaryReader): ManifestPro;
}

export namespace ManifestPro {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
    dict?: ManifestProDict.AsObject,
  }
}

export class Product extends jspb.Message {
  getId(): string;
  setId(value: string): Product;

  getName(): string;
  setName(value: string): Product;

  getVersion(): number;
  setVersion(value: number): Product;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Product.AsObject;
  static toObject(includeInstance: boolean, msg: Product): Product.AsObject;
  static serializeBinaryToWriter(message: Product, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Product;
  static deserializeBinaryFromReader(message: Product, reader: jspb.BinaryReader): Product;
}

export namespace Product {
  export type AsObject = {
    id: string,
    name: string,
    version: number,
  }
}

export class Station extends jspb.Message {
  getId(): string;
  setId(value: string): Station;

  getName(): string;
  setName(value: string): Station;

  getLat(): number;
  setLat(value: number): Station;

  getLon(): number;
  setLon(value: number): Station;

  getVersion(): number;
  setVersion(value: number): Station;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Station.AsObject;
  static toObject(includeInstance: boolean, msg: Station): Station.AsObject;
  static serializeBinaryToWriter(message: Station, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Station;
  static deserializeBinaryFromReader(message: Station, reader: jspb.BinaryReader): Station;
}

export namespace Station {
  export type AsObject = {
    id: string,
    name: string,
    lat: number,
    lon: number,
    version: number,
  }
}

export class RouteStation extends jspb.Message {
  getId(): string;
  setId(value: string): RouteStation;

  getPos(): number;
  setPos(value: number): RouteStation;

  getVersion(): number;
  setVersion(value: number): RouteStation;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RouteStation.AsObject;
  static toObject(includeInstance: boolean, msg: RouteStation): RouteStation.AsObject;
  static serializeBinaryToWriter(message: RouteStation, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RouteStation;
  static deserializeBinaryFromReader(message: RouteStation, reader: jspb.BinaryReader): RouteStation;
}

export namespace RouteStation {
  export type AsObject = {
    id: string,
    pos: number,
    version: number,
  }
}

export class Transport extends jspb.Message {
  getId(): string;
  setId(value: string): Transport;

  getNumber(): string;
  setNumber(value: string): Transport;

  getType(): common_pb.TransportType;
  setType(value: common_pb.TransportType): Transport;

  getConstraintList(): Array<common$manifest$core_pb.Constraint>;
  setConstraintList(value: Array<common$manifest$core_pb.Constraint>): Transport;
  clearConstraintList(): Transport;
  addConstraint(value?: common$manifest$core_pb.Constraint, index?: number): common$manifest$core_pb.Constraint;

  getVersion(): number;
  setVersion(value: number): Transport;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Transport.AsObject;
  static toObject(includeInstance: boolean, msg: Transport): Transport.AsObject;
  static serializeBinaryToWriter(message: Transport, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Transport;
  static deserializeBinaryFromReader(message: Transport, reader: jspb.BinaryReader): Transport;
}

export namespace Transport {
  export type AsObject = {
    id: string,
    number: string,
    type: common_pb.TransportType,
    constraintList: Array<common$manifest$core_pb.Constraint.AsObject>,
    version: number,
  }
}

export class DispatchingRouteOrganization extends jspb.Message {
  getId(): string;
  setId(value: string): DispatchingRouteOrganization;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DispatchingRouteOrganization.AsObject;
  static toObject(includeInstance: boolean, msg: DispatchingRouteOrganization): DispatchingRouteOrganization.AsObject;
  static serializeBinaryToWriter(message: DispatchingRouteOrganization, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DispatchingRouteOrganization;
  static deserializeBinaryFromReader(message: DispatchingRouteOrganization, reader: jspb.BinaryReader): DispatchingRouteOrganization;
}

export namespace DispatchingRouteOrganization {
  export type AsObject = {
    id: string,
  }
}

export class Route extends jspb.Message {
  getId(): string;
  setId(value: string): Route;

  getName(): string;
  setName(value: string): Route;

  getScheme(): common_pb.RouteScheme;
  setScheme(value: common_pb.RouteScheme): Route;

  getStationList(): Array<RouteStation>;
  setStationList(value: Array<RouteStation>): Route;
  clearStationList(): Route;
  addStation(value?: RouteStation, index?: number): RouteStation;

  getConstraintList(): Array<common$manifest$core_pb.Constraint>;
  setConstraintList(value: Array<common$manifest$core_pb.Constraint>): Route;
  clearConstraintList(): Route;
  addConstraint(value?: common$manifest$core_pb.Constraint, index?: number): common$manifest$core_pb.Constraint;

  getRouteindex(): number;
  setRouteindex(value: number): Route;

  getNumber(): string;
  setNumber(value: string): Route;

  getDispatchingorganizationList(): Array<DispatchingRouteOrganization>;
  setDispatchingorganizationList(value: Array<DispatchingRouteOrganization>): Route;
  clearDispatchingorganizationList(): Route;
  addDispatchingorganization(value?: DispatchingRouteOrganization, index?: number): DispatchingRouteOrganization;

  getVersion(): number;
  setVersion(value: number): Route;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Route.AsObject;
  static toObject(includeInstance: boolean, msg: Route): Route.AsObject;
  static serializeBinaryToWriter(message: Route, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Route;
  static deserializeBinaryFromReader(message: Route, reader: jspb.BinaryReader): Route;
}

export namespace Route {
  export type AsObject = {
    id: string,
    name: string,
    scheme: common_pb.RouteScheme,
    stationList: Array<RouteStation.AsObject>,
    constraintList: Array<common$manifest$core_pb.Constraint.AsObject>,
    routeindex: number,
    number: string,
    dispatchingorganizationList: Array<DispatchingRouteOrganization.AsObject>,
    version: number,
  }
}

export class Tariff extends jspb.Message {
  getId(): string;
  setId(value: string): Tariff;

  getName(): string;
  setName(value: string): Tariff;

  getConstraintList(): Array<common$manifest$core_pb.Constraint>;
  setConstraintList(value: Array<common$manifest$core_pb.Constraint>): Tariff;
  clearConstraintList(): Tariff;
  addConstraint(value?: common$manifest$core_pb.Constraint, index?: number): common$manifest$core_pb.Constraint;

  getVersion(): number;
  setVersion(value: number): Tariff;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Tariff.AsObject;
  static toObject(includeInstance: boolean, msg: Tariff): Tariff.AsObject;
  static serializeBinaryToWriter(message: Tariff, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Tariff;
  static deserializeBinaryFromReader(message: Tariff, reader: jspb.BinaryReader): Tariff;
}

export namespace Tariff {
  export type AsObject = {
    id: string,
    name: string,
    constraintList: Array<common$manifest$core_pb.Constraint.AsObject>,
    version: number,
  }
}

export class PriceRuleMatrixItem extends jspb.Message {
  getStationfrom(): string;
  setStationfrom(value: string): PriceRuleMatrixItem;

  getStationto(): string;
  setStationto(value: string): PriceRuleMatrixItem;

  getPrice(): number;
  setPrice(value: number): PriceRuleMatrixItem;

  getVersion(): number;
  setVersion(value: number): PriceRuleMatrixItem;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PriceRuleMatrixItem.AsObject;
  static toObject(includeInstance: boolean, msg: PriceRuleMatrixItem): PriceRuleMatrixItem.AsObject;
  static serializeBinaryToWriter(message: PriceRuleMatrixItem, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PriceRuleMatrixItem;
  static deserializeBinaryFromReader(message: PriceRuleMatrixItem, reader: jspb.BinaryReader): PriceRuleMatrixItem;
}

export namespace PriceRuleMatrixItem {
  export type AsObject = {
    stationfrom: string,
    stationto: string,
    price: number,
    version: number,
  }
}

export class PriceRule extends jspb.Message {
  getPaymenttype(): PriceRule.TPaymentType;
  setPaymenttype(value: PriceRule.TPaymentType): PriceRule;

  getPrice(): number;
  setPrice(value: number): PriceRule;

  getMatrixList(): Array<PriceRuleMatrixItem>;
  setMatrixList(value: Array<PriceRuleMatrixItem>): PriceRule;
  clearMatrixList(): PriceRule;
  addMatrix(value?: PriceRuleMatrixItem, index?: number): PriceRuleMatrixItem;

  getVersion(): number;
  setVersion(value: number): PriceRule;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PriceRule.AsObject;
  static toObject(includeInstance: boolean, msg: PriceRule): PriceRule.AsObject;
  static serializeBinaryToWriter(message: PriceRule, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PriceRule;
  static deserializeBinaryFromReader(message: PriceRule, reader: jspb.BinaryReader): PriceRule;
}

export namespace PriceRule {
  export type AsObject = {
    paymenttype: PriceRule.TPaymentType,
    price: number,
    matrixList: Array<PriceRuleMatrixItem.AsObject>,
    version: number,
  }

  export enum TPaymentType { 
    CASH = 0,
    EMV = 1,
    TROIKA_TICKET = 2,
    TROIKA_WALLET = 3,
    ABT_TICKET = 4,
    ABT_WALLET = 5,
    PROSTOR_TICKET = 6,
    QR_TICKET = 7,
    QR_WALLET = 8,
  }
}

export class ProductMenu extends jspb.Message {
  getProductid(): string;
  setProductid(value: string): ProductMenu;

  getTariffid(): string;
  setTariffid(value: string): ProductMenu;

  getPricerulesList(): Array<PriceRule>;
  setPricerulesList(value: Array<PriceRule>): ProductMenu;
  clearPricerulesList(): ProductMenu;
  addPricerules(value?: PriceRule, index?: number): PriceRule;

  getVersion(): number;
  setVersion(value: number): ProductMenu;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ProductMenu.AsObject;
  static toObject(includeInstance: boolean, msg: ProductMenu): ProductMenu.AsObject;
  static serializeBinaryToWriter(message: ProductMenu, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ProductMenu;
  static deserializeBinaryFromReader(message: ProductMenu, reader: jspb.BinaryReader): ProductMenu;
}

export namespace ProductMenu {
  export type AsObject = {
    productid: string,
    tariffid: string,
    pricerulesList: Array<PriceRule.AsObject>,
    version: number,
  }
}

export class Employee extends jspb.Message {
  getId(): string;
  setId(value: string): Employee;

  getRole(): string;
  setRole(value: string): Employee;

  getSurname(): string;
  setSurname(value: string): Employee;

  getName(): string;
  setName(value: string): Employee;

  getMiddlename(): string;
  setMiddlename(value: string): Employee;

  getVersion(): number;
  setVersion(value: number): Employee;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Employee.AsObject;
  static toObject(includeInstance: boolean, msg: Employee): Employee.AsObject;
  static serializeBinaryToWriter(message: Employee, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Employee;
  static deserializeBinaryFromReader(message: Employee, reader: jspb.BinaryReader): Employee;
}

export namespace Employee {
  export type AsObject = {
    id: string,
    role: string,
    surname: string,
    name: string,
    middlename: string,
    version: number,
  }
}

export class ManifestProEmv extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestProEmv;
  clearFeaturesList(): ManifestProEmv;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestProEmv.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestProEmv): ManifestProEmv.AsObject;
  static serializeBinaryToWriter(message: ManifestProEmv, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestProEmv;
  static deserializeBinaryFromReader(message: ManifestProEmv, reader: jspb.BinaryReader): ManifestProEmv;
}

export namespace ManifestProEmv {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
  }
}

export class ManifestProCash extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestProCash;
  clearFeaturesList(): ManifestProCash;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestProCash.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestProCash): ManifestProCash.AsObject;
  static serializeBinaryToWriter(message: ManifestProCash, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestProCash;
  static deserializeBinaryFromReader(message: ManifestProCash, reader: jspb.BinaryReader): ManifestProCash;
}

export namespace ManifestProCash {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
  }
}

export class ManifestProTroika extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestProTroika;
  clearFeaturesList(): ManifestProTroika;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  getDict(): ManifestProTroikaDict | undefined;
  setDict(value?: ManifestProTroikaDict): ManifestProTroika;
  hasDict(): boolean;
  clearDict(): ManifestProTroika;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestProTroika.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestProTroika): ManifestProTroika.AsObject;
  static serializeBinaryToWriter(message: ManifestProTroika, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestProTroika;
  static deserializeBinaryFromReader(message: ManifestProTroika, reader: jspb.BinaryReader): ManifestProTroika;
}

export namespace ManifestProTroika {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
    dict?: ManifestProTroikaDict.AsObject,
  }
}

export class ManifestProTroikaDict extends jspb.Message {
  getTemplatesList(): Array<TroikaTemplate>;
  setTemplatesList(value: Array<TroikaTemplate>): ManifestProTroikaDict;
  clearTemplatesList(): ManifestProTroikaDict;
  addTemplates(value?: TroikaTemplate, index?: number): TroikaTemplate;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestProTroikaDict.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestProTroikaDict): ManifestProTroikaDict.AsObject;
  static serializeBinaryToWriter(message: ManifestProTroikaDict, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestProTroikaDict;
  static deserializeBinaryFromReader(message: ManifestProTroikaDict, reader: jspb.BinaryReader): ManifestProTroikaDict;
}

export namespace ManifestProTroikaDict {
  export type AsObject = {
    templatesList: Array<TroikaTemplate.AsObject>,
  }
}

export class TroikaTemplate extends jspb.Message {
  getId(): string;
  setId(value: string): TroikaTemplate;

  getAppcode(): number;
  setAppcode(value: number): TroikaTemplate;

  getCrdcode(): number;
  setCrdcode(value: number): TroikaTemplate;

  getName(): string;
  setName(value: string): TroikaTemplate;

  getType(): common_pb.AbonementType;
  setType(value: common_pb.AbonementType): TroikaTemplate;

  getLimit(): number;
  setLimit(value: number): TroikaTemplate;

  getProlongtype(): common_pb.ProlongType;
  setProlongtype(value: common_pb.ProlongType): TroikaTemplate;

  getProlongstartdate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setProlongstartdate(value?: google_protobuf_timestamp_pb.Timestamp): TroikaTemplate;
  hasProlongstartdate(): boolean;
  clearProlongstartdate(): TroikaTemplate;

  getProlongenddate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setProlongenddate(value?: google_protobuf_timestamp_pb.Timestamp): TroikaTemplate;
  hasProlongenddate(): boolean;
  clearProlongenddate(): TroikaTemplate;

  getProlongdays(): number;
  setProlongdays(value: number): TroikaTemplate;

  getProlongcardcode(): number;
  setProlongcardcode(value: number): TroikaTemplate;

  getVersion(): number;
  setVersion(value: number): TroikaTemplate;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): TroikaTemplate.AsObject;
  static toObject(includeInstance: boolean, msg: TroikaTemplate): TroikaTemplate.AsObject;
  static serializeBinaryToWriter(message: TroikaTemplate, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): TroikaTemplate;
  static deserializeBinaryFromReader(message: TroikaTemplate, reader: jspb.BinaryReader): TroikaTemplate;
}

export namespace TroikaTemplate {
  export type AsObject = {
    id: string,
    appcode: number,
    crdcode: number,
    name: string,
    type: common_pb.AbonementType,
    limit: number,
    prolongtype: common_pb.ProlongType,
    prolongstartdate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    prolongenddate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    prolongdays: number,
    prolongcardcode: number,
    version: number,
  }
}

export class ManifestProAbt extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestProAbt;
  clearFeaturesList(): ManifestProAbt;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  getDict(): ManifestProAbtDict | undefined;
  setDict(value?: ManifestProAbtDict): ManifestProAbt;
  hasDict(): boolean;
  clearDict(): ManifestProAbt;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestProAbt.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestProAbt): ManifestProAbt.AsObject;
  static serializeBinaryToWriter(message: ManifestProAbt, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestProAbt;
  static deserializeBinaryFromReader(message: ManifestProAbt, reader: jspb.BinaryReader): ManifestProAbt;
}

export namespace ManifestProAbt {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
    dict?: ManifestProAbtDict.AsObject,
  }
}

export class ManifestProAbtDict extends jspb.Message {
  getTemplateList(): Array<SubscriptionTemplate>;
  setTemplateList(value: Array<SubscriptionTemplate>): ManifestProAbtDict;
  clearTemplateList(): ManifestProAbtDict;
  addTemplate(value?: SubscriptionTemplate, index?: number): SubscriptionTemplate;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestProAbtDict.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestProAbtDict): ManifestProAbtDict.AsObject;
  static serializeBinaryToWriter(message: ManifestProAbtDict, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestProAbtDict;
  static deserializeBinaryFromReader(message: ManifestProAbtDict, reader: jspb.BinaryReader): ManifestProAbtDict;
}

export namespace ManifestProAbtDict {
  export type AsObject = {
    templateList: Array<SubscriptionTemplate.AsObject>,
  }
}

export class SubscriptionTemplate extends jspb.Message {
  getId(): string;
  setId(value: string): SubscriptionTemplate;

  getVersion(): number;
  setVersion(value: number): SubscriptionTemplate;

  getAppcode(): number;
  setAppcode(value: number): SubscriptionTemplate;

  getCrdcode(): number;
  setCrdcode(value: number): SubscriptionTemplate;

  getName(): string;
  setName(value: string): SubscriptionTemplate;

  getType(): common_pb.AbonementType;
  setType(value: common_pb.AbonementType): SubscriptionTemplate;

  getRulesList(): Array<SubscriptionTemplatePassRule>;
  setRulesList(value: Array<SubscriptionTemplatePassRule>): SubscriptionTemplate;
  clearRulesList(): SubscriptionTemplate;
  addRules(value?: SubscriptionTemplatePassRule, index?: number): SubscriptionTemplatePassRule;

  getIssocial(): boolean;
  setIssocial(value: boolean): SubscriptionTemplate;

  getCounterList(): Array<SubscriptionTemplateCounter>;
  setCounterList(value: Array<SubscriptionTemplateCounter>): SubscriptionTemplate;
  clearCounterList(): SubscriptionTemplate;
  addCounter(value?: SubscriptionTemplateCounter, index?: number): SubscriptionTemplateCounter;

  getCardtypeList(): Array<AbtCardType>;
  setCardtypeList(value: Array<AbtCardType>): SubscriptionTemplate;
  clearCardtypeList(): SubscriptionTemplate;
  addCardtype(value?: AbtCardType, index?: number): AbtCardType;

  getValidtimetype(): ValidTimeType;
  setValidtimetype(value: ValidTimeType): SubscriptionTemplate;

  getValidtimestart(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setValidtimestart(value?: google_protobuf_timestamp_pb.Timestamp): SubscriptionTemplate;
  hasValidtimestart(): boolean;
  clearValidtimestart(): SubscriptionTemplate;

  getValidtimedays(): number;
  setValidtimedays(value: number): SubscriptionTemplate;
  hasValidtimedays(): boolean;
  clearValidtimedays(): SubscriptionTemplate;

  getValidtimeend(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setValidtimeend(value?: google_protobuf_timestamp_pb.Timestamp): SubscriptionTemplate;
  hasValidtimeend(): boolean;
  clearValidtimeend(): SubscriptionTemplate;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SubscriptionTemplate.AsObject;
  static toObject(includeInstance: boolean, msg: SubscriptionTemplate): SubscriptionTemplate.AsObject;
  static serializeBinaryToWriter(message: SubscriptionTemplate, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SubscriptionTemplate;
  static deserializeBinaryFromReader(message: SubscriptionTemplate, reader: jspb.BinaryReader): SubscriptionTemplate;
}

export namespace SubscriptionTemplate {
  export type AsObject = {
    id: string,
    version: number,
    appcode: number,
    crdcode: number,
    name: string,
    type: common_pb.AbonementType,
    rulesList: Array<SubscriptionTemplatePassRule.AsObject>,
    issocial: boolean,
    counterList: Array<SubscriptionTemplateCounter.AsObject>,
    cardtypeList: Array<AbtCardType.AsObject>,
    validtimetype: ValidTimeType,
    validtimestart?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    validtimedays?: number,
    validtimeend?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum ValidtimestartCase { 
    _VALIDTIMESTART_NOT_SET = 0,
    VALIDTIMESTART = 12,
  }

  export enum ValidtimedaysCase { 
    _VALIDTIMEDAYS_NOT_SET = 0,
    VALIDTIMEDAYS = 13,
  }

  export enum ValidtimeendCase { 
    _VALIDTIMEEND_NOT_SET = 0,
    VALIDTIMEEND = 14,
  }
}

export class SubscriptionTemplatePassRule extends jspb.Message {
  getId(): string;
  setId(value: string): SubscriptionTemplatePassRule;

  getVersion(): number;
  setVersion(value: number): SubscriptionTemplatePassRule;

  getIndex(): number;
  setIndex(value: number): SubscriptionTemplatePassRule;

  getAction(): string;
  setAction(value: string): SubscriptionTemplatePassRule;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SubscriptionTemplatePassRule.AsObject;
  static toObject(includeInstance: boolean, msg: SubscriptionTemplatePassRule): SubscriptionTemplatePassRule.AsObject;
  static serializeBinaryToWriter(message: SubscriptionTemplatePassRule, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SubscriptionTemplatePassRule;
  static deserializeBinaryFromReader(message: SubscriptionTemplatePassRule, reader: jspb.BinaryReader): SubscriptionTemplatePassRule;
}

export namespace SubscriptionTemplatePassRule {
  export type AsObject = {
    id: string,
    version: number,
    index: number,
    action: string,
  }
}

export class SubscriptionTemplateCounter extends jspb.Message {
  getId(): string;
  setId(value: string): SubscriptionTemplateCounter;

  getVersion(): number;
  setVersion(value: number): SubscriptionTemplateCounter;

  getType(): common_pb.SubscriptionCounterType;
  setType(value: common_pb.SubscriptionCounterType): SubscriptionTemplateCounter;

  getValue(): number;
  setValue(value: number): SubscriptionTemplateCounter;

  getIsbus(): boolean;
  setIsbus(value: boolean): SubscriptionTemplateCounter;

  getIstrolleybus(): boolean;
  setIstrolleybus(value: boolean): SubscriptionTemplateCounter;

  getIstram(): boolean;
  setIstram(value: boolean): SubscriptionTemplateCounter;

  getIsmetro(): boolean;
  setIsmetro(value: boolean): SubscriptionTemplateCounter;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SubscriptionTemplateCounter.AsObject;
  static toObject(includeInstance: boolean, msg: SubscriptionTemplateCounter): SubscriptionTemplateCounter.AsObject;
  static serializeBinaryToWriter(message: SubscriptionTemplateCounter, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SubscriptionTemplateCounter;
  static deserializeBinaryFromReader(message: SubscriptionTemplateCounter, reader: jspb.BinaryReader): SubscriptionTemplateCounter;
}

export namespace SubscriptionTemplateCounter {
  export type AsObject = {
    id: string,
    version: number,
    type: common_pb.SubscriptionCounterType,
    value: number,
    isbus: boolean,
    istrolleybus: boolean,
    istram: boolean,
    ismetro: boolean,
  }
}

export class AbtCardType extends jspb.Message {
  getId(): string;
  setId(value: string): AbtCardType;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AbtCardType.AsObject;
  static toObject(includeInstance: boolean, msg: AbtCardType): AbtCardType.AsObject;
  static serializeBinaryToWriter(message: AbtCardType, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AbtCardType;
  static deserializeBinaryFromReader(message: AbtCardType, reader: jspb.BinaryReader): AbtCardType;
}

export namespace AbtCardType {
  export type AsObject = {
    id: string,
  }
}

export enum ValidTimeType { 
  INTERVAL = 0,
  DAYS = 1,
  INTERVAL_AND_DAYS = 2,
}
