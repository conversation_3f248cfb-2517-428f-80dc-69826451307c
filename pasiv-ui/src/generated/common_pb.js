// source: common.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js');
goog.object.extend(proto, google_protobuf_empty_pb);
var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
goog.exportSymbol('proto.ru.sbertroika.common.v1.AbonementType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.AuthType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.ByIdRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.Card', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.ConstraintBaseRule', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.ConstraintType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.CreateResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.CreateResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.EmptyResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.EmptyResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.ErrorType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.Filter', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.History', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.HistoryChange', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.HistoryResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.HistoryResponse.ResponseCase', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.HistoryResult', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.ModelStatus', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.OperationError', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.OperationStatus', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.PaginationRequest', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.PaginationResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.PaymentAttribute', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.PaymentMethod', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.PaymentType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.Position', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.ProlongType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.RouteScheme', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.Sorted', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.SortedType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.SubscriptionCounterType', null, global);
goog.exportSymbol('proto.ru.sbertroika.common.v1.TransportType', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.OperationError = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.OperationError, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.OperationError.displayName = 'proto.ru.sbertroika.common.v1.OperationError';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.PaginationRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.PaginationRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.PaginationRequest.displayName = 'proto.ru.sbertroika.common.v1.PaginationRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.PaginationResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.PaginationResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.PaginationResponse.displayName = 'proto.ru.sbertroika.common.v1.PaginationResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.PaymentMethod = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.PaymentMethod, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.PaymentMethod.displayName = 'proto.ru.sbertroika.common.v1.PaymentMethod';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.Card = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.Card, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.Card.displayName = 'proto.ru.sbertroika.common.v1.Card';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.Sorted = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.Sorted, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.Sorted.displayName = 'proto.ru.sbertroika.common.v1.Sorted';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.Filter = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.Filter, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.Filter.displayName = 'proto.ru.sbertroika.common.v1.Filter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.Position = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.Position, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.Position.displayName = 'proto.ru.sbertroika.common.v1.Position';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.EmptyResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.common.v1.EmptyResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.common.v1.EmptyResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.EmptyResponse.displayName = 'proto.ru.sbertroika.common.v1.EmptyResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.CreateResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.common.v1.CreateResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.common.v1.CreateResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.CreateResponse.displayName = 'proto.ru.sbertroika.common.v1.CreateResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.HistoryChange = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.HistoryChange, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.HistoryChange.displayName = 'proto.ru.sbertroika.common.v1.HistoryChange';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.History = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.v1.History.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.History, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.History.displayName = 'proto.ru.sbertroika.common.v1.History';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.HistoryResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ru.sbertroika.common.v1.HistoryResult.repeatedFields_, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.HistoryResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.HistoryResult.displayName = 'proto.ru.sbertroika.common.v1.HistoryResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.HistoryResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.common.v1.HistoryResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.common.v1.HistoryResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.HistoryResponse.displayName = 'proto.ru.sbertroika.common.v1.HistoryResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.common.v1.ByIdRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.ru.sbertroika.common.v1.ByIdRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.common.v1.ByIdRequest.displayName = 'proto.ru.sbertroika.common.v1.ByIdRequest';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.OperationError.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.OperationError} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.OperationError.toObject = function(includeInstance, msg) {
  var f, obj = {
    type: jspb.Message.getFieldWithDefault(msg, 1, 0),
    message: jspb.Message.getFieldWithDefault(msg, 2, ""),
    code: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.common.v1.OperationError.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.OperationError;
  return proto.ru.sbertroika.common.v1.OperationError.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.OperationError} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.common.v1.OperationError.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.ru.sbertroika.common.v1.ErrorType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMessage(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.OperationError.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.OperationError} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.OperationError.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMessage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCode();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
};


/**
 * optional ErrorType type = 1;
 * @return {!proto.ru.sbertroika.common.v1.ErrorType}
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.ErrorType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.ErrorType} value
 * @return {!proto.ru.sbertroika.common.v1.OperationError} returns this
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string message = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.getMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.OperationError} returns this
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.setMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 code = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.getCode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.OperationError} returns this
 */
proto.ru.sbertroika.common.v1.OperationError.prototype.setCode = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.PaginationRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.PaginationRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.PaginationRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.PaginationRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    page: jspb.Message.getFieldWithDefault(msg, 1, 0),
    limit: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.common.v1.PaginationRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.PaginationRequest;
  return proto.ru.sbertroika.common.v1.PaginationRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.PaginationRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.PaginationRequest}
 */
proto.ru.sbertroika.common.v1.PaginationRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPage(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setLimit(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.PaginationRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.PaginationRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.PaginationRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.PaginationRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPage();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getLimit();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
};


/**
 * optional int32 page = 1;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.PaginationRequest.prototype.getPage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.PaginationRequest} returns this
 */
proto.ru.sbertroika.common.v1.PaginationRequest.prototype.setPage = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 limit = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.PaginationRequest.prototype.getLimit = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.PaginationRequest} returns this
 */
proto.ru.sbertroika.common.v1.PaginationRequest.prototype.setLimit = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.PaginationResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.PaginationResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.PaginationResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    page: jspb.Message.getFieldWithDefault(msg, 1, 0),
    limit: jspb.Message.getFieldWithDefault(msg, 2, 0),
    totalpage: jspb.Message.getFieldWithDefault(msg, 3, 0),
    totalcount: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.PaginationResponse;
  return proto.ru.sbertroika.common.v1.PaginationResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.PaginationResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPage(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setLimit(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTotalpage(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTotalcount(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.PaginationResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.PaginationResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.PaginationResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPage();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getLimit();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getTotalpage();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getTotalcount();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional int32 page = 1;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.getPage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.PaginationResponse} returns this
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.setPage = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 limit = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.getLimit = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.PaginationResponse} returns this
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.setLimit = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 totalPage = 3;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.getTotalpage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.PaginationResponse} returns this
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.setTotalpage = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 totalCount = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.getTotalcount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.PaginationResponse} returns this
 */
proto.ru.sbertroika.common.v1.PaginationResponse.prototype.setTotalcount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.PaymentMethod.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.PaymentMethod} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.PaymentMethod.toObject = function(includeInstance, msg) {
  var f, obj = {
    paymentid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    type: jspb.Message.getFieldWithDefault(msg, 2, 0),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    label: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.PaymentMethod}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.PaymentMethod;
  return proto.ru.sbertroika.common.v1.PaymentMethod.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.PaymentMethod} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.PaymentMethod}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPaymentid(value);
      break;
    case 2:
      var value = /** @type {!proto.ru.sbertroika.common.v1.PaymentType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setLabel(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.PaymentMethod.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.PaymentMethod} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.PaymentMethod.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPaymentid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getLabel();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string paymentId = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.getPaymentid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.PaymentMethod} returns this
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.setPaymentid = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional PaymentType type = 2;
 * @return {!proto.ru.sbertroika.common.v1.PaymentType}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.PaymentType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.PaymentType} value
 * @return {!proto.ru.sbertroika.common.v1.PaymentMethod} returns this
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional string name = 3;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.PaymentMethod} returns this
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string label = 4;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.getLabel = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.PaymentMethod} returns this
 */
proto.ru.sbertroika.common.v1.PaymentMethod.prototype.setLabel = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.Card.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.Card.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.Card} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Card.toObject = function(includeInstance, msg) {
  var f, obj = {
    cardnum: jspb.Message.getFieldWithDefault(msg, 1, ""),
    carddate: jspb.Message.getFieldWithDefault(msg, 2, ""),
    cardholder: jspb.Message.getFieldWithDefault(msg, 3, ""),
    cvc: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.Card}
 */
proto.ru.sbertroika.common.v1.Card.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.Card;
  return proto.ru.sbertroika.common.v1.Card.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.Card} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.Card}
 */
proto.ru.sbertroika.common.v1.Card.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCardnum(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCarddate(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setCardholder(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCvc(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.Card.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.Card.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.Card} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Card.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCardnum();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getCarddate();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCardholder();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getCvc();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional string cardNum = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.Card.prototype.getCardnum = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.Card} returns this
 */
proto.ru.sbertroika.common.v1.Card.prototype.setCardnum = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string cardDate = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.Card.prototype.getCarddate = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.Card} returns this
 */
proto.ru.sbertroika.common.v1.Card.prototype.setCarddate = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string cardHolder = 3;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.Card.prototype.getCardholder = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.Card} returns this
 */
proto.ru.sbertroika.common.v1.Card.prototype.setCardholder = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 cvc = 4;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.Card.prototype.getCvc = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.Card} returns this
 */
proto.ru.sbertroika.common.v1.Card.prototype.setCvc = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.Sorted.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.Sorted.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.Sorted} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Sorted.toObject = function(includeInstance, msg) {
  var f, obj = {
    column: jspb.Message.getFieldWithDefault(msg, 1, ""),
    type: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.Sorted}
 */
proto.ru.sbertroika.common.v1.Sorted.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.Sorted;
  return proto.ru.sbertroika.common.v1.Sorted.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.Sorted} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.Sorted}
 */
proto.ru.sbertroika.common.v1.Sorted.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setColumn(value);
      break;
    case 2:
      var value = /** @type {!proto.ru.sbertroika.common.v1.SortedType} */ (reader.readEnum());
      msg.setType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.Sorted.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.Sorted.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.Sorted} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Sorted.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getColumn();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
};


/**
 * optional string column = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.Sorted.prototype.getColumn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.Sorted} returns this
 */
proto.ru.sbertroika.common.v1.Sorted.prototype.setColumn = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional SortedType type = 2;
 * @return {!proto.ru.sbertroika.common.v1.SortedType}
 */
proto.ru.sbertroika.common.v1.Sorted.prototype.getType = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.SortedType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.SortedType} value
 * @return {!proto.ru.sbertroika.common.v1.Sorted} returns this
 */
proto.ru.sbertroika.common.v1.Sorted.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.Filter.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.Filter.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.Filter} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Filter.toObject = function(includeInstance, msg) {
  var f, obj = {
    column: jspb.Message.getFieldWithDefault(msg, 1, ""),
    value: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.Filter}
 */
proto.ru.sbertroika.common.v1.Filter.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.Filter;
  return proto.ru.sbertroika.common.v1.Filter.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.Filter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.Filter}
 */
proto.ru.sbertroika.common.v1.Filter.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setColumn(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.Filter.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.Filter.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.Filter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Filter.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getColumn();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getValue();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string column = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.Filter.prototype.getColumn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.Filter} returns this
 */
proto.ru.sbertroika.common.v1.Filter.prototype.setColumn = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string value = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.Filter.prototype.getValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.Filter} returns this
 */
proto.ru.sbertroika.common.v1.Filter.prototype.setValue = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.Position.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.Position.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.Position} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Position.toObject = function(includeInstance, msg) {
  var f, obj = {
    latitude: jspb.Message.getFloatingPointFieldWithDefault(msg, 1, 0.0),
    longitude: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.Position}
 */
proto.ru.sbertroika.common.v1.Position.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.Position;
  return proto.ru.sbertroika.common.v1.Position.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.Position} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.Position}
 */
proto.ru.sbertroika.common.v1.Position.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLatitude(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setLongitude(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.Position.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.Position.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.Position} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.Position.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLatitude();
  if (f !== 0.0) {
    writer.writeDouble(
      1,
      f
    );
  }
  f = message.getLongitude();
  if (f !== 0.0) {
    writer.writeDouble(
      2,
      f
    );
  }
};


/**
 * optional double latitude = 1;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.Position.prototype.getLatitude = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 1, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.Position} returns this
 */
proto.ru.sbertroika.common.v1.Position.prototype.setLatitude = function(value) {
  return jspb.Message.setProto3FloatField(this, 1, value);
};


/**
 * optional double longitude = 2;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.Position.prototype.getLongitude = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.Position} returns this
 */
proto.ru.sbertroika.common.v1.Position.prototype.setLongitude = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.common.v1.EmptyResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  EMPTY: 2
};

/**
 * @return {proto.ru.sbertroika.common.v1.EmptyResponse.ResponseCase}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.common.v1.EmptyResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.common.v1.EmptyResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.EmptyResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.EmptyResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.EmptyResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && proto.ru.sbertroika.common.v1.OperationError.toObject(includeInstance, f),
    empty: (f = msg.getEmpty()) && google_protobuf_empty_pb.Empty.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.EmptyResponse}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.EmptyResponse;
  return proto.ru.sbertroika.common.v1.EmptyResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.EmptyResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.EmptyResponse}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.v1.OperationError;
      reader.readMessage(value,proto.ru.sbertroika.common.v1.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new google_protobuf_empty_pb.Empty;
      reader.readMessage(value,google_protobuf_empty_pb.Empty.deserializeBinaryFromReader);
      msg.setEmpty(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.EmptyResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.EmptyResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.EmptyResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.ru.sbertroika.common.v1.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getEmpty();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      google_protobuf_empty_pb.Empty.serializeBinaryToWriter
    );
  }
};


/**
 * optional OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.v1.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.common.v1.EmptyResponse} returns this
*/
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.common.v1.EmptyResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.EmptyResponse} returns this
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional google.protobuf.Empty empty = 2;
 * @return {?proto.google.protobuf.Empty}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.getEmpty = function() {
  return /** @type{?proto.google.protobuf.Empty} */ (
    jspb.Message.getWrapperField(this, google_protobuf_empty_pb.Empty, 2));
};


/**
 * @param {?proto.google.protobuf.Empty|undefined} value
 * @return {!proto.ru.sbertroika.common.v1.EmptyResponse} returns this
*/
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.setEmpty = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.common.v1.EmptyResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.EmptyResponse} returns this
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.clearEmpty = function() {
  return this.setEmpty(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.EmptyResponse.prototype.hasEmpty = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.common.v1.CreateResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.CreateResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  ID: 2
};

/**
 * @return {proto.ru.sbertroika.common.v1.CreateResponse.ResponseCase}
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.common.v1.CreateResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.common.v1.CreateResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.CreateResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.CreateResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.CreateResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && proto.ru.sbertroika.common.v1.OperationError.toObject(includeInstance, f),
    id: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.CreateResponse}
 */
proto.ru.sbertroika.common.v1.CreateResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.CreateResponse;
  return proto.ru.sbertroika.common.v1.CreateResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.CreateResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.CreateResponse}
 */
proto.ru.sbertroika.common.v1.CreateResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.v1.OperationError;
      reader.readMessage(value,proto.ru.sbertroika.common.v1.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.CreateResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.CreateResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.CreateResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.ru.sbertroika.common.v1.OperationError.serializeBinaryToWriter
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.v1.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.common.v1.CreateResponse} returns this
*/
proto.ru.sbertroika.common.v1.CreateResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.common.v1.CreateResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.CreateResponse} returns this
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string id = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.CreateResponse} returns this
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.setId = function(value) {
  return jspb.Message.setOneofField(this, 2, proto.ru.sbertroika.common.v1.CreateResponse.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.CreateResponse} returns this
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.clearId = function() {
  return jspb.Message.setOneofField(this, 2, proto.ru.sbertroika.common.v1.CreateResponse.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.CreateResponse.prototype.hasId = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.HistoryChange.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.HistoryChange} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.HistoryChange.toObject = function(includeInstance, msg) {
  var f, obj = {
    field: jspb.Message.getFieldWithDefault(msg, 1, ""),
    oldvalue: jspb.Message.getFieldWithDefault(msg, 2, ""),
    value: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange}
 */
proto.ru.sbertroika.common.v1.HistoryChange.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.HistoryChange;
  return proto.ru.sbertroika.common.v1.HistoryChange.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.HistoryChange} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange}
 */
proto.ru.sbertroika.common.v1.HistoryChange.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setField(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setOldvalue(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.HistoryChange.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.HistoryChange} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.HistoryChange.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getField();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string field = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.getField = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange} returns this
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.setField = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string oldValue = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.getOldvalue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange} returns this
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.setOldvalue = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange} returns this
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.clearOldvalue = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.hasOldvalue = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string value = 3;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.getValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange} returns this
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.setValue = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange} returns this
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.clearValue = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.HistoryChange.prototype.hasValue = function() {
  return jspb.Message.getField(this, 3) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.v1.History.repeatedFields_ = [4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.History.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.History.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.History} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.History.toObject = function(includeInstance, msg) {
  var f, obj = {
    version: jspb.Message.getFieldWithDefault(msg, 1, 0),
    versioncreateby: jspb.Message.getFieldWithDefault(msg, 2, ""),
    versioncreateat: (f = msg.getVersioncreateat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    changeList: jspb.Message.toObjectList(msg.getChangeList(),
    proto.ru.sbertroika.common.v1.HistoryChange.toObject, includeInstance),
    status: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.History}
 */
proto.ru.sbertroika.common.v1.History.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.History;
  return proto.ru.sbertroika.common.v1.History.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.History} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.History}
 */
proto.ru.sbertroika.common.v1.History.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setVersion(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setVersioncreateby(value);
      break;
    case 3:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setVersioncreateat(value);
      break;
    case 4:
      var value = new proto.ru.sbertroika.common.v1.HistoryChange;
      reader.readMessage(value,proto.ru.sbertroika.common.v1.HistoryChange.deserializeBinaryFromReader);
      msg.addChange(value);
      break;
    case 5:
      var value = /** @type {!proto.ru.sbertroika.common.v1.ModelStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.History.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.History.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.History} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.History.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVersion();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getVersioncreateby();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getVersioncreateat();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getChangeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.ru.sbertroika.common.v1.HistoryChange.serializeBinaryToWriter
    );
  }
  f = /** @type {!proto.ru.sbertroika.common.v1.ModelStatus} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeEnum(
      5,
      f
    );
  }
};


/**
 * optional int64 version = 1;
 * @return {number}
 */
proto.ru.sbertroika.common.v1.History.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
 */
proto.ru.sbertroika.common.v1.History.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string versionCreateBy = 2;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.History.prototype.getVersioncreateby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
 */
proto.ru.sbertroika.common.v1.History.prototype.setVersioncreateby = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional google.protobuf.Timestamp versionCreateAt = 3;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.ru.sbertroika.common.v1.History.prototype.getVersioncreateat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 3));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
*/
proto.ru.sbertroika.common.v1.History.prototype.setVersioncreateat = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
 */
proto.ru.sbertroika.common.v1.History.prototype.clearVersioncreateat = function() {
  return this.setVersioncreateat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.History.prototype.hasVersioncreateat = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * repeated HistoryChange change = 4;
 * @return {!Array<!proto.ru.sbertroika.common.v1.HistoryChange>}
 */
proto.ru.sbertroika.common.v1.History.prototype.getChangeList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.v1.HistoryChange>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.v1.HistoryChange, 4));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.v1.HistoryChange>} value
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
*/
proto.ru.sbertroika.common.v1.History.prototype.setChangeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.ru.sbertroika.common.v1.HistoryChange=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.v1.HistoryChange}
 */
proto.ru.sbertroika.common.v1.History.prototype.addChange = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.ru.sbertroika.common.v1.HistoryChange, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
 */
proto.ru.sbertroika.common.v1.History.prototype.clearChangeList = function() {
  return this.setChangeList([]);
};


/**
 * optional ModelStatus status = 5;
 * @return {!proto.ru.sbertroika.common.v1.ModelStatus}
 */
proto.ru.sbertroika.common.v1.History.prototype.getStatus = function() {
  return /** @type {!proto.ru.sbertroika.common.v1.ModelStatus} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.ru.sbertroika.common.v1.ModelStatus} value
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
 */
proto.ru.sbertroika.common.v1.History.prototype.setStatus = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.History} returns this
 */
proto.ru.sbertroika.common.v1.History.prototype.clearStatus = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.History.prototype.hasStatus = function() {
  return jspb.Message.getField(this, 5) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ru.sbertroika.common.v1.HistoryResult.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.HistoryResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.HistoryResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.HistoryResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    pagination: (f = msg.getPagination()) && proto.ru.sbertroika.common.v1.PaginationResponse.toObject(includeInstance, f),
    historyList: jspb.Message.toObjectList(msg.getHistoryList(),
    proto.ru.sbertroika.common.v1.History.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResult}
 */
proto.ru.sbertroika.common.v1.HistoryResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.HistoryResult;
  return proto.ru.sbertroika.common.v1.HistoryResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.HistoryResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResult}
 */
proto.ru.sbertroika.common.v1.HistoryResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.v1.PaginationResponse;
      reader.readMessage(value,proto.ru.sbertroika.common.v1.PaginationResponse.deserializeBinaryFromReader);
      msg.setPagination(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.v1.History;
      reader.readMessage(value,proto.ru.sbertroika.common.v1.History.deserializeBinaryFromReader);
      msg.addHistory(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.HistoryResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.HistoryResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.HistoryResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPagination();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.ru.sbertroika.common.v1.PaginationResponse.serializeBinaryToWriter
    );
  }
  f = message.getHistoryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.ru.sbertroika.common.v1.History.serializeBinaryToWriter
    );
  }
};


/**
 * optional PaginationResponse pagination = 1;
 * @return {?proto.ru.sbertroika.common.v1.PaginationResponse}
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.getPagination = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.PaginationResponse} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.v1.PaginationResponse, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.PaginationResponse|undefined} value
 * @return {!proto.ru.sbertroika.common.v1.HistoryResult} returns this
*/
proto.ru.sbertroika.common.v1.HistoryResult.prototype.setPagination = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResult} returns this
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.clearPagination = function() {
  return this.setPagination(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.hasPagination = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * repeated History history = 2;
 * @return {!Array<!proto.ru.sbertroika.common.v1.History>}
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.getHistoryList = function() {
  return /** @type{!Array<!proto.ru.sbertroika.common.v1.History>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.ru.sbertroika.common.v1.History, 2));
};


/**
 * @param {!Array<!proto.ru.sbertroika.common.v1.History>} value
 * @return {!proto.ru.sbertroika.common.v1.HistoryResult} returns this
*/
proto.ru.sbertroika.common.v1.HistoryResult.prototype.setHistoryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.ru.sbertroika.common.v1.History=} opt_value
 * @param {number=} opt_index
 * @return {!proto.ru.sbertroika.common.v1.History}
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.addHistory = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.ru.sbertroika.common.v1.History, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResult} returns this
 */
proto.ru.sbertroika.common.v1.HistoryResult.prototype.clearHistoryList = function() {
  return this.setHistoryList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.common.v1.HistoryResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  RESULT: 2
};

/**
 * @return {proto.ru.sbertroika.common.v1.HistoryResponse.ResponseCase}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.common.v1.HistoryResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.common.v1.HistoryResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.HistoryResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.HistoryResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.HistoryResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && proto.ru.sbertroika.common.v1.OperationError.toObject(includeInstance, f),
    result: (f = msg.getResult()) && proto.ru.sbertroika.common.v1.HistoryResult.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResponse}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.HistoryResponse;
  return proto.ru.sbertroika.common.v1.HistoryResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.HistoryResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResponse}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.ru.sbertroika.common.v1.OperationError;
      reader.readMessage(value,proto.ru.sbertroika.common.v1.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new proto.ru.sbertroika.common.v1.HistoryResult;
      reader.readMessage(value,proto.ru.sbertroika.common.v1.HistoryResult.deserializeBinaryFromReader);
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.HistoryResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.HistoryResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.HistoryResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.ru.sbertroika.common.v1.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getResult();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ru.sbertroika.common.v1.HistoryResult.serializeBinaryToWriter
    );
  }
};


/**
 * optional OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.v1.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.common.v1.HistoryResponse} returns this
*/
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.common.v1.HistoryResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResponse} returns this
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional HistoryResult result = 2;
 * @return {?proto.ru.sbertroika.common.v1.HistoryResult}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.getResult = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.HistoryResult} */ (
    jspb.Message.getWrapperField(this, proto.ru.sbertroika.common.v1.HistoryResult, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.HistoryResult|undefined} value
 * @return {!proto.ru.sbertroika.common.v1.HistoryResponse} returns this
*/
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.setResult = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.common.v1.HistoryResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.common.v1.HistoryResponse} returns this
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.clearResult = function() {
  return this.setResult(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.common.v1.HistoryResponse.prototype.hasResult = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.common.v1.ByIdRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.common.v1.ByIdRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.common.v1.ByIdRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.ByIdRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.common.v1.ByIdRequest}
 */
proto.ru.sbertroika.common.v1.ByIdRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.common.v1.ByIdRequest;
  return proto.ru.sbertroika.common.v1.ByIdRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.common.v1.ByIdRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.common.v1.ByIdRequest}
 */
proto.ru.sbertroika.common.v1.ByIdRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.common.v1.ByIdRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.common.v1.ByIdRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.common.v1.ByIdRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.common.v1.ByIdRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.ru.sbertroika.common.v1.ByIdRequest.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.ru.sbertroika.common.v1.ByIdRequest} returns this
 */
proto.ru.sbertroika.common.v1.ByIdRequest.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.ErrorType = {
  UNKNOWN_ERROR: 0,
  SERVICE_ERROR: 1,
  UNSUPPORTED_OPERATION: 2,
  BAD_REQUEST: 3,
  AUTHENTICATION_ERROR: 4,
  NOT_FOUND: 5
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.TransportType = {
  BUS: 0,
  TROLLEYBUS: 1,
  TRAM: 2,
  METRO: 3
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.OperationStatus = {
  NEW: 0,
  SUCCESS: 1,
  FAILED: 2
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.AbonementType = {
  WALLET: 0,
  TRAVEL: 1,
  UNLIMITED: 2
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.SubscriptionCounterType = {
  SCT_ALL: 0,
  SCT_ALLOW_LIST: 1,
  SCT_SINGLE: 2,
  SCT_ALL_UNLIMITED: 3,
  SCT_ALLOW_LIST_UNLIMITED: 4
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.PaymentType = {
  EMV: 0,
  CARRIER: 1,
  SBER_PAY: 2,
  SBP: 3,
  BINDING: 4
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.ProlongType = {
  PT_PERIOD: 0,
  PT_END_DATE: 1,
  PT_ACTIVE_DATE: 2
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.PaymentAttribute = {
  PAYMENT_TYPE: 0,
  CLIENT_ID: 1,
  USER_ID: 3,
  ORDER_ID: 4,
  PAYMENT_DATE: 5,
  RETURN_URL: 6,
  FAIL_URL: 7,
  ITEMS: 8,
  CONFIG: 9,
  JSON_PARAMS: 10,
  USER_EMAIL: 11,
  USER_PHONE: 12
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.AuthType = {
  OPEN_ID: 0,
  MTLS: 1
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.SortedType = {
  ASC: 0,
  DESC: 1
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.ConstraintType = {
  TARIFF: 0,
  ROUTE: 1,
  TRANSPORT: 2,
  SERVICE: 3,
  ORGANIZATION: 4
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.ConstraintBaseRule = {
  ALLOW: 0,
  DENY: 1
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.RouteScheme = {
  DIRECTIONAL: 0,
  CIRCLE: 1
};

/**
 * @enum {number}
 */
proto.ru.sbertroika.common.v1.ModelStatus = {
  ACTIVE: 0,
  DISABLED: 1,
  BLOCKED: 2,
  IS_DELETED: 3
};

goog.object.extend(exports, proto.ru.sbertroika.common.v1);
