import * as jspb from 'google-protobuf'

import * as google_protobuf_empty_pb from 'google-protobuf/google/protobuf/empty_pb'; // proto import: "google/protobuf/empty.proto"
import * as common_pb from './common_pb'; // proto import: "common.proto"
import * as common$manifest_pb from './common-manifest_pb'; // proto import: "common-manifest.proto"
import * as common$manifest$pasiv_pb from './common-manifest-pasiv_pb'; // proto import: "common-manifest-pasiv.proto"


export class ManifestResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): ManifestResponse;
  hasError(): boolean;
  clearError(): ManifestResponse;

  getManifest(): common$manifest$pasiv_pb.ManifestPasiv | undefined;
  setManifest(value?: common$manifest$pasiv_pb.ManifestPasiv): ManifestResponse;
  hasManifest(): boolean;
  clearManifest(): ManifestResponse;

  getResponseCase(): ManifestResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestResponse.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestResponse): ManifestResponse.AsObject;
  static serializeBinaryToWriter(message: ManifestResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestResponse;
  static deserializeBinaryFromReader(message: ManifestResponse, reader: jspb.BinaryReader): ManifestResponse;
}

export namespace ManifestResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    manifest?: common$manifest$pasiv_pb.ManifestPasiv.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    MANIFEST = 2,
  }
}

