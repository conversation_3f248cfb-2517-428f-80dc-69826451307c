import * as jspb from 'google-protobuf'

import * as common$manifest$core_pb from './common-manifest-core_pb'; // proto import: "common-manifest-core.proto"


export class ManifestPasivDict extends jspb.Message {
  getOrganizationList(): Array<Organization>;
  setOrganizationList(value: Array<Organization>): ManifestPasivDict;
  clearOrganizationList(): ManifestPasivDict;
  addOrganization(value?: Organization, index?: number): Organization;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestPasivDict.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestPasivDict): ManifestPasivDict.AsObject;
  static serializeBinaryToWriter(message: ManifestPasivDict, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestPasivDict;
  static deserializeBinaryFromReader(message: ManifestPasivDict, reader: jspb.BinaryReader): ManifestPasivDict;
}

export namespace ManifestPasivDict {
  export type AsObject = {
    organizationList: Array<Organization.AsObject>,
  }
}

export class ManifestPasiv extends jspb.Message {
  getFeaturesList(): Array<common$manifest$core_pb.TkpFeature>;
  setFeaturesList(value: Array<common$manifest$core_pb.TkpFeature>): ManifestPasiv;
  clearFeaturesList(): ManifestPasiv;
  addFeatures(value?: common$manifest$core_pb.TkpFeature, index?: number): common$manifest$core_pb.TkpFeature;

  getDict(): ManifestPasivDict | undefined;
  setDict(value?: ManifestPasivDict): ManifestPasiv;
  hasDict(): boolean;
  clearDict(): ManifestPasiv;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ManifestPasiv.AsObject;
  static toObject(includeInstance: boolean, msg: ManifestPasiv): ManifestPasiv.AsObject;
  static serializeBinaryToWriter(message: ManifestPasiv, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ManifestPasiv;
  static deserializeBinaryFromReader(message: ManifestPasiv, reader: jspb.BinaryReader): ManifestPasiv;
}

export namespace ManifestPasiv {
  export type AsObject = {
    featuresList: Array<common$manifest$core_pb.TkpFeature.AsObject>,
    dict?: ManifestPasivDict.AsObject,
  }
}

export class Organization extends jspb.Message {
  getId(): string;
  setId(value: string): Organization;

  getName(): string;
  setName(value: string): Organization;

  getShortname(): string;
  setShortname(value: string): Organization;

  getInn(): string;
  setInn(value: string): Organization;

  getKpp(): string;
  setKpp(value: string): Organization;

  getAddress(): string;
  setAddress(value: string): Organization;

  getPaymentplace(): string;
  setPaymentplace(value: string): Organization;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Organization.AsObject;
  static toObject(includeInstance: boolean, msg: Organization): Organization.AsObject;
  static serializeBinaryToWriter(message: Organization, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Organization;
  static deserializeBinaryFromReader(message: Organization, reader: jspb.BinaryReader): Organization;
}

export namespace Organization {
  export type AsObject = {
    id: string,
    name: string,
    shortname: string,
    inn: string,
    kpp: string,
    address: string,
    paymentplace: string,
  }
}

