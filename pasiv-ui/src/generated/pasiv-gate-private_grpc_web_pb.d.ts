import * as grpcWeb from 'grpc-web';

import * as common_pb from './common_pb'; // proto import: "common.proto"
import * as pasiv$gate$private_pb from './pasiv-gate-private_pb'; // proto import: "pasiv-gate-private.proto"


export class PASIVGatePrivateServiceClient {
  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; });

  createOrganization(
    request: pasiv$gate$private_pb.OrganizationWithAddresses,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  updateOrganization(
    request: pasiv$gate$private_pb.Organization,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  organizationList(
    request: pasiv$gate$private_pb.OrganizationListRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.OrganizationListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.OrganizationListResponse>;

  organizationById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.OrganizationResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.OrganizationResponse>;

  deleteOrganization(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  recoverOrganization(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  organizationListForProject(
    request: pasiv$gate$private_pb.OrganizationListForProjectRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.OrganizationListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.OrganizationListResponse>;

  addOrganizationInProject(
    request: pasiv$gate$private_pb.OrganizationInProjectRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  removeOrganizationInProject(
    request: pasiv$gate$private_pb.OrganizationInProjectRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  createAddress(
    request: pasiv$gate$private_pb.AddressCreateOrDelete,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  updateAddress(
    request: pasiv$gate$private_pb.AddressCreateOrDelete,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  addressById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.AddressResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.AddressResponse>;

  addressList(
    request: pasiv$gate$private_pb.AddressListRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.AddressListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.AddressListResponse>;

  deleteAddress(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  recoverAddress(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  createContact(
    request: pasiv$gate$private_pb.Contact,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  updateContact(
    request: pasiv$gate$private_pb.Contact,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  contactList(
    request: pasiv$gate$private_pb.ContactListRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.ContactListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.ContactListResponse>;

  contactById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.ContactResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.ContactResponse>;

  deleteContact(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  recoverContact(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  contactHistoryById(
    request: pasiv$gate$private_pb.ByIdWithPaginationRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.HistoryResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.HistoryResponse>;

  addressHistoryById(
    request: pasiv$gate$private_pb.ByIdWithPaginationRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.HistoryResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.HistoryResponse>;

  organizationHistoryById(
    request: pasiv$gate$private_pb.ByIdWithPaginationRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.HistoryResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.HistoryResponse>;

  organizationHintByINN(
    request: pasiv$gate$private_pb.OrganizationHintRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.OrganizationHintResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.OrganizationHintResponse>;

  contractList(
    request: pasiv$gate$private_pb.ContractListRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.ContractListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.ContractListResponse>;

  contractById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.ContractResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.ContractResponse>;

  createContract(
    request: pasiv$gate$private_pb.ContractWithOrganizations,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  updateContract(
    request: pasiv$gate$private_pb.Contract,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  deleteContract(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  contractsByProject(
    request: pasiv$gate$private_pb.ContractsByProjectRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.ContractListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.ContractListResponse>;

  paymentMethodList(
    request: pasiv$gate$private_pb.PaymentMethodListRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.PaymentMethodListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.PaymentMethodListResponse>;

  paymentMethodById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.PaymentMethodResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.PaymentMethodResponse>;

  createPaymentMethod(
    request: pasiv$gate$private_pb.ContractPaymentMethod,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  updatePaymentMethod(
    request: pasiv$gate$private_pb.ContractPaymentMethod,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  deletePaymentMethod(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: common_pb.EmptyResponse) => void
  ): grpcWeb.ClientReadableStream<common_pb.EmptyResponse>;

  paymentMethodsByContract(
    request: pasiv$gate$private_pb.PaymentMethodsByContractRequest,
    metadata: grpcWeb.Metadata | undefined,
    callback: (err: grpcWeb.RpcError,
               response: pasiv$gate$private_pb.PaymentMethodListResponse) => void
  ): grpcWeb.ClientReadableStream<pasiv$gate$private_pb.PaymentMethodListResponse>;

}

export class PASIVGatePrivateServicePromiseClient {
  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; });

  createOrganization(
    request: pasiv$gate$private_pb.OrganizationWithAddresses,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  updateOrganization(
    request: pasiv$gate$private_pb.Organization,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  organizationList(
    request: pasiv$gate$private_pb.OrganizationListRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.OrganizationListResponse>;

  organizationById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.OrganizationResponse>;

  deleteOrganization(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  recoverOrganization(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  organizationListForProject(
    request: pasiv$gate$private_pb.OrganizationListForProjectRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.OrganizationListResponse>;

  addOrganizationInProject(
    request: pasiv$gate$private_pb.OrganizationInProjectRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  removeOrganizationInProject(
    request: pasiv$gate$private_pb.OrganizationInProjectRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  createAddress(
    request: pasiv$gate$private_pb.AddressCreateOrDelete,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  updateAddress(
    request: pasiv$gate$private_pb.AddressCreateOrDelete,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  addressById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.AddressResponse>;

  addressList(
    request: pasiv$gate$private_pb.AddressListRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.AddressListResponse>;

  deleteAddress(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  recoverAddress(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  createContact(
    request: pasiv$gate$private_pb.Contact,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  updateContact(
    request: pasiv$gate$private_pb.Contact,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  contactList(
    request: pasiv$gate$private_pb.ContactListRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.ContactListResponse>;

  contactById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.ContactResponse>;

  deleteContact(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  recoverContact(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  contactHistoryById(
    request: pasiv$gate$private_pb.ByIdWithPaginationRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.HistoryResponse>;

  addressHistoryById(
    request: pasiv$gate$private_pb.ByIdWithPaginationRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.HistoryResponse>;

  organizationHistoryById(
    request: pasiv$gate$private_pb.ByIdWithPaginationRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.HistoryResponse>;

  organizationHintByINN(
    request: pasiv$gate$private_pb.OrganizationHintRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.OrganizationHintResponse>;

  contractList(
    request: pasiv$gate$private_pb.ContractListRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.ContractListResponse>;

  contractById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.ContractResponse>;

  createContract(
    request: pasiv$gate$private_pb.ContractWithOrganizations,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  updateContract(
    request: pasiv$gate$private_pb.Contract,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  deleteContract(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  contractsByProject(
    request: pasiv$gate$private_pb.ContractsByProjectRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.ContractListResponse>;

  paymentMethodList(
    request: pasiv$gate$private_pb.PaymentMethodListRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.PaymentMethodListResponse>;

  paymentMethodById(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.PaymentMethodResponse>;

  createPaymentMethod(
    request: pasiv$gate$private_pb.ContractPaymentMethod,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  updatePaymentMethod(
    request: pasiv$gate$private_pb.ContractPaymentMethod,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  deletePaymentMethod(
    request: pasiv$gate$private_pb.ByIdRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<common_pb.EmptyResponse>;

  paymentMethodsByContract(
    request: pasiv$gate$private_pb.PaymentMethodsByContractRequest,
    metadata?: grpcWeb.Metadata
  ): Promise<pasiv$gate$private_pb.PaymentMethodListResponse>;

}

