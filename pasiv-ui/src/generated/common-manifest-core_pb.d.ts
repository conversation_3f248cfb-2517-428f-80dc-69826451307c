import * as jspb from 'google-protobuf'

import * as common_pb from './common_pb'; // proto import: "common.proto"


export class ConstraintException extends jspb.Message {
  getId(): string;
  setId(value: string): ConstraintException;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ConstraintException.AsObject;
  static toObject(includeInstance: boolean, msg: ConstraintException): ConstraintException.AsObject;
  static serializeBinaryToWriter(message: ConstraintException, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ConstraintException;
  static deserializeBinaryFromReader(message: ConstraintException, reader: jspb.BinaryReader): ConstraintException;
}

export namespace ConstraintException {
  export type AsObject = {
    id: string,
  }
}

export class Constraint extends jspb.Message {
  getType(): common_pb.ConstraintType;
  setType(value: common_pb.ConstraintType): Constraint;

  getBaserule(): common_pb.ConstraintBaseRule;
  setBaserule(value: common_pb.ConstraintBaseRule): Constraint;

  getExceptionList(): Array<ConstraintException>;
  setExceptionList(value: Array<ConstraintException>): Constraint;
  clearExceptionList(): Constraint;
  addException(value?: ConstraintException, index?: number): ConstraintException;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Constraint.AsObject;
  static toObject(includeInstance: boolean, msg: Constraint): Constraint.AsObject;
  static serializeBinaryToWriter(message: Constraint, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Constraint;
  static deserializeBinaryFromReader(message: Constraint, reader: jspb.BinaryReader): Constraint;
}

export namespace Constraint {
  export type AsObject = {
    type: common_pb.ConstraintType,
    baserule: common_pb.ConstraintBaseRule,
    exceptionList: Array<ConstraintException.AsObject>,
  }
}

export class TkpFeature extends jspb.Message {
  getName(): string;
  setName(value: string): TkpFeature;

  getState(): TkpFeature.FeatureState;
  setState(value: TkpFeature.FeatureState): TkpFeature;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): TkpFeature.AsObject;
  static toObject(includeInstance: boolean, msg: TkpFeature): TkpFeature.AsObject;
  static serializeBinaryToWriter(message: TkpFeature, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): TkpFeature;
  static deserializeBinaryFromReader(message: TkpFeature, reader: jspb.BinaryReader): TkpFeature;
}

export namespace TkpFeature {
  export type AsObject = {
    name: string,
    state: TkpFeature.FeatureState,
  }

  export enum FeatureState { 
    FS_ACTIVE = 0,
  }
}

