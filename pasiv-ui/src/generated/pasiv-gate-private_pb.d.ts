import * as jspb from 'google-protobuf'

import * as google_protobuf_empty_pb from 'google-protobuf/google/protobuf/empty_pb'; // proto import: "google/protobuf/empty.proto"
import * as google_protobuf_timestamp_pb from 'google-protobuf/google/protobuf/timestamp_pb'; // proto import: "google/protobuf/timestamp.proto"
import * as common_pb from './common_pb'; // proto import: "common.proto"


export class OrganizationResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): OrganizationResponse;
  hasError(): boolean;
  clearError(): OrganizationResponse;

  getResult(): Organization | undefined;
  setResult(value?: Organization): OrganizationResponse;
  hasResult(): boolean;
  clearResult(): OrganizationResponse;

  getResponseCase(): OrganizationResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationResponse.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationResponse): OrganizationResponse.AsObject;
  static serializeBinaryToWriter(message: OrganizationResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationResponse;
  static deserializeBinaryFromReader(message: OrganizationResponse, reader: jspb.BinaryReader): OrganizationResponse;
}

export namespace OrganizationResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: Organization.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class OrganizationWithAddresses extends jspb.Message {
  getOrganization(): Organization | undefined;
  setOrganization(value?: Organization): OrganizationWithAddresses;
  hasOrganization(): boolean;
  clearOrganization(): OrganizationWithAddresses;

  getAddresslegal(): Address | undefined;
  setAddresslegal(value?: Address): OrganizationWithAddresses;
  hasAddresslegal(): boolean;
  clearAddresslegal(): OrganizationWithAddresses;

  getAddressactual(): Address | undefined;
  setAddressactual(value?: Address): OrganizationWithAddresses;
  hasAddressactual(): boolean;
  clearAddressactual(): OrganizationWithAddresses;

  getAddressmailing(): Address | undefined;
  setAddressmailing(value?: Address): OrganizationWithAddresses;
  hasAddressmailing(): boolean;
  clearAddressmailing(): OrganizationWithAddresses;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationWithAddresses.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationWithAddresses): OrganizationWithAddresses.AsObject;
  static serializeBinaryToWriter(message: OrganizationWithAddresses, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationWithAddresses;
  static deserializeBinaryFromReader(message: OrganizationWithAddresses, reader: jspb.BinaryReader): OrganizationWithAddresses;
}

export namespace OrganizationWithAddresses {
  export type AsObject = {
    organization?: Organization.AsObject,
    addresslegal?: Address.AsObject,
    addressactual?: Address.AsObject,
    addressmailing?: Address.AsObject,
  }

  export enum AddressactualCase { 
    _ADDRESSACTUAL_NOT_SET = 0,
    ADDRESSACTUAL = 3,
  }

  export enum AddressmailingCase { 
    _ADDRESSMAILING_NOT_SET = 0,
    ADDRESSMAILING = 4,
  }
}

export class Organization extends jspb.Message {
  getId(): string;
  setId(value: string): Organization;

  getParent(): Organization | undefined;
  setParent(value?: Organization): Organization;
  hasParent(): boolean;
  clearParent(): Organization;

  getName(): string;
  setName(value: string): Organization;

  getShortname(): string;
  setShortname(value: string): Organization;

  getKpp(): string;
  setKpp(value: string): Organization;

  getInn(): string;
  setInn(value: string): Organization;

  getNote(): string;
  setNote(value: string): Organization;
  hasNote(): boolean;
  clearNote(): Organization;

  getOkpo(): string;
  setOkpo(value: string): Organization;
  hasOkpo(): boolean;
  clearOkpo(): Organization;

  getOktmo(): string;
  setOktmo(value: string): Organization;
  hasOktmo(): boolean;
  clearOktmo(): Organization;

  getOkved(): string;
  setOkved(value: string): Organization;
  hasOkved(): boolean;
  clearOkved(): Organization;

  getFiodirector(): string;
  setFiodirector(value: string): Organization;
  hasFiodirector(): boolean;
  clearFiodirector(): Organization;

  getAddresslegal(): string;
  setAddresslegal(value: string): Organization;
  hasAddresslegal(): boolean;
  clearAddresslegal(): Organization;

  getAddressactual(): string;
  setAddressactual(value: string): Organization;
  hasAddressactual(): boolean;
  clearAddressactual(): Organization;

  getAddressmailing(): string;
  setAddressmailing(value: string): Organization;
  hasAddressmailing(): boolean;
  clearAddressmailing(): Organization;

  getManageractionreason(): string;
  setManageractionreason(value: string): Organization;
  hasManageractionreason(): boolean;
  clearManageractionreason(): Organization;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): Organization;

  getOgrn(): string;
  setOgrn(value: string): Organization;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Organization.AsObject;
  static toObject(includeInstance: boolean, msg: Organization): Organization.AsObject;
  static serializeBinaryToWriter(message: Organization, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Organization;
  static deserializeBinaryFromReader(message: Organization, reader: jspb.BinaryReader): Organization;
}

export namespace Organization {
  export type AsObject = {
    id: string,
    parent?: Organization.AsObject,
    name: string,
    shortname: string,
    kpp: string,
    inn: string,
    note?: string,
    okpo?: string,
    oktmo?: string,
    okved?: string,
    fiodirector?: string,
    addresslegal?: string,
    addressactual?: string,
    addressmailing?: string,
    manageractionreason?: string,
    isdeleted: boolean,
    ogrn: string,
  }

  export enum ParentCase { 
    _PARENT_NOT_SET = 0,
    PARENT = 2,
  }

  export enum NoteCase { 
    _NOTE_NOT_SET = 0,
    NOTE = 7,
  }

  export enum OkpoCase { 
    _OKPO_NOT_SET = 0,
    OKPO = 8,
  }

  export enum OktmoCase { 
    _OKTMO_NOT_SET = 0,
    OKTMO = 9,
  }

  export enum OkvedCase { 
    _OKVED_NOT_SET = 0,
    OKVED = 10,
  }

  export enum FiodirectorCase { 
    _FIODIRECTOR_NOT_SET = 0,
    FIODIRECTOR = 11,
  }

  export enum AddresslegalCase { 
    _ADDRESSLEGAL_NOT_SET = 0,
    ADDRESSLEGAL = 12,
  }

  export enum AddressactualCase { 
    _ADDRESSACTUAL_NOT_SET = 0,
    ADDRESSACTUAL = 13,
  }

  export enum AddressmailingCase { 
    _ADDRESSMAILING_NOT_SET = 0,
    ADDRESSMAILING = 14,
  }

  export enum ManageractionreasonCase { 
    _MANAGERACTIONREASON_NOT_SET = 0,
    MANAGERACTIONREASON = 15,
  }
}

export class OrganizationFilter extends jspb.Message {
  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): OrganizationFilter;
  hasIsdeleted(): boolean;
  clearIsdeleted(): OrganizationFilter;

  getName(): string;
  setName(value: string): OrganizationFilter;
  hasName(): boolean;
  clearName(): OrganizationFilter;

  getInn(): string;
  setInn(value: string): OrganizationFilter;
  hasInn(): boolean;
  clearInn(): OrganizationFilter;

  getKpp(): string;
  setKpp(value: string): OrganizationFilter;
  hasKpp(): boolean;
  clearKpp(): OrganizationFilter;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationFilter.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationFilter): OrganizationFilter.AsObject;
  static serializeBinaryToWriter(message: OrganizationFilter, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationFilter;
  static deserializeBinaryFromReader(message: OrganizationFilter, reader: jspb.BinaryReader): OrganizationFilter;
}

export namespace OrganizationFilter {
  export type AsObject = {
    isdeleted?: boolean,
    name?: string,
    inn?: string,
    kpp?: string,
  }

  export enum IsdeletedCase { 
    _ISDELETED_NOT_SET = 0,
    ISDELETED = 1,
  }

  export enum NameCase { 
    _NAME_NOT_SET = 0,
    NAME = 2,
  }

  export enum InnCase { 
    _INN_NOT_SET = 0,
    INN = 3,
  }

  export enum KppCase { 
    _KPP_NOT_SET = 0,
    KPP = 4,
  }
}

export class OrganizationListRequest extends jspb.Message {
  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): OrganizationListRequest;
  hasPagination(): boolean;
  clearPagination(): OrganizationListRequest;

  getFilter(): OrganizationFilter | undefined;
  setFilter(value?: OrganizationFilter): OrganizationListRequest;
  hasFilter(): boolean;
  clearFilter(): OrganizationListRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationListRequest.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationListRequest): OrganizationListRequest.AsObject;
  static serializeBinaryToWriter(message: OrganizationListRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationListRequest;
  static deserializeBinaryFromReader(message: OrganizationListRequest, reader: jspb.BinaryReader): OrganizationListRequest;
}

export namespace OrganizationListRequest {
  export type AsObject = {
    pagination?: common_pb.PaginationRequest.AsObject,
    filter?: OrganizationFilter.AsObject,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FilterCase { 
    _FILTER_NOT_SET = 0,
    FILTER = 2,
  }
}

export class OrganizationResult extends jspb.Message {
  getPagination(): common_pb.PaginationResponse | undefined;
  setPagination(value?: common_pb.PaginationResponse): OrganizationResult;
  hasPagination(): boolean;
  clearPagination(): OrganizationResult;

  getFilter(): OrganizationFilter | undefined;
  setFilter(value?: OrganizationFilter): OrganizationResult;
  hasFilter(): boolean;
  clearFilter(): OrganizationResult;

  getOrganizationList(): Array<Organization>;
  setOrganizationList(value: Array<Organization>): OrganizationResult;
  clearOrganizationList(): OrganizationResult;
  addOrganization(value?: Organization, index?: number): Organization;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationResult.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationResult): OrganizationResult.AsObject;
  static serializeBinaryToWriter(message: OrganizationResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationResult;
  static deserializeBinaryFromReader(message: OrganizationResult, reader: jspb.BinaryReader): OrganizationResult;
}

export namespace OrganizationResult {
  export type AsObject = {
    pagination?: common_pb.PaginationResponse.AsObject,
    filter?: OrganizationFilter.AsObject,
    organizationList: Array<Organization.AsObject>,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FilterCase { 
    _FILTER_NOT_SET = 0,
    FILTER = 2,
  }
}

export class OrganizationListResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): OrganizationListResponse;
  hasError(): boolean;
  clearError(): OrganizationListResponse;

  getResult(): OrganizationResult | undefined;
  setResult(value?: OrganizationResult): OrganizationListResponse;
  hasResult(): boolean;
  clearResult(): OrganizationListResponse;

  getResponseCase(): OrganizationListResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationListResponse.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationListResponse): OrganizationListResponse.AsObject;
  static serializeBinaryToWriter(message: OrganizationListResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationListResponse;
  static deserializeBinaryFromReader(message: OrganizationListResponse, reader: jspb.BinaryReader): OrganizationListResponse;
}

export namespace OrganizationListResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: OrganizationResult.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class OrganizationListForProjectRequest extends jspb.Message {
  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): OrganizationListForProjectRequest;
  hasPagination(): boolean;
  clearPagination(): OrganizationListForProjectRequest;

  getProjectid(): string;
  setProjectid(value: string): OrganizationListForProjectRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationListForProjectRequest.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationListForProjectRequest): OrganizationListForProjectRequest.AsObject;
  static serializeBinaryToWriter(message: OrganizationListForProjectRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationListForProjectRequest;
  static deserializeBinaryFromReader(message: OrganizationListForProjectRequest, reader: jspb.BinaryReader): OrganizationListForProjectRequest;
}

export namespace OrganizationListForProjectRequest {
  export type AsObject = {
    pagination?: common_pb.PaginationRequest.AsObject,
    projectid: string,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }
}

export class OrganizationInProjectRequest extends jspb.Message {
  getOrganizationid(): string;
  setOrganizationid(value: string): OrganizationInProjectRequest;

  getProjectid(): string;
  setProjectid(value: string): OrganizationInProjectRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationInProjectRequest.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationInProjectRequest): OrganizationInProjectRequest.AsObject;
  static serializeBinaryToWriter(message: OrganizationInProjectRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationInProjectRequest;
  static deserializeBinaryFromReader(message: OrganizationInProjectRequest, reader: jspb.BinaryReader): OrganizationInProjectRequest;
}

export namespace OrganizationInProjectRequest {
  export type AsObject = {
    organizationid: string,
    projectid: string,
  }
}

export class Address extends jspb.Message {
  getId(): string;
  setId(value: string): Address;
  hasId(): boolean;
  clearId(): Address;

  getName(): string;
  setName(value: string): Address;

  getIndex(): number;
  setIndex(value: number): Address;
  hasIndex(): boolean;
  clearIndex(): Address;

  getCountry(): string;
  setCountry(value: string): Address;
  hasCountry(): boolean;
  clearCountry(): Address;

  getRegion(): string;
  setRegion(value: string): Address;

  getDistrict(): string;
  setDistrict(value: string): Address;
  hasDistrict(): boolean;
  clearDistrict(): Address;

  getCity(): string;
  setCity(value: string): Address;

  getStreet(): string;
  setStreet(value: string): Address;
  hasStreet(): boolean;
  clearStreet(): Address;

  getHouse(): string;
  setHouse(value: string): Address;

  getBuildingorhousing(): string;
  setBuildingorhousing(value: string): Address;
  hasBuildingorhousing(): boolean;
  clearBuildingorhousing(): Address;

  getOfficeorroom(): string;
  setOfficeorroom(value: string): Address;
  hasOfficeorroom(): boolean;
  clearOfficeorroom(): Address;

  getLongitude(): number;
  setLongitude(value: number): Address;
  hasLongitude(): boolean;
  clearLongitude(): Address;

  getLatitude(): number;
  setLatitude(value: number): Address;
  hasLatitude(): boolean;
  clearLatitude(): Address;

  getComment(): string;
  setComment(value: string): Address;
  hasComment(): boolean;
  clearComment(): Address;

  getOktmo(): number;
  setOktmo(value: number): Address;
  hasOktmo(): boolean;
  clearOktmo(): Address;

  getFiac(): string;
  setFiac(value: string): Address;
  hasFiac(): boolean;
  clearFiac(): Address;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): Address;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Address.AsObject;
  static toObject(includeInstance: boolean, msg: Address): Address.AsObject;
  static serializeBinaryToWriter(message: Address, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Address;
  static deserializeBinaryFromReader(message: Address, reader: jspb.BinaryReader): Address;
}

export namespace Address {
  export type AsObject = {
    id?: string,
    name: string,
    index?: number,
    country?: string,
    region: string,
    district?: string,
    city: string,
    street?: string,
    house: string,
    buildingorhousing?: string,
    officeorroom?: string,
    longitude?: number,
    latitude?: number,
    comment?: string,
    oktmo?: number,
    fiac?: string,
    isdeleted: boolean,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum IndexCase { 
    _INDEX_NOT_SET = 0,
    INDEX = 3,
  }

  export enum CountryCase { 
    _COUNTRY_NOT_SET = 0,
    COUNTRY = 4,
  }

  export enum DistrictCase { 
    _DISTRICT_NOT_SET = 0,
    DISTRICT = 6,
  }

  export enum StreetCase { 
    _STREET_NOT_SET = 0,
    STREET = 8,
  }

  export enum BuildingorhousingCase { 
    _BUILDINGORHOUSING_NOT_SET = 0,
    BUILDINGORHOUSING = 10,
  }

  export enum OfficeorroomCase { 
    _OFFICEORROOM_NOT_SET = 0,
    OFFICEORROOM = 11,
  }

  export enum LongitudeCase { 
    _LONGITUDE_NOT_SET = 0,
    LONGITUDE = 12,
  }

  export enum LatitudeCase { 
    _LATITUDE_NOT_SET = 0,
    LATITUDE = 13,
  }

  export enum CommentCase { 
    _COMMENT_NOT_SET = 0,
    COMMENT = 14,
  }

  export enum OktmoCase { 
    _OKTMO_NOT_SET = 0,
    OKTMO = 15,
  }

  export enum FiacCase { 
    _FIAC_NOT_SET = 0,
    FIAC = 16,
  }
}

export class Contact extends jspb.Message {
  getId(): string;
  setId(value: string): Contact;
  hasId(): boolean;
  clearId(): Contact;

  getOrganizationid(): string;
  setOrganizationid(value: string): Contact;

  getType(): ContactType;
  setType(value: ContactType): Contact;

  getValue(): string;
  setValue(value: string): Contact;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): Contact;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Contact.AsObject;
  static toObject(includeInstance: boolean, msg: Contact): Contact.AsObject;
  static serializeBinaryToWriter(message: Contact, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Contact;
  static deserializeBinaryFromReader(message: Contact, reader: jspb.BinaryReader): Contact;
}

export namespace Contact {
  export type AsObject = {
    id?: string,
    organizationid: string,
    type: ContactType,
    value: string,
    isdeleted: boolean,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }
}

export class AddressFilter extends jspb.Message {
  getOrganizationid(): string;
  setOrganizationid(value: string): AddressFilter;
  hasOrganizationid(): boolean;
  clearOrganizationid(): AddressFilter;

  getCity(): string;
  setCity(value: string): AddressFilter;
  hasCity(): boolean;
  clearCity(): AddressFilter;

  getStreet(): string;
  setStreet(value: string): AddressFilter;
  hasStreet(): boolean;
  clearStreet(): AddressFilter;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): AddressFilter;
  hasIsdeleted(): boolean;
  clearIsdeleted(): AddressFilter;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AddressFilter.AsObject;
  static toObject(includeInstance: boolean, msg: AddressFilter): AddressFilter.AsObject;
  static serializeBinaryToWriter(message: AddressFilter, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AddressFilter;
  static deserializeBinaryFromReader(message: AddressFilter, reader: jspb.BinaryReader): AddressFilter;
}

export namespace AddressFilter {
  export type AsObject = {
    organizationid?: string,
    city?: string,
    street?: string,
    isdeleted?: boolean,
  }

  export enum OrganizationidCase { 
    _ORGANIZATIONID_NOT_SET = 0,
    ORGANIZATIONID = 1,
  }

  export enum CityCase { 
    _CITY_NOT_SET = 0,
    CITY = 2,
  }

  export enum StreetCase { 
    _STREET_NOT_SET = 0,
    STREET = 3,
  }

  export enum IsdeletedCase { 
    _ISDELETED_NOT_SET = 0,
    ISDELETED = 4,
  }
}

export class AddressListRequest extends jspb.Message {
  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): AddressListRequest;
  hasPagination(): boolean;
  clearPagination(): AddressListRequest;

  getFilters(): AddressFilter | undefined;
  setFilters(value?: AddressFilter): AddressListRequest;
  hasFilters(): boolean;
  clearFilters(): AddressListRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AddressListRequest.AsObject;
  static toObject(includeInstance: boolean, msg: AddressListRequest): AddressListRequest.AsObject;
  static serializeBinaryToWriter(message: AddressListRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AddressListRequest;
  static deserializeBinaryFromReader(message: AddressListRequest, reader: jspb.BinaryReader): AddressListRequest;
}

export namespace AddressListRequest {
  export type AsObject = {
    pagination?: common_pb.PaginationRequest.AsObject,
    filters?: AddressFilter.AsObject,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FiltersCase { 
    _FILTERS_NOT_SET = 0,
    FILTERS = 2,
  }
}

export class AddressListResult extends jspb.Message {
  getPagination(): common_pb.PaginationResponse | undefined;
  setPagination(value?: common_pb.PaginationResponse): AddressListResult;
  hasPagination(): boolean;
  clearPagination(): AddressListResult;

  getFilters(): AddressFilter | undefined;
  setFilters(value?: AddressFilter): AddressListResult;
  hasFilters(): boolean;
  clearFilters(): AddressListResult;

  getAddressList(): Array<Address>;
  setAddressList(value: Array<Address>): AddressListResult;
  clearAddressList(): AddressListResult;
  addAddress(value?: Address, index?: number): Address;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AddressListResult.AsObject;
  static toObject(includeInstance: boolean, msg: AddressListResult): AddressListResult.AsObject;
  static serializeBinaryToWriter(message: AddressListResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AddressListResult;
  static deserializeBinaryFromReader(message: AddressListResult, reader: jspb.BinaryReader): AddressListResult;
}

export namespace AddressListResult {
  export type AsObject = {
    pagination?: common_pb.PaginationResponse.AsObject,
    filters?: AddressFilter.AsObject,
    addressList: Array<Address.AsObject>,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FiltersCase { 
    _FILTERS_NOT_SET = 0,
    FILTERS = 2,
  }
}

export class AddressListResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): AddressListResponse;
  hasError(): boolean;
  clearError(): AddressListResponse;

  getResult(): AddressListResult | undefined;
  setResult(value?: AddressListResult): AddressListResponse;
  hasResult(): boolean;
  clearResult(): AddressListResponse;

  getResponseCase(): AddressListResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AddressListResponse.AsObject;
  static toObject(includeInstance: boolean, msg: AddressListResponse): AddressListResponse.AsObject;
  static serializeBinaryToWriter(message: AddressListResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AddressListResponse;
  static deserializeBinaryFromReader(message: AddressListResponse, reader: jspb.BinaryReader): AddressListResponse;
}

export namespace AddressListResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: AddressListResult.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class ContactFilter extends jspb.Message {
  getOrganizationid(): string;
  setOrganizationid(value: string): ContactFilter;
  hasOrganizationid(): boolean;
  clearOrganizationid(): ContactFilter;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): ContactFilter;
  hasIsdeleted(): boolean;
  clearIsdeleted(): ContactFilter;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContactFilter.AsObject;
  static toObject(includeInstance: boolean, msg: ContactFilter): ContactFilter.AsObject;
  static serializeBinaryToWriter(message: ContactFilter, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContactFilter;
  static deserializeBinaryFromReader(message: ContactFilter, reader: jspb.BinaryReader): ContactFilter;
}

export namespace ContactFilter {
  export type AsObject = {
    organizationid?: string,
    isdeleted?: boolean,
  }

  export enum OrganizationidCase { 
    _ORGANIZATIONID_NOT_SET = 0,
    ORGANIZATIONID = 1,
  }

  export enum IsdeletedCase { 
    _ISDELETED_NOT_SET = 0,
    ISDELETED = 2,
  }
}

export class ContactListRequest extends jspb.Message {
  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): ContactListRequest;
  hasPagination(): boolean;
  clearPagination(): ContactListRequest;

  getFilters(): ContactFilter | undefined;
  setFilters(value?: ContactFilter): ContactListRequest;
  hasFilters(): boolean;
  clearFilters(): ContactListRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContactListRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ContactListRequest): ContactListRequest.AsObject;
  static serializeBinaryToWriter(message: ContactListRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContactListRequest;
  static deserializeBinaryFromReader(message: ContactListRequest, reader: jspb.BinaryReader): ContactListRequest;
}

export namespace ContactListRequest {
  export type AsObject = {
    pagination?: common_pb.PaginationRequest.AsObject,
    filters?: ContactFilter.AsObject,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FiltersCase { 
    _FILTERS_NOT_SET = 0,
    FILTERS = 2,
  }
}

export class ContactListResult extends jspb.Message {
  getPagination(): common_pb.PaginationResponse | undefined;
  setPagination(value?: common_pb.PaginationResponse): ContactListResult;
  hasPagination(): boolean;
  clearPagination(): ContactListResult;

  getFilters(): ContactFilter | undefined;
  setFilters(value?: ContactFilter): ContactListResult;
  hasFilters(): boolean;
  clearFilters(): ContactListResult;

  getContactsList(): Array<Contact>;
  setContactsList(value: Array<Contact>): ContactListResult;
  clearContactsList(): ContactListResult;
  addContacts(value?: Contact, index?: number): Contact;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContactListResult.AsObject;
  static toObject(includeInstance: boolean, msg: ContactListResult): ContactListResult.AsObject;
  static serializeBinaryToWriter(message: ContactListResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContactListResult;
  static deserializeBinaryFromReader(message: ContactListResult, reader: jspb.BinaryReader): ContactListResult;
}

export namespace ContactListResult {
  export type AsObject = {
    pagination?: common_pb.PaginationResponse.AsObject,
    filters?: ContactFilter.AsObject,
    contactsList: Array<Contact.AsObject>,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FiltersCase { 
    _FILTERS_NOT_SET = 0,
    FILTERS = 2,
  }
}

export class ContactListResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): ContactListResponse;
  hasError(): boolean;
  clearError(): ContactListResponse;

  getResult(): ContactListResult | undefined;
  setResult(value?: ContactListResult): ContactListResponse;
  hasResult(): boolean;
  clearResult(): ContactListResponse;

  getResponseCase(): ContactListResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContactListResponse.AsObject;
  static toObject(includeInstance: boolean, msg: ContactListResponse): ContactListResponse.AsObject;
  static serializeBinaryToWriter(message: ContactListResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContactListResponse;
  static deserializeBinaryFromReader(message: ContactListResponse, reader: jspb.BinaryReader): ContactListResponse;
}

export namespace ContactListResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: ContactListResult.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class ByIdRequest extends jspb.Message {
  getId(): string;
  setId(value: string): ByIdRequest;

  getVersion(): number;
  setVersion(value: number): ByIdRequest;
  hasVersion(): boolean;
  clearVersion(): ByIdRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ByIdRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ByIdRequest): ByIdRequest.AsObject;
  static serializeBinaryToWriter(message: ByIdRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ByIdRequest;
  static deserializeBinaryFromReader(message: ByIdRequest, reader: jspb.BinaryReader): ByIdRequest;
}

export namespace ByIdRequest {
  export type AsObject = {
    id: string,
    version?: number,
  }

  export enum VersionCase { 
    _VERSION_NOT_SET = 0,
    VERSION = 2,
  }
}

export class AddressCreateOrDelete extends jspb.Message {
  getAddress(): Address | undefined;
  setAddress(value?: Address): AddressCreateOrDelete;
  hasAddress(): boolean;
  clearAddress(): AddressCreateOrDelete;

  getType(): AddressType;
  setType(value: AddressType): AddressCreateOrDelete;

  getOrganizationid(): string;
  setOrganizationid(value: string): AddressCreateOrDelete;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AddressCreateOrDelete.AsObject;
  static toObject(includeInstance: boolean, msg: AddressCreateOrDelete): AddressCreateOrDelete.AsObject;
  static serializeBinaryToWriter(message: AddressCreateOrDelete, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AddressCreateOrDelete;
  static deserializeBinaryFromReader(message: AddressCreateOrDelete, reader: jspb.BinaryReader): AddressCreateOrDelete;
}

export namespace AddressCreateOrDelete {
  export type AsObject = {
    address?: Address.AsObject,
    type: AddressType,
    organizationid: string,
  }
}

export class AddressResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): AddressResponse;
  hasError(): boolean;
  clearError(): AddressResponse;

  getResult(): Address | undefined;
  setResult(value?: Address): AddressResponse;
  hasResult(): boolean;
  clearResult(): AddressResponse;

  getResponseCase(): AddressResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AddressResponse.AsObject;
  static toObject(includeInstance: boolean, msg: AddressResponse): AddressResponse.AsObject;
  static serializeBinaryToWriter(message: AddressResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AddressResponse;
  static deserializeBinaryFromReader(message: AddressResponse, reader: jspb.BinaryReader): AddressResponse;
}

export namespace AddressResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: Address.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class ContactResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): ContactResponse;
  hasError(): boolean;
  clearError(): ContactResponse;

  getResult(): Contact | undefined;
  setResult(value?: Contact): ContactResponse;
  hasResult(): boolean;
  clearResult(): ContactResponse;

  getResponseCase(): ContactResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContactResponse.AsObject;
  static toObject(includeInstance: boolean, msg: ContactResponse): ContactResponse.AsObject;
  static serializeBinaryToWriter(message: ContactResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContactResponse;
  static deserializeBinaryFromReader(message: ContactResponse, reader: jspb.BinaryReader): ContactResponse;
}

export namespace ContactResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: Contact.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class ByIdWithPaginationRequest extends jspb.Message {
  getId(): string;
  setId(value: string): ByIdWithPaginationRequest;

  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): ByIdWithPaginationRequest;
  hasPagination(): boolean;
  clearPagination(): ByIdWithPaginationRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ByIdWithPaginationRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ByIdWithPaginationRequest): ByIdWithPaginationRequest.AsObject;
  static serializeBinaryToWriter(message: ByIdWithPaginationRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ByIdWithPaginationRequest;
  static deserializeBinaryFromReader(message: ByIdWithPaginationRequest, reader: jspb.BinaryReader): ByIdWithPaginationRequest;
}

export namespace ByIdWithPaginationRequest {
  export type AsObject = {
    id: string,
    pagination?: common_pb.PaginationRequest.AsObject,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 2,
  }
}

export class OrganizationHintRequest extends jspb.Message {
  getInn(): string;
  setInn(value: string): OrganizationHintRequest;

  getKpp(): string;
  setKpp(value: string): OrganizationHintRequest;
  hasKpp(): boolean;
  clearKpp(): OrganizationHintRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationHintRequest.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationHintRequest): OrganizationHintRequest.AsObject;
  static serializeBinaryToWriter(message: OrganizationHintRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationHintRequest;
  static deserializeBinaryFromReader(message: OrganizationHintRequest, reader: jspb.BinaryReader): OrganizationHintRequest;
}

export namespace OrganizationHintRequest {
  export type AsObject = {
    inn: string,
    kpp?: string,
  }

  export enum KppCase { 
    _KPP_NOT_SET = 0,
    KPP = 2,
  }
}

export class OrganizationHintResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): OrganizationHintResponse;
  hasError(): boolean;
  clearError(): OrganizationHintResponse;

  getResult(): OrganizationHintList | undefined;
  setResult(value?: OrganizationHintList): OrganizationHintResponse;
  hasResult(): boolean;
  clearResult(): OrganizationHintResponse;

  getResponseCase(): OrganizationHintResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationHintResponse.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationHintResponse): OrganizationHintResponse.AsObject;
  static serializeBinaryToWriter(message: OrganizationHintResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationHintResponse;
  static deserializeBinaryFromReader(message: OrganizationHintResponse, reader: jspb.BinaryReader): OrganizationHintResponse;
}

export namespace OrganizationHintResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: OrganizationHintList.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class OrganizationHintList extends jspb.Message {
  getOrganizationhintList(): Array<OrganizationHint>;
  setOrganizationhintList(value: Array<OrganizationHint>): OrganizationHintList;
  clearOrganizationhintList(): OrganizationHintList;
  addOrganizationhint(value?: OrganizationHint, index?: number): OrganizationHint;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationHintList.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationHintList): OrganizationHintList.AsObject;
  static serializeBinaryToWriter(message: OrganizationHintList, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationHintList;
  static deserializeBinaryFromReader(message: OrganizationHintList, reader: jspb.BinaryReader): OrganizationHintList;
}

export namespace OrganizationHintList {
  export type AsObject = {
    organizationhintList: Array<OrganizationHint.AsObject>,
  }
}

export class OrganizationHint extends jspb.Message {
  getName(): string;
  setName(value: string): OrganizationHint;

  getShortname(): string;
  setShortname(value: string): OrganizationHint;

  getKpp(): string;
  setKpp(value: string): OrganizationHint;

  getInn(): string;
  setInn(value: string): OrganizationHint;

  getNote(): string;
  setNote(value: string): OrganizationHint;
  hasNote(): boolean;
  clearNote(): OrganizationHint;

  getOkpo(): string;
  setOkpo(value: string): OrganizationHint;
  hasOkpo(): boolean;
  clearOkpo(): OrganizationHint;

  getOktmo(): string;
  setOktmo(value: string): OrganizationHint;
  hasOktmo(): boolean;
  clearOktmo(): OrganizationHint;

  getOkved(): string;
  setOkved(value: string): OrganizationHint;
  hasOkved(): boolean;
  clearOkved(): OrganizationHint;

  getFiodirector(): string;
  setFiodirector(value: string): OrganizationHint;
  hasFiodirector(): boolean;
  clearFiodirector(): OrganizationHint;

  getManageractionreason(): string;
  setManageractionreason(value: string): OrganizationHint;
  hasManageractionreason(): boolean;
  clearManageractionreason(): OrganizationHint;

  getOgrn(): string;
  setOgrn(value: string): OrganizationHint;

  getAddresslegalhint(): AddressHint | undefined;
  setAddresslegalhint(value?: AddressHint): OrganizationHint;
  hasAddresslegalhint(): boolean;
  clearAddresslegalhint(): OrganizationHint;

  getContacthintsList(): Array<ContactHint>;
  setContacthintsList(value: Array<ContactHint>): OrganizationHint;
  clearContacthintsList(): OrganizationHint;
  addContacthints(value?: ContactHint, index?: number): ContactHint;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OrganizationHint.AsObject;
  static toObject(includeInstance: boolean, msg: OrganizationHint): OrganizationHint.AsObject;
  static serializeBinaryToWriter(message: OrganizationHint, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OrganizationHint;
  static deserializeBinaryFromReader(message: OrganizationHint, reader: jspb.BinaryReader): OrganizationHint;
}

export namespace OrganizationHint {
  export type AsObject = {
    name: string,
    shortname: string,
    kpp: string,
    inn: string,
    note?: string,
    okpo?: string,
    oktmo?: string,
    okved?: string,
    fiodirector?: string,
    manageractionreason?: string,
    ogrn: string,
    addresslegalhint?: AddressHint.AsObject,
    contacthintsList: Array<ContactHint.AsObject>,
  }

  export enum NoteCase { 
    _NOTE_NOT_SET = 0,
    NOTE = 5,
  }

  export enum OkpoCase { 
    _OKPO_NOT_SET = 0,
    OKPO = 6,
  }

  export enum OktmoCase { 
    _OKTMO_NOT_SET = 0,
    OKTMO = 7,
  }

  export enum OkvedCase { 
    _OKVED_NOT_SET = 0,
    OKVED = 8,
  }

  export enum FiodirectorCase { 
    _FIODIRECTOR_NOT_SET = 0,
    FIODIRECTOR = 9,
  }

  export enum ManageractionreasonCase { 
    _MANAGERACTIONREASON_NOT_SET = 0,
    MANAGERACTIONREASON = 10,
  }

  export enum AddresslegalhintCase { 
    _ADDRESSLEGALHINT_NOT_SET = 0,
    ADDRESSLEGALHINT = 12,
  }
}

export class ContactHint extends jspb.Message {
  getType(): ContactType;
  setType(value: ContactType): ContactHint;

  getValue(): string;
  setValue(value: string): ContactHint;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContactHint.AsObject;
  static toObject(includeInstance: boolean, msg: ContactHint): ContactHint.AsObject;
  static serializeBinaryToWriter(message: ContactHint, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContactHint;
  static deserializeBinaryFromReader(message: ContactHint, reader: jspb.BinaryReader): ContactHint;
}

export namespace ContactHint {
  export type AsObject = {
    type: ContactType,
    value: string,
  }
}

export class AddressHint extends jspb.Message {
  getIndex(): number;
  setIndex(value: number): AddressHint;
  hasIndex(): boolean;
  clearIndex(): AddressHint;

  getCountry(): string;
  setCountry(value: string): AddressHint;
  hasCountry(): boolean;
  clearCountry(): AddressHint;

  getRegion(): string;
  setRegion(value: string): AddressHint;

  getDistrict(): string;
  setDistrict(value: string): AddressHint;
  hasDistrict(): boolean;
  clearDistrict(): AddressHint;

  getCity(): string;
  setCity(value: string): AddressHint;

  getStreet(): string;
  setStreet(value: string): AddressHint;
  hasStreet(): boolean;
  clearStreet(): AddressHint;

  getHouse(): string;
  setHouse(value: string): AddressHint;

  getBuildingorhousing(): string;
  setBuildingorhousing(value: string): AddressHint;
  hasBuildingorhousing(): boolean;
  clearBuildingorhousing(): AddressHint;

  getOfficeorroom(): string;
  setOfficeorroom(value: string): AddressHint;
  hasOfficeorroom(): boolean;
  clearOfficeorroom(): AddressHint;

  getLongitude(): number;
  setLongitude(value: number): AddressHint;
  hasLongitude(): boolean;
  clearLongitude(): AddressHint;

  getLatitude(): number;
  setLatitude(value: number): AddressHint;
  hasLatitude(): boolean;
  clearLatitude(): AddressHint;

  getOktmo(): number;
  setOktmo(value: number): AddressHint;
  hasOktmo(): boolean;
  clearOktmo(): AddressHint;

  getFiac(): string;
  setFiac(value: string): AddressHint;
  hasFiac(): boolean;
  clearFiac(): AddressHint;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AddressHint.AsObject;
  static toObject(includeInstance: boolean, msg: AddressHint): AddressHint.AsObject;
  static serializeBinaryToWriter(message: AddressHint, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AddressHint;
  static deserializeBinaryFromReader(message: AddressHint, reader: jspb.BinaryReader): AddressHint;
}

export namespace AddressHint {
  export type AsObject = {
    index?: number,
    country?: string,
    region: string,
    district?: string,
    city: string,
    street?: string,
    house: string,
    buildingorhousing?: string,
    officeorroom?: string,
    longitude?: number,
    latitude?: number,
    oktmo?: number,
    fiac?: string,
  }

  export enum IndexCase { 
    _INDEX_NOT_SET = 0,
    INDEX = 1,
  }

  export enum CountryCase { 
    _COUNTRY_NOT_SET = 0,
    COUNTRY = 2,
  }

  export enum DistrictCase { 
    _DISTRICT_NOT_SET = 0,
    DISTRICT = 4,
  }

  export enum StreetCase { 
    _STREET_NOT_SET = 0,
    STREET = 6,
  }

  export enum BuildingorhousingCase { 
    _BUILDINGORHOUSING_NOT_SET = 0,
    BUILDINGORHOUSING = 8,
  }

  export enum OfficeorroomCase { 
    _OFFICEORROOM_NOT_SET = 0,
    OFFICEORROOM = 9,
  }

  export enum LongitudeCase { 
    _LONGITUDE_NOT_SET = 0,
    LONGITUDE = 10,
  }

  export enum LatitudeCase { 
    _LATITUDE_NOT_SET = 0,
    LATITUDE = 11,
  }

  export enum OktmoCase { 
    _OKTMO_NOT_SET = 0,
    OKTMO = 12,
  }

  export enum FiacCase { 
    _FIAC_NOT_SET = 0,
    FIAC = 14,
  }
}

export class Contract extends jspb.Message {
  getId(): string;
  setId(value: string): Contract;

  getProjectcode(): string;
  setProjectcode(value: string): Contract;

  getProjectname(): string;
  setProjectname(value: string): Contract;

  getProjecttype(): ProjectType;
  setProjecttype(value: ProjectType): Contract;

  getContracttype(): ContractType;
  setContracttype(value: ContractType): Contract;

  getContractname(): string;
  setContractname(value: string): Contract;

  getContractnumber(): string;
  setContractnumber(value: string): Contract;

  getSignaturedate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setSignaturedate(value?: google_protobuf_timestamp_pb.Timestamp): Contract;
  hasSignaturedate(): boolean;
  clearSignaturedate(): Contract;

  getConclusiondate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setConclusiondate(value?: google_protobuf_timestamp_pb.Timestamp): Contract;
  hasConclusiondate(): boolean;
  clearConclusiondate(): Contract;

  getCompletiondate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCompletiondate(value?: google_protobuf_timestamp_pb.Timestamp): Contract;
  hasCompletiondate(): boolean;
  clearCompletiondate(): Contract;

  getStatus(): ContractStatus;
  setStatus(value: ContractStatus): Contract;

  getExternalid1c(): string;
  setExternalid1c(value: string): Contract;
  hasExternalid1c(): boolean;
  clearExternalid1c(): Contract;

  getDescription(): string;
  setDescription(value: string): Contract;
  hasDescription(): boolean;
  clearDescription(): Contract;

  getTotalamount(): number;
  setTotalamount(value: number): Contract;
  hasTotalamount(): boolean;
  clearTotalamount(): Contract;

  getCurrency(): string;
  setCurrency(value: string): Contract;
  hasCurrency(): boolean;
  clearCurrency(): Contract;

  getPaymentterms(): number;
  setPaymentterms(value: number): Contract;
  hasPaymentterms(): boolean;
  clearPaymentterms(): Contract;

  getVatrate(): number;
  setVatrate(value: number): Contract;
  hasVatrate(): boolean;
  clearVatrate(): Contract;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): Contract;

  getCreateddate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreateddate(value?: google_protobuf_timestamp_pb.Timestamp): Contract;
  hasCreateddate(): boolean;
  clearCreateddate(): Contract;

  getLastsyncdate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setLastsyncdate(value?: google_protobuf_timestamp_pb.Timestamp): Contract;
  hasLastsyncdate(): boolean;
  clearLastsyncdate(): Contract;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Contract.AsObject;
  static toObject(includeInstance: boolean, msg: Contract): Contract.AsObject;
  static serializeBinaryToWriter(message: Contract, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Contract;
  static deserializeBinaryFromReader(message: Contract, reader: jspb.BinaryReader): Contract;
}

export namespace Contract {
  export type AsObject = {
    id: string,
    projectcode: string,
    projectname: string,
    projecttype: ProjectType,
    contracttype: ContractType,
    contractname: string,
    contractnumber: string,
    signaturedate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    conclusiondate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    completiondate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    status: ContractStatus,
    externalid1c?: string,
    description?: string,
    totalamount?: number,
    currency?: string,
    paymentterms?: number,
    vatrate?: number,
    isdeleted: boolean,
    createddate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    lastsyncdate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum Externalid1cCase { 
    _EXTERNALID1C_NOT_SET = 0,
    EXTERNALID1C = 12,
  }

  export enum DescriptionCase { 
    _DESCRIPTION_NOT_SET = 0,
    DESCRIPTION = 13,
  }

  export enum TotalamountCase { 
    _TOTALAMOUNT_NOT_SET = 0,
    TOTALAMOUNT = 14,
  }

  export enum CurrencyCase { 
    _CURRENCY_NOT_SET = 0,
    CURRENCY = 15,
  }

  export enum PaymenttermsCase { 
    _PAYMENTTERMS_NOT_SET = 0,
    PAYMENTTERMS = 16,
  }

  export enum VatrateCase { 
    _VATRATE_NOT_SET = 0,
    VATRATE = 17,
  }

  export enum LastsyncdateCase { 
    _LASTSYNCDATE_NOT_SET = 0,
    LASTSYNCDATE = 20,
  }
}

export class ContractOrganization extends jspb.Message {
  getId(): string;
  setId(value: string): ContractOrganization;

  getContractid(): string;
  setContractid(value: string): ContractOrganization;

  getOrganizationid(): string;
  setOrganizationid(value: string): ContractOrganization;

  getOrganizationname(): string;
  setOrganizationname(value: string): ContractOrganization;

  getRole(): OrganizationRole;
  setRole(value: OrganizationRole): ContractOrganization;

  getRoledescription(): string;
  setRoledescription(value: string): ContractOrganization;
  hasRoledescription(): boolean;
  clearRoledescription(): ContractOrganization;

  getActivefrom(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setActivefrom(value?: google_protobuf_timestamp_pb.Timestamp): ContractOrganization;
  hasActivefrom(): boolean;
  clearActivefrom(): ContractOrganization;

  getActivetill(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setActivetill(value?: google_protobuf_timestamp_pb.Timestamp): ContractOrganization;
  hasActivetill(): boolean;
  clearActivetill(): ContractOrganization;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): ContractOrganization;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractOrganization.AsObject;
  static toObject(includeInstance: boolean, msg: ContractOrganization): ContractOrganization.AsObject;
  static serializeBinaryToWriter(message: ContractOrganization, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractOrganization;
  static deserializeBinaryFromReader(message: ContractOrganization, reader: jspb.BinaryReader): ContractOrganization;
}

export namespace ContractOrganization {
  export type AsObject = {
    id: string,
    contractid: string,
    organizationid: string,
    organizationname: string,
    role: OrganizationRole,
    roledescription?: string,
    activefrom?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    activetill?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    isdeleted: boolean,
  }

  export enum RoledescriptionCase { 
    _ROLEDESCRIPTION_NOT_SET = 0,
    ROLEDESCRIPTION = 6,
  }

  export enum ActivetillCase { 
    _ACTIVETILL_NOT_SET = 0,
    ACTIVETILL = 8,
  }
}

export class ContractWithOrganizations extends jspb.Message {
  getContract(): Contract | undefined;
  setContract(value?: Contract): ContractWithOrganizations;
  hasContract(): boolean;
  clearContract(): ContractWithOrganizations;

  getOrganizationsList(): Array<ContractOrganization>;
  setOrganizationsList(value: Array<ContractOrganization>): ContractWithOrganizations;
  clearOrganizationsList(): ContractWithOrganizations;
  addOrganizations(value?: ContractOrganization, index?: number): ContractOrganization;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractWithOrganizations.AsObject;
  static toObject(includeInstance: boolean, msg: ContractWithOrganizations): ContractWithOrganizations.AsObject;
  static serializeBinaryToWriter(message: ContractWithOrganizations, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractWithOrganizations;
  static deserializeBinaryFromReader(message: ContractWithOrganizations, reader: jspb.BinaryReader): ContractWithOrganizations;
}

export namespace ContractWithOrganizations {
  export type AsObject = {
    contract?: Contract.AsObject,
    organizationsList: Array<ContractOrganization.AsObject>,
  }
}

export class ContractFilter extends jspb.Message {
  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): ContractFilter;
  hasIsdeleted(): boolean;
  clearIsdeleted(): ContractFilter;

  getProjectcode(): string;
  setProjectcode(value: string): ContractFilter;
  hasProjectcode(): boolean;
  clearProjectcode(): ContractFilter;

  getContractnumber(): string;
  setContractnumber(value: string): ContractFilter;
  hasContractnumber(): boolean;
  clearContractnumber(): ContractFilter;

  getContractname(): string;
  setContractname(value: string): ContractFilter;
  hasContractname(): boolean;
  clearContractname(): ContractFilter;

  getStatus(): ContractStatus;
  setStatus(value: ContractStatus): ContractFilter;
  hasStatus(): boolean;
  clearStatus(): ContractFilter;

  getContracttype(): ContractType;
  setContracttype(value: ContractType): ContractFilter;
  hasContracttype(): boolean;
  clearContracttype(): ContractFilter;

  getProjecttype(): ProjectType;
  setProjecttype(value: ProjectType): ContractFilter;
  hasProjecttype(): boolean;
  clearProjecttype(): ContractFilter;

  getOrganizationid(): string;
  setOrganizationid(value: string): ContractFilter;
  hasOrganizationid(): boolean;
  clearOrganizationid(): ContractFilter;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractFilter.AsObject;
  static toObject(includeInstance: boolean, msg: ContractFilter): ContractFilter.AsObject;
  static serializeBinaryToWriter(message: ContractFilter, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractFilter;
  static deserializeBinaryFromReader(message: ContractFilter, reader: jspb.BinaryReader): ContractFilter;
}

export namespace ContractFilter {
  export type AsObject = {
    isdeleted?: boolean,
    projectcode?: string,
    contractnumber?: string,
    contractname?: string,
    status?: ContractStatus,
    contracttype?: ContractType,
    projecttype?: ProjectType,
    organizationid?: string,
  }

  export enum IsdeletedCase { 
    _ISDELETED_NOT_SET = 0,
    ISDELETED = 1,
  }

  export enum ProjectcodeCase { 
    _PROJECTCODE_NOT_SET = 0,
    PROJECTCODE = 2,
  }

  export enum ContractnumberCase { 
    _CONTRACTNUMBER_NOT_SET = 0,
    CONTRACTNUMBER = 3,
  }

  export enum ContractnameCase { 
    _CONTRACTNAME_NOT_SET = 0,
    CONTRACTNAME = 4,
  }

  export enum StatusCase { 
    _STATUS_NOT_SET = 0,
    STATUS = 5,
  }

  export enum ContracttypeCase { 
    _CONTRACTTYPE_NOT_SET = 0,
    CONTRACTTYPE = 6,
  }

  export enum ProjecttypeCase { 
    _PROJECTTYPE_NOT_SET = 0,
    PROJECTTYPE = 7,
  }

  export enum OrganizationidCase { 
    _ORGANIZATIONID_NOT_SET = 0,
    ORGANIZATIONID = 8,
  }
}

export class ContractListRequest extends jspb.Message {
  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): ContractListRequest;
  hasPagination(): boolean;
  clearPagination(): ContractListRequest;

  getFilter(): ContractFilter | undefined;
  setFilter(value?: ContractFilter): ContractListRequest;
  hasFilter(): boolean;
  clearFilter(): ContractListRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractListRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ContractListRequest): ContractListRequest.AsObject;
  static serializeBinaryToWriter(message: ContractListRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractListRequest;
  static deserializeBinaryFromReader(message: ContractListRequest, reader: jspb.BinaryReader): ContractListRequest;
}

export namespace ContractListRequest {
  export type AsObject = {
    pagination?: common_pb.PaginationRequest.AsObject,
    filter?: ContractFilter.AsObject,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FilterCase { 
    _FILTER_NOT_SET = 0,
    FILTER = 2,
  }
}

export class ContractResult extends jspb.Message {
  getPagination(): common_pb.PaginationResponse | undefined;
  setPagination(value?: common_pb.PaginationResponse): ContractResult;
  hasPagination(): boolean;
  clearPagination(): ContractResult;

  getFilter(): ContractFilter | undefined;
  setFilter(value?: ContractFilter): ContractResult;
  hasFilter(): boolean;
  clearFilter(): ContractResult;

  getContractsList(): Array<Contract>;
  setContractsList(value: Array<Contract>): ContractResult;
  clearContractsList(): ContractResult;
  addContracts(value?: Contract, index?: number): Contract;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractResult.AsObject;
  static toObject(includeInstance: boolean, msg: ContractResult): ContractResult.AsObject;
  static serializeBinaryToWriter(message: ContractResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractResult;
  static deserializeBinaryFromReader(message: ContractResult, reader: jspb.BinaryReader): ContractResult;
}

export namespace ContractResult {
  export type AsObject = {
    pagination?: common_pb.PaginationResponse.AsObject,
    filter?: ContractFilter.AsObject,
    contractsList: Array<Contract.AsObject>,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FilterCase { 
    _FILTER_NOT_SET = 0,
    FILTER = 2,
  }
}

export class ContractListResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): ContractListResponse;
  hasError(): boolean;
  clearError(): ContractListResponse;

  getResult(): ContractResult | undefined;
  setResult(value?: ContractResult): ContractListResponse;
  hasResult(): boolean;
  clearResult(): ContractListResponse;

  getResponseCase(): ContractListResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractListResponse.AsObject;
  static toObject(includeInstance: boolean, msg: ContractListResponse): ContractListResponse.AsObject;
  static serializeBinaryToWriter(message: ContractListResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractListResponse;
  static deserializeBinaryFromReader(message: ContractListResponse, reader: jspb.BinaryReader): ContractListResponse;
}

export namespace ContractListResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: ContractResult.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class ContractResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): ContractResponse;
  hasError(): boolean;
  clearError(): ContractResponse;

  getResult(): Contract | undefined;
  setResult(value?: Contract): ContractResponse;
  hasResult(): boolean;
  clearResult(): ContractResponse;

  getResponseCase(): ContractResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractResponse.AsObject;
  static toObject(includeInstance: boolean, msg: ContractResponse): ContractResponse.AsObject;
  static serializeBinaryToWriter(message: ContractResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractResponse;
  static deserializeBinaryFromReader(message: ContractResponse, reader: jspb.BinaryReader): ContractResponse;
}

export namespace ContractResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: Contract.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class ContractsByProjectRequest extends jspb.Message {
  getProjectcode(): string;
  setProjectcode(value: string): ContractsByProjectRequest;

  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): ContractsByProjectRequest;
  hasPagination(): boolean;
  clearPagination(): ContractsByProjectRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractsByProjectRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ContractsByProjectRequest): ContractsByProjectRequest.AsObject;
  static serializeBinaryToWriter(message: ContractsByProjectRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractsByProjectRequest;
  static deserializeBinaryFromReader(message: ContractsByProjectRequest, reader: jspb.BinaryReader): ContractsByProjectRequest;
}

export namespace ContractsByProjectRequest {
  export type AsObject = {
    projectcode: string,
    pagination?: common_pb.PaginationRequest.AsObject,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 2,
  }
}

export class ContractPaymentMethod extends jspb.Message {
  getId(): string;
  setId(value: string): ContractPaymentMethod;

  getContractid(): string;
  setContractid(value: string): ContractPaymentMethod;

  getMethodtype(): PaymentMethodType;
  setMethodtype(value: PaymentMethodType): ContractPaymentMethod;

  getCode(): string;
  setCode(value: string): ContractPaymentMethod;

  getName(): string;
  setName(value: string): ContractPaymentMethod;

  getDescription(): string;
  setDescription(value: string): ContractPaymentMethod;
  hasDescription(): boolean;
  clearDescription(): ContractPaymentMethod;

  getIsactive(): boolean;
  setIsactive(value: boolean): ContractPaymentMethod;

  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): ContractPaymentMethod;

  getCreateddate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreateddate(value?: google_protobuf_timestamp_pb.Timestamp): ContractPaymentMethod;
  hasCreateddate(): boolean;
  clearCreateddate(): ContractPaymentMethod;

  getLastsyncdate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setLastsyncdate(value?: google_protobuf_timestamp_pb.Timestamp): ContractPaymentMethod;
  hasLastsyncdate(): boolean;
  clearLastsyncdate(): ContractPaymentMethod;

  getExternalid(): string;
  setExternalid(value: string): ContractPaymentMethod;
  hasExternalid(): boolean;
  clearExternalid(): ContractPaymentMethod;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ContractPaymentMethod.AsObject;
  static toObject(includeInstance: boolean, msg: ContractPaymentMethod): ContractPaymentMethod.AsObject;
  static serializeBinaryToWriter(message: ContractPaymentMethod, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ContractPaymentMethod;
  static deserializeBinaryFromReader(message: ContractPaymentMethod, reader: jspb.BinaryReader): ContractPaymentMethod;
}

export namespace ContractPaymentMethod {
  export type AsObject = {
    id: string,
    contractid: string,
    methodtype: PaymentMethodType,
    code: string,
    name: string,
    description?: string,
    isactive: boolean,
    isdeleted: boolean,
    createddate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    lastsyncdate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    externalid?: string,
  }

  export enum DescriptionCase { 
    _DESCRIPTION_NOT_SET = 0,
    DESCRIPTION = 6,
  }

  export enum LastsyncdateCase { 
    _LASTSYNCDATE_NOT_SET = 0,
    LASTSYNCDATE = 10,
  }

  export enum ExternalidCase { 
    _EXTERNALID_NOT_SET = 0,
    EXTERNALID = 11,
  }
}

export class PaymentMethodFilter extends jspb.Message {
  getIsdeleted(): boolean;
  setIsdeleted(value: boolean): PaymentMethodFilter;
  hasIsdeleted(): boolean;
  clearIsdeleted(): PaymentMethodFilter;

  getContractid(): string;
  setContractid(value: string): PaymentMethodFilter;
  hasContractid(): boolean;
  clearContractid(): PaymentMethodFilter;

  getMethodtype(): PaymentMethodType;
  setMethodtype(value: PaymentMethodType): PaymentMethodFilter;
  hasMethodtype(): boolean;
  clearMethodtype(): PaymentMethodFilter;

  getCode(): string;
  setCode(value: string): PaymentMethodFilter;
  hasCode(): boolean;
  clearCode(): PaymentMethodFilter;

  getIsactive(): boolean;
  setIsactive(value: boolean): PaymentMethodFilter;
  hasIsactive(): boolean;
  clearIsactive(): PaymentMethodFilter;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaymentMethodFilter.AsObject;
  static toObject(includeInstance: boolean, msg: PaymentMethodFilter): PaymentMethodFilter.AsObject;
  static serializeBinaryToWriter(message: PaymentMethodFilter, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaymentMethodFilter;
  static deserializeBinaryFromReader(message: PaymentMethodFilter, reader: jspb.BinaryReader): PaymentMethodFilter;
}

export namespace PaymentMethodFilter {
  export type AsObject = {
    isdeleted?: boolean,
    contractid?: string,
    methodtype?: PaymentMethodType,
    code?: string,
    isactive?: boolean,
  }

  export enum IsdeletedCase { 
    _ISDELETED_NOT_SET = 0,
    ISDELETED = 1,
  }

  export enum ContractidCase { 
    _CONTRACTID_NOT_SET = 0,
    CONTRACTID = 2,
  }

  export enum MethodtypeCase { 
    _METHODTYPE_NOT_SET = 0,
    METHODTYPE = 3,
  }

  export enum CodeCase { 
    _CODE_NOT_SET = 0,
    CODE = 4,
  }

  export enum IsactiveCase { 
    _ISACTIVE_NOT_SET = 0,
    ISACTIVE = 5,
  }
}

export class PaymentMethodListRequest extends jspb.Message {
  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): PaymentMethodListRequest;
  hasPagination(): boolean;
  clearPagination(): PaymentMethodListRequest;

  getFilter(): PaymentMethodFilter | undefined;
  setFilter(value?: PaymentMethodFilter): PaymentMethodListRequest;
  hasFilter(): boolean;
  clearFilter(): PaymentMethodListRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaymentMethodListRequest.AsObject;
  static toObject(includeInstance: boolean, msg: PaymentMethodListRequest): PaymentMethodListRequest.AsObject;
  static serializeBinaryToWriter(message: PaymentMethodListRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaymentMethodListRequest;
  static deserializeBinaryFromReader(message: PaymentMethodListRequest, reader: jspb.BinaryReader): PaymentMethodListRequest;
}

export namespace PaymentMethodListRequest {
  export type AsObject = {
    pagination?: common_pb.PaginationRequest.AsObject,
    filter?: PaymentMethodFilter.AsObject,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FilterCase { 
    _FILTER_NOT_SET = 0,
    FILTER = 2,
  }
}

export class PaymentMethodResult extends jspb.Message {
  getPagination(): common_pb.PaginationResponse | undefined;
  setPagination(value?: common_pb.PaginationResponse): PaymentMethodResult;
  hasPagination(): boolean;
  clearPagination(): PaymentMethodResult;

  getFilter(): PaymentMethodFilter | undefined;
  setFilter(value?: PaymentMethodFilter): PaymentMethodResult;
  hasFilter(): boolean;
  clearFilter(): PaymentMethodResult;

  getPaymentmethodsList(): Array<ContractPaymentMethod>;
  setPaymentmethodsList(value: Array<ContractPaymentMethod>): PaymentMethodResult;
  clearPaymentmethodsList(): PaymentMethodResult;
  addPaymentmethods(value?: ContractPaymentMethod, index?: number): ContractPaymentMethod;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaymentMethodResult.AsObject;
  static toObject(includeInstance: boolean, msg: PaymentMethodResult): PaymentMethodResult.AsObject;
  static serializeBinaryToWriter(message: PaymentMethodResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaymentMethodResult;
  static deserializeBinaryFromReader(message: PaymentMethodResult, reader: jspb.BinaryReader): PaymentMethodResult;
}

export namespace PaymentMethodResult {
  export type AsObject = {
    pagination?: common_pb.PaginationResponse.AsObject,
    filter?: PaymentMethodFilter.AsObject,
    paymentmethodsList: Array<ContractPaymentMethod.AsObject>,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 1,
  }

  export enum FilterCase { 
    _FILTER_NOT_SET = 0,
    FILTER = 2,
  }
}

export class PaymentMethodListResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): PaymentMethodListResponse;
  hasError(): boolean;
  clearError(): PaymentMethodListResponse;

  getResult(): PaymentMethodResult | undefined;
  setResult(value?: PaymentMethodResult): PaymentMethodListResponse;
  hasResult(): boolean;
  clearResult(): PaymentMethodListResponse;

  getResponseCase(): PaymentMethodListResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaymentMethodListResponse.AsObject;
  static toObject(includeInstance: boolean, msg: PaymentMethodListResponse): PaymentMethodListResponse.AsObject;
  static serializeBinaryToWriter(message: PaymentMethodListResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaymentMethodListResponse;
  static deserializeBinaryFromReader(message: PaymentMethodListResponse, reader: jspb.BinaryReader): PaymentMethodListResponse;
}

export namespace PaymentMethodListResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: PaymentMethodResult.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class PaymentMethodResponse extends jspb.Message {
  getError(): common_pb.OperationError | undefined;
  setError(value?: common_pb.OperationError): PaymentMethodResponse;
  hasError(): boolean;
  clearError(): PaymentMethodResponse;

  getResult(): ContractPaymentMethod | undefined;
  setResult(value?: ContractPaymentMethod): PaymentMethodResponse;
  hasResult(): boolean;
  clearResult(): PaymentMethodResponse;

  getResponseCase(): PaymentMethodResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaymentMethodResponse.AsObject;
  static toObject(includeInstance: boolean, msg: PaymentMethodResponse): PaymentMethodResponse.AsObject;
  static serializeBinaryToWriter(message: PaymentMethodResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaymentMethodResponse;
  static deserializeBinaryFromReader(message: PaymentMethodResponse, reader: jspb.BinaryReader): PaymentMethodResponse;
}

export namespace PaymentMethodResponse {
  export type AsObject = {
    error?: common_pb.OperationError.AsObject,
    result?: ContractPaymentMethod.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    ERROR = 1,
    RESULT = 2,
  }
}

export class PaymentMethodsByContractRequest extends jspb.Message {
  getContractid(): string;
  setContractid(value: string): PaymentMethodsByContractRequest;

  getPagination(): common_pb.PaginationRequest | undefined;
  setPagination(value?: common_pb.PaginationRequest): PaymentMethodsByContractRequest;
  hasPagination(): boolean;
  clearPagination(): PaymentMethodsByContractRequest;

  getIncludeinactive(): boolean;
  setIncludeinactive(value: boolean): PaymentMethodsByContractRequest;
  hasIncludeinactive(): boolean;
  clearIncludeinactive(): PaymentMethodsByContractRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PaymentMethodsByContractRequest.AsObject;
  static toObject(includeInstance: boolean, msg: PaymentMethodsByContractRequest): PaymentMethodsByContractRequest.AsObject;
  static serializeBinaryToWriter(message: PaymentMethodsByContractRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PaymentMethodsByContractRequest;
  static deserializeBinaryFromReader(message: PaymentMethodsByContractRequest, reader: jspb.BinaryReader): PaymentMethodsByContractRequest;
}

export namespace PaymentMethodsByContractRequest {
  export type AsObject = {
    contractid: string,
    pagination?: common_pb.PaginationRequest.AsObject,
    includeinactive?: boolean,
  }

  export enum PaginationCase { 
    _PAGINATION_NOT_SET = 0,
    PAGINATION = 2,
  }

  export enum IncludeinactiveCase { 
    _INCLUDEINACTIVE_NOT_SET = 0,
    INCLUDEINACTIVE = 3,
  }
}

export enum ContactType { 
  CT_PHONE = 0,
  CT_EMAIL = 1,
}
export enum AddressType { 
  AT_LEGAL = 0,
  AT_ACTUAL = 1,
  AT_MAILING = 2,
}
export enum ContractStatus { 
  CS_DRAFT = 0,
  CS_ACTIVE = 1,
  CS_EXPIRING = 2,
  CS_COMPLETED = 3,
  CS_TERMINATED = 4,
}
export enum ContractType { 
  CT_SYSTEM_RULES = 0,
  CT_SERVICE = 1,
  CT_TRANSPORT = 2,
  CT_PROCESSING = 3,
}
export enum ProjectType { 
  PT_TRANSPORT_SYSTEM = 0,
  PT_METRO_SYSTEM = 1,
  PT_BUS_SYSTEM = 2,
  PT_TAXI_SYSTEM = 3,
}
export enum OrganizationRole { 
  OR_OPERATOR = 0,
  OR_CARRIER = 1,
  OR_PROCESSING_CENTER = 2,
  OR_CONTRACTOR = 3,
  OR_PARTNER = 4,
}
export enum PaymentMethodType { 
  PMT_BANK_CARD = 0,
  PMT_CASH = 1,
  PMT_TROIKA_SINGLE = 2,
  PMT_TROIKA_SUBSCRIPTION = 3,
  PMT_MPC_DISCOUNT = 4,
  PMT_MPC_SOCIAL = 5,
  PMT_MPC_SCHOOL = 6,
  PMT_MPC_STUDENT_SINGLE = 7,
  PMT_MPC_STUDENT_SUBSCRIPTION = 8,
  PMT_TC_RESIDENT = 9,
  PMT_MOBILE_BC = 10,
  PMT_MOBILE_VIRTUAL_TC = 11,
  PMT_MOBILE_SBP = 12,
  PMT_REGIONAL_TC = 13,
  PMT_SOCIAL_TC = 14,
  PMT_OTHER_CARDS = 15,
}
