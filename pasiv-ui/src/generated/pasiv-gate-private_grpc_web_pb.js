/**
 * @fileoverview gRPC-Web generated client stub for ru.sbertroika.pasiv.gate.v1
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v3.19.1
// source: pasiv-gate-private.proto


/* eslint-disable */
// @ts-nocheck



const grpc = {};
grpc.web = require('grpc-web');


var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js')

var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js')

var common_pb = require('./common_pb.js')
const proto = {};
proto.ru = {};
proto.ru.sbertroika = {};
proto.ru.sbertroika.pasiv = {};
proto.ru.sbertroika.pasiv.gate = {};
proto.ru.sbertroika.pasiv.gate.v1 = require('./pasiv-gate-private_pb.js');

/**
 * @param {string} hostname
 * @param {?Object} credentials
 * @param {?grpc.web.ClientOptions} options
 * @constructor
 * @struct
 * @final
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient =
    function(hostname, credentials, options) {
  if (!options) options = {};
  options.format = 'text';

  /**
   * @private @const {!grpc.web.GrpcWebClientBase} The client
   */
  this.client_ = new grpc.web.GrpcWebClientBase(options);

  /**
   * @private @const {string} The hostname
   */
  this.hostname_ = hostname.replace(/\/+$/, '');

};


/**
 * @param {string} hostname
 * @param {?Object} credentials
 * @param {?grpc.web.ClientOptions} options
 * @constructor
 * @struct
 * @final
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient =
    function(hostname, credentials, options) {
  if (!options) options = {};
  options.format = 'text';

  /**
   * @private @const {!grpc.web.GrpcWebClientBase} The client
   */
  this.client_ = new grpc.web.GrpcWebClientBase(options);

  /**
   * @private @const {string} The hostname
   */
  this.hostname_ = hostname.replace(/\/+$/, '');

};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_createOrganization = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createOrganization',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.createOrganization =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createOrganization,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.createOrganization =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createOrganization);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.Organization,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_updateOrganization = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateOrganization',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.Organization,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.Organization} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Organization} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.updateOrganization =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateOrganization,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Organization} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.updateOrganization =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateOrganization);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_organizationList = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationList',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.organizationList =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationList,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.organizationList =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationList);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_organizationById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.organizationById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.organizationById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_deleteOrganization = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteOrganization',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.deleteOrganization =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteOrganization,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.deleteOrganization =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteOrganization);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_recoverOrganization = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverOrganization',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.recoverOrganization =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_recoverOrganization,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.recoverOrganization =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverOrganization',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_recoverOrganization);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_organizationListForProject = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationListForProject',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.organizationListForProject =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationListForProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationListForProject,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.organizationListForProject =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationListForProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationListForProject);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_addOrganizationInProject = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addOrganizationInProject',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.addOrganizationInProject =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addOrganizationInProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addOrganizationInProject,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.addOrganizationInProject =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addOrganizationInProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addOrganizationInProject);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_removeOrganizationInProject = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/removeOrganizationInProject',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.removeOrganizationInProject =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/removeOrganizationInProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_removeOrganizationInProject,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.removeOrganizationInProject =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/removeOrganizationInProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_removeOrganizationInProject);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_createAddress = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createAddress',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.createAddress =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createAddress,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.createAddress =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createAddress);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_updateAddress = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateAddress',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.updateAddress =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateAddress,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.updateAddress =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateAddress);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.AddressResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_addressById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  proto.ru.sbertroika.pasiv.gate.v1.AddressResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.AddressResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.AddressResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.addressById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addressById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.AddressResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.addressById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addressById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_addressList = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressList',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest,
  proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.addressList =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addressList,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.AddressListRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.addressList =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addressList);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_deleteAddress = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteAddress',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.deleteAddress =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteAddress,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.deleteAddress =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteAddress);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_recoverAddress = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverAddress',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.recoverAddress =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_recoverAddress,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.recoverAddress =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverAddress',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_recoverAddress);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.Contact,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_createContact = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createContact',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.Contact,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.createContact =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createContact,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.createContact =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createContact);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.Contact,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_updateContact = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateContact',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.Contact,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.updateContact =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateContact,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contact} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.updateContact =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateContact);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_contactList = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactList',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest,
  proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.contactList =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contactList,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContactListRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.ContactListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.contactList =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contactList);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContactResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_contactById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  proto.ru.sbertroika.pasiv.gate.v1.ContactResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.ContactResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.ContactResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.contactById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contactById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.ContactResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.contactById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contactById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_deleteContact = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteContact',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.deleteContact =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteContact,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.deleteContact =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteContact);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_recoverContact = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverContact',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.recoverContact =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_recoverContact,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.recoverContact =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/recoverContact',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_recoverContact);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest,
 *   !proto.ru.sbertroika.common.v1.HistoryResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_contactHistoryById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactHistoryById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest,
  common_pb.HistoryResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.HistoryResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.HistoryResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.HistoryResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.contactHistoryById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactHistoryById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contactHistoryById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.HistoryResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.contactHistoryById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contactHistoryById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contactHistoryById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest,
 *   !proto.ru.sbertroika.common.v1.HistoryResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_addressHistoryById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressHistoryById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest,
  common_pb.HistoryResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.HistoryResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.HistoryResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.HistoryResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.addressHistoryById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressHistoryById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addressHistoryById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.HistoryResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.addressHistoryById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/addressHistoryById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_addressHistoryById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest,
 *   !proto.ru.sbertroika.common.v1.HistoryResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_organizationHistoryById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationHistoryById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest,
  common_pb.HistoryResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.HistoryResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.HistoryResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.HistoryResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.organizationHistoryById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationHistoryById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationHistoryById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.HistoryResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.organizationHistoryById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationHistoryById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationHistoryById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_organizationHintByINN = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationHintByINN',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest,
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.organizationHintByINN =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationHintByINN',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationHintByINN,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.organizationHintByINN =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/organizationHintByINN',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_organizationHintByINN);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_contractList = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractList',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest,
  proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.contractList =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contractList,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractListRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.contractList =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contractList);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_contractById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  proto.ru.sbertroika.pasiv.gate.v1.ContractResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.ContractResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.ContractResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.contractById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contractById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.ContractResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.contractById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contractById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_createContract = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createContract',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.createContract =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createContract,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractWithOrganizations} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.createContract =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createContract);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.Contract,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_updateContract = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateContract',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.Contract,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contract} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contract} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.updateContract =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateContract,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.Contract} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.updateContract =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updateContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updateContract);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_deleteContract = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteContract',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.deleteContract =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteContract,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.deleteContract =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deleteContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deleteContract);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_contractsByProject = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractsByProject',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest,
  proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.contractsByProject =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractsByProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contractsByProject,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractsByProjectRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.ContractListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.contractsByProject =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/contractsByProject',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_contractsByProject);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_paymentMethodList = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodList',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest,
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.paymentMethodList =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_paymentMethodList,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.paymentMethodList =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodList',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_paymentMethodList);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_paymentMethodById = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodById',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.paymentMethodById =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_paymentMethodById,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.paymentMethodById =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodById',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_paymentMethodById);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_createPaymentMethod = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createPaymentMethod',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.createPaymentMethod =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createPaymentMethod',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createPaymentMethod,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.createPaymentMethod =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/createPaymentMethod',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_createPaymentMethod);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_updatePaymentMethod = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updatePaymentMethod',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.updatePaymentMethod =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updatePaymentMethod',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updatePaymentMethod,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ContractPaymentMethod} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.updatePaymentMethod =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/updatePaymentMethod',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_updatePaymentMethod);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
 *   !proto.ru.sbertroika.common.v1.EmptyResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_deletePaymentMethod = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deletePaymentMethod',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest,
  common_pb.EmptyResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  common_pb.EmptyResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.common.v1.EmptyResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.common.v1.EmptyResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.deletePaymentMethod =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deletePaymentMethod',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deletePaymentMethod,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.common.v1.EmptyResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.deletePaymentMethod =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/deletePaymentMethod',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_deletePaymentMethod);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse>}
 */
const methodDescriptor_PASIVGatePrivateService_paymentMethodsByContract = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodsByContract',
  grpc.web.MethodType.UNARY,
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest,
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse,
  /**
   * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceClient.prototype.paymentMethodsByContract =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodsByContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_paymentMethodsByContract,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodsByContractRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.PaymentMethodListResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServicePromiseClient.prototype.paymentMethodsByContract =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateService/paymentMethodsByContract',
      request,
      metadata || {},
      methodDescriptor_PASIVGatePrivateService_paymentMethodsByContract);
};


module.exports = proto.ru.sbertroika.pasiv.gate.v1;

