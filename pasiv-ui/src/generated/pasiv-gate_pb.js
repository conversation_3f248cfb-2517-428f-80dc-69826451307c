// source: pasiv-gate.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js');
goog.object.extend(proto, google_protobuf_empty_pb);
var common_pb = require('./common_pb.js');
goog.object.extend(proto, common_pb);
var common$manifest_pb = require('./common-manifest_pb.js');
goog.object.extend(proto, common$manifest_pb);
var common$manifest$pasiv_pb = require('./common-manifest-pasiv_pb.js');
goog.object.extend(proto, common$manifest$pasiv_pb);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse', null, global);
goog.exportSymbol('proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.ResponseCase', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.oneofGroups_);
};
goog.inherits(proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.displayName = 'proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse';
}

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  ERROR: 1,
  MANIFEST: 2
};

/**
 * @return {proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.ResponseCase}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.getResponseCase = function() {
  return /** @type {proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    error: (f = msg.getError()) && common_pb.OperationError.toObject(includeInstance, f),
    manifest: (f = msg.getManifest()) && common$manifest$pasiv_pb.ManifestPasiv.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse;
  return proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new common_pb.OperationError;
      reader.readMessage(value,common_pb.OperationError.deserializeBinaryFromReader);
      msg.setError(value);
      break;
    case 2:
      var value = new common$manifest$pasiv_pb.ManifestPasiv;
      reader.readMessage(value,common$manifest$pasiv_pb.ManifestPasiv.deserializeBinaryFromReader);
      msg.setManifest(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      common_pb.OperationError.serializeBinaryToWriter
    );
  }
  f = message.getManifest();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      common$manifest$pasiv_pb.ManifestPasiv.serializeBinaryToWriter
    );
  }
};


/**
 * optional ru.sbertroika.common.v1.OperationError error = 1;
 * @return {?proto.ru.sbertroika.common.v1.OperationError}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.getError = function() {
  return /** @type{?proto.ru.sbertroika.common.v1.OperationError} */ (
    jspb.Message.getWrapperField(this, common_pb.OperationError, 1));
};


/**
 * @param {?proto.ru.sbertroika.common.v1.OperationError|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.setError = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.clearError = function() {
  return this.setError(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.hasError = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv manifest = 2;
 * @return {?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.getManifest = function() {
  return /** @type{?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv} */ (
    jspb.Message.getWrapperField(this, common$manifest$pasiv_pb.ManifestPasiv, 2));
};


/**
 * @param {?proto.ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv|undefined} value
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse} returns this
*/
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.setManifest = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse} returns this
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.clearManifest = function() {
  return this.setManifest(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.prototype.hasManifest = function() {
  return jspb.Message.getField(this, 2) != null;
};


goog.object.extend(exports, proto.ru.sbertroika.pasiv.gate.v1);
