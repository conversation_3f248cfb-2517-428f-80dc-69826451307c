/**
 * @fileoverview gRPC-Web generated client stub for ru.sbertroika.pasiv.gate.v1
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v3.19.1
// source: pasiv-gate.proto


/* eslint-disable */
// @ts-nocheck



const grpc = {};
grpc.web = require('grpc-web');


var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js')

var common_pb = require('./common_pb.js')

var common$manifest_pb = require('./common-manifest_pb.js')

var common$manifest$pasiv_pb = require('./common-manifest-pasiv_pb.js')
const proto = {};
proto.ru = {};
proto.ru.sbertroika = {};
proto.ru.sbertroika.pasiv = {};
proto.ru.sbertroika.pasiv.gate = {};
proto.ru.sbertroika.pasiv.gate.v1 = require('./pasiv-gate_pb.js');

/**
 * @param {string} hostname
 * @param {?Object} credentials
 * @param {?grpc.web.ClientOptions} options
 * @constructor
 * @struct
 * @final
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGateServiceClient =
    function(hostname, credentials, options) {
  if (!options) options = {};
  options.format = 'text';

  /**
   * @private @const {!grpc.web.GrpcWebClientBase} The client
   */
  this.client_ = new grpc.web.GrpcWebClientBase(options);

  /**
   * @private @const {string} The hostname
   */
  this.hostname_ = hostname.replace(/\/+$/, '');

};


/**
 * @param {string} hostname
 * @param {?Object} credentials
 * @param {?grpc.web.ClientOptions} options
 * @constructor
 * @struct
 * @final
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGateServicePromiseClient =
    function(hostname, credentials, options) {
  if (!options) options = {};
  options.format = 'text';

  /**
   * @private @const {!grpc.web.GrpcWebClientBase} The client
   */
  this.client_ = new grpc.web.GrpcWebClientBase(options);

  /**
   * @private @const {string} The hostname
   */
  this.hostname_ = hostname.replace(/\/+$/, '');

};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.ru.sbertroika.common.manifest.v1.ManifestRequest,
 *   !proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse>}
 */
const methodDescriptor_PASIVGateService_getManifest = new grpc.web.MethodDescriptor(
  '/ru.sbertroika.pasiv.gate.v1.PASIVGateService/getManifest',
  grpc.web.MethodType.UNARY,
  common$manifest_pb.ManifestRequest,
  proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse,
  /**
   * @param {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse.deserializeBinary
);


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGateServiceClient.prototype.getManifest =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGateService/getManifest',
      request,
      metadata || {},
      methodDescriptor_PASIVGateService_getManifest,
      callback);
};


/**
 * @param {!proto.ru.sbertroika.common.manifest.v1.ManifestRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.ru.sbertroika.pasiv.gate.v1.ManifestResponse>}
 *     Promise that resolves to the response
 */
proto.ru.sbertroika.pasiv.gate.v1.PASIVGateServicePromiseClient.prototype.getManifest =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/ru.sbertroika.pasiv.gate.v1.PASIVGateService/getManifest',
      request,
      metadata || {},
      methodDescriptor_PASIVGateService_getManifest);
};


module.exports = proto.ru.sbertroika.pasiv.gate.v1;

