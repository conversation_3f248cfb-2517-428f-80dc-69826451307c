<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { usePaymentMethods } from '@/composables/usePaymentMethods';
import { ContractService } from '@/service/ContractService';
import { PaymentMethodFormatter } from '@/utils/paymentMethodUtils';
import { PAYMENT_METHOD_CATEGORIES } from '@/constants/paymentMethod';
import PaymentMethodStats from '@/components/finance/PaymentMethodStats.vue';
import PaymentMethodFilters from '@/components/finance/PaymentMethodFilters.vue';

const route = useRoute();
const router = useRouter();
const toast = useToast();
const confirm = useConfirm();
const projectCode = route.params.code;

const contract = ref(null);
const contractLoading = ref(true);
const showFilters = ref(false);
const showStats = ref(true);

// Используем композабл для работы со средствами оплаты
const {
    paymentMethods,
    loading,
    error,
    statistics,
    formattedPaymentMethods,
    groupedPaymentMethods,
    hasData,
    filters,
    loadPaymentMethods,
    deletePaymentMethod,
    refreshData,
    exportToCSV,
    applyFilter,
    clearFilters
} = usePaymentMethods();

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    code: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    category: { value: null, matchMode: FilterMatchMode.EQUALS }
});

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        code: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        category: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};

// Опции для фильтра категорий
const categoryOptions = computed(() => {
    return Object.entries(PAYMENT_METHOD_CATEGORIES).map(([key, category]) => ({
        label: category.name,
        value: key
    }));
});

onMounted(async () => {
    await loadProjectContract();
    if (contract.value) {
        await loadPaymentMethods(contract.value.id);
    }
});

const loadProjectContract = async () => {
    try {
        contractLoading.value = true;
        const contracts = await ContractService.getContractsByProject(projectCode);
        if (contracts && contracts.length > 0) {
            contract.value = contracts[0];
        }
    } catch (error) {
        console.error('Ошибка загрузки договора проекта:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить договор проекта',
            life: 5000
        });
    } finally {
        contractLoading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const createPaymentMethod = () => {
    router.push(`/pro/${projectCode}/finance/payment-method/create`);
};

const editPaymentMethod = (method) => {
    router.push(`/pro/${projectCode}/finance/payment-method/${method.id}/edit`);
};

const handleDeletePaymentMethod = async (method) => {
    try {
        await deletePaymentMethod(method);
    } catch (error) {
        // Ошибка уже обработана в композабле
        console.error('Ошибка удаления средства оплаты:', error);
    }
};

const goToContract = () => {
    router.push(`/pro/${projectCode}/finance/contract`);
};

const goToCashFlow = (method) => {
    router.push(`/pro/${projectCode}/finance/cash-flow?method=${method.id}`);
};

const handleExport = () => {
    exportToCSV();
};

const getCategoryIcon = (code) => {
    for (const category of Object.values(PAYMENT_METHOD_CATEGORIES)) {
        if (category.types.includes(code)) {
            return category.icon;
        }
    }
    return 'pi-credit-card';
};

const getCategoryColor = (code) => {
    for (const category of Object.values(PAYMENT_METHOD_CATEGORIES)) {
        if (category.types.includes(code)) {
            return category.color;
        }
    }
    return '#6b7280';
};

const handleFiltersChanged = (newFilters) => {
    Object.assign(filters, newFilters);
};

const handleStatsUpdated = (newStats) => {
    // Можно добавить дополнительную логику при обновлении статистики
    console.log('Статистика обновлена:', newStats);
};

const toggleFilters = () => {
    showFilters.value = !showFilters.value;
};

const toggleStats = () => {
    showStats.value = !showStats.value;
};
</script>

<template>
    <div class="payment-method-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Способы оплаты</h2>
            <div class="flex gap-2">
                <Button
                    :label="showStats ? 'Скрыть статистику' : 'Показать статистику'"
                    :icon="showStats ? 'pi pi-eye-slash' : 'pi pi-chart-bar'"
                    outlined
                    @click="toggleStats"
                    v-if="hasData"
                />
                <Button
                    :label="showFilters ? 'Скрыть фильтры' : 'Показать фильтры'"
                    :icon="showFilters ? 'pi pi-filter-slash' : 'pi pi-filter'"
                    outlined
                    @click="toggleFilters"
                    v-if="hasData"
                />
                <Button
                    label="К договору"
                    icon="pi pi-arrow-left"
                    outlined
                    @click="goToContract"
                />
            </div>
        </div>

        <!-- Информация о договоре -->
        <div class="card mb-4" v-if="contract">
            <div class="flex align-items-center">
                <div class="w-3rem h-3rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                    <i class="pi pi-file-edit text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold m-0 mb-1">{{ contract.name }}</h3>
                    <p class="text-color-secondary m-0">Договор № {{ contract.number }}</p>
                </div>
            </div>
        </div>

        <!-- Нет договора -->
        <div v-if="!contract" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Договор не найден</h3>
                <p class="text-color-secondary mb-4">
                    Для настройки способов оплаты необходимо создать договор проекта.
                </p>
                <Button
                    label="Перейти к договору"
                    icon="pi pi-arrow-right"
                    @click="goToContract"
                />
            </div>
        </div>

        <!-- Компонент статистики -->
        <PaymentMethodStats
            v-if="showStats && hasData"
            :contract-id="contract?.id"
            :show-category-stats="true"
            :show-sync-stats="true"
            :auto-refresh="true"
            class="mb-4"
            @stats-updated="handleStatsUpdated"
        />

        <!-- Компонент фильтров -->
        <PaymentMethodFilters
            v-if="showFilters && hasData"
            v-model="filters"
            :contract-id="contract?.id"
            :contracts="contract ? [contract] : []"
            class="mb-4"
            @filters-changed="handleFiltersChanged"
        />

        <!-- Таблица способов оплаты -->
        <div v-else class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button
                        label="Добавить способ оплаты"
                        icon="pi pi-plus"
                        @click="createPaymentMethod"
                    />
                    <Button
                        label="Экспорт"
                        icon="pi pi-download"
                        outlined
                        @click="handleExport"
                        :disabled="!hasData"
                    />
                </div>
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="formattedPaymentMethods"
                :paginator="true"
                :rows="15"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading || contractLoading"
                :globalFilterFields="['code', 'name', 'typeName']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Способы оплаты не добавлены</p>
                        <Button
                            label="Добавить первый способ оплаты"
                            icon="pi pi-plus"
                            class="mt-3"
                            @click="createPaymentMethod"
                        />
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="code" header="Код" :sortable="true" style="min-width: 180px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по коду"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono font-semibold text-primary">{{ data.code }}</span>
                    </template>
                </Column>

                <Column field="name" header="Наименование" :sortable="true" style="min-width: 350px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по наименованию"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i :class="getCategoryIcon(data.code)"
                               :style="{ color: getCategoryColor(data.code) }"
                               class="mr-2"></i>
                            <div>
                                <div class="font-semibold">{{ data.name }}</div>
                                <div class="text-sm text-color-secondary">{{ data.category?.name }}</div>
                            </div>
                        </div>
                    </template>
                </Column>

                <Column field="category" header="Категория" :sortable="false" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <Select
                            v-model="filterModel.value"
                            :options="categoryOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Все категории"
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag
                            :value="data.category?.name"
                            :style="{ backgroundColor: data.category?.color + '20', color: data.category?.color }"
                            class="font-semibold"
                        />
                    </template>
                </Column>

                <Column field="isActive" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <Tag
                            :value="data.statusBadge.label"
                            :severity="data.statusBadge.severity"
                            :icon="data.statusBadge.icon"
                        />
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-chart-line"
                                size="small"
                                text
                                @click="goToCashFlow(data)"
                                v-tooltip.top="'Движение средств'"
                            />
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editPaymentMethod(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="handleDeletePaymentMethod(data)"
                                v-tooltip.top="'Удалить'"
                                :loading="loading"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

