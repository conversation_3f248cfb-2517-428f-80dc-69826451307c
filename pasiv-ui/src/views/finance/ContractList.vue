<script setup>
import { useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { ContractService } from '@/service/ContractService';
import {
    getContractStatusLabel,
    getContractStatusSeverity,
    getContractTypeLabel,
    formatContractAmount,
    formatContractDate,
    isContractExpiringSoon
} from '@/utils/contractUtils';
import { CONTRACT_STATUS_OPTIONS } from '@/constants/contract';

// Импортируем PrimeVue компоненты
import Button from 'primevue/button';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import Tag from 'primevue/tag';
import Dropdown from 'primevue/dropdown';

const router = useRouter();
const toast = useToast();
const confirm = useConfirm();

const contracts = ref([]);
const loading = ref(true);
const syncing = ref(false);
const syncingId = ref(null);
const error = ref(null);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    number: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

const statuses = ref(CONTRACT_STATUS_OPTIONS);

const syncStatuses = ref([
    { label: 'Синхронизирован', value: 'synced' },
    { label: 'Ожидает синхронизации', value: 'pending' },
    { label: 'Ошибка синхронизации', value: 'error' },
    { label: 'Не синхронизировался', value: 'never' }
]);

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        number: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};

onMounted(() => {
    loadContracts();
});

const loadContracts = async () => {
    try {
        loading.value = true;
        error.value = null;

        const data = await ContractService.getContracts({
            pagination: { page: 1, size: 100 },
            filter: { isDeleted: false }
        });

        contracts.value = data || [];

        // Показать предупреждения об истекающих договорах
        const expiring = contracts.value.filter(contract =>
            isContractExpiringSoon(contract.completionDate)
        );

        if (expiring.length > 0) {
            toast.add({
                severity: 'warn',
                summary: 'Внимание',
                detail: `${expiring.length} договор(ов) истекает в ближайшее время`,
                life: 5000
            });
        }
    } catch (err) {
        console.error('Ошибка загрузки договоров:', err);
        error.value = 'Не удалось загрузить список договоров';
        contracts.value = [];

        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить список договоров',
            life: 5000
        });
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const createContract = () => {
    router.push('/contracts/create');
};

const editContract = (contract) => {
    router.push(`/contracts/${contract.id}/edit`);
};

const viewContract = (contract) => {
    router.push(`/contracts/${contract.id}`);
};

const deleteContract = async (contract) => {
    confirm.require({
        message: `Вы уверены, что хотите удалить договор "${contract.contractNumber}"?`,
        header: 'Подтверждение удаления',
        icon: 'pi pi-exclamation-triangle',
        rejectClass: 'p-button-secondary p-button-outlined',
        rejectLabel: 'Отмена',
        acceptLabel: 'Удалить',
        accept: async () => {
            try {
                const result = await ContractService.deleteContract(contract.id);

                if (result.success) {
                    toast.add({
                        severity: 'success',
                        summary: 'Успех',
                        detail: 'Договор успешно удален',
                        life: 3000
                    });
                    await loadContracts();
                } else {
                    toast.add({
                        severity: 'error',
                        summary: 'Ошибка',
                        detail: result.error?.message || 'Не удалось удалить договор',
                        life: 5000
                    });
                }
            } catch (error) {
                console.error('Ошибка удаления договора:', error);
                toast.add({
                    severity: 'error',
                    summary: 'Ошибка',
                    detail: 'Произошла ошибка при удалении договора',
                    life: 5000
                });
            }
        }
    });
};

const syncContract = async (contract) => {
    try {
        syncingId.value = contract.id;
        const result = await ContractService.syncContract(contract.id);

        if (result.success) {
            const contractItem = contracts.value.find(c => c.id === contract.id);
            if (contractItem) {
                contractItem.syncStatus = 'synced';
                contractItem.lastSyncDate = result.syncDate;
            }
            console.log('Синхронизация успешна:', result.message);
        } else {
            console.error('Ошибка синхронизации:', result.message);
        }
    } catch (error) {
        console.error('Ошибка синхронизации:', error);
    } finally {
        syncingId.value = null;
    }
};

const syncAllContracts = async () => {
    try {
        syncing.value = true;
        const result = await ContractService.syncAllContracts();

        console.log('Массовая синхронизация завершена:', result.message);
        await loadContracts();
    } catch (error) {
        console.error('Ошибка массовой синхронизации:', error);
    } finally {
        syncing.value = false;
    }
};

// Вспомогательные функции для отображения
const getStatusLabel = (status) => getContractStatusLabel(status);
const getStatusSeverity = (status) => getContractStatusSeverity(status);
const getTypeLabel = (type) => getContractTypeLabel(type);
const formatAmount = (amount, currency) => formatContractAmount(amount, currency);
const formatDate = (date) => formatContractDate(date);

// Проверка истечения договора
const isExpiring = (contract) => {
    return isContractExpiringSoon(contract.completionDate);
};

// Функции для синхронизации
const getSyncStatusLabel = (status) => {
    const option = syncStatuses.value.find(s => s.value === status);
    return option ? option.label : status;
};

const getSyncStatusSeverity = (status) => {
    const severityMap = {
        'synced': 'success',
        'pending': 'warning',
        'error': 'danger',
        'never': 'secondary'
    };
    return severityMap[status] || 'secondary';
};

const importFromFile = () => {
    console.log('Загрузить договоры из файла');
};

const exportData = () => {
    console.log('Экспорт всех договоров');
};

// Дублированная функция удалена - используется из строки 195

// Дублированная функция getStatusLabel удалена - используется из строки 194

const getRoleIcon = (role) => {
    switch (role) {
        case 'operator': return 'pi pi-star';
        case 'carrier': return 'pi pi-car';
        case 'processing_center': return 'pi pi-cog';
        case 'customer': return 'pi pi-user';
        case 'contractor': return 'pi pi-briefcase';
        default: return 'pi pi-circle';
    }
};

const getRoleColor = (role) => {
    switch (role) {
        case 'operator': return '#3b82f6';
        case 'carrier': return '#f59e0b';
        case 'processing_center': return '#10b981';
        case 'customer': return '#8b5cf6';
        case 'contractor': return '#ef4444';
        default: return '#6b7280';
    }
};

const getRoleLabel = (role) => {
    switch (role) {
        case 'operator': return 'Оператор';
        case 'carrier': return 'Перевозчик';
        case 'processing_center': return 'Процессинг';
        case 'customer': return 'Заказчик';
        case 'contractor': return 'Подрядчик';
        default: return 'Участник';
    }
};

const getRoleSeverity = (role) => {
    switch (role) {
        case 'operator': return 'info';
        case 'carrier': return 'warning';
        case 'processing_center': return 'success';
        case 'customer': return 'secondary';
        case 'contractor': return 'danger';
        default: return 'secondary';
    }
};
</script>

<template>
    <div class="contract-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Все договоры</h1>
            <div class="flex gap-2">
                <Button
                    label="Синхронизация с 1С"
                    icon="pi pi-refresh"
                    outlined
                    :loading="syncing"
                    @click="syncAllContracts"
                />
                <Button
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button
                    label="Создать"
                    icon="pi pi-plus"
                    @click="createContract"
                />
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="contracts"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['contractNumber', 'contractName', 'projectName', 'externalId1C']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Договоры не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="contractNumber" header="Номер договора" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по номеру"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-semibold font-mono">{{ data.contractNumber }}</span>
                    </template>
                </Column>

                <Column field="contractName" header="Наименование" :sortable="true" style="min-width: 300px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по наименованию"
                        />
                    </template>
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.contractName }}</div>
                            <small class="text-color-secondary">{{ getTypeLabel(data.contractType) }}</small>
                        </div>
                    </template>
                </Column>

                <Column field="projectName" header="Проект" :sortable="true" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-chart-bar mr-2 text-color-secondary"></i>
                            <div>
                                <div class="font-semibold">{{ data.projectName }}</div>
                                <small class="text-color-secondary font-mono">{{ data.projectCode }}</small>
                            </div>
                        </div>
                    </template>
                </Column>

                <Column header="Организации в договоре" style="min-width: 250px">
                    <template #body="{ data }">
                        <div class="space-y-1">
                            <div
                                v-for="org in data.contractOrganizations"
                                :key="org.organizationId"
                                class="flex align-items-center text-sm"
                            >
                                <i :class="getRoleIcon(org.role)" :style="{ color: getRoleColor(org.role) }" class="mr-2 text-xs"></i>
                                <span class="mr-2">{{ org.organizationName }}</span>
                                <Tag
                                    :value="getRoleLabel(org.role)"
                                    :severity="getRoleSeverity(org.role)"
                                    class="text-xs"
                                />
                            </div>
                        </div>
                    </template>
                </Column>

                <Column header="Даты договора" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div class="mb-1">
                                <span class="text-color-secondary">Заключение:</span> {{ formatDate(data.conclusionDate) }}
                            </div>
                            <div class="mb-1">
                                <span class="text-color-secondary">Подпись:</span>
                                {{ data.signatureDate ? formatDate(data.signatureDate) : 'Не подписан' }}
                            </div>
                            <div>
                                <span class="text-color-secondary">Завершение:</span> {{ formatDate(data.completionDate) }}
                            </div>
                        </div>
                    </template>
                </Column>

                <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <Dropdown
                            v-model="filterModel.value"
                            :options="statuses"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите статус"
                            class="p-column-filter"
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag
                            :value="getStatusLabel(data.status)"
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                </Column>

                <Column header="Синхронизация с 1С" style="min-width: 180px">
                    <template #body="{ data }">
                        <div>
                            <Tag
                                :value="getSyncStatusLabel(data.syncStatus)"
                                :severity="getSyncStatusSeverity(data.syncStatus)"
                                class="mb-1"
                            />
                            <div class="text-xs">
                                <div class="text-color-secondary">ID: {{ data.externalId1C || 'Не синхронизирован' }}</div>
                                <div class="text-color-secondary">
                                    {{ formatDate(data.lastSyncDate) }}
                                </div>
                            </div>
                        </div>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button
                                icon="pi pi-refresh"
                                size="small"
                                text
                                :loading="syncingId === data.id"
                                @click="syncContract(data)"
                                v-tooltip.top="'Синхронизировать с 1С'"
                            />
                            <Button
                                icon="pi pi-eye"
                                size="small"
                                text
                                @click="viewContract(data)"
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editContract(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteContract(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

