<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { usePaymentMethodForm } from '@/composables/usePaymentMethods';
import { PaymentMethodService } from '@/service/PaymentMethodService';
import { ContractService } from '@/service/ContractService';
import { PaymentMethodHelper } from '@/utils/paymentMethodUtils';
import { PAYMENT_METHOD_CATEGORIES } from '@/constants/paymentMethod';

const route = useRoute();
const router = useRouter();
const toast = useToast();

const projectCode = route.params.code;
const methodId = route.params.methodId;
const isEdit = computed(() => !!methodId);

const loading = ref(false);
const contract = ref(null);
const contractLoading = ref(true);

// Используем композабл для формы
const {
    form,
    errors,
    saving,
    predefinedMethods,
    validateForm,
    resetForm,
    updateNameByCode
} = usePaymentMethodForm();

// Группировка предустановленных методов по категориям
const groupedMethods = computed(() => {
    const groups = {};

    predefinedMethods.value.forEach(method => {
        const categoryKey = method.category?.key || 'other';
        if (!groups[categoryKey]) {
            groups[categoryKey] = {
                name: method.category?.name || 'Прочие',
                icon: method.category?.icon || 'pi-ellipsis-h',
                color: method.category?.color || '#6b7280',
                methods: []
            };
        }
        groups[categoryKey].methods.push(method);
    });

    return Object.values(groups);
});

onMounted(async () => {
    await loadProjectContract();
    if (isEdit.value && contract.value) {
        await loadPaymentMethod();
    } else if (contract.value) {
        // Устанавливаем ID договора для новой записи
        form.contractId = contract.value.id;
    }
});

const loadProjectContract = async () => {
    try {
        contractLoading.value = true;
        const contracts = await ContractService.getContractsByProject(projectCode);
        if (contracts && contracts.length > 0) {
            contract.value = contracts[0];
            form.contractId = contract.value.id;
        }
    } catch (error) {
        console.error('Ошибка загрузки договора проекта:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить договор проекта',
            life: 5000
        });
    } finally {
        contractLoading.value = false;
    }
};

const loadPaymentMethod = async () => {
    try {
        loading.value = true;
        const method = await PaymentMethodService.getPaymentMethodById(methodId);
        if (method) {
            Object.assign(form, method);
        } else {
            toast.add({
                severity: 'error',
                summary: 'Ошибка',
                detail: 'Средство оплаты не найдено',
                life: 5000
            });
            goBack();
        }
    } catch (error) {
        console.error('Ошибка загрузки средства оплаты:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить средство оплаты',
            life: 5000
        });
    } finally {
        loading.value = false;
    }
};

const savePaymentMethod = async () => {
    if (!validateForm()) {
        return;
    }

    if (!contract.value) {
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Договор не найден',
            life: 5000
        });
        return;
    }

    try {
        saving.value = true;

        if (isEdit.value) {
            const result = await PaymentMethodService.updatePaymentMethod(methodId, {
                ...form,
                contractId: contract.value.id
            });
            if (result.success) {
                toast.add({
                    severity: 'success',
                    summary: 'Успех',
                    detail: 'Средство оплаты успешно обновлено',
                    life: 3000
                });
                goBack();
            }
        } else {
            const result = await PaymentMethodService.createPaymentMethod(contract.value.id, form);
            if (result.success) {
                toast.add({
                    severity: 'success',
                    summary: 'Успех',
                    detail: 'Средство оплаты успешно создано',
                    life: 3000
                });
                goBack();
            }
        }
    } catch (error) {
        console.error('Ошибка сохранения средства оплаты:', error);
        // Ошибка уже обработана в сервисе через toast
    } finally {
        saving.value = false;
    }
};

const goBack = () => {
    router.push(`/pro/${projectCode}/finance/payment-method`);
};

const cancel = () => {
    goBack();
};

const selectPredefinedMethod = (method) => {
    form.code = method.code;
    form.name = method.name;
    form.methodType = method.code;
    errors.value = {};
};

const getCategoryIcon = (categoryKey) => {
    return PAYMENT_METHOD_CATEGORIES[categoryKey]?.icon || 'pi-ellipsis-h';
};

const getCategoryColor = (categoryKey) => {
    return PAYMENT_METHOD_CATEGORIES[categoryKey]?.color || '#6b7280';
};

const generateCode = () => {
    const name = form.value.name.trim();
    if (name) {
        // Простая генерация кода на основе названия
        const code = name
            .toUpperCase()
            .replace(/[^А-ЯA-Z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 20);
        form.value.code = code;
        delete errors.value.code;
    }
};
</script>

<template>
    <div class="payment-method-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование способа оплаты' : 'Создание способа оплаты' }}
            </h1>
            <Button
                label="Назад к списку"
                icon="pi pi-arrow-left"
                outlined
                @click="cancel"
            />
        </div>

        <!-- Информация о договоре -->
        <div class="card mb-4" v-if="contract">
            <div class="flex align-items-center">
                <div class="w-3rem h-3rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                    <i class="pi pi-file-edit text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold m-0 mb-1">{{ contract.name }}</h3>
                    <p class="text-color-secondary m-0">Договор № {{ contract.number }}</p>
                </div>
            </div>
        </div>

        <div class="grid">
            <!-- Форма -->
            <div class="col-12 lg:col-8">
                <div class="card" v-if="!loading">
                    <form @submit.prevent="savePaymentMethod">
                        <div class="grid">
                            <div class="col-12">
                                <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="code" class="font-medium">Код способа оплаты *</label>
                                    <div class="p-inputgroup">
                                        <InputText
                                            id="code"
                                            v-model="form.code"
                                            :class="{ 'p-invalid': errors.code }"
                                            placeholder="BANK_CARD"
                                            class="font-mono"
                                        />
                                        <Button
                                            icon="pi pi-refresh"
                                            @click="generateCode"
                                            v-tooltip.top="'Сгенерировать код'"
                                        />
                                    </div>
                                    <small v-if="errors.code" class="p-error">{{ errors.code }}</small>
                                    <small v-else class="text-color-secondary">Уникальный код для идентификации способа оплаты</small>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="name" class="font-medium">Наименование способа оплаты *</label>
                                    <InputText
                                        id="name"
                                        v-model="form.name"
                                        :class="{ 'p-invalid': errors.name }"
                                        placeholder="Банковская карта"
                                        class="w-full"
                                    />
                                    <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                                </div>
                            </div>
                        </div>

                        <!-- Кнопки действий -->
                        <div class="flex justify-content-end gap-2 mt-4">
                            <Button
                                label="Отмена"
                                icon="pi pi-times"
                                outlined
                                @click="cancel"
                                :disabled="saving"
                            />
                            <Button
                                type="submit"
                                :label="isEdit ? 'Сохранить' : 'Создать'"
                                :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                                :loading="saving"
                            />
                        </div>
                    </form>
                </div>

                <!-- Загрузка -->
                <div v-else class="card">
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных способа оплаты...</p>
                    </div>
                </div>
            </div>

            <!-- Предустановленные варианты -->
            <div class="col-12 lg:col-4" v-if="!isEdit">
                <div class="card">
                    <h3 class="text-lg font-medium mb-3">Стандартные способы оплаты</h3>
                    <p class="text-color-secondary text-sm mb-3">
                        Выберите один из предустановленных вариантов или создайте свой
                    </p>

                    <div class="space-y-2">
                        <div
                            v-for="method in predefinedMethods"
                            :key="method.code"
                            class="predefined-method p-3 border-round cursor-pointer"
                            @click="selectPredefinedMethod(method)"
                        >
                            <div class="font-semibold text-sm">{{ method.name }}</div>
                            <div class="text-xs text-color-secondary font-mono">{{ method.code }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

