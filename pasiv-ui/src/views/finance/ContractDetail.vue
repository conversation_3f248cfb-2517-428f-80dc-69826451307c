<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { ContractService } from '@/service/ContractService';
import { PaymentMethodService } from '@/service/PaymentMethodService';

// Импортируем PrimeVue компоненты
import Button from 'primevue/button';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.code;
const contractId = route.params.contractId;
const isEdit = computed(() => route.name.includes('edit'));

const loading = ref(false);
const saving = ref(false);
const activeTab = ref(0);

const contract = ref({});
const paymentMethods = ref([]);

onMounted(async () => {
    await loadContract();
    await loadPaymentMethods();
});

const loadContract = async () => {
    try {
        loading.value = true;
        const data = await ContractService.getContractById(contractId);
        if (data) {
            contract.value = data;
        }
    } catch (error) {
        console.error('Ошибка загрузки договора:', error);
    } finally {
        loading.value = false;
    }
};

const loadPaymentMethods = async () => {
    try {
        const data = await PaymentMethodService.getPaymentMethodsByContract(contractId);
        paymentMethods.value = data;
    } catch (error) {
        console.error('Ошибка загрузки способов оплаты:', error);
    }
};

const goBack = () => {
    router.push(`/pro/${projectCode}/finance/contract`);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'secondary';
        case 'expiring': return 'warning';
        case 'completed': return 'info';
        case 'terminated': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'draft': return 'Черновик';
        case 'expiring': return 'Истекает';
        case 'completed': return 'Завершен';
        case 'terminated': return 'Расторгнут';
        default: return 'Неизвестно';
    }
};


</script>

<template>
    <div class="contract-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование договора' : 'Просмотр договора' }}
            </h1>
            <div class="flex gap-2">
                <Button
                    label="Назад к списку"
                    icon="pi pi-arrow-left"
                    outlined
                    @click="goBack"
                />
                <Button
                    v-if="!isEdit"
                    label="Редактировать"
                    icon="pi pi-pencil"
                    @click="router.push(`/pro/${projectCode}/finance/contract/${contractId}/edit`)"
                />
            </div>
        </div>

        <div v-if="!loading" class="grid">
            <!-- Основная информация о договоре -->
            <div class="col-12">
                <div class="card mb-4">
                    <div class="flex align-items-center mb-4">
                        <div class="w-4rem h-4rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-file-edit text-blue-600 text-2xl"></i>
                        </div>
                        <div class="flex-1">
                            <h2 class="text-xl font-bold m-0 mb-1">{{ contract.name }}</h2>
                            <p class="text-color-secondary m-0">{{ contract.description }}</p>
                        </div>
                        <div class="text-right">
                            <Tag
                                :value="getStatusLabel(contract.status)"
                                :severity="getStatusSeverity(contract.status)"
                                class="mb-2"
                            />
                            <div class="text-2xl font-bold text-primary">{{ formatAmount(contract.totalAmount) }}</div>
                        </div>
                    </div>

                    <div class="grid">
                        <div class="col-12 md:col-3">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Номер договора</label>
                                <p class="m-0 font-mono text-lg">{{ contract.number }}</p>
                            </div>
                        </div>
                        <div class="col-12 md:col-3">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Организация</label>
                                <p class="m-0">{{ contract.organizationName }}</p>
                            </div>
                        </div>
                        <div class="col-12 md:col-3">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Контрагент</label>
                                <p class="m-0">{{ contract.contractorName }}</p>
                            </div>
                        </div>
                        <div class="col-12 md:col-3">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Период действия</label>
                                <p class="m-0">{{ formatDate(contract.startDate) }} - {{ formatDate(contract.endDate) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Вкладки -->
            <div class="col-12">
                <TabView v-model:activeIndex="activeTab">
                    <TabPanel header="Способы оплаты">
                        <div class="card">
                            <div class="flex justify-content-between align-items-center mb-4">
                                <h3 class="text-lg font-semibold m-0">Способы оплаты по договору</h3>
                                <Button
                                    label="Управление способами оплаты"
                                    icon="pi pi-external-link"
                                    @click="router.push(`/pro/${projectCode}/finance/payment-methods`)"
                                />
                            </div>

                            <DataTable :value="paymentMethods" responsiveLayout="scroll">
                                <template #empty>
                                    <div class="text-center p-4">
                                        <i class="pi pi-info-circle text-2xl text-color-secondary mb-2"></i>
                                        <p class="text-color-secondary mb-3">Способы оплаты не добавлены</p>
                                        <Button
                                            label="Добавить способы оплаты"
                                            icon="pi pi-plus"
                                            @click="router.push(`/pro/${projectCode}/finance/payment-methods`)"
                                        />
                                    </div>
                                </template>

                                <Column field="code" header="Код" style="min-width: 150px">
                                    <template #body="{ data }">
                                        <span class="font-mono font-semibold">{{ data.code }}</span>
                                    </template>
                                </Column>

                                <Column field="name" header="Наименование" style="min-width: 300px">
                                    <template #body="{ data }">
                                        <div class="flex align-items-center">
                                            <i class="pi pi-credit-card mr-2 text-color-secondary"></i>
                                            <span>{{ data.name }}</span>
                                        </div>
                                    </template>
                                </Column>

                                <Column header="Действия" style="min-width: 120px">
                                    <template #body="{ data }">
                                        <Button
                                            icon="pi pi-chart-line"
                                            label="Движение средств"
                                            size="small"
                                            text
                                            @click="router.push(`/pro/${projectCode}/finance/cash-flow?method=${data.id}`)"
                                        />
                                    </template>
                                </Column>
                            </DataTable>
                        </div>
                    </TabPanel>

                    <TabPanel header="Финансовая отчетность">
                        <div class="card">
                            <h3 class="text-lg font-semibold mb-4">Финансовая отчетность</h3>
                            <p class="text-color-secondary">Здесь будет отображаться финансовая отчетность по договору</p>
                        </div>
                    </TabPanel>

                    <TabPanel header="История изменений">
                        <div class="card">
                            <h3 class="text-lg font-semibold mb-4">История изменений</h3>
                            <p class="text-color-secondary">Здесь будет отображаться история изменений договора</p>
                        </div>
                    </TabPanel>
                </TabView>
            </div>
        </div>

        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных договора...</p>
            </div>
        </div>
    </div>
</template>

