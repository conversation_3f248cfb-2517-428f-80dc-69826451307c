<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const toggleNavigation = () => {
    console.log('Переключить навигацию');
};

const reportMenuItems = ref([
    {
        label: 'Отчет по выручке',
        icon: 'pi pi-chart-line',
        command: () => router.push('/pasiv/financial-reports/revenue')
    },
    {
        label: 'Отчет по комиссиям',
        icon: 'pi pi-percentage',
        command: () => router.push('/pasiv/financial-reports/commissions')
    },
    {
        label: 'Сводный финансовый отчет',
        icon: 'pi pi-chart-bar',
        command: () => router.push('/pasiv/financial-reports/summary')
    },
    {
        label: 'Отчет по взаиморасчетам',
        icon: 'pi pi-arrows-h',
        command: () => router.push('/pasiv/financial-reports/settlements')
    },
    {
        label: 'Отчет по агентским вознаграждениям',
        icon: 'pi pi-gift',
        command: () => router.push('/pasiv/financial-reports/agent-rewards')
    }
]);
</script>

<template>
    <div class="financial-reports-container">
        <!-- Заголовок -->
        <div class="reports-header card mb-4">
            <div class="flex align-items-center justify-content-between">
                <div class="flex align-items-center">
                    <div class="w-4rem h-4rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                        <i class="pi pi-chart-line text-blue-600 text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold m-0 mb-1">Финансовые отчеты</h1>
                        <p class="text-color-secondary m-0">Отчеты по финансовым показателям системы ПАСИВ</p>
                    </div>
                </div>
            </div>
        </div>

        <Splitter style="height: calc(100vh - 140px)">
            <SplitterPanel :size="20" :minSize="15">
                <div class="navigation-panel h-full">
                    <div class="p-3">
                        <div class="flex align-items-center justify-content-between mb-3">
                            <h3 class="text-lg font-semibold m-0">Отчеты</h3>
                            <Button
                                icon="pi pi-bars"
                                text
                                size="small"
                                @click="toggleNavigation"
                                v-tooltip.right="'Свернуть меню'"
                            />
                        </div>
                        <Menu :model="reportMenuItems" class="w-full navigation-menu" />
                    </div>
                </div>
            </SplitterPanel>

            <SplitterPanel :size="80">
                <div class="content-panel h-full overflow-hidden">
                    <router-view v-if="$route.path !== '/pasiv/financial-reports'" />
                    <div v-else class="reports-dashboard p-4">
                        <div class="grid">
                            <!-- Карточки быстрого доступа к отчетам -->
                            <div class="col-12 md:col-6 lg:col-4">
                                <div class="card report-card cursor-pointer" @click="router.push('/pasiv/financial-reports/revenue')">
                                    <div class="flex align-items-center">
                                        <div class="w-3rem h-3rem bg-green-100 border-round flex align-items-center justify-content-center mr-3">
                                            <i class="pi pi-chart-line text-green-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold m-0 mb-1">Отчет по выручке</h3>
                                            <p class="text-color-secondary m-0 text-sm">Анализ выручки по организациям и периодам</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 md:col-6 lg:col-4">
                                <div class="card report-card cursor-pointer" @click="router.push('/pasiv/financial-reports/commissions')">
                                    <div class="flex align-items-center">
                                        <div class="w-3rem h-3rem bg-orange-100 border-round flex align-items-center justify-content-center mr-3">
                                            <i class="pi pi-percentage text-orange-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold m-0 mb-1">Отчет по комиссиям</h3>
                                            <p class="text-color-secondary m-0 text-sm">Анализ комиссионных доходов</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 md:col-6 lg:col-4">
                                <div class="card report-card cursor-pointer" @click="router.push('/pasiv/financial-reports/summary')">
                                    <div class="flex align-items-center">
                                        <div class="w-3rem h-3rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                                            <i class="pi pi-chart-bar text-blue-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold m-0 mb-1">Сводный отчет</h3>
                                            <p class="text-color-secondary m-0 text-sm">Общие финансовые показатели</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 md:col-6 lg:col-4">
                                <div class="card report-card cursor-pointer" @click="router.push('/pasiv/financial-reports/settlements')">
                                    <div class="flex align-items-center">
                                        <div class="w-3rem h-3rem bg-purple-100 border-round flex align-items-center justify-content-center mr-3">
                                            <i class="pi pi-arrows-h text-purple-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold m-0 mb-1">Взаиморасчеты</h3>
                                            <p class="text-color-secondary m-0 text-sm">Отчеты по взаиморасчетам с участниками</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 md:col-6 lg:col-4">
                                <div class="card report-card cursor-pointer" @click="router.push('/pasiv/financial-reports/agent-rewards')">
                                    <div class="flex align-items-center">
                                        <div class="w-3rem h-3rem bg-indigo-100 border-round flex align-items-center justify-content-center mr-3">
                                            <i class="pi pi-gift text-indigo-600 text-xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold m-0 mb-1">Агентские вознаграждения</h3>
                                            <p class="text-color-secondary m-0 text-sm">Отчеты по вознаграждениям агентов</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Быстрая статистика -->
                        <div class="card mt-4">
                            <h3 class="text-lg font-semibold mb-4">Быстрая статистика</h3>
                            <div class="grid">
                                <div class="col-12 md:col-3">
                                    <div class="stat-item text-center p-3">
                                        <div class="text-2xl font-bold text-green-600">2.45M ₽</div>
                                        <div class="text-sm text-color-secondary">Общая выручка за месяц</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-3">
                                    <div class="stat-item text-center p-3">
                                        <div class="text-2xl font-bold text-blue-600">245K ₽</div>
                                        <div class="text-sm text-color-secondary">Комиссионные доходы</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-3">
                                    <div class="stat-item text-center p-3">
                                        <div class="text-2xl font-bold text-orange-600">15.4K</div>
                                        <div class="text-sm text-color-secondary">Транзакций за день</div>
                                    </div>
                                </div>
                                <div class="col-12 md:col-3">
                                    <div class="stat-item text-center p-3">
                                        <div class="text-2xl font-bold text-purple-600">12</div>
                                        <div class="text-sm text-color-secondary">Активных организаций</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </SplitterPanel>
        </Splitter>
    </div>
</template>

