<script setup>
import { ref, onMounted } from 'vue';
import { FinanceService } from '@/service/FinanceService';

const loading = ref(false);
const reportData = ref(null);

const reportParams = ref({
    dateFrom: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // Начало текущего месяца
    dateTo: new Date(), // Сегодня
    organizationId: '',
    groupBy: 'organization',
    includeDetails: true
});

const organizationOptions = ref([
    { label: 'Все организации', value: '' },
    { label: 'ООО "Транспорт Плюс"', value: '1' },
    { label: 'ИП Иванов И.И.', value: '2' },
    { label: 'ООО "Городской транспорт"', value: '3' }
]);

const groupByOptions = ref([
    { label: 'По организациям', value: 'organization' },
    { label: 'По дням', value: 'day' },
    { label: 'По неделям', value: 'week' },
    { label: 'По месяцам', value: 'month' },
    { label: 'По способам оплаты', value: 'payment_method' }
]);

onMounted(() => {
    generateReport();
});

const generateReport = async () => {
    try {
        loading.value = true;
        console.log('Генерация отчета по выручке:', reportParams.value);
        
        // Мок данные для отчета
        const mockData = {
            summary: {
                totalRevenue: 2450000,
                totalTransactions: 15420,
                averageTicket: 158.9,
                period: `${reportParams.value.dateFrom.toLocaleDateString('ru-RU')} - ${reportParams.value.dateTo.toLocaleDateString('ru-RU')}`
            },
            details: [
                {
                    id: 1,
                    name: 'ООО "Транспорт Плюс"',
                    revenue: 1470000,
                    transactions: 9252,
                    averageTicket: 158.9,
                    percentage: 60.0
                },
                {
                    id: 2,
                    name: 'ИП Иванов И.И.',
                    revenue: 735000,
                    transactions: 4626,
                    averageTicket: 158.9,
                    percentage: 30.0
                },
                {
                    id: 3,
                    name: 'ООО "Городской транспорт"',
                    revenue: 245000,
                    transactions: 1542,
                    averageTicket: 158.9,
                    percentage: 10.0
                }
            ],
            chartData: {
                labels: ['ООО "Транспорт Плюс"', 'ИП Иванов И.И.', 'ООО "Городской транспорт"'],
                datasets: [{
                    data: [1470000, 735000, 245000],
                    backgroundColor: ['#3b82f6', '#10b981', '#f59e0b']
                }]
            }
        };
        
        reportData.value = mockData;
    } catch (error) {
        console.error('Ошибка генерации отчета:', error);
    } finally {
        loading.value = false;
    }
};

const exportReport = (format) => {
    console.log('Экспорт отчета в формате:', format);
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0
    }).format(amount);
};

const formatNumber = (number) => {
    return new Intl.NumberFormat('ru-RU').format(number);
};

const formatPercent = (value) => {
    return `${value.toFixed(1)}%`;
};
</script>

<template>
    <div class="revenue-report p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Отчет по выручке</h2>
            <div class="flex gap-2">
                <Button 
                    label="Excel" 
                    icon="pi pi-file-excel" 
                    outlined 
                    size="small"
                    @click="exportReport('excel')"
                />
                <Button 
                    label="PDF" 
                    icon="pi pi-file-pdf" 
                    outlined 
                    size="small"
                    @click="exportReport('pdf')"
                />
            </div>
        </div>

        <!-- Форма параметров отчета -->
        <div class="card mb-4">
            <h3 class="text-lg font-semibold mb-3">Параметры отчета</h3>
            <div class="grid">
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Период с</label>
                    <Calendar 
                        v-model="reportParams.dateFrom" 
                        dateFormat="dd.mm.yy"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Период по</label>
                    <Calendar 
                        v-model="reportParams.dateTo" 
                        dateFormat="dd.mm.yy"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Организация</label>
                    <Dropdown 
                        v-model="reportParams.organizationId" 
                        :options="organizationOptions" 
                        optionLabel="label" 
                        optionValue="value"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Группировка</label>
                    <Dropdown 
                        v-model="reportParams.groupBy" 
                        :options="groupByOptions" 
                        optionLabel="label" 
                        optionValue="value"
                        class="w-full"
                    />
                </div>
                <div class="col-12">
                    <div class="flex align-items-center gap-4 mt-3">
                        <div class="flex align-items-center">
                            <Checkbox v-model="reportParams.includeDetails" binary />
                            <label class="ml-2">Включить детализацию</label>
                        </div>
                        <Button 
                            label="Сформировать отчет" 
                            icon="pi pi-refresh"
                            @click="generateReport"
                            :loading="loading"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Результат отчета -->
        <div v-if="reportData" class="grid">
            <!-- Сводная информация -->
            <div class="col-12">
                <div class="card mb-4">
                    <h3 class="text-lg font-semibold mb-4">Сводная информация</h3>
                    <div class="grid">
                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-blue-50 border-blue-200 p-3 border-round">
                                <div class="text-blue-600 font-medium text-sm">Общая выручка</div>
                                <div class="text-2xl font-bold text-blue-700">{{ formatAmount(reportData.summary.totalRevenue) }}</div>
                                <div class="text-blue-600 text-xs mt-1">{{ reportData.summary.period }}</div>
                            </div>
                        </div>
                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-green-50 border-green-200 p-3 border-round">
                                <div class="text-green-600 font-medium text-sm">Транзакций</div>
                                <div class="text-2xl font-bold text-green-700">{{ formatNumber(reportData.summary.totalTransactions) }}</div>
                                <div class="text-green-600 text-xs mt-1">За период</div>
                            </div>
                        </div>
                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-orange-50 border-orange-200 p-3 border-round">
                                <div class="text-orange-600 font-medium text-sm">Средний чек</div>
                                <div class="text-2xl font-bold text-orange-700">{{ formatAmount(reportData.summary.averageTicket) }}</div>
                                <div class="text-orange-600 text-xs mt-1">На транзакцию</div>
                            </div>
                        </div>
                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-purple-50 border-purple-200 p-3 border-round">
                                <div class="text-purple-600 font-medium text-sm">Организаций</div>
                                <div class="text-2xl font-bold text-purple-700">{{ reportData.details.length }}</div>
                                <div class="text-purple-600 text-xs mt-1">Активных</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Детализация -->
            <div v-if="reportParams.includeDetails" class="col-12">
                <div class="card">
                    <h3 class="text-lg font-semibold mb-4">Детализация по организациям</h3>
                    <DataTable
                        :value="reportData.details"
                        responsiveLayout="scroll"
                        :rowHover="true"
                        showGridlines
                    >
                        <Column field="name" header="Организация" sortable></Column>
                        
                        <Column field="revenue" header="Выручка" sortable style="min-width: 120px">
                            <template #body="{ data }">
                                <span class="font-bold">{{ formatAmount(data.revenue) }}</span>
                            </template>
                        </Column>

                        <Column field="transactions" header="Транзакций" sortable style="min-width: 100px">
                            <template #body="{ data }">
                                {{ formatNumber(data.transactions) }}
                            </template>
                        </Column>

                        <Column field="averageTicket" header="Средний чек" sortable style="min-width: 120px">
                            <template #body="{ data }">
                                {{ formatAmount(data.averageTicket) }}
                            </template>
                        </Column>

                        <Column field="percentage" header="Доля %" sortable style="min-width: 80px">
                            <template #body="{ data }">
                                <div class="flex align-items-center">
                                    <div class="w-full bg-gray-200 border-round" style="height: 8px;">
                                        <div 
                                            class="bg-blue-500 border-round h-full"
                                            :style="{ width: data.percentage + '%' }"
                                        ></div>
                                    </div>
                                    <span class="ml-2 text-sm font-medium">{{ formatPercent(data.percentage) }}</span>
                                </div>
                            </template>
                        </Column>
                    </DataTable>
                </div>
            </div>
        </div>

        <!-- Индикатор загрузки -->
        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Формирование отчета...</p>
            </div>
        </div>
    </div>
</template>

