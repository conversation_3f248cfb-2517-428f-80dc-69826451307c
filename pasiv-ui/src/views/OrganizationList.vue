<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ref, onMounted, computed, defineAsyncComponent } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { DataTableWrapper } from '@tkp3/common-ui';
import { OrganizationService } from '@/service/OrganizationService';
import {
    getStatusLabel,
    getStatusSeverity,
    getSyncStatusLabel,
    getSyncStatusSeverity
} from '@/constants/organization.js';

// Импортируем PrimeVue компоненты
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import Tag from 'primevue/tag';
import Toast from 'primevue/toast';
import ConfirmDialog from 'primevue/confirmdialog';

const router = useRouter();
const toast = useToast();
const confirm = useConfirm();

const organizations = ref([]);
const loading = ref(true);
const syncing = ref(false);
const syncingId = ref(null);
const error = ref(null);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    inn: { value: null, matchMode: FilterMatchMode.CONTAINS },
    ogrn: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

// Конфигурация колонок для DataTableWrapper
const columns = computed(() => [
    {
        field: 'name',
        header: 'Наименование',
        sortable: true,
        style: 'min-width: 300px',
        type: 'complex',
        title: { field: 'name' },
        subtitle: { field: 'shortName' },
        tags: [
            {
                field: 'type',
                mapping: {
                    organization: { label: 'Организация', severity: 'info' },
                    individual: { label: 'ИП', severity: 'warning' }
                }
            }
        ],
        filter: {
            component: defineAsyncComponent(() => import('primevue/inputtext')),
            props: {
                type: 'text',
                placeholder: 'Поиск по наименованию'
            }
        }
    },
    {
        field: 'inn',
        header: 'ИНН',
        sortable: true,
        type: 'code',
        filter: {
            component: defineAsyncComponent(() => import('primevue/inputtext')),
            props: {
                type: 'text',
                placeholder: 'Поиск по ИНН'
            }
        }
    },
    {
        field: 'ogrn',
        header: 'ОГРН/ОГРНИП',
        sortable: true,
        type: 'code',
        style: 'min-width: 150px',
        filter: {
            component: defineAsyncComponent(() => import('primevue/inputtext')),
            props: {
                type: 'text',
                placeholder: 'Поиск по ОГРН'
            }
        }
    },
    {
        field: 'director',
        header: 'Руководитель',
        sortable: true,
        style: 'min-width: 200px'
    },
    {
        field: 'status',
        header: 'Статус',
        sortable: true,
        type: 'tag',
        tagMapping: {
            active: { label: 'Активна', severity: 'success' },
            inactive: { label: 'Неактивна', severity: 'danger' },
            pending: { label: 'На рассмотрении', severity: 'warning' }
        },
        defaultSeverity: 'info',
        filter: {
            component: defineAsyncComponent(() => import('primevue/dropdown')),
            props: {
                options: [
                    { label: 'Активна', value: 'active' },
                    { label: 'Неактивна', value: 'inactive' },
                    { label: 'На рассмотрении', value: 'pending' }
                ],
                optionLabel: 'label',
                optionValue: 'value',
                placeholder: 'Выберите статус',
                showClear: true
            }
        }
    },
    {
        field: 'syncStatus',
        header: 'Синхронизация с 1С',
        sortable: true,
        type: 'complex',
        style: 'min-width: 180px',
        slot: 'sync-status'
    }
]);

// Конфигурация действий
const actions = computed(() => [
    {
        key: 'sync',
        icon: 'pi pi-refresh',
        tooltip: 'Синхронизировать с 1С',
        visible: true
    },
    {
        key: 'view',
        icon: 'pi pi-eye',
        tooltip: 'Просмотр',
        visible: true
    },
    {
        key: 'edit',
        icon: 'pi pi-pencil',
        tooltip: 'Редактировать',
        visible: true
    },
    {
        key: 'delete',
        icon: 'pi pi-trash',
        tooltip: 'Удалить',
        severity: 'danger',
        visible: true
    }
]);

// Состояния загрузки для действий
const loadingStates = computed(() => {
    const states = {};
    if (syncingId.value) {
        states[`sync_${syncingId.value}`] = true;
    }
    return states;
});

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        inn: { value: null, matchMode: FilterMatchMode.CONTAINS },
        ogrn: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};

onMounted(() => {
    loadOrganizations();
});

const loadOrganizations = async () => {
    try {
        loading.value = true;
        error.value = null;

        const data = await OrganizationService.getOrganizations();

        // Проверяем, что данные получены корректно
        if (Array.isArray(data)) {
            organizations.value = data;
            console.log(`Загружено ${data.length} организаций`);

            if (data.length === 0) {
                toast.add({
                    severity: 'info',
                    summary: 'Информация',
                    detail: 'Список организаций пуст',
                    life: 3000
                });
            }
        } else {
            console.warn('Получены некорректные данные:', data);
            organizations.value = [];
            error.value = 'Получены некорректные данные от сервера';

            toast.add({
                severity: 'warn',
                summary: 'Предупреждение',
                detail: 'Получены некорректные данные от сервера',
                life: 4000
            });
        }
    } catch (err) {
        console.error('Ошибка загрузки организаций:', err);
        organizations.value = [];
        error.value = 'Не удалось загрузить список организаций';

        toast.add({
            severity: 'error',
            summary: 'Ошибка загрузки',
            detail: 'Не удалось загрузить список организаций. Проверьте подключение к серверу.',
            life: 5000
        });
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const createOrganization = () => {
    router.push('/organizations/create');
};

const viewOrganization = (organization) => {
    router.push(`/organizations/${organization.id}`);
};

const editOrganization = (organization) => {
    router.push(`/organizations/${organization.id}/edit`);
};

const deleteOrganization = async (organization) => {
    confirm.require({
        message: `Вы уверены, что хотите удалить организацию "${organization.name}"?`,
        header: 'Подтверждение удаления',
        icon: 'pi pi-exclamation-triangle',
        rejectClass: 'p-button-secondary p-button-outlined',
        rejectLabel: 'Отмена',
        acceptLabel: 'Удалить',
        accept: async () => {
            try {
                const result = await OrganizationService.deleteOrganization(organization.id);

                if (result.success) {
                    console.log('Организация удалена:', organization.id);

                    toast.add({
                        severity: 'success',
                        summary: 'Успех',
                        detail: result.message || 'Организация успешно удалена',
                        life: 3000
                    });

                    await loadOrganizations();
                } else {
                    console.error('Ошибка удаления организации:', result.error);

                    toast.add({
                        severity: 'error',
                        summary: 'Ошибка удаления',
                        detail: result.error?.message || 'Не удалось удалить организацию',
                        life: 5000
                    });
                }
            } catch (error) {
                console.error('Ошибка при вызове API удаления:', error);

                toast.add({
                    severity: 'error',
                    summary: 'Ошибка',
                    detail: 'Произошла неизвестная ошибка при удалении организации',
                    life: 5000
                });
            }
        }
    });
};

const syncOrganization = async (organization) => {
    try {
        syncingId.value = organization.id;
        const result = await OrganizationService.syncOrganization(organization.id);

        if (result.success) {
            // Обновляем статус синхронизации в локальных данных
            const org = organizations.value.find((o) => o.id === organization.id);
            if (org) {
                org.syncStatus = 'synced';
                org.lastSyncDate = result.syncDate;
            }
            console.log('Синхронизация успешна:', result.message);
        } else {
            console.error('Ошибка синхронизации:', result.message);
        }
    } catch (error) {
        console.error('Ошибка синхронизации:', error);
    } finally {
        syncingId.value = null;
    }
};

const syncAllOrganizations = async () => {
    try {
        syncing.value = true;
        const result = await OrganizationService.syncAllOrganizations();

        console.log('Массовая синхронизация завершена:', result.message);

        // Перезагружаем список после массовой синхронизации
        await loadOrganizations();
    } catch (error) {
        console.error('Ошибка массовой синхронизации:', error);
    } finally {
        syncing.value = false;
    }
};

// Обработчик действий для DataTableWrapper
const handleAction = ({ action, data }) => {
    switch (action) {
        case 'sync':
            syncOrganization(data);
            break;
        case 'view':
            viewOrganization(data);
            break;
        case 'edit':
            editOrganization(data);
            break;
        case 'delete':
            deleteOrganization(data);
            break;
        default:
            console.warn('Неизвестное действие:', action);
    }
};

const importFromFile = () => {
    console.log('Загрузить организации из файла');
};

const exportData = () => {
    console.log('Экспорт организаций');
};

const formatDate = (dateString) => {
    if (!dateString) return 'Никогда';
    return new Date(dateString).toLocaleString('ru-RU');
};
</script>

<template>
    <div class="p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Организации</h1>
            <div class="flex gap-2">
                <Button label="Синхронизация с 1С" icon="pi pi-refresh" outlined :loading="syncing" @click="syncAllOrganizations" />
                <Button label="Загрузить из файла" icon="pi pi-upload" outlined @click="importFromFile" />
                <Button label="Экспорт" icon="pi pi-download" outlined @click="exportData" />
            </div>
        </div>

        <div class="flex justify-content-between align-items-center mb-4">
            <Button label="Создать" icon="pi pi-plus" @click="createOrganization" />
            <div class="flex gap-2">
                <Button type="button" icon="pi pi-filter-slash" label="Очистить" outlined @click="clearFilter" />
                <IconField>
                    <InputIcon>
                        <i class="pi pi-search" />
                    </InputIcon>
                    <InputText v-model="filters.global.value" placeholder="Поиск по всем полям" />
                </IconField>
            </div>
        </div>

        <div>
            <DataTableWrapper
                class="flex-1"
                :data="organizations"
                :loading="loading"
                :columns="columns"
                :actions="actions"
                :filters="filters"
                :loading-states="loadingStates"
                :table-config="{
                    paginator: true,
                    rows: 10,
                    dataKey: 'id',
                    rowHover: true,
                    filterDisplay: 'menu',
                    globalFilterFields: ['name', 'shortName', 'inn', 'ogrn', 'director'],
                    showGridlines: true,
                    responsiveLayout: 'scroll'
                }"
                :empty-config="{
                    icon: 'pi pi-info-circle',
                    message: 'Организации не найдены'
                }"
                @action="handleAction"
            >
                <!-- Кастомный слот для синхронизации -->
                <template #column-sync-status="{ data }">
                    <div>
                        <Tag :value="getSyncStatusLabel(data.syncStatus)" :severity="getSyncStatusSeverity(data.syncStatus)" class="mb-1" />
                        <div>
                            <small class="text-color-secondary">
                                {{ formatDate(data.lastSyncDate) }}
                            </small>
                        </div>
                    </div>
                </template>
            </DataTableWrapper>
        </div>
    </div>

    <!-- Toast для уведомлений -->
    <Toast />

    <!-- Диалог подтверждения -->
    <ConfirmDialog />
</template>
