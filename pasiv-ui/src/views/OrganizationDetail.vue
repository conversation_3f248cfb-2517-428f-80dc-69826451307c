<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();
const organizationId = route.params.organizationId;

const loading = ref(true);
const organization = ref(null);

onMounted(() => {
    loadOrganization();
});

const loadOrganization = async () => {
    try {
        loading.value = true;
        const data = await OrganizationService.getOrganizationById(organizationId);
        organization.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организации:', error);
    } finally {
        loading.value = false;
    }
};

const goBack = () => {
    router.push('/organizations');
};

const toggleNavigation = () => {
    console.log('Переключить навигацию');
};

const menuItems = ref([
    {
        label: 'Общая информация',
        icon: 'pi pi-info-circle',
        command: () => router.push(`/organizations/${organizationId}`)
    },
    {
        label: 'Сотрудники',
        icon: 'pi pi-users',
        command: () => router.push(`/organizations/${organizationId}/employees`)
    },
    {
        label: 'Договоры',
        icon: 'pi pi-file-edit',
        command: () => router.push(`/organizations/${organizationId}/contracts`)
    },
    {
        label: 'Финансовая информация',
        icon: 'pi pi-chart-line',
        command: () => router.push(`/organizations/${organizationId}/finance`)
    },
    {
        label: 'История операций',
        icon: 'pi pi-history',
        command: () => router.push(`/organizations/${organizationId}/history`)
    },
    {
        label: 'Аудит изменений',
        icon: 'pi pi-eye',
        command: () => router.push(`/organizations/${organizationId}/audit`)
    }
]);

const quickActions = ref([
    {
        label: 'Синхронизировать с 1С',
        icon: 'pi pi-sync',
        command: () => syncOrganization()
    },
    {
        label: 'Экспорт данных',
        icon: 'pi pi-download',
        command: () => exportOrganization()
    },
    {
        separator: true
    },
    {
        label: 'Заблокировать',
        icon: 'pi pi-ban',
        command: () => blockOrganization()
    }
]);

const syncOrganization = () => {
    console.log('Синхронизация с 1С...');
};

const exportOrganization = () => {
    console.log('Экспорт данных организации...');
};

const blockOrganization = () => {
    if (confirm('Заблокировать организацию?')) {
        console.log('Блокировка организации...');
    }
};

const editOrganization = () => {
    router.push(`/organizations/${organizationId}/edit`);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'inactive': return 'secondary';
        case 'suspended': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активная';
        case 'inactive': return 'Неактивная';
        case 'suspended': return 'Приостановлена';
        default: return 'Неизвестно';
    }
};

const getSyncStatusLabel = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'Синхронизирована';
        case 'pending': return 'Ожидает синхронизации';
        case 'error': return 'Ошибка синхронизации';
        case 'never': return 'Не синхронизировалась';
        default: return 'Неизвестно';
    }
};
</script>

<template>
    <div class="organization-container">
        <!-- Загрузка -->
        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных организации...</p>
            </div>
        </div>

        <!-- Организация не найдена -->
        <div v-else-if="!organization" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Организация не найдена</h3>
                <p class="text-color-secondary mb-4">
                    Организация с указанным идентификатором не существует или была удалена.
                </p>
                <Button
                    label="Вернуться к списку"
                    icon="pi pi-arrow-left"
                    @click="goBack"
                />
            </div>
        </div>

        <!-- Основной контент -->
        <div v-else>
            <!-- Заголовок организации -->
            <div class="organization-header card mb-4">
                <div class="flex align-items-center justify-content-between">
                    <div class="flex align-items-center">
                        <div class="w-4rem h-4rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-building text-blue-600 text-2xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold m-0 mb-1">{{ organization.name }}</h1>
                            <p class="text-color-secondary m-0">{{ organization.fullName }}</p>
                            <div class="flex align-items-center gap-2 mt-2">
                                <Tag
                                    :value="getStatusLabel(organization.status)"
                                    :severity="getStatusSeverity(organization.status)"
                                />
                                <Tag
                                    :value="getSyncStatusLabel(organization.syncStatus)"
                                    :severity="getSyncStatusSeverity(organization.syncStatus)"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Быстрые действия -->
                    <div class="flex gap-2">
                        <Button
                            label="Назад к списку"
                            icon="pi pi-arrow-left"
                            outlined
                            size="small"
                            @click="goBack"
                        />
                        <SplitButton
                            label="Действия"
                            icon="pi pi-cog"
                            :model="quickActions"
                            size="small"
                        />
                    </div>
                </div>
            </div>

            <Splitter style="height: calc(100vh - 140px)">
                <SplitterPanel :size="20" :minSize="15">
                    <div class="navigation-panel h-full">
                        <div class="p-3">
                            <div class="flex align-items-center justify-content-between mb-3">
                                <h3 class="text-lg font-semibold m-0">Навигация</h3>
                                <Button
                                    icon="pi pi-bars"
                                    text
                                    size="small"
                                    @click="toggleNavigation"
                                    v-tooltip.right="'Свернуть меню'"
                                />
                            </div>
                            <Menu :model="menuItems" class="w-full navigation-menu" />
                        </div>
                    </div>
                </SplitterPanel>

                <SplitterPanel :size="80">
                    <div class="content-panel h-full overflow-hidden">
                        <router-view v-if="$route.path !== `/organizations/${organizationId}`" />
                        <div v-else class="organization-dashboard p-4">
                            <!-- Основная информация об организации -->
                            <div class="grid">
                                <!-- Основная карточка -->
                                <div class="col-12">
                                    <div class="card mb-4">
                                        <div class="flex align-items-center mb-4">
                                            <div class="flex align-items-center">
                                                <Tag
                                                    :value="organization.type === 'organization' ? 'Организация' : 'ИП'"
                                                    :severity="organization.type === 'organization' ? 'info' : 'warning'"
                                                    class="mr-2"
                                                />
                                                <span class="text-xl font-bold">{{ organization.ownershipForm }}</span>
                                            </div>
                                        </div>

                                        <Divider />

                                        <!-- Основные реквизиты -->
                                        <h3 class="text-lg font-semibold mb-3">Основные реквизиты</h3>
                                        <div class="grid">
                                            <div class="col-12 md:col-4">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">ИНН</label>
                                                    <p class="m-0 font-mono text-lg">{{ organization.inn }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-4">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">
                                                        {{ organization.type === 'organization' ? 'ОГРН' : 'ОГРНИП' }}
                                                    </label>
                                                    <p class="m-0 font-mono">{{ organization.ogrn }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-4" v-if="organization.kpp">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">КПП</label>
                                                    <p class="m-0 font-mono">{{ organization.kpp }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-4">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">ОКПО</label>
                                                    <p class="m-0 font-mono">{{ organization.okpo }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-4">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">ОКТМО</label>
                                                    <p class="m-0 font-mono">{{ organization.oktmo }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">ОКВЭД</label>
                                                    <p class="m-0">{{ organization.okved }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <Divider />

                                        <!-- Адреса -->
                                        <h3 class="text-lg font-semibold mb-3">Адреса</h3>
                                        <div class="grid">
                                            <div class="col-12 md:col-6">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Юридический адрес</label>
                                                    <p class="m-0">{{ organization.legalAddress }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-6">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Фактический адрес</label>
                                                    <p class="m-0">{{ organization.actualAddress }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <Divider />

                                        <!-- Руководство -->
                                        <h3 class="text-lg font-semibold mb-3">Руководство и контакты</h3>
                                        <div class="grid">
                                            <div class="col-12 md:col-6">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Действует на основании</label>
                                                    <p class="m-0">{{ organization.basedOn }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-6" v-if="organization.generalDirector">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Генеральный директор</label>
                                                    <p class="m-0">{{ organization.generalDirector }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-6">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Ответственный за подпись</label>
                                                    <p class="m-0">{{ organization.responsibleForSignature }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-6">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Электронная почта</label>
                                                    <p class="m-0">
                                                        <a :href="`mailto:${organization.email}`" class="text-primary">{{ organization.email }}</a>
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-6">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Контактный телефон</label>
                                                    <p class="m-0">
                                                        <a :href="`tel:${organization.phone}`" class="text-primary">{{ organization.phone }}</a>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <Divider />

                                        <!-- Системная информация -->
                                        <h3 class="text-lg font-semibold mb-3">Системная информация</h3>
                                        <div class="grid">
                                            <div class="col-12 md:col-4">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Дата создания</label>
                                                    <p class="m-0">{{ formatDate(organization.createdDate) }}</p>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-4" v-if="organization.lastSyncDate">
                                                <div class="field">
                                                    <label class="font-semibold text-color-secondary text-sm">Последняя синхронизация</label>
                                                    <p class="m-0">{{ formatDate(organization.lastSyncDate) }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </SplitterPanel>
            </Splitter>
        </div>
    </div>
</template>

