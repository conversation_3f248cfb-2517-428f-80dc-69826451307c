<script setup>
import { ref, onMounted } from 'vue';
import { FinanceService } from '@/service/FinanceService';

const loading = ref(true);
const clearingData = ref([]);
const selectedPeriod = ref('');
const selectedStatus = ref('');

const periodOptions = ref([
    { label: 'Все периоды', value: '' },
    { label: 'Февраль 2024', value: '2024-02' },
    { label: 'Январь 2024', value: '2024-01' },
    { label: 'Декабрь 2023', value: '2023-12' }
]);

const statusOptions = ref([
    { label: 'Все статусы', value: '' },
    { label: 'Завершен', value: 'completed' },
    { label: 'В процессе', value: 'processing' },
    { label: 'Ожидает', value: 'pending' },
    { label: 'Ошибка', value: 'error' }
]);

onMounted(() => {
    loadClearingData();
});

const loadClearingData = async () => {
    try {
        loading.value = true;
        const filters = {
            period: selectedPeriod.value,
            status: selectedStatus.value
        };
        const data = await FinanceService.getClearingData(filters);
        clearingData.value = data;
    } catch (error) {
        console.error('Ошибка загрузки данных клиринга:', error);
    } finally {
        loading.value = false;
    }
};

const applyFilters = () => {
    loadClearingData();
};

const clearFilters = () => {
    selectedPeriod.value = '';
    selectedStatus.value = '';
    loadClearingData();
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed':
            return 'success';
        case 'processing':
            return 'warning';
        case 'pending':
            return 'info';
        case 'error':
            return 'danger';
        default:
            return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed':
            return 'Завершен';
        case 'processing':
            return 'В процессе';
        case 'pending':
            return 'Ожидает';
        case 'error':
            return 'Ошибка';
        default:
            return status;
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ru-RU');
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0
    }).format(amount);
};

const startClearing = () => {
    console.log('Запуск клиринга...');
};

const downloadClearingFile = (clearing) => {
    console.log('Скачивание файла клиринга:', clearing.id);
};

const viewClearingDetails = (clearing) => {
    console.log('Просмотр деталей клиринга:', clearing.id);
};
</script>

<template>
    <div class="clearing-list p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Клиринг</h2>
            <Button label="Запустить клиринг" icon="pi pi-play" @click="startClearing" />
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3 class="text-lg font-semibold mb-3">Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Период</label>
                    <Dropdown v-model="selectedPeriod" :options="periodOptions" optionLabel="label" optionValue="value" placeholder="Выберите период" class="w-full" />
                </div>
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Статус</label>
                    <Dropdown v-model="selectedStatus" :options="statusOptions" optionLabel="label" optionValue="value" placeholder="Выберите статус" class="w-full" />
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">&nbsp;</label>
                    <div class="flex gap-2">
                        <Button label="Применить" icon="pi pi-search" @click="applyFilters" size="small" />
                        <Button label="Сбросить" icon="pi pi-times" outlined @click="clearFilters" size="small" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Таблица клиринга -->
        <div class="card">
            <DataTable :value="clearingData" :loading="loading" :paginator="true" :rows="10" dataKey="id" :rowHover="true" showGridlines responsiveLayout="scroll" sortField="startDate" :sortOrder="-1" data-testid="clearing-table">
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="operationDate" header="Дата" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.operationDate) }}
                    </template>
                </Column>

                <Column field="operationType" header="Тип операции" :sortable="true">
                    <template #body="{ data }">
                        <Tag :value="getOperationTypeLabel(data.operationType)" severity="info" />
                    </template>
                </Column>

                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag :value="getStatusLabel(data.status)" :severity="getStatusSeverity(data.status)" />
                    </template>
                </Column>

                <Column field="totalAmount" header="Сумма" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ data.totalAmount > 0 ? formatAmount(data.totalAmount) : '-' }}
                        </div>
                    </template>
                </Column>

                <Column field="transactionCount" header="Транзакций" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right">
                            {{ data.transactionCount > 0 ? formatNumber(data.transactionCount) : '-' }}
                        </div>
                    </template>
                </Column>

                <Column field="organizationCount" header="Организаций" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-center">
                            {{ data.organizationCount > 0 ? data.organizationCount : '-' }}
                        </div>
                    </template>
                </Column>

                <Column header="Время выполнения">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div v-if="data.startTime">Начало: {{ formatTime(data.startTime) }}</div>
                            <div v-if="data.endTime">Окончание: {{ formatTime(data.endTime) }}</div>
                            <div v-if="data.processingTime" class="font-semibold">{{ data.processingTime }}</div>
                            <div v-else-if="data.status === 'processing'" class="text-orange-600">Выполняется...</div>
                        </div>
                    </template>
                </Column>

                <Column header="Действия">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button v-if="data.reportUrl" icon="pi pi-file-pdf" size="small" text @click="viewReport(data)" v-tooltip.top="'Скачать отчет'" />
                            <Button v-if="data.status === 'error'" icon="pi pi-refresh" size="small" text severity="warning" v-tooltip.top="'Повторить'" />
                            <Button icon="pi pi-eye" size="small" text v-tooltip.top="'Детали'" />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

