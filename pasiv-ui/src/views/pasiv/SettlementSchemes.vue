<script setup>
import { ref, onMounted } from 'vue';
import { FinanceService } from '@/service/FinanceService';

const loading = ref(true);
const schemes = ref([]);
const showAddDialog = ref(false);
const selectedScheme = ref(null);

const newScheme = ref({
    name: '',
    type: '',
    description: '',
    commissionRate: 0,
    settlementPeriod: '',
    isActive: true
});

const schemeTypes = ref([
    { label: 'Через оператора', value: 'through_operator' },
    { label: 'Прямые расчеты', value: 'direct' },
    { label: 'Через расчетный центр', value: 'through_rc' },
    { label: 'Агентская схема', value: 'agent' }
]);

const settlementPeriods = ref([
    { label: 'Ежедневно', value: 'daily' },
    { label: 'Еженедельно', value: 'weekly' },
    { label: 'Ежемесячно', value: 'monthly' },
    { label: 'По требованию', value: 'on_demand' }
]);

onMounted(() => {
    loadSchemes();
});

const loadSchemes = async () => {
    try {
        loading.value = true;
        // Мок данные для схем расчетов
        const mockData = [
            {
                id: 1,
                name: 'Стандартная схема через оператора',
                type: 'through_operator',
                description: 'Расчеты через оператора с комиссией 2.5%',
                commissionRate: 2.5,
                settlementPeriod: 'monthly',
                isActive: true,
                organizationCount: 12,
                createdDate: '2024-01-15T10:00:00Z'
            },
            {
                id: 2,
                name: 'Прямые расчеты для крупных перевозчиков',
                type: 'direct',
                description: 'Прямые расчеты без посредников',
                commissionRate: 1.8,
                settlementPeriod: 'weekly',
                isActive: true,
                organizationCount: 3,
                createdDate: '2024-01-20T14:00:00Z'
            },
            {
                id: 3,
                name: 'Агентская схема',
                type: 'agent',
                description: 'Схема для агентов с вознаграждением',
                commissionRate: 3.0,
                settlementPeriod: 'monthly',
                isActive: true,
                organizationCount: 8,
                createdDate: '2024-02-01T09:00:00Z'
            }
        ];
        schemes.value = mockData;
    } catch (error) {
        console.error('Ошибка загрузки схем расчетов:', error);
    } finally {
        loading.value = false;
    }
};

const addScheme = () => {
    newScheme.value = {
        name: '',
        type: '',
        description: '',
        commissionRate: 0,
        settlementPeriod: '',
        isActive: true
    };
    showAddDialog.value = true;
};

const saveScheme = async () => {
    try {
        console.log('Сохранение схемы:', newScheme.value);
        showAddDialog.value = false;
        await loadSchemes();
    } catch (error) {
        console.error('Ошибка сохранения схемы:', error);
    }
};

const editScheme = (scheme) => {
    selectedScheme.value = { ...scheme };
    console.log('Редактирование схемы:', scheme.id);
};

const deleteScheme = async (scheme) => {
    if (confirm(`Удалить схему "${scheme.name}"?`)) {
        try {
            console.log('Удаление схемы:', scheme.id);
            await loadSchemes();
        } catch (error) {
            console.error('Ошибка удаления схемы:', error);
        }
    }
};

const toggleSchemeStatus = async (scheme) => {
    try {
        console.log('Изменение статуса схемы:', scheme.id, !scheme.isActive);
        await loadSchemes();
    } catch (error) {
        console.error('Ошибка изменения статуса схемы:', error);
    }
};

const getTypeLabel = (type) => {
    const typeObj = schemeTypes.value.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
};

const getTypeSeverity = (type) => {
    switch (type) {
        case 'through_operator': return 'info';
        case 'direct': return 'success';
        case 'through_rc': return 'warning';
        case 'agent': return 'secondary';
        default: return 'secondary';
    }
};

const getPeriodLabel = (period) => {
    const periodObj = settlementPeriods.value.find(p => p.value === period);
    return periodObj ? periodObj.label : period;
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};
</script>

<template>
    <div class="settlement-schemes p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Схемы расчетов</h2>
            <Button
                label="Добавить схему"
                icon="pi pi-plus"
                @click="addScheme"
            />
        </div>

        <!-- Таблица схем -->
        <div class="card">
            <DataTable
                :value="schemes"
                :loading="loading"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                showGridlines
                responsiveLayout="scroll"
                data-testid="schemes-table"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-share-alt text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Схемы расчетов не найдены</p>
                        <Button 
                            label="Добавить первую схему" 
                            icon="pi pi-plus" 
                            class="mt-3"
                            @click="addScheme"
                        />
                    </div>
                </template>

                <Column field="name" header="Название" sortable></Column>

                <Column field="type" header="Тип схемы" sortable style="min-width: 150px">
                    <template #body="{ data }">
                        <Tag 
                            :value="getTypeLabel(data.type)" 
                            :severity="getTypeSeverity(data.type)" 
                        />
                    </template>
                </Column>

                <Column field="commissionRate" header="Комиссия %" sortable style="min-width: 100px">
                    <template #body="{ data }">
                        <span class="font-bold">{{ data.commissionRate }}%</span>
                    </template>
                </Column>

                <Column field="settlementPeriod" header="Период расчетов" sortable style="min-width: 130px">
                    <template #body="{ data }">
                        {{ getPeriodLabel(data.settlementPeriod) }}
                    </template>
                </Column>

                <Column field="organizationCount" header="Организаций" sortable style="min-width: 100px">
                    <template #body="{ data }">
                        <span class="font-bold">{{ data.organizationCount }}</span>
                    </template>
                </Column>

                <Column field="isActive" header="Статус" sortable style="min-width: 100px">
                    <template #body="{ data }">
                        <Tag 
                            :value="data.isActive ? 'Активна' : 'Неактивна'" 
                            :severity="data.isActive ? 'success' : 'secondary'" 
                        />
                    </template>
                </Column>

                <Column field="createdDate" header="Дата создания" sortable style="min-width: 120px">
                    <template #body="{ data }">
                        {{ formatDate(data.createdDate) }}
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 150px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                @click="editScheme(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                :icon="data.isActive ? 'pi pi-pause' : 'pi pi-play'" 
                                size="small" 
                                text 
                                :severity="data.isActive ? 'warning' : 'success'"
                                @click="toggleSchemeStatus(data)"
                                :v-tooltip.top="data.isActive ? 'Деактивировать' : 'Активировать'"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                size="small" 
                                text 
                                severity="danger" 
                                @click="deleteScheme(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>

        <!-- Диалог добавления схемы -->
        <Dialog 
            v-model:visible="showAddDialog" 
            modal 
            header="Добавить схему расчетов" 
            :style="{ width: '600px' }"
        >
            <div class="grid">
                <div class="col-12">
                    <label class="block text-sm font-medium mb-2">Название схемы *</label>
                    <InputText v-model="newScheme.name" class="w-full" />
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Тип схемы *</label>
                    <Dropdown 
                        v-model="newScheme.type" 
                        :options="schemeTypes" 
                        optionLabel="label" 
                        optionValue="value"
                        placeholder="Выберите тип"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Период расчетов *</label>
                    <Dropdown 
                        v-model="newScheme.settlementPeriod" 
                        :options="settlementPeriods" 
                        optionLabel="label" 
                        optionValue="value"
                        placeholder="Выберите период"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Комиссия %</label>
                    <InputNumber 
                        v-model="newScheme.commissionRate" 
                        :minFractionDigits="1"
                        :maxFractionDigits="2"
                        suffix="%"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Статус</label>
                    <div class="flex align-items-center">
                        <Checkbox v-model="newScheme.isActive" binary />
                        <label class="ml-2">Активна</label>
                    </div>
                </div>
                <div class="col-12">
                    <label class="block text-sm font-medium mb-2">Описание</label>
                    <Textarea v-model="newScheme.description" rows="3" class="w-full" />
                </div>
            </div>

            <template #footer>
                <Button 
                    label="Отмена" 
                    icon="pi pi-times" 
                    text 
                    @click="showAddDialog = false" 
                />
                <Button 
                    label="Сохранить" 
                    icon="pi pi-check" 
                    @click="saveScheme" 
                />
            </template>
        </Dialog>
    </div>
</template>

