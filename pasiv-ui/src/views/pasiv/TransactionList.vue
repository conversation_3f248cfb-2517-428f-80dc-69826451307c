<script setup>
import { ref, onMounted, computed } from 'vue';
import { DataTableWrapper } from '@tkp3/common-ui';
import { TransactionService } from '@/service/TransactionService';

const loading = ref(true);
const transactions = ref([]);
const selectedOrganization = ref('');
const selectedPaymentMethod = ref('');
const selectedStatus = ref('');
const dateFrom = ref(null);
const dateTo = ref(null);

const organizationOptions = ref([
    { label: 'Все организации', value: '' },
    { label: 'ООО "Транспорт Плюс"', value: '1' },
    { label: 'ИП Иванов И.И.', value: '2' },
    { label: 'ООО "Городской транспорт"', value: '3' }
]);

const paymentMethodOptions = ref([
    { label: 'Все способы оплаты', value: '' },
    { label: 'EMV карты', value: 'emv_card' },
    { label: 'Транспортные карты', value: 'transport_card' },
    { label: 'Наличные', value: 'cash' },
    { label: 'QR-код', value: 'qr_code' }
]);

const statusOptions = ref([
    { label: 'Все статусы', value: '' },
    { label: 'Завершено', value: 'completed' },
    { label: 'В обработке', value: 'processing' },
    { label: 'Ошибка', value: 'error' },
    { label: 'Отменено', value: 'cancelled' }
]);

onMounted(() => {
    loadTransactions();
});

const loadTransactions = async () => {
    try {
        loading.value = true;
        const filters = {
            organizationId: selectedOrganization.value,
            paymentMethod: selectedPaymentMethod.value,
            status: selectedStatus.value,
            dateFrom: dateFrom.value,
            dateTo: dateTo.value
        };
        const data = await TransactionService.getTransactions(filters);
        transactions.value = data;
    } catch (error) {
        console.error('Ошибка загрузки транзакций:', error);
    } finally {
        loading.value = false;
    }
};

const applyFilters = () => {
    loadTransactions();
};

const clearFilters = () => {
    selectedOrganization.value = '';
    selectedPaymentMethod.value = '';
    selectedStatus.value = '';
    dateFrom.value = null;
    dateTo.value = null;
    loadTransactions();
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed':
            return 'success';
        case 'processing':
            return 'warning';
        case 'error':
            return 'danger';
        case 'cancelled':
            return 'secondary';
        default:
            return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed':
            return 'Завершено';
        case 'processing':
            return 'В обработке';
        case 'error':
            return 'Ошибка';
        case 'cancelled':
            return 'Отменено';
        default:
            return status;
    }
};

const getPaymentMethodLabel = (method) => {
    switch (method) {
        case 'emv_card':
            return 'EMV карта';
        case 'transport_card':
            return 'Транспортная карта';
        case 'cash':
            return 'Наличные';
        case 'qr_code':
            return 'QR-код';
        default:
            return method;
    }
};

const getPaymentMethodSeverity = (method) => {
    switch (method) {
        case 'emv_card':
            return 'info';
        case 'transport_card':
            return 'success';
        case 'cash':
            return 'warning';
        case 'qr_code':
            return 'secondary';
        default:
            return 'secondary';
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 2
    }).format(amount);
};

const exportTransactions = () => {
    console.log('Экспорт транзакций...');
};

const viewTransactionDetails = (transaction) => {
    console.log('Просмотр деталей транзакции:', transaction.id);
};
</script>

<template>
    <div class="h-full overflow-y-auto p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Транзакции</h2>
            <Button label="Экспорт" icon="pi pi-download" outlined @click="exportTransactions" />
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3 class="text-lg font-semibold mb-3">Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Организация</label>
                    <Dropdown v-model="selectedOrganization" :options="organizationOptions" optionLabel="label" optionValue="value" placeholder="Выберите организацию" class="w-full" />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Способ оплаты</label>
                    <Dropdown v-model="selectedPaymentMethod" :options="paymentMethodOptions" optionLabel="label" optionValue="value" placeholder="Выберите способ" class="w-full" />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Статус</label>
                    <Dropdown v-model="selectedStatus" :options="statusOptions" optionLabel="label" optionValue="value" placeholder="Выберите статус" class="w-full" />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Дата с</label>
                    <Calendar v-model="dateFrom" dateFormat="dd.mm.yy" placeholder="дд.мм.гггг" class="w-full" />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Дата по</label>
                    <Calendar v-model="dateTo" dateFormat="dd.mm.yy" placeholder="дд.мм.гггг" class="w-full" />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">&nbsp;</label>
                    <div class="flex gap-2">
                        <Button label="Применить" icon="pi pi-search" @click="applyFilters" size="small" />
                        <Button label="Сбросить" icon="pi pi-times" outlined @click="clearFilters" size="small" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Таблица транзакций -->
        <div class="card">
            <DataTable :value="transactions" :loading="loading" :paginator="true" :rows="20" dataKey="id" :rowHover="true" showGridlines responsiveLayout="scroll" sortField="timestamp" :sortOrder="-1" data-testid="transaction-table">
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-list text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Транзакции не найдены</p>
                    </div>
                </template>

                <Column field="timestamp" header="Дата и время" sortable style="min-width: 150px">
                    <template #body="{ data }">
                        {{ formatDate(data.timestamp) }}
                    </template>
                </Column>

                <Column field="amount" header="Сумма" sortable style="min-width: 100px">
                    <template #body="{ data }">
                        <span class="font-bold">{{ formatAmount(data.amount) }}</span>
                    </template>
                </Column>

                <Column field="paymentMethod" header="Способ оплаты" sortable style="min-width: 140px">
                    <template #body="{ data }">
                        <Tag :value="getPaymentMethodLabel(data.paymentMethod)" :severity="getPaymentMethodSeverity(data.paymentMethod)" />
                    </template>
                </Column>

                <Column field="routeName" header="Маршрут" sortable></Column>

                <Column field="vehicleId" header="ТС" style="min-width: 80px">
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.vehicleId }}</span>
                    </template>
                </Column>

                <Column field="cardNumber" header="Карта" style="min-width: 100px">
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.cardNumber || '-' }}</span>
                    </template>
                </Column>

                <Column field="processingSystem" header="Система" style="min-width: 80px">
                    <template #body="{ data }">
                        <span class="text-sm">{{ data.processingSystem }}</span>
                    </template>
                </Column>

                <Column field="status" header="Статус" sortable style="min-width: 100px">
                    <template #body="{ data }">
                        <Tag :value="getStatusLabel(data.status)" :severity="getStatusSeverity(data.status)" />
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 80px">
                    <template #body="{ data }">
                        <Button icon="pi pi-eye" size="small" text @click="viewTransactionDetails(data)" v-tooltip.top="'Просмотр'" />
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>
