<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { EmployeeService } from '@/service/EmployeeService';

const route = useRoute();
const organizationId = route.params.organizationId;

const loading = ref(true);
const employees = ref([]);
const showAddDialog = ref(false);
const selectedEmployee = ref(null);

const newEmployee = ref({
    firstName: '',
    lastName: '',
    middleName: '',
    email: '',
    phone: '',
    position: '',
    roles: []
});

const availableRoles = ref([
    { label: 'Администратор ПАСИВ', value: 'pasiv_admin' },
    { label: 'Оператор биллинга', value: 'billing_operator' },
    { label: 'Аналитик отчетности', value: 'report_analyst' },
    { label: 'Контролер взаиморасчетов', value: 'settlement_controller' }
]);

onMounted(() => {
    loadEmployees();
});

const loadEmployees = async () => {
    try {
        loading.value = true;
        const data = await EmployeeService.getEmployeesByOrganization(organizationId);
        employees.value = data;
    } catch (error) {
        console.error('Ошибка загрузки сотрудников:', error);
    } finally {
        loading.value = false;
    }
};

const addEmployee = () => {
    newEmployee.value = {
        firstName: '',
        lastName: '',
        middleName: '',
        email: '',
        phone: '',
        position: '',
        roles: []
    };
    showAddDialog.value = true;
};

const saveEmployee = async () => {
    try {
        await EmployeeService.createEmployee(organizationId, newEmployee.value);
        showAddDialog.value = false;
        await loadEmployees();
    } catch (error) {
        console.error('Ошибка создания сотрудника:', error);
    }
};

const editEmployee = (employee) => {
    selectedEmployee.value = { ...employee };
    // Открыть диалог редактирования
};

const deleteEmployee = async (employee) => {
    if (confirm(`Удалить сотрудника ${employee.firstName} ${employee.lastName}?`)) {
        try {
            await EmployeeService.deleteEmployee(employee.id);
            await loadEmployees();
        } catch (error) {
            console.error('Ошибка удаления сотрудника:', error);
        }
    }
};

const getRoleLabels = (roles) => {
    return roles.map(role => {
        const roleObj = availableRoles.value.find(r => r.value === role);
        return roleObj ? roleObj.label : role;
    }).join(', ');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'blocked': return 'danger';
        case 'pending': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активен';
        case 'blocked': return 'Заблокирован';
        case 'pending': return 'Ожидает активации';
        default: return status;
    }
};
</script>

<template>
    <div class="employee-list p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Сотрудники организации</h2>
            <Button
                label="Добавить сотрудника"
                icon="pi pi-plus"
                @click="addEmployee"
            />
        </div>

        <div class="card">
            <DataTable
                :value="employees"
                :loading="loading"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                showGridlines
                responsiveLayout="scroll"
                data-testid="employee-table"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-users text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Сотрудники не найдены</p>
                        <Button 
                            label="Добавить первого сотрудника" 
                            icon="pi pi-plus" 
                            class="mt-3"
                            @click="addEmployee"
                        />
                    </div>
                </template>

                <Column field="lastName" header="Фамилия" sortable></Column>
                <Column field="firstName" header="Имя" sortable></Column>
                <Column field="middleName" header="Отчество" sortable></Column>
                <Column field="position" header="Должность" sortable></Column>
                <Column field="email" header="Email" sortable></Column>
                <Column field="phone" header="Телефон"></Column>
                
                <Column header="Роли">
                    <template #body="{ data }">
                        <div class="flex flex-wrap gap-1">
                            <Tag 
                                v-for="role in data.roles" 
                                :key="role"
                                :value="getRoleLabels([role])"
                                severity="info"
                                class="text-xs"
                            />
                        </div>
                    </template>
                </Column>

                <Column header="Статус">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                @click="editEmployee(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                size="small" 
                                text 
                                severity="danger" 
                                @click="deleteEmployee(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>

        <!-- Диалог добавления сотрудника -->
        <Dialog 
            v-model:visible="showAddDialog" 
            modal 
            header="Добавить сотрудника" 
            :style="{ width: '600px' }"
        >
            <div class="grid">
                <div class="col-12 md:col-4">
                    <label class="block text-sm font-medium mb-2">Фамилия *</label>
                    <InputText v-model="newEmployee.lastName" class="w-full" />
                </div>
                <div class="col-12 md:col-4">
                    <label class="block text-sm font-medium mb-2">Имя *</label>
                    <InputText v-model="newEmployee.firstName" class="w-full" />
                </div>
                <div class="col-12 md:col-4">
                    <label class="block text-sm font-medium mb-2">Отчество</label>
                    <InputText v-model="newEmployee.middleName" class="w-full" />
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Email *</label>
                    <InputText v-model="newEmployee.email" type="email" class="w-full" />
                </div>
                <div class="col-12 md:col-6">
                    <label class="block text-sm font-medium mb-2">Телефон</label>
                    <InputText v-model="newEmployee.phone" class="w-full" />
                </div>
                <div class="col-12">
                    <label class="block text-sm font-medium mb-2">Должность *</label>
                    <InputText v-model="newEmployee.position" class="w-full" />
                </div>
                <div class="col-12">
                    <label class="block text-sm font-medium mb-2">Роли в системе</label>
                    <MultiSelect 
                        v-model="newEmployee.roles" 
                        :options="availableRoles" 
                        optionLabel="label" 
                        optionValue="value"
                        placeholder="Выберите роли"
                        class="w-full"
                    />
                </div>
            </div>

            <template #footer>
                <Button 
                    label="Отмена" 
                    icon="pi pi-times" 
                    text 
                    @click="showAddDialog = false" 
                />
                <Button 
                    label="Сохранить" 
                    icon="pi pi-check" 
                    @click="saveEmployee" 
                />
            </template>
        </Dialog>
    </div>
</template>

