<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { AuditService } from '@/service/AuditService';

const route = useRoute();
const organizationId = route.params.organizationId;

const loading = ref(true);
const auditLogs = ref([]);
const selectedAction = ref('');
const selectedUser = ref('');
const dateFrom = ref(null);
const dateTo = ref(null);

const actionOptions = ref([
    { label: 'Все действия', value: '' },
    { label: 'Создание', value: 'CREATE' },
    { label: 'Обновление', value: 'UPDATE' },
    { label: 'Удаление', value: 'DELETE' },
    { label: 'Синхронизация', value: 'SYNC' },
    { label: 'Блокировка', value: 'BLOCK' },
    { label: 'Разблокировка', value: 'UNBLOCK' }
]);

onMounted(() => {
    loadAuditLogs();
});

const loadAuditLogs = async () => {
    try {
        loading.value = true;
        const filters = {
            action: selectedAction.value,
            user: selectedUser.value,
            dateFrom: dateFrom.value,
            dateTo: dateTo.value
        };
        const data = await AuditService.getOrganizationAuditLog(organizationId, filters);
        auditLogs.value = data;
    } catch (error) {
        console.error('Ошибка загрузки журнала аудита:', error);
    } finally {
        loading.value = false;
    }
};

const applyFilters = () => {
    loadAuditLogs();
};

const clearFilters = () => {
    selectedAction.value = '';
    selectedUser.value = '';
    dateFrom.value = null;
    dateTo.value = null;
    loadAuditLogs();
};

const getActionSeverity = (action) => {
    switch (action) {
        case 'CREATE': return 'success';
        case 'UPDATE': return 'info';
        case 'DELETE': return 'danger';
        case 'BLOCK': return 'warning';
        case 'UNBLOCK': return 'success';
        case 'SYNC': return 'secondary';
        default: return 'secondary';
    }
};

const getActionLabel = (action) => {
    switch (action) {
        case 'CREATE': return 'Создание';
        case 'UPDATE': return 'Обновление';
        case 'DELETE': return 'Удаление';
        case 'BLOCK': return 'Блокировка';
        case 'UNBLOCK': return 'Разблокировка';
        case 'SYNC': return 'Синхронизация';
        default: return action;
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const formatDetails = (details) => {
    if (!details) return '-';
    return Object.entries(details)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
};
</script>

<template>
    <div class="audit-log p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Журнал аудита организации</h2>
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3 class="text-lg font-semibold mb-3">Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Действие</label>
                    <Dropdown 
                        v-model="selectedAction" 
                        :options="actionOptions" 
                        optionLabel="label" 
                        optionValue="value"
                        placeholder="Выберите действие"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-3">
                    <label class="block text-sm font-medium mb-2">Пользователь</label>
                    <InputText 
                        v-model="selectedUser" 
                        placeholder="Логин пользователя"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Дата с</label>
                    <Calendar 
                        v-model="dateFrom" 
                        dateFormat="dd.mm.yy"
                        placeholder="дд.мм.гггг"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Дата по</label>
                    <Calendar 
                        v-model="dateTo" 
                        dateFormat="dd.mm.yy"
                        placeholder="дд.мм.гггг"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">&nbsp;</label>
                    <div class="flex gap-2">
                        <Button 
                            label="Применить" 
                            icon="pi pi-search" 
                            @click="applyFilters"
                            size="small"
                        />
                        <Button 
                            label="Сбросить" 
                            icon="pi pi-times" 
                            outlined 
                            @click="clearFilters"
                            size="small"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Таблица журнала -->
        <div class="card">
            <DataTable
                :value="auditLogs"
                :loading="loading"
                :paginator="true"
                :rows="20"
                dataKey="id"
                :rowHover="true"
                showGridlines
                responsiveLayout="scroll"
                sortField="timestamp"
                :sortOrder="-1"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-eye text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Записи аудита не найдены</p>
                    </div>
                </template>

                <Column field="timestamp" header="Дата и время" sortable style="min-width: 150px">
                    <template #body="{ data }">
                        {{ formatDate(data.timestamp) }}
                    </template>
                </Column>

                <Column field="action" header="Действие" sortable style="min-width: 120px">
                    <template #body="{ data }">
                        <Tag 
                            :value="getActionLabel(data.action)" 
                            :severity="getActionSeverity(data.action)" 
                        />
                    </template>
                </Column>

                <Column field="description" header="Описание" sortable></Column>

                <Column field="userId" header="Пользователь" sortable style="min-width: 120px">
                    <template #body="{ data }">
                        <span class="font-mono">{{ data.userId }}</span>
                    </template>
                </Column>

                <Column field="ipAddress" header="IP адрес" style="min-width: 120px">
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.ipAddress || '-' }}</span>
                    </template>
                </Column>

                <Column field="details" header="Детали" style="min-width: 200px">
                    <template #body="{ data }">
                        <span class="text-sm">{{ formatDetails(data.details) }}</span>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

