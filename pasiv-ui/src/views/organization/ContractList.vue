<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ContractService } from '@/service/ContractService';

const route = useRoute();
const router = useRouter();
const organizationId = route.params.organizationId;

const loading = ref(true);
const contracts = ref([]);

onMounted(() => {
    loadContracts();
});

const loadContracts = async () => {
    try {
        loading.value = true;
        const data = await ContractService.getContractsByOrganization(organizationId);
        contracts.value = data;
    } catch (error) {
        console.error('Ошибка загрузки договоров:', error);
    } finally {
        loading.value = false;
    }
};

const viewContract = (contract) => {
    router.push(`/contracts/${contract.id}`);
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'warning';
        case 'expired': return 'danger';
        case 'terminated': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'draft': return 'Черновик';
        case 'expired': return 'Истек';
        case 'terminated': return 'Расторгнут';
        default: return status;
    }
};

const getSyncStatusSeverity = (status) => {
    switch (status) {
        case 'synced': return 'success';
        case 'pending': return 'warning';
        case 'error': return 'danger';
        case 'never': return 'secondary';
        default: return 'secondary';
    }
};

const getSyncStatusLabel = (status) => {
    switch (status) {
        case 'synced': return 'Синхронизирован';
        case 'pending': return 'Ожидает синхронизации';
        case 'error': return 'Ошибка синхронизации';
        case 'never': return 'Не синхронизирован';
        default: return status;
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0
    }).format(amount);
};
</script>

<template>
    <div class="contract-list p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Договоры организации</h2>
        </div>

        <div class="card">
            <DataTable
                :value="contracts"
                :loading="loading"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-file-edit text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Договоры не найдены</p>
                    </div>
                </template>

                <Column field="number" header="Номер договора" sortable>
                    <template #body="{ data }">
                        <span class="font-mono">{{ data.number }}</span>
                    </template>
                </Column>

                <Column field="name" header="Наименование" sortable></Column>

                <Column field="contractorName" header="Контрагент" sortable></Column>

                <Column header="Период действия">
                    <template #body="{ data }">
                        {{ formatDate(data.startDate) }} - {{ formatDate(data.endDate) }}
                    </template>
                </Column>

                <Column field="totalAmount" header="Сумма договора" sortable>
                    <template #body="{ data }">
                        {{ formatAmount(data.totalAmount) }}
                    </template>
                </Column>

                <Column header="Статус">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>

                <Column header="Синхронизация">
                    <template #body="{ data }">
                        <Tag 
                            :value="getSyncStatusLabel(data.syncStatus)" 
                            :severity="getSyncStatusSeverity(data.syncStatus)" 
                        />
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 100px">
                    <template #body="{ data }">
                        <Button 
                            icon="pi pi-eye" 
                            size="small" 
                            text 
                            @click="viewContract(data)"
                            v-tooltip.top="'Просмотр'"
                        />
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

