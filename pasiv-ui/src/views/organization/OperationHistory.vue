<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { TransactionService } from '@/service/TransactionService';

const route = useRoute();
const organizationId = route.params.organizationId;

const loading = ref(true);
const operations = ref([]);
const selectedType = ref('');
const selectedStatus = ref('');
const dateFrom = ref(null);
const dateTo = ref(null);

const typeOptions = ref([
    { label: 'Все операции', value: '' },
    { label: 'Транзакции', value: 'transaction' },
    { label: 'Биллинг', value: 'billing' },
    { label: 'Взаиморасчеты', value: 'settlement' },
    { label: 'Клиринг', value: 'clearing' }
]);

const statusOptions = ref([
    { label: 'Все статусы', value: '' },
    { label: 'Успешно', value: 'success' },
    { label: 'В обработке', value: 'processing' },
    { label: 'Ошибка', value: 'error' },
    { label: 'Отменено', value: 'cancelled' }
]);

onMounted(() => {
    loadOperations();
});

const loadOperations = async () => {
    try {
        loading.value = true;
        const filters = {
            type: selectedType.value,
            status: selectedStatus.value,
            dateFrom: dateFrom.value,
            dateTo: dateTo.value
        };
        const data = await TransactionService.getOrganizationOperations(organizationId, filters);
        operations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки истории операций:', error);
    } finally {
        loading.value = false;
    }
};

const applyFilters = () => {
    loadOperations();
};

const clearFilters = () => {
    selectedType.value = '';
    selectedStatus.value = '';
    dateFrom.value = null;
    dateTo.value = null;
    loadOperations();
};

const getTypeSeverity = (type) => {
    switch (type) {
        case 'transaction': return 'info';
        case 'billing': return 'success';
        case 'settlement': return 'warning';
        case 'clearing': return 'secondary';
        default: return 'secondary';
    }
};

const getTypeLabel = (type) => {
    switch (type) {
        case 'transaction': return 'Транзакция';
        case 'billing': return 'Биллинг';
        case 'settlement': return 'Взаиморасчет';
        case 'clearing': return 'Клиринг';
        default: return type;
    }
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'success': return 'success';
        case 'processing': return 'warning';
        case 'error': return 'danger';
        case 'cancelled': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'success': return 'Успешно';
        case 'processing': return 'В обработке';
        case 'error': return 'Ошибка';
        case 'cancelled': return 'Отменено';
        default: return status;
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const formatAmount = (amount) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0
    }).format(amount);
};
</script>

<template>
    <div class="operation-history p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">История операций</h2>
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3 class="text-lg font-semibold mb-3">Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Тип операции</label>
                    <Dropdown 
                        v-model="selectedType" 
                        :options="typeOptions" 
                        optionLabel="label" 
                        optionValue="value"
                        placeholder="Выберите тип"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Статус</label>
                    <Dropdown 
                        v-model="selectedStatus" 
                        :options="statusOptions" 
                        optionLabel="label" 
                        optionValue="value"
                        placeholder="Выберите статус"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Дата с</label>
                    <Calendar 
                        v-model="dateFrom" 
                        dateFormat="dd.mm.yy"
                        placeholder="дд.мм.гггг"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-2">
                    <label class="block text-sm font-medium mb-2">Дата по</label>
                    <Calendar 
                        v-model="dateTo" 
                        dateFormat="dd.mm.yy"
                        placeholder="дд.мм.гггг"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-4">
                    <label class="block text-sm font-medium mb-2">&nbsp;</label>
                    <div class="flex gap-2">
                        <Button 
                            label="Применить" 
                            icon="pi pi-search" 
                            @click="applyFilters"
                            size="small"
                        />
                        <Button 
                            label="Сбросить" 
                            icon="pi pi-times" 
                            outlined 
                            @click="clearFilters"
                            size="small"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Таблица операций -->
        <div class="card">
            <DataTable
                :value="operations"
                :loading="loading"
                :paginator="true"
                :rows="20"
                dataKey="id"
                :rowHover="true"
                showGridlines
                responsiveLayout="scroll"
                sortField="timestamp"
                :sortOrder="-1"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-history text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">История операций не найдена</p>
                    </div>
                </template>

                <Column field="timestamp" header="Дата и время" sortable style="min-width: 150px">
                    <template #body="{ data }">
                        {{ formatDate(data.timestamp) }}
                    </template>
                </Column>

                <Column field="type" header="Тип операции" sortable style="min-width: 120px">
                    <template #body="{ data }">
                        <Tag 
                            :value="getTypeLabel(data.type)" 
                            :severity="getTypeSeverity(data.type)" 
                        />
                    </template>
                </Column>

                <Column field="description" header="Описание" sortable></Column>

                <Column field="amount" header="Сумма" sortable style="min-width: 120px">
                    <template #body="{ data }">
                        {{ formatAmount(data.amount) }}
                    </template>
                </Column>

                <Column field="status" header="Статус" sortable style="min-width: 100px">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>

                <Column field="reference" header="Ссылка" style="min-width: 120px">
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.reference || '-' }}</span>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

