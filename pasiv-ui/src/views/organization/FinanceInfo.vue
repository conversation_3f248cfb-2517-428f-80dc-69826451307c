<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { FinanceService } from '@/service/FinanceService';

const route = useRoute();
const organizationId = route.params.organizationId;

const loading = ref(true);
const financeData = ref(null);
const selectedPeriod = ref('current_month');

const periodOptions = ref([
    { label: 'Текущий месяц', value: 'current_month' },
    { label: 'Предыдущий месяц', value: 'previous_month' },
    { label: 'Текущий квартал', value: 'current_quarter' },
    { label: 'Текущий год', value: 'current_year' }
]);

onMounted(() => {
    loadFinanceData();
});

const loadFinanceData = async () => {
    try {
        loading.value = true;
        const data = await FinanceService.getOrganizationFinanceInfo(organizationId, selectedPeriod.value);
        financeData.value = data;
    } catch (error) {
        console.error('Ошибка загрузки финансовой информации:', error);
    } finally {
        loading.value = false;
    }
};

const onPeriodChange = () => {
    loadFinanceData();
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0
    }).format(amount);
};

const formatPercent = (value) => {
    return `${value.toFixed(2)}%`;
};

const getChangeClass = (change) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-color-secondary';
};

const getChangeIcon = (change) => {
    if (change > 0) return 'pi pi-arrow-up';
    if (change < 0) return 'pi pi-arrow-down';
    return 'pi pi-minus';
};
</script>

<template>
    <div class="finance-info p-4">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-bold m-0">Финансовая информация</h2>
            <Dropdown 
                v-model="selectedPeriod" 
                :options="periodOptions" 
                optionLabel="label" 
                optionValue="value"
                @change="onPeriodChange"
                class="w-12rem"
            />
        </div>

        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка финансовых данных...</p>
            </div>
        </div>

        <div v-else-if="financeData" class="grid">
            <!-- Основные показатели -->
            <div class="col-12">
                <div class="card mb-4">
                    <h3 class="text-lg font-semibold mb-4">Основные показатели</h3>
                    <div class="grid">
                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-blue-50 border-blue-200 p-3 border-round">
                                <div class="flex align-items-center justify-content-between">
                                    <div>
                                        <div class="text-blue-600 font-medium text-sm">Общая выручка</div>
                                        <div class="text-2xl font-bold text-blue-700">{{ formatAmount(financeData.totalRevenue) }}</div>
                                        <div class="flex align-items-center mt-1">
                                            <i :class="getChangeIcon(financeData.revenueChange)" class="text-xs mr-1"></i>
                                            <span :class="getChangeClass(financeData.revenueChange)" class="text-xs">
                                                {{ formatPercent(Math.abs(financeData.revenueChange)) }}
                                            </span>
                                        </div>
                                    </div>
                                    <i class="pi pi-chart-line text-blue-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-green-50 border-green-200 p-3 border-round">
                                <div class="flex align-items-center justify-content-between">
                                    <div>
                                        <div class="text-green-600 font-medium text-sm">Комиссии</div>
                                        <div class="text-2xl font-bold text-green-700">{{ formatAmount(financeData.totalCommissions) }}</div>
                                        <div class="flex align-items-center mt-1">
                                            <i :class="getChangeIcon(financeData.commissionsChange)" class="text-xs mr-1"></i>
                                            <span :class="getChangeClass(financeData.commissionsChange)" class="text-xs">
                                                {{ formatPercent(Math.abs(financeData.commissionsChange)) }}
                                            </span>
                                        </div>
                                    </div>
                                    <i class="pi pi-percentage text-green-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-orange-50 border-orange-200 p-3 border-round">
                                <div class="flex align-items-center justify-content-between">
                                    <div>
                                        <div class="text-orange-600 font-medium text-sm">Транзакции</div>
                                        <div class="text-2xl font-bold text-orange-700">{{ financeData.totalTransactions.toLocaleString('ru-RU') }}</div>
                                        <div class="flex align-items-center mt-1">
                                            <i :class="getChangeIcon(financeData.transactionsChange)" class="text-xs mr-1"></i>
                                            <span :class="getChangeClass(financeData.transactionsChange)" class="text-xs">
                                                {{ formatPercent(Math.abs(financeData.transactionsChange)) }}
                                            </span>
                                        </div>
                                    </div>
                                    <i class="pi pi-list text-orange-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-3">
                            <div class="stat-card bg-purple-50 border-purple-200 p-3 border-round">
                                <div class="flex align-items-center justify-content-between">
                                    <div>
                                        <div class="text-purple-600 font-medium text-sm">Средний чек</div>
                                        <div class="text-2xl font-bold text-purple-700">{{ formatAmount(financeData.averageTicket) }}</div>
                                        <div class="flex align-items-center mt-1">
                                            <i :class="getChangeIcon(financeData.averageTicketChange)" class="text-xs mr-1"></i>
                                            <span :class="getChangeClass(financeData.averageTicketChange)" class="text-xs">
                                                {{ formatPercent(Math.abs(financeData.averageTicketChange)) }}
                                            </span>
                                        </div>
                                    </div>
                                    <i class="pi pi-calculator text-purple-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Распределение по способам оплаты -->
            <div class="col-12 md:col-6">
                <div class="card">
                    <h3 class="text-lg font-semibold mb-4">Распределение по способам оплаты</h3>
                    <div class="space-y-3">
                        <div v-for="method in financeData.paymentMethods" :key="method.name" class="flex align-items-center justify-content-between p-2 border-round bg-gray-50">
                            <div class="flex align-items-center">
                                <div class="w-1rem h-1rem border-round mr-2" :style="{ backgroundColor: method.color }"></div>
                                <span class="font-medium">{{ method.name }}</span>
                            </div>
                            <div class="text-right">
                                <div class="font-bold">{{ formatAmount(method.amount) }}</div>
                                <div class="text-sm text-color-secondary">{{ formatPercent(method.percentage) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Топ маршрутов -->
            <div class="col-12 md:col-6">
                <div class="card">
                    <h3 class="text-lg font-semibold mb-4">Топ маршрутов по выручке</h3>
                    <div class="space-y-2">
                        <div v-for="route in financeData.topRoutes" :key="route.id" class="flex align-items-center justify-content-between p-2 border-round bg-gray-50">
                            <div>
                                <div class="font-medium">{{ route.name }}</div>
                                <div class="text-sm text-color-secondary">{{ route.transactions }} транзакций</div>
                            </div>
                            <div class="text-right">
                                <div class="font-bold">{{ formatAmount(route.revenue) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

