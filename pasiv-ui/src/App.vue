<script setup lang="ts">
// Тестовый компонент для проверки стилей
</script>

<template>
    <div class="app-container">
        <router-view />
    </div>
</template>

<style scoped>
.app-container {
    min-height: 100vh;
    padding: 20px;
}

.style-test {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 20px;
}

.custom-style-test {
    background-color: #f3f4f6;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #10b981;
}

.custom-style-test p {
    margin: 0;
    color: #374151;
    font-weight: 500;
}
</style>
