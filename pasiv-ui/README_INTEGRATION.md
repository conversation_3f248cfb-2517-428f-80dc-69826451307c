# Интеграция PASIV UI с gRPC API

## Статус интеграции

✅ **ЗАВЕРШЕНО**: Интеграция фронтенда с бэкендом PASIV Gate Private через gRPC API
✅ **ЗАВЕРШЕНО**: Интеграция модуля договоров с gRPC API

## Что реализовано

### 1. gRPC клиент
- ✅ **PasivGatePrivateService.js** - основной сервис для работы с API
- ✅ **Автоматическая генерация** proto файлов
- ✅ **Маппинг данных** между proto и фронтенд моделями
- ✅ **Обработка ошибок** с детальной информацией

### 2. Обновленные сервисы
- ✅ **OrganizationService.js** - интеграция с gRPC API
- ✅ **Обратная совместимость** с существующими компонентами
- ✅ **Новые методы**: поиск, подсказки по ИНН

### 3. Компоненты Vue
- ✅ **OrganizationList.vue** - список с реальными данными
- ✅ **OrganizationForm.vue** - создание/редактирование через API
- ✅ **Toast уведомления** для обратной связи
- ✅ **Confirm диалоги** для подтверждения действий

### 4. Новые компоненты
- ✅ **LoadingState.vue** - универсальные состояния загрузки
- ✅ **OrganizationStats.vue** - статистика организаций
- ✅ **OrganizationFilters.vue** - расширенные фильтры
- ✅ **ContractStats.vue** - статистика договоров
- ✅ **ContractFilters.vue** - фильтры для договоров

### 5. Утилиты и константы
- ✅ **TypeScript типы** для организаций и договоров
- ✅ **Константы и валидация** полей
- ✅ **Утилиты уведомлений** (Toast)
- ✅ **Тестирование интеграции** организаций и договоров

## Быстрый старт

### 1. Настройка окружения

```bash
# Скопируйте файл конфигурации
cp .env.example .env.local

# Настройте URL бэкенда
echo "VUE_APP_PASIV_GATE_PRIVATE_URL=http://localhost:5005" >> .env.local
```

### 2. Генерация gRPC клиентов

```bash
# Генерация proto файлов
npm run generate-grpc
```

### 3. Запуск приложения

```bash
# Установка зависимостей
npm install

# Запуск в режиме разработки
npm run dev
```

## Тестирование интеграции

### Автоматическое тестирование

В консоли браузера выполните:

```javascript
// Быстрый тест подключения
await quickConnectionTest();

// Быстрый тест API договоров
await quickContractTest();

// Полное тестирование всех операций (включая договоры)
const tester = new PasivIntegrationTester();
await tester.runAllTests();

// Тест производительности
await performanceTest();
```

### Ручное тестирование

1. **Список организаций**: `/organizations`
   - Проверьте загрузку данных
   - Протестируйте фильтрацию и поиск
   - Проверьте удаление организации

2. **Создание организации**: `/organizations/create`
   - Заполните все обязательные поля
   - Проверьте валидацию
   - Убедитесь в успешном создании

3. **Редактирование**: `/organizations/:id/edit`
   - Загрузите существующую организацию
   - Измените данные и сохраните
   - Проверьте обновление в списке

4. **Список договоров**: `/finance/contract`
   - Проверьте загрузку данных
   - Протестируйте фильтрацию и поиск
   - Проверьте удаление договора

5. **Создание договора**: `/finance/contract/create`
   - Заполните все обязательные поля
   - Проверьте валидацию
   - Убедитесь в успешном создании

6. **Редактирование договора**: `/finance/contract/:id/edit`
   - Загрузите существующий договор
   - Измените данные и сохраните
   - Проверьте обновление в списке

## Структура файлов

```
src/
├── service/
│   ├── PasivGatePrivateService.js    # gRPC клиент для организаций
│   ├── ContractGatePrivateService.js # gRPC клиент для договоров
│   ├── OrganizationService.js        # Обновленный сервис организаций
│   └── ContractService.js            # Обновленный сервис договоров
├── components/
│   ├── LoadingState.vue              # Состояния загрузки
│   ├── OrganizationStats.vue         # Статистика организаций
│   ├── OrganizationFilters.vue       # Фильтры организаций
│   ├── ContractStats.vue             # Статистика договоров
│   └── ContractFilters.vue           # Фильтры договоров
├── views/
│   ├── OrganizationList.vue          # Список организаций (обновлен)
│   ├── OrganizationForm.vue          # Форма организаций (обновлена)
│   ├── ContractList.vue              # Список договоров (обновлен)
│   └── ContractForm.vue              # Форма договоров (обновлена)
├── types/
│   ├── organization.ts               # TypeScript типы организаций
│   └── contract.ts                   # TypeScript типы договоров
├── constants/
│   ├── organization.js               # Константы организаций
│   └── contract.js                   # Константы договоров
├── utils/
│   ├── notifications.js              # Утилиты уведомлений
│   ├── contractUtils.js              # Утилиты для договоров
│   └── testIntegration.js            # Тестирование API
└── generated/                        # Сгенерированные gRPC файлы
```

## API методы

### OrganizationService

```javascript
// Получение списка
const organizations = await OrganizationService.getOrganizations();

// Получение по ID
const org = await OrganizationService.getOrganizationById(id);

// Создание
const result = await OrganizationService.createOrganization(data);

// Обновление
const result = await OrganizationService.updateOrganization(id, data);

// Удаление
const result = await OrganizationService.deleteOrganization(id);

// Поиск
const results = await OrganizationService.searchOrganizations(filters);

// Подсказки по ИНН
const hints = await OrganizationService.getOrganizationHintByINN(inn);
```

### ContractService

```javascript
// Получение списка
const contracts = await ContractService.getContracts();

// Получение по ID
const contract = await ContractService.getContractById(id);

// Создание
const result = await ContractService.createContract(projectCode, data);

// Обновление
const result = await ContractService.updateContract(id, data);

// Удаление
const result = await ContractService.deleteContract(id);

// Поиск
const results = await ContractService.searchContracts(filters);

// Договоры по проекту
const contracts = await ContractService.getContractsByProject(projectCode);

// Договоры по организации
const contracts = await ContractService.getContractsByOrganization(orgId);
```

### PasivGatePrivateService

```javascript
// Прямое использование gRPC API для организаций
const result = await pasivGatePrivateService.getOrganizationList({
    pagination: { page: 1, size: 20 },
    filter: { name: 'ООО' }
});
```

### ContractGatePrivateService

```javascript
// Прямое использование gRPC API для договоров
const result = await contractGatePrivateService.getContractList({
    pagination: { page: 1, size: 20 },
    filter: { status: 'active' }
});
```

## Обработка ошибок

Все API методы возвращают объект с полем `success`:

```javascript
const result = await OrganizationService.createOrganization(data);

if (result.success) {
    // Успех
    console.log('Организация создана:', result.data);
} else {
    // Ошибка
    console.error('Ошибка:', result.error.message);
}
```

## Уведомления

Используйте Toast для информирования пользователя:

```javascript
import { useToast } from 'primevue/usetoast';

const toast = useToast();

// Успех
toast.add({
    severity: 'success',
    summary: 'Успех',
    detail: 'Операция выполнена успешно',
    life: 3000
});

// Ошибка
toast.add({
    severity: 'error',
    summary: 'Ошибка',
    detail: 'Произошла ошибка',
    life: 5000
});
```

## Отладка

### Логирование

Все gRPC вызовы логируются в консоль. Для детальной отладки:

```bash
VUE_APP_LOG_LEVEL=debug
```

### Проблемы и решения

1. **Ошибка подключения**
   - Проверьте URL в `.env.local`
   - Убедитесь, что бэкенд запущен
   - Проверьте CORS настройки

2. **Ошибки аутентификации**
   - Проверьте токен в localStorage
   - Убедитесь в корректности роли
   - Проверьте настройки Keycloak

3. **Ошибки валидации**
   - Проверьте обязательные поля
   - Убедитесь в корректности форматов
   - Проверьте маппинг данных

## Производительность

### Оптимизации

- ✅ Пагинация на сервере
- ✅ Дебаунсинг поиска
- ✅ Кэширование результатов (на уровне компонентов)
- ✅ Ленивая загрузка компонентов

### Мониторинг

- Время ответа API: отображается в консоли
- Количество ошибок: логируется
- Использование памяти: Vue DevTools

## Следующие шаги

1. **Добавить кэширование** на уровне приложения
2. **Реализовать офлайн поддержку**
3. **Добавить виртуализацию** для больших списков
4. **Интегрировать с системой мониторинга**

## Поддержка

При возникновении проблем:

1. Проверьте консоль браузера на ошибки
2. Запустите автоматические тесты
3. Изучите документацию в `docs/`
4. Обратитесь к команде разработки
