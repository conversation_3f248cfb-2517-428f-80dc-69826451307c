# Конфигурация для PASIV UI

# URL для подключения к PASIV Gate Private REST API
VITE_API_BASE_URL=http://localhost:8080/api/v1

# URL для подключения к PASIV Gate API (публичный) - пока не используется
VUE_APP_PASIV_GATE_URL=http://localhost:5004

# Режим разработки
NODE_ENV=development

# Базовый URL приложения
VUE_APP_BASE_URL=/

# Настройки аутентификации
VUE_APP_AUTH_ENABLED=true
VUE_APP_KEYCLOAK_URL=http://localhost:8080
VUE_APP_KEYCLOAK_REALM=tkp3
VUE_APP_KEYCLOAK_CLIENT_ID=pasiv-ui

# Настройки логирования
VUE_APP_LOG_LEVEL=debug

# Настройки REST API (заменяет gRPC)
VITE_API_TIMEOUT=30000
VITE_API_WITH_CREDENTIALS=true
VITE_ENABLE_API_LOGS=true
