# Руководство по миграции с мок-сервиса на gRPC API

## Обзор изменений

Этот документ описывает изменения, внесенные при переходе от мок-сервиса к реальному gRPC API для работы с организациями.

## Что изменилось

### 1. OrganizationService.js

**Было (мок-сервис):**
```javascript
export const OrganizationService = {
    getData: () => {
        return [/* статические данные */];
    },
    
    createOrganization(data) {
        return Promise.resolve(newOrganization);
    }
};
```

**Стало (интеграция с gRPC):**
```javascript
import pasivGatePrivateService from './PasivGatePrivateService';

export const OrganizationService = {
    async getData(params = {}) {
        const result = await pasivGatePrivateService.getOrganizationList(params);
        return result.success ? result.data.organizations : [];
    },
    
    async createOrganization(data) {
        const result = await pasivGatePrivateService.createOrganization(data);
        return {
            success: result.success,
            data: result.success ? data : null,
            error: result.error
        };
    }
};
```

### 2. Обработка результатов API

**Новый формат ответов:**
```javascript
{
    success: boolean,
    data?: any,
    message?: string,
    error?: {
        code: string,
        message: string,
        details?: any
    }
}
```

### 3. Компоненты Vue

#### OrganizationList.vue
- Добавлена обработка ошибок API
- Улучшена логика загрузки данных
- Добавлены проверки типов данных

#### OrganizationForm.vue
- Обновлен маппинг данных между формой и API
- Добавлена обработка результатов сохранения
- Улучшена валидация полей

## Новые файлы

### 1. PasivGatePrivateService.js
Основной сервис для работы с gRPC API:
- Методы CRUD для организаций
- Автоматический маппинг данных
- Обработка ошибок
- Аутентификация

### 2. src/types/organization.ts
TypeScript типы для организаций:
- Интерфейсы для всех сущностей
- Типы запросов и ответов
- Параметры API вызовов

### 3. src/constants/organization.js
Константы и утилиты:
- Статусы организаций
- Типы данных
- Функции валидации
- Опции для форм

### 4. src/utils/notifications.js
Утилиты для уведомлений:
- Обертка над PrimeVue Toast
- Методы для разных типов уведомлений
- Обработка результатов API

### 5. .env.example
Конфигурация окружения:
- URL API
- Настройки аутентификации
- Параметры gRPC

## Обратная совместимость

Все изменения сохраняют обратную совместимость:

1. **API OrganizationService** остался прежним
2. **Компоненты** работают без изменений
3. **Форматы данных** адаптированы автоматически

## Настройка окружения

### 1. Переменные окружения

Создайте файл `.env.local`:
```bash
VUE_APP_PASIV_GATE_PRIVATE_URL=http://localhost:5005
VUE_APP_AUTH_ENABLED=true
```

### 2. Генерация gRPC клиентов

```bash
npm run generate-grpc
```

### 3. Запуск приложения

```bash
npm run dev
```

## Тестирование

### Проверка интеграции

1. **Список организаций**: `/organizations`
   - Загрузка данных из API
   - Фильтрация и поиск
   - Пагинация

2. **Создание организации**: `/organizations/create`
   - Валидация полей
   - Отправка данных в API
   - Обработка ошибок

3. **Редактирование**: `/organizations/:id/edit`
   - Загрузка данных организации
   - Сохранение изменений
   - Маппинг данных

### Отладка

1. **Консоль браузера**: Логи API вызовов
2. **Network tab**: gRPC запросы
3. **Vue DevTools**: Состояние компонентов

## Возможные проблемы

### 1. Ошибки подключения

**Проблема**: `Failed to fetch` или `Network Error`

**Решение**:
- Проверьте URL в `.env.local`
- Убедитесь, что бэкенд запущен
- Проверьте CORS настройки

### 2. Ошибки аутентификации

**Проблема**: `401 Unauthorized`

**Решение**:
- Проверьте токен в localStorage
- Убедитесь в корректности роли пользователя
- Проверьте настройки Keycloak

### 3. Ошибки валидации

**Проблема**: `400 Bad Request`

**Решение**:
- Проверьте обязательные поля
- Убедитесь в корректности форматов данных
- Проверьте маппинг полей

## Откат к мок-сервису

Если нужно временно вернуться к мок-данным:

1. Создайте файл `OrganizationService.mock.js` с оригинальным кодом
2. Измените импорт в компонентах:
```javascript
// import { OrganizationService } from '@/service/OrganizationService';
import { OrganizationService } from '@/service/OrganizationService.mock';
```

## Дальнейшие улучшения

### 1. Кэширование
- Добавить кэширование запросов
- Реализовать инвалидацию кэша
- Оптимизировать повторные запросы

### 2. Оптимизация
- Пагинация на сервере
- Ленивая загрузка данных
- Виртуализация больших списков

### 3. UX улучшения
- Скелетоны загрузки
- Оптимистичные обновления
- Офлайн поддержка

### 4. Мониторинг
- Метрики производительности
- Отслеживание ошибок
- Аналитика использования

## Контакты

При возникновении вопросов или проблем:
- Проверьте логи в консоли браузера
- Изучите документацию в `docs/GRPC_INTEGRATION.md`
- Обратитесь к команде разработки
