# Интеграция с PASIV Gate Private API через gRPC

## Обзор

Этот документ описывает интеграцию фронтенда PASIV UI с бэкендом PASIV Gate Private через gRPC API для работы с организациями.

## Архитектура

```
┌─────────────────┐    gRPC     ┌──────────────────────┐
│   PASIV UI      │◄──────────►│ PASIV Gate Private   │
│   (Vue 3)       │             │   (Spring Boot)      │
└─────────────────┘             └──────────────────────┘
```

## Компоненты интеграции

### 1. PasivGatePrivateService.js

Основной сервис для работы с gRPC API:

```javascript
import pasivGatePrivateService from '@/service/PasivGatePrivateService';

// Получение списка организаций
const result = await pasivGatePrivateService.getOrganizationList({
    pagination: { page: 1, size: 20 },
    filter: { name: 'ООО' }
});

// Создание организации
const createResult = await pasivGatePrivateService.createOrganization({
    name: 'ООО Тест',
    shortName: 'Тест',
    inn: '1234567890',
    kpp: '123456789',
    ogrn: '1234567890123',
    legalAddress: 'г. Москва, ул. Тестовая, д. 1'
});
```

### 2. Методы API

#### Основные операции с организациями:

- `getOrganizationList(params)` - получение списка организаций
- `getOrganizationById(id)` - получение организации по ID
- `createOrganization(data)` - создание новой организации
- `updateOrganization(data)` - обновление организации
- `deleteOrganization(id)` - удаление организации (мягкое)
- `getOrganizationHintByINN(inn, kpp?)` - получение подсказок по ИНН

### 3. Типы данных

Все типы данных определены в `src/types/organization.ts`:

```typescript
interface Organization {
    id?: string;
    name: string;
    shortName: string;
    inn: string;
    kpp: string;
    ogrn: string;
    // ... другие поля
}
```

## Конфигурация

### Переменные окружения

Создайте файл `.env.local` на основе `.env.example`:

```bash
# URL бэкенда
VUE_APP_PASIV_GATE_PRIVATE_URL=http://localhost:5005

# Настройки аутентификации
VUE_APP_AUTH_ENABLED=true
```

### Настройка gRPC клиента

Клиент автоматически настраивается при создании экземпляра сервиса. Основные параметры:

- **URL**: Берется из переменной окружения `VUE_APP_PASIV_GATE_PRIVATE_URL`
- **Аутентификация**: Токен берется из localStorage или sessionStorage
- **Таймауты**: Настраиваются через опции gRPC

## Генерация proto файлов

Для обновления gRPC клиента выполните:

```bash
npm run generate-grpc
```

Этот скрипт:
1. Извлекает proto файлы из зависимостей бэкенда
2. Копирует их в директорию `proto/`
3. Генерирует JavaScript и TypeScript файлы в `src/generated/`

## Обработка ошибок

Все методы возвращают объект с полем `success`:

```javascript
const result = await pasivGatePrivateService.getOrganizationList();

if (result.success) {
    console.log('Данные:', result.data);
} else {
    console.error('Ошибка:', result.error.message);
}
```

### Типы ошибок:

- **Сетевые ошибки**: Проблемы с подключением к серверу
- **Ошибки аутентификации**: Неверный или отсутствующий токен
- **Ошибки валидации**: Некорректные данные в запросе
- **Бизнес-логика**: Ошибки на уровне бизнес-правил

## Маппинг данных

Сервис автоматически преобразует данные между форматами:

### Proto → Frontend
```javascript
mapOrganizationFromProto(protoOrg) {
    return {
        id: protoOrg.getId(),
        name: protoOrg.getName(),
        shortName: protoOrg.getShortname(),
        // ... остальные поля
    };
}
```

### Frontend → Proto
```javascript
mapOrganizationToProto(orgData) {
    const organization = new Organization();
    organization.setName(orgData.name);
    organization.setShortname(orgData.shortName);
    // ... остальные поля
    return organization;
}
```

## Использование в компонентах

### Пример в OrganizationList.vue:

```vue
<script setup>
import pasivGatePrivateService from '@/service/PasivGatePrivateService';
import { ref, onMounted } from 'vue';

const organizations = ref([]);
const loading = ref(false);

const loadOrganizations = async () => {
    loading.value = true;
    try {
        const result = await pasivGatePrivateService.getOrganizationList({
            pagination: { page: 1, size: 20 }
        });
        
        if (result.success) {
            organizations.value = result.data.organizations;
        } else {
            console.error('Ошибка загрузки:', result.error.message);
        }
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    loadOrganizations();
});
</script>
```

## Отладка

### Логирование

Все gRPC вызовы логируются в консоль браузера. Для детальной отладки установите:

```bash
VUE_APP_LOG_LEVEL=debug
```

### Инструменты разработчика

1. **Network tab**: Просмотр gRPC запросов
2. **Console**: Логи ошибок и отладочная информация
3. **Vue DevTools**: Состояние компонентов

## Тестирование

### Юнит-тесты

```javascript
import pasivGatePrivateService from '@/service/PasivGatePrivateService';

describe('PasivGatePrivateService', () => {
    test('should get organization list', async () => {
        const result = await pasivGatePrivateService.getOrganizationList();
        expect(result.success).toBe(true);
        expect(result.data.organizations).toBeInstanceOf(Array);
    });
});
```

### E2E тесты

```javascript
test('organization CRUD operations', async ({ page }) => {
    // Создание организации
    await page.goto('/organizations/create');
    await page.fill('[data-testid="name"]', 'Тестовая организация');
    await page.click('[data-testid="save"]');
    
    // Проверка создания
    await expect(page.locator('.success-message')).toBeVisible();
});
```

## Производительность

### Оптимизации:

1. **Пагинация**: Всегда используйте пагинацию для больших списков
2. **Кэширование**: Кэшируйте результаты на уровне компонентов
3. **Дебаунсинг**: Используйте дебаунсинг для поиска и фильтрации

### Мониторинг:

- Время ответа gRPC вызовов
- Количество ошибок
- Использование памяти

## Безопасность

### Аутентификация

Все запросы требуют JWT токен в заголовке `Authorization`:

```javascript
getAuthMetadata() {
    const token = localStorage.getItem('auth_token');
    return {
        'authorization': `Bearer ${token}`
    };
}
```

### Авторизация

Доступ к API требует роль `ROLE_pasiv_console_admin`.

## Миграция с мок-сервиса

Для перехода с мок-сервиса на реальный API:

1. Замените импорт в компонентах:
```javascript
// Было
import { OrganizationService } from '@/service/OrganizationService';

// Стало
import pasivGatePrivateService from '@/service/PasivGatePrivateService';
```

2. Обновите вызовы методов согласно новому API
3. Добавьте обработку ошибок
4. Протестируйте функциональность
