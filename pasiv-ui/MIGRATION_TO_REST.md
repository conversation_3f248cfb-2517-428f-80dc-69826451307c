# Миграция с gRPC на REST API

## Обзор

Этот документ описывает миграцию фронтенда PASIV UI с gRPC API на REST API для работы с PASIV Gate Private.

## Что изменилось

### 🔄 Архитектура

**Было (gRPC):**
```
┌─────────────────┐    gRPC     ┌──────────────────────┐
│   PASIV UI      │◄──────────►│ PASIV Gate Private   │
│   (Vue 3)       │             │   (Spring Boot)      │
└─────────────────┘             └──────────────────────┘
```

**Стало (REST):**
```
┌─────────────────┐    HTTP     ┌──────────────────────┐
│   PASIV UI      │◄──────────►│ PASIV Gate Private   │
│   (Vue 3)       │   REST API  │   (Spring Boot)      │
└─────────────────┘             └──────────────────────┘
```

### 📁 Новые файлы

1. **`src/service/HttpClient.ts`** - Базовый HTTP клиент
2. **`src/service/RestApiClient.ts`** - REST API клиент с маппингом
3. **`src/service/PasivGatePrivateServiceRest.ts`** - Новая реализация сервиса
4. **`src/types/rest-api.ts`** - Типы для REST API

### 🗑️ Удаленные зависимости

- `google-protobuf`
- `grpc-web`
- `grpc-tools`
- `protoc-gen-grpc-web`
- `grpc_tools_node_protoc_ts`

### ⚙️ Обновленная конфигурация

**package.json:**
- Удалены gRPC зависимости
- Отключены скрипты генерации proto файлов

**.env.example:**
- `VITE_API_BASE_URL` - URL для REST API (заменяет gRPC URL)
- `VITE_API_TIMEOUT` - Таймаут для HTTP запросов
- `VITE_API_WITH_CREDENTIALS` - Включение CORS credentials

## Использование

### Базовый HTTP клиент

```typescript
import { defaultHttpClient } from '@/service/HttpClient';

// GET запрос
const response = await defaultHttpClient.get('/organizations');

// POST запрос
const response = await defaultHttpClient.post('/organizations', data);
```

### REST API клиент

```typescript
import { restApiClient } from '@/service/RestApiClient';

// Получение списка организаций
const organizations = await restApiClient.getOrganizations({
    page: 0,
    size: 20,
    name: 'ООО'
});

// Создание организации
const result = await restApiClient.createOrganization({
    organization: organizationData,
    addressLegal: addressData
});
```

### Обновленный сервис

```typescript
import pasivGatePrivateService from '@/service/PasivGatePrivateServiceRest';

// API остается тем же для совместимости
const result = await pasivGatePrivateService.organizationList({
    pagination: { page: 0, size: 20 },
    filter: { name: 'ООО' }
});
```

## Маппинг данных

### Организации

**gRPC → REST:**
- `Organization` → `OrganizationDto`
- Автоматическое преобразование через `ApiMapper`

**Frontend совместимость:**
- `FrontendOrganization` остается без изменений
- Добавлены утилиты маппинга для совместимости

### Адреса

**gRPC → REST:**
- `Address` → `AddressDto`
- Поддержка типов адресов: `LEGAL`, `ACTUAL`, `MAILING`

### Контакты

**gRPC → REST:**
- `Contact` → `ContactDto`
- Поддержка типов: `PHONE`, `EMAIL`

## API Endpoints

### Организации

| Метод | Endpoint | Описание |
|-------|----------|----------|
| `POST` | `/api/v1/organizations` | Создание организации |
| `PUT` | `/api/v1/organizations/{id}` | Обновление организации |
| `GET` | `/api/v1/organizations` | Список организаций |
| `GET` | `/api/v1/organizations/{id}` | Получение по ID |
| `DELETE` | `/api/v1/organizations/{id}` | Удаление |
| `POST` | `/api/v1/organizations/{id}/recover` | Восстановление |

### Адреса

| Метод | Endpoint | Описание |
|-------|----------|----------|
| `POST` | `/api/v1/addresses` | Создание адреса |
| `PUT` | `/api/v1/addresses/{id}` | Обновление адреса |
| `GET` | `/api/v1/addresses` | Список адресов |
| `DELETE` | `/api/v1/addresses/{id}` | Удаление адреса |

### Контакты

| Метод | Endpoint | Описание |
|-------|----------|----------|
| `POST` | `/api/v1/contacts` | Создание контакта |
| `PUT` | `/api/v1/contacts/{id}` | Обновление контакта |
| `GET` | `/api/v1/contacts` | Список контактов |
| `DELETE` | `/api/v1/contacts/{id}` | Удаление контакта |

### DaData

| Метод | Endpoint | Описание |
|-------|----------|----------|
| `GET` | `/api/v1/dadata/organizations/hint` | Подсказка по ИНН |
| `POST` | `/api/v1/dadata/organizations/hints` | Пакетные подсказки |

## Обработка ошибок

### HTTP статусы

- `200` - Успешный запрос
- `201` - Ресурс создан
- `400` - Некорректный запрос
- `401` - Не авторизован
- `403` - Недостаточно прав
- `404` - Ресурс не найден
- `500` - Внутренняя ошибка сервера

### Формат ошибок

```typescript
interface ApiError {
    code: string;
    message: string;
    details?: string;
    status?: number;
}
```

## Совместимость

### Существующие компоненты

Все существующие Vue компоненты продолжают работать без изменений благодаря:

1. **Сохранению интерфейсов** - `PasivGatePrivateServiceMethods`
2. **Маппингу типов** - `ApiMapper` для конвертации данных
3. **Обратной совместимости** - `FrontendOrganization` остается неизменным

### Миграция компонентов

Для компонентов, использующих gRPC напрямую:

```typescript
// Было
import pasivGatePrivateService from '@/service/PasivGatePrivateService';

// Стало
import pasivGatePrivateService from '@/service/PasivGatePrivateServiceRest';
```

## Настройка окружения

### Разработка

```bash
# .env.local
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_API_TIMEOUT=30000
VITE_ENABLE_API_LOGS=true
```

### Продакшен

```bash
# .env.production
VITE_API_BASE_URL=/api/v1
VITE_API_TIMEOUT=30000
VITE_ENABLE_API_LOGS=false
```

## Тестирование

### Unit тесты

```bash
npm run test:unit
```

Тесты покрывают:
- HTTP клиент
- REST API клиент
- Маппинг данных
- Обработку ошибок

### E2E тесты

```bash
npm run test:e2e
```

## Производительность

### Преимущества REST API

1. **Простота отладки** - стандартные HTTP инструменты
2. **Кэширование** - поддержка HTTP кэширования
3. **Совместимость** - работает с любыми HTTP клиентами
4. **Размер бандла** - меньше зависимостей

### Мониторинг

- Логирование запросов в режиме разработки
- Метрики производительности через Network tab
- Обработка таймаутов и повторных попыток

## Troubleshooting

### Частые проблемы

1. **CORS ошибки**
   ```
   Решение: Настроить CORS на backend или использовать прокси
   ```

2. **Таймауты**
   ```
   Решение: Увеличить VITE_API_TIMEOUT
   ```

3. **Ошибки аутентификации**
   ```
   Решение: Проверить настройки VITE_API_WITH_CREDENTIALS
   ```

### Отладка

```typescript
// Включить детальное логирование
const httpClient = createHttpClient({
    baseURL: 'http://localhost:8080/api/v1',
    enableLogs: true
});
```

## Дальнейшие шаги

1. ✅ Базовая миграция завершена
2. 🔄 Тестирование всех компонентов
3. 📝 Обновление документации
4. 🚀 Деплой в тестовую среду
