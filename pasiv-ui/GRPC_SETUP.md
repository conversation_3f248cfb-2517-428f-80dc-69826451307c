# gRPC Client Setup для PASIV UI

Этот документ описывает настройку автоматической генерации gRPC клиента для работы с pasiv-gate-private API.

## Обзор

При запуске задачи `:pasiv-ui:runDev` автоматически выполняются следующие шаги:

1. **Извлечение proto файлов** из `pasiv-api-private` и зависимостей
2. **Копирование proto файлов** в директорию `proto/`
3. **Генерация TypeScript/JavaScript клиента** из proto файлов
4. **Запуск development сервера** с готовым gRPC клиентом

## Gradle задачи

### `:pasiv-ui:runDev`

Основная задача для разработки. Выполняет полный цикл подготовки и запуска:

```bash
../gradlew runDev
```

### `:pasiv-ui:copyProtoFiles`

Копирует proto файлы из зависимых модулей:

```bash
../gradlew copyProtoFiles
```

### `:pasiv-ui:generateGrpcClient`

Генерирует gRPC клиент из proto файлов:

```bash
../gradlew generateGrpcClient
```

### `:pasiv-ui:cleanProto`

Очищает сгенерированные файлы:

```bash
../gradlew cleanProto
```

### `:pasiv-api-private:extractProtoFromDeps`

Извлекает proto файлы из зависимостей (common-api, common-manifest):

```bash
../gradlew :pasiv-api-private:extractProtoFromDeps
```

## Структура файлов

```
pasiv-ui/
├── proto/                    # Скопированные proto файлы (генерируется)
│   ├── pasiv-gate-private.proto
│   ├── pasiv-gate.proto
│   ├── common.proto
│   ├── common-manifest.proto
│   ├── common-manifest-pasiv.proto
│   └── ...
├── src/
│   ├── generated/           # Сгенерированный gRPC клиент (генерируется)
│   │   ├── *.js
│   │   ├── *.d.ts
│   │   └── *_grpc_pb.js
│   └── service/
│       └── PasivGatePrivateService.js  # Обертка для gRPC клиента
└── package.json
```

## Использование в коде

После генерации клиента можно использовать сервис:

```javascript
import { PASIVGatePrivateServiceClient } from '../generated/pasiv-gate-private_grpc_pb';
import { OrganizationListRequest } from '../generated/pasiv-gate-private_pb';

const client = new PASIVGatePrivateServiceClient('http://localhost:5005');

const request = new OrganizationListRequest();
client.organizationList(request, {}, (err, response) => {
    if (err) {
        console.error('Error:', err);
    } else {
        console.log('Organizations:', response.toObject());
    }
});
```

## NPM скрипты

Доступны следующие NPM скрипты для работы с proto файлами:

- `npm run generate-grpc` - **Полная генерация** (извлечение зависимостей + копирование + генерация)
- `npm run generate-proto` - Генерация JavaScript и TypeScript кода из существующих proto файлов
- `npm run generate-proto-js` - Генерация только JavaScript кода
- `npm run generate-proto-ts` - Генерация только TypeScript кода

### Рекомендуемый workflow

Для полной генерации gRPC клиента используйте:

```bash
npm run generate-grpc
```

Эта команда автоматически:

1. Извлекает proto файлы из зависимостей
2. Копирует все необходимые proto файлы
3. Генерирует TypeScript/JavaScript клиент

## Конфигурация

### Пути к proto файлам

Настроены в `build.gradle.kts`:

- `../pasiv-api-private/src/main/proto` - Основные proto файлы
- `../pasiv-api-private/build/extracted-include-protos/main` - Извлеченные из зависимостей
- `../pasiv-api/src/main/proto` - API proto файлы

### gRPC-Web настройки

- **Режим**: `grpcwebtext` - совместимость с большинством прокси
- **Стиль импорта**: `typescript` - для лучшей типизации
- **Выходная директория**: `./src/generated`

## Разработка

1. **Запуск development сервера**:

    ```bash
    ../gradlew runDev
    ```

2. **Обновление gRPC клиента** (рекомендуемый способ):

    ```bash
    npm run generate-grpc
    ```

3. **Альтернативные способы обновления proto файлов**:

    ```bash
    # Через Gradle (если pasiv-ui включен в build)
    ../gradlew copyProtoFiles generateGrpcClient

    # Только генерация из существующих proto файлов
    npm run generate-proto
    ```

4. **Очистка сгенерированных файлов**:

    ```bash
    rm -rf proto/* src/generated/*
    # или через Gradle
    ../gradlew cleanProto
    ```

## Зависимости

Проект использует следующие proto зависимости:

- `ru.sbertroika.common:common-api:1.0.1` - Общие типы и сообщения
- `ru.sbertroika.common:common-manifest:1.0.1` - Манифест типы

## Troubleshooting

### Ошибка "File not found" для proto файлов

Убедитесь, что задача `extractProtoFromDeps` была выполнена:

```bash
../gradlew :pasiv-api-private:extractProtoFromDeps
```

### Ошибки компиляции TypeScript

Проверьте, что все proto файлы скопированы:

```bash
ls -la proto/
```

### Проблемы с зависимостями

Очистите и пересоберите:

```bash
../gradlew clean :pasiv-api-private:extractProtoFromDeps copyProtoFiles generateGrpcClient
```
