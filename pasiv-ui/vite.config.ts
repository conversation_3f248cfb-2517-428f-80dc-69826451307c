import { fileURLToPath, URL } from 'node:url';

import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';
import commonjs from '@rollup/plugin-commonjs';

// https://vitejs.dev/config/
export default defineConfig({
    define: {
        global: 'globalThis',
        // Определяем require для браузерной среды
        'process.env': {},
    },
    optimizeDeps: {
        noDiscovery: true,
        include: [
            'grpc-web',
            'google-protobuf'
        ]
    },
    // Настройка для обработки CommonJS модулей
    build: {
        commonjsOptions: {
            include: [/node_modules/],
            exclude: [/src\/generated/], // Исключаем сгенерированные gRPC файлы
            transformMixedEsModules: true
        }
    },
    plugins: [
        vue(),
        Components({
            // resolvers: [PrimeVueResolver()]
        }),
        commonjs({
            include: [/node_modules/],
            exclude: [/src\/generated/], // Исключаем сгенерированные gRPC файлы
            transformMixedEsModules: true
        })
    ],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        }
    }
});
