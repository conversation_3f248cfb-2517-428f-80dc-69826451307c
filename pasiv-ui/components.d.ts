/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BestSellingWidget: typeof import('./src/components/dashboard/BestSellingWidget.vue')['default']
    Button: typeof import('primevue/button')['default']
    Calendar: typeof import('primevue/calendar')['default']
    Column: typeof import('primevue/column')['default']
    ConfirmDialog: typeof import('primevue/confirmdialog')['default']
    ContractFilters: typeof import('./src/components/ContractFilters.vue')['default']
    ContractStats: typeof import('./src/components/ContractStats.vue')['default']
    DataTable: typeof import('primevue/datatable')['default']
    Dropdown: typeof import('primevue/dropdown')['default']
    FeaturesWidget: typeof import('./src/components/landing/FeaturesWidget.vue')['default']
    FooterWidget: typeof import('./src/components/landing/FooterWidget.vue')['default']
    HeroWidget: typeof import('./src/components/landing/HeroWidget.vue')['default']
    HighlightsWidget: typeof import('./src/components/landing/HighlightsWidget.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputText: typeof import('primevue/inputtext')['default']
    LoadingState: typeof import('./src/components/LoadingState.vue')['default']
    NotificationsWidget: typeof import('./src/components/dashboard/NotificationsWidget.vue')['default']
    OrganizationFilters: typeof import('./src/components/OrganizationFilters.vue')['default']
    OrganizationStats: typeof import('./src/components/OrganizationStats.vue')['default']
    PasivLayout: typeof import('./src/components/PasivLayout.vue')['default']
    PaymentMethodFilters: typeof import('./src/components/finance/PaymentMethodFilters.vue')['default']
    PaymentMethodStats: typeof import('./src/components/finance/PaymentMethodStats.vue')['default']
    PricingWidget: typeof import('./src/components/landing/PricingWidget.vue')['default']
    RecentSalesWidget: typeof import('./src/components/dashboard/RecentSalesWidget.vue')['default']
    RevenueStreamWidget: typeof import('./src/components/dashboard/RevenueStreamWidget.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatsWidget: typeof import('./src/components/dashboard/StatsWidget.vue')['default']
    Tag: typeof import('primevue/tag')['default']
    Toast: typeof import('primevue/toast')['default']
    TopbarWidget: typeof import('./src/components/landing/TopbarWidget.vue')['default']
    TypeScriptExample: typeof import('./src/components/TypeScriptExample.vue')['default']
  }
  export interface ComponentCustomProperties {
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
