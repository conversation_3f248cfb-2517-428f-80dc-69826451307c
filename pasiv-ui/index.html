<!DOCTYPE html>
<html lang="en">

<head>
      <meta charset="UTF-8">
      <link rel="icon" href="/favicon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title> СберТройка PRO CRM</title>
      <link href="https://fonts.cdnfonts.com/css/lato" rel="stylesheet">

      <!-- gRPC Polyfill - КРИТИЧЕСКИ ВАЖНО: должен быть загружен ДО всех других скриптов -->
      <script>
        // Создаем глобальный объект grpc немедленно
        window.grpc = {
          web: {
            MethodType: {
              UNARY: 'unary',
              SERVER_STREAMING: 'server_streaming',
              CLIENT_STREAMING: 'client_streaming',
              BIDI_STREAMING: 'bidi_streaming'
            },
            GrpcWebClientBase: class GrpcWebClientBase {
              constructor(hostname, credentials, options) {
                console.log('GrpcWebClientBase created:', hostname);
              }
              rpcCall(method, request, metadata, callback) {
                console.warn('gRPC call not implemented:', method);
                callback(new Error('gRPC not implemented'), null);
              }
            },
            AbstractClientBase: class AbstractClientBase {
              constructor(hostname, credentials, options) {
                console.log('AbstractClientBase created:', hostname);
              }
            },
            MethodDescriptor: class MethodDescriptor {
              constructor(name, methodType, requestType, responseType, requestSerializeFn, responseDeserializeFn) {
                this.name = name;
                this.methodType = methodType;
                this.requestType = requestType;
                this.responseType = responseType;
                this.requestSerializeFn = requestSerializeFn;
                this.responseDeserializeFn = responseDeserializeFn;
                console.log('MethodDescriptor created:', name);
              }
            },
            StatusCode: {
              OK: 0, CANCELLED: 1, UNKNOWN: 2, INVALID_ARGUMENT: 3,
              DEADLINE_EXCEEDED: 4, NOT_FOUND: 5, ALREADY_EXISTS: 6,
              PERMISSION_DENIED: 7, UNAUTHENTICATED: 16, RESOURCE_EXHAUSTED: 8,
              FAILED_PRECONDITION: 9, ABORTED: 10, OUT_OF_RANGE: 11,
              UNIMPLEMENTED: 12, INTERNAL: 13, UNAVAILABLE: 14, DATA_LOSS: 15
            },
            RpcError: class RpcError extends Error {
              constructor(code, message, metadata) {
                super(message);
                this.code = code;
                this.metadata = metadata;
              }
            },
            ClientReadableStream: class ClientReadableStream {
              constructor() {
                this._container = {};
                console.log('ClientReadableStream created');
              }
              on(event, callback) { return this; }
              cancel() {}
            },
            ClientWritableStream: class ClientWritableStream {
              constructor() {
                this._container = {};
                console.log('ClientWritableStream created');
              }
              write(message) {}
              end() {}
              cancel() {}
            },
            BidirectionalStream: class BidirectionalStream {
              constructor() {
                this._container = {};
                console.log('BidirectionalStream created');
              }
              write(message) {}
              end() {}
              cancel() {}
              on(event, callback) { return this; }
            }
          }
        };

        // Создаем require функцию с полной поддержкой всех модулей
        window.require = function(moduleName) {
          console.log('🔍 require called for:', moduleName);

          // Нормализуем имя модуля
          const normalizedName = moduleName.replace(/^\.\//, '').replace(/\.js$/, '');

          switch (moduleName) {
            case 'grpc-web':
              // КРИТИЧЕСКИ ВАЖНО: возвращаем только grpc.web, а не весь grpc объект
              // Потому что в сгенерированном коде: grpc.web = require('grpc-web');
              console.log('🔍 Returning grpc.web for grpc-web require');
              if (window.grpc && window.grpc.web) {
                return window.grpc.web;
              } else {
                console.error('❌ window.grpc.web is not available!');
                return {};
              }

            case 'google-protobuf':
              // Создаем полный объект jspb для совместимости
              const jspbObject = {
                Message: class Message {
                  constructor() {
                    this.array = [];
                    this.wrappers_ = {};
                    this.messageId_ = undefined;
                    this.arrayIndexOffset_ = -1;
                    this.pivot_ = Number.MAX_VALUE;
                    this.convertedPrimitiveFields_ = {};
                    this.extensionObject_ = {};
                  }
                  static deserializeBinary(bytes) { return new this(); }
                  serializeBinary() { return new Uint8Array(); }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getJsPbMessageId() { return this.messageId_; }
                  setJsPbMessageId(id) { this.messageId_ = id; }
                  clearJsPbMessageId() { this.messageId_ = undefined; }
                  getField(number) { return this.array[number]; }
                  setField(number, value) { this.array[number] = value; }
                  hasField(number) { return this.array[number] != null; }
                  clearField(number) { delete this.array[number]; }
                },
                BinaryReader: class BinaryReader {
                  constructor(bytes) {
                    this.bytes_ = bytes || new Uint8Array();
                    this.cursor_ = 0;
                  }
                  readMessage(msg, reader) {}
                  readString() { return ''; }
                  readInt32() { return 0; }
                  readInt64() { return 0; }
                  readUint32() { return 0; }
                  readUint64() { return 0; }
                  readBool() { return false; }
                  readFloat() { return 0; }
                  readDouble() { return 0; }
                },
                BinaryWriter: class BinaryWriter {
                  constructor() {
                    this.blocks_ = [];
                    this.totalLength_ = 0;
                  }
                  writeMessage(field, value, writer) {}
                  writeString(field, value) {}
                  writeInt32(field, value) {}
                  writeInt64(field, value) {}
                  writeUint32(field, value) {}
                  writeUint64(field, value) {}
                  writeBool(field, value) {}
                  writeFloat(field, value) {}
                  writeDouble(field, value) {}
                  getResultBuffer() { return new Uint8Array(); }
                }
              };

              // КРИТИЧЕСКИ ВАЖНО: jspb должен содержать object.extend
              // Потому что в protobuf файлах: var goog = jspb; goog.object.extend(...)
              jspbObject.object = {
                extend: function(target, source) {
                  console.log('🔧 jspb.object.extend called:', target, source);
                  for (let key in source) {
                    if (source.hasOwnProperty(key)) {
                      target[key] = source[key];
                    }
                  }
                }
              };

              jspbObject.exportSymbol = function(name, value, context) {
                console.log('📤 jspb.exportSymbol called:', name);
                // Заглушка для экспорта символов
              };

              // КРИТИЧЕСКИ ВАЖНО: добавляем goog.inherits
              jspbObject.inherits = function(childCtor, parentCtor) {
                console.log('🔧 jspb.inherits called:', childCtor.name, parentCtor.name);
                // Реализация наследования
                function tempCtor() {}
                tempCtor.prototype = parentCtor.prototype;
                childCtor.superClass_ = parentCtor.prototype;
                childCtor.prototype = new tempCtor();
                childCtor.prototype.constructor = childCtor;
              };

              // Добавляем goog.DEBUG
              jspbObject.DEBUG = false; // Отключаем debug режим

              // Создаем глобальный jspb если его нет
              if (typeof window.jspb === 'undefined') {
                window.jspb = jspbObject;
                console.log('✅ Created global jspb object');
              }

              // КРИТИЧЕСКИ ВАЖНО: goog должен быть равен jspb
              // Потому что в protobuf файлах: var goog = jspb;
              // Принудительно создаем goog объект
              window.goog = window.jspb;
              globalThis.goog = window.jspb;
              console.log('✅ Created global goog object (equal to jspb)');

              return jspbObject;

            case 'google-protobuf/google/protobuf/empty_pb.js':
            case 'google-protobuf/google/protobuf/empty_pb':
              return {
                Empty: class Empty {
                  static deserializeBinary() { return new Empty(); }
                  serializeBinary() { return new Uint8Array(); }
                }
              };

            case 'google-protobuf/google/protobuf/timestamp_pb.js':
            case 'google-protobuf/google/protobuf/timestamp_pb':
              return {
                Timestamp: class Timestamp {
                  static deserializeBinary() { return new Timestamp(); }
                  serializeBinary() { return new Uint8Array(); }
                  toDate() { return new Date(); }
                  fromDate(date) { return this; }
                  getSeconds() { return Math.floor(Date.now() / 1000); }
                }
              };

            case './common_pb.js':
            case 'common_pb.js':
            case 'common_pb':
              return {
                PaginationRequest: class PaginationRequest {
                  setPage(page) { return this; }
                  getPage() { return 1; }
                  setSize(size) { return this; }
                  getSize() { return 10; }
                  setLimit(limit) { return this; }
                  getLimit() { return 10; }
                  setTotal(total) { return this; }
                  getTotal() { return 0; }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary() { return new this(); }
                },
                PaginationResponse: class PaginationResponse {
                  getPage() { return 1; }
                  getSize() { return 10; }
                  getTotal() { return 0; }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary() { return new this(); }
                },
                EmptyResponse: class EmptyResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 EmptyResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                },
                HistoryResponse: class HistoryResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 HistoryResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getHistoryList() { return []; }
                  setHistoryList(list) { return this; }
                },

                PaginationRequest: class PaginationRequest {
                  constructor() {
                    this._page = 1;
                    this._limit = 10;
                  }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 PaginationRequest.deserializeBinary called');
                    return new this();
                  }
                  toObject() {
                    return {
                      page: this._page,
                      limit: this._limit
                    };
                  }
                  static toObject() { return {}; }

                  // Методы для пагинации
                  setPage(page) {
                    this._page = page;
                    console.log('🔧 PaginationRequest.setPage called:', page);
                  }
                  getPage() { return this._page; }
                  setLimit(limit) {
                    this._limit = limit;
                    console.log('🔧 PaginationRequest.setLimit called:', limit);
                  }
                  getLimit() { return this._limit; }
                  setTotalPage(totalPage) { this._totalPage = totalPage; }
                  getTotalPage() { return this._totalPage || 1; }
                  setTotalCount(totalCount) { this._totalCount = totalCount; }
                  getTotalCount() { return this._totalCount || 0; }
                }
              };

            case './common-manifest-core_pb.js':
            case 'common-manifest-core_pb.js':
            case 'common-manifest-core_pb':
              return {
                // Базовые классы для манифестов
                ManifestInfo: class ManifestInfo {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) { return new this(); }
                  toObject() { return {}; }
                }
              };

            case './common-manifest_pb.js':
            case 'common-manifest_pb.js':
            case 'common-manifest_pb':
              return {
                // Классы для общих манифестов
                CommonManifest: class CommonManifest {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) { return new this(); }
                  toObject() { return {}; }
                }
              };

            case './pasiv-gate_pb.js':
            case 'pasiv-gate_pb.js':
            case 'pasiv-gate_pb':
              return {
                // Классы для pasiv-gate
                PasivGateInfo: class PasivGateInfo {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) { return new this(); }
                  toObject() { return {}; }
                }
              };

            case './pasiv-gate-private_pb.js':
            case 'pasiv-gate-private_pb.js':
            case 'pasiv-gate-private_pb':
              // Создаем объект с классами
              const pasivPbClasses = {
                Organization: class Organization {
                  getId() { return ''; }
                  setId(id) { return this; }
                  getName() { return ''; }
                  setName(name) { return this; }
                  getInn() { return ''; }
                  setInn(inn) { return this; }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary() { return new this(); }
                },
                Contract: class Contract {
                  getId() { return ''; }
                  setId(id) { return this; }
                  getProjectcode() { return ''; }
                  setProjectcode(code) { return this; }
                  getContractname() { return ''; }
                  setContractname(name) { return this; }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary() { return new this(); }
                },
                OrganizationListRequest: class OrganizationListRequest {
                  constructor() {
                    this._pagination = null;
                    this._filter = null;
                  }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 OrganizationListRequest.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }

                  // Методы для пагинации
                  setPagination(pagination) {
                    this._pagination = pagination;
                    console.log('🔧 OrganizationListRequest.setPagination called:', pagination);
                  }
                  getPagination() { return this._pagination; }

                  // Методы для фильтров
                  setFilter(filter) {
                    this._filter = filter;
                    console.log('🔧 OrganizationListRequest.setFilter called:', filter);
                  }
                  getFilter() { return this._filter; }
                },

                OrganizationFilter: class OrganizationFilter {
                  constructor() {
                    this._name = '';
                    this._inn = '';
                    this._kpp = '';
                    this._parentId = '';
                    this._isDeleted = false;
                  }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 OrganizationFilter.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }

                  // Методы для фильтров
                  setName(name) { this._name = name; }
                  getName() { return this._name; }
                  setInn(inn) { this._inn = inn; }
                  getInn() { return this._inn; }
                  setKpp(kpp) { this._kpp = kpp; }
                  getKpp() { return this._kpp; }
                  setParentId(parentId) { this._parentId = parentId; }
                  getParentId() { return this._parentId; }
                  setIsDeleted(isDeleted) { this._isDeleted = isDeleted; }
                  getIsDeleted() { return this._isDeleted; }
                },
                OrganizationListResponse: class OrganizationListResponse {
                  constructor() {
                    this._organizationsList = [];
                    this._pagination = null;
                  }
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    return new this();
                  }
                  toObject() {
                    return {
                      organizationsList: this._organizationsList.map(org => org.toObject ? org.toObject() : org),
                      pagination: this._pagination ? this._pagination.toObject() : null
                    };
                  }
                  static toObject() { return {}; }

                  // Методы для списка организаций
                  getOrganizationsList() {
                    console.log('🔍 OrganizationListResponse.getOrganizationsList called, returning:', this._organizationsList.length, 'organizations');
                    return this._organizationsList;
                  }
                  setOrganizationsList(list) {
                    this._organizationsList = list || [];
                    console.log('🔧 OrganizationListResponse.setOrganizationsList called with', this._organizationsList.length, 'organizations');
                    return this;
                  }

                  // Методы для пагинации
                  getPagination() { return this._pagination; }
                  setPagination(pagination) {
                    this._pagination = pagination;
                    return this;
                  }
                  deserializeBinary(bytes) { return new this(); }
                },
                OrganizationResponse: class OrganizationResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getOrganization() { return null; }
                  setOrganization(org) { return this; }
                },
                ByIdRequest: class ByIdRequest {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 ByIdRequest.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getId() { return ''; }
                  setId(id) { return this; }
                },
                AddressResponse: class AddressResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 AddressResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getAddress() { return null; }
                  setAddress(address) { return this; }
                },
                AddressListResponse: class AddressListResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 AddressListResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getAddressesList() { return []; }
                  setAddressesList(list) { return this; }
                },
                ContactResponse: class ContactResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 ContactResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getContact() { return null; }
                  setContact(contact) { return this; }
                },
                ContactListResponse: class ContactListResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 ContactListResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getContactsList() { return []; }
                  setContactsList(list) { return this; }
                },
                ContractResponse: class ContractResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 ContractResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getContract() { return null; }
                  setContract(contract) { return this; }
                },
                ContractListResponse: class ContractListResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 ContractListResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getContractsList() { return []; }
                  setContractsList(list) { return this; }
                },
                OrganizationHintResponse: class OrganizationHintResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 OrganizationHintResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getHintsList() { return []; }
                  setHintsList(list) { return this; }
                },
                PaymentMethodResponse: class PaymentMethodResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 PaymentMethodResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getPaymentMethod() { return null; }
                  setPaymentMethod(method) { return this; }
                },
                PaymentMethodListResponse: class PaymentMethodListResponse {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 PaymentMethodListResponse.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getPaymentMethodsList() { return []; }
                  setPaymentMethodsList(list) { return this; }
                },
                ByIdWithPaginationRequest: class ByIdWithPaginationRequest {
                  constructor() {}
                  serializeBinary() { return new Uint8Array(); }
                  static deserializeBinary(bytes) {
                    console.log('🔍 ByIdWithPaginationRequest.deserializeBinary called');
                    return new this();
                  }
                  toObject() { return {}; }
                  static toObject() { return {}; }
                  getId() { return ''; }
                  setId(id) { return this; }
                  getPagination() { return null; }
                  setPagination(pagination) { return this; }
                },
                ContractStatus: { CS_DRAFT: 0, CS_ACTIVE: 1, CS_EXPIRING: 2, CS_COMPLETED: 3, CS_TERMINATED: 4 },
                ContractType: { CT_SYSTEM_RULES: 0, CT_SERVICE: 1, CT_TRANSPORT: 2, CT_PROCESSING: 3 },
                ProjectType: { PT_TRANSPORT_SYSTEM: 0, PT_METRO_SYSTEM: 1, PT_BUS_SYSTEM: 2, PT_TAXI_SYSTEM: 3 },
                OrganizationRole: { OR_OPERATOR: 0, OR_CARRIER: 1, OR_PROCESSING_CENTER: 2, OR_CONTRACTOR: 3, OR_PARTNER: 4 }
              };

            case './pasiv-gate-private_grpc_web_pb.js':
            case 'pasiv-gate-private_grpc_web_pb.js':
            case 'pasiv-gate-private_grpc_web_pb':
              // Возвращаем объект с gRPC клиентом
              return {
                PASIVGatePrivateServiceClient: class PASIVGatePrivateServiceClient {
                  constructor(hostname, credentials, options) {
                    console.log('🔧 PASIVGatePrivateServiceClient created:', hostname);
                    this.hostname = hostname;
                    this.credentials = credentials;
                    this.options = options;
                  }

                  // Методы для работы с организациями
                  createOrganization(request, metadata, callback) {
                    console.log('🔧 createOrganization called');
                    if (callback) callback(null, { toObject: () => ({}) });
                  }

                  updateOrganization(request, metadata, callback) {
                    console.log('🔧 updateOrganization called');
                    if (callback) callback(null, { toObject: () => ({}) });
                  }

                  organizationList(request, metadata, callback) {
                    console.log('🔧 organizationList called with request:', request);

                    // Создаем мок-данные организаций
                    const mockOrganizations = [
                      {
                        getId: () => '1',
                        getName: () => 'ООО "Городской транспорт"',
                        getShortname: () => 'Городской транспорт',
                        getInn: () => '7701234567',
                        getKpp: () => '770101001',
                        getOgrn: () => '1027700123456',
                        getOkpo: () => '12345678',
                        getOktmo: () => '45000000',
                        getOkved: () => '49.31',
                        getNote: () => 'Основная организация',
                        getFiodirector: () => 'Иванов Иван Иванович',
                        getManageractionreason: () => '',
                        getAddresslegal: () => '123456, г. Москва, ул. Транспортная, д. 1',
                        getAddressactual: () => '123456, г. Москва, ул. Транспортная, д. 1',
                        getAddressmailing: () => '123456, г. Москва, ул. Транспортная, д. 1',
                        getIsdeleted: () => false,
                        getParent: () => null,
                        toObject: () => ({
                          id: '1',
                          name: 'ООО "Городской транспорт"',
                          shortName: 'Городской транспорт',
                          inn: '7701234567'
                        })
                      },
                      {
                        getId: () => '2',
                        getName: () => 'АО "Метрополитен"',
                        getShortname: () => 'Метрополитен',
                        getInn: () => '7702345678',
                        getKpp: () => '770201001',
                        getOgrn: () => '1027700234567',
                        getOkpo: () => '23456789',
                        getOktmo: () => '45000000',
                        getOkved: () => '49.31',
                        getNote: () => 'Метрополитен',
                        getFiodirector: () => 'Петров Петр Петрович',
                        getManageractionreason: () => '',
                        getAddresslegal: () => '123456, г. Москва, ул. Метро, д. 2',
                        getAddressactual: () => '123456, г. Москва, ул. Метро, д. 2',
                        getAddressmailing: () => '123456, г. Москва, ул. Метро, д. 2',
                        getIsdeleted: () => false,
                        getParent: () => null,
                        toObject: () => ({
                          id: '2',
                          name: 'АО "Метрополитен"',
                          shortName: 'Метрополитен',
                          inn: '7702345678'
                        })
                      }
                    ];

                    // Создаем мок-пагинацию
                    const mockPagination = {
                      getPage: () => 1,
                      getLimit: () => 10,
                      getTotalPage: () => 1,
                      getTotalCount: () => mockOrganizations.length,
                      toObject: () => ({
                        page: 1,
                        limit: 10,
                        totalPage: 1,
                        totalCount: mockOrganizations.length
                      })
                    };

                    // Создаем ответ
                    const response = {
                      toObject: () => ({
                        organizationsList: mockOrganizations.map(org => org.toObject()),
                        pagination: mockPagination.toObject()
                      }),
                      getOrganizationsList: () => mockOrganizations,
                      getPagination: () => mockPagination
                    };

                    console.log('🔧 organizationList returning', mockOrganizations.length, 'organizations');
                    if (callback) callback(null, response);
                  }

                  organizationById(request, metadata, callback) {
                    console.log('🔧 organizationById called');
                    if (callback) callback(null, { toObject: () => ({}) });
                  }

                  deleteOrganization(request, metadata, callback) {
                    console.log('🔧 deleteOrganization called');
                    if (callback) callback(null, { toObject: () => ({}) });
                  }

                  // Добавляем остальные методы как заглушки
                  recoverOrganization(request, metadata, callback) {
                    console.log('🔧 recoverOrganization called');
                    if (callback) callback(null, { toObject: () => ({}) });
                  }

                  organizationListForProject(request, metadata, callback) {
                    console.log('🔧 organizationListForProject called');
                    if (callback) callback(null, { toObject: () => ({ organizationsList: [] }) });
                  }

                  // Методы для адресов
                  createAddress(request, metadata, callback) {
                    console.log('🔧 createAddress called');
                    if (callback) callback(null, { toObject: () => ({}) });
                  }

                  addressList(request, metadata, callback) {
                    console.log('🔧 addressList called');
                    if (callback) callback(null, { toObject: () => ({ addressesList: [] }) });
                  }

                  // Методы для контактов
                  contactList(request, metadata, callback) {
                    console.log('🔧 contactList called');
                    if (callback) callback(null, { toObject: () => ({ contactsList: [] }) });
                  }

                  // Методы для договоров
                  contractList(request, metadata, callback) {
                    console.log('🔧 contractList called');
                    if (callback) callback(null, { toObject: () => ({ contractsList: [] }) });
                  }
                }
              };

            default:
              // Проверяем нормализованное имя
              if (normalizedName === 'common_pb' || normalizedName === 'pasiv-gate-private_pb') {
                return window.require(normalizedName);
              }
              console.warn('Module', moduleName, 'not found, returning empty object');
              return {};
          }
        };

        // Создаем module и exports
        window.module = { exports: {} };
        window.exports = window.module.exports;

        // Создаем глобальный объект proto для protobuf файлов
        if (typeof window.proto === 'undefined') {
          // Получаем классы из pasiv-gate-private_pb модуля
          const pasivPbClasses = window.require('./pasiv-gate-private_pb.js');

          window.proto = {
            ru: {
              sbertroika: {
                pasiv: {
                  gate: {
                    v1: {
                      // Добавляем все классы из pasiv-gate-private_pb
                      Organization: pasivPbClasses.Organization,
                      Contract: pasivPbClasses.Contract,
                      OrganizationListRequest: pasivPbClasses.OrganizationListRequest,
                      OrganizationListResponse: pasivPbClasses.OrganizationListResponse,
                      OrganizationResponse: pasivPbClasses.OrganizationResponse,
                      OrganizationFilter: pasivPbClasses.OrganizationFilter,
                      ByIdRequest: pasivPbClasses.ByIdRequest,
                      AddressResponse: pasivPbClasses.AddressResponse,
                      AddressListResponse: pasivPbClasses.AddressListResponse,
                      ContactResponse: pasivPbClasses.ContactResponse,
                      ContactListResponse: pasivPbClasses.ContactListResponse,
                      ContractResponse: pasivPbClasses.ContractResponse,
                      ContractListResponse: pasivPbClasses.ContractListResponse,
                      OrganizationHintResponse: pasivPbClasses.OrganizationHintResponse,
                      PaymentMethodResponse: pasivPbClasses.PaymentMethodResponse,
                      PaymentMethodListResponse: pasivPbClasses.PaymentMethodListResponse,
                      ByIdWithPaginationRequest: pasivPbClasses.ByIdWithPaginationRequest,
                      // Добавляем enum значения
                      ContractStatus: pasivPbClasses.ContractStatus,
                      ContractType: pasivPbClasses.ContractType,
                      ProjectType: pasivPbClasses.ProjectType,
                      OrganizationRole: pasivPbClasses.OrganizationRole
                    }
                  }
                },
                common: {
                  v1: {
                    // Добавляем классы из common_pb модуля
                    HistoryResponse: window.require('./common_pb.js').HistoryResponse
                  }
                }
              }
            }
          };

          console.log('✅ Created global proto object with classes');
        }

        // КРИТИЧЕСКИ ВАЖНО: proto должен быть доступен как глобальная переменная
        // Потому что в protobuf файлах используется: goog.object.extend(proto, ...)
        if (typeof globalThis.proto === 'undefined') {
          globalThis.proto = window.proto;
        }

        // Дополнительная защита - создаем глобальные ссылки
        if (typeof globalThis !== 'undefined') {
          globalThis.grpc = window.grpc;
          globalThis.require = window.require;
          globalThis.module = window.module;
          globalThis.exports = window.exports;
          globalThis.jspb = window.jspb;
          globalThis.goog = window.goog;
          globalThis.proto = window.proto;
        }

        // Защита через Object.defineProperty
        Object.defineProperty(window, 'grpc', {
          value: window.grpc,
          writable: false,
          configurable: false
        });

        // КРИТИЧЕСКИ ВАЖНО: Создаем дополнительную защиту
        // Перехватываем любые попытки перезаписать grpc объект
        const originalGrpc = window.grpc;

        setInterval(() => {
          if (window.grpc !== originalGrpc || !window.grpc?.web?.MethodType?.UNARY) {
            console.error('🚨 CRITICAL: grpc object was overwritten or corrupted!');
            console.log('Current grpc:', window.grpc);
            console.log('Expected grpc:', originalGrpc);

            // Восстанавливаем объект
            window.grpc = originalGrpc;
            globalThis.grpc = originalGrpc;

            console.log('✅ grpc object restored');
          }
        }, 100); // Проверяем каждые 100ms

        console.log('🚀 gRPC polyfill initialized in HTML head');
        console.log('✅ grpc.web.MethodType.UNARY =', window.grpc.web.MethodType.UNARY);
        console.log('✅ require function available =', typeof window.require === 'function');

        // Тестируем все модули
        try {
          const grpcWeb = window.require('grpc-web');
          console.log('✅ grpc-web test:', !!grpcWeb);
          console.log('✅ grpc-web.MethodType test:', !!grpcWeb.MethodType);
          console.log('✅ grpc-web.MethodType.UNARY test:', grpcWeb.MethodType?.UNARY === 'unary');
          console.log('✅ grpc-web.MethodDescriptor test:', !!grpcWeb.MethodDescriptor);

          // Тестируем google-protobuf функции
          const googleProtobuf = window.require('google-protobuf');
          console.log('✅ google-protobuf.object.extend test:', typeof googleProtobuf.object?.extend === 'function');
          console.log('✅ google-protobuf.inherits test:', typeof googleProtobuf.inherits === 'function');
          console.log('✅ google-protobuf.DEBUG test:', typeof googleProtobuf.DEBUG === 'boolean');
          console.log('✅ google-protobuf test:', !!googleProtobuf.Message);
          console.log('✅ jspb global test:', !!window.jspb);
          console.log('✅ goog global test:', !!window.goog);
          console.log('✅ proto global test:', !!window.proto);

          console.log('✅ empty_pb test:', !!window.require('google-protobuf/google/protobuf/empty_pb.js').Empty);
          console.log('✅ timestamp_pb test:', !!window.require('google-protobuf/google/protobuf/timestamp_pb.js').Timestamp);
          const commonPb = window.require('./common_pb.js');
          console.log('✅ common_pb test:', !!commonPb.PaginationRequest);
          console.log('✅ common_pb.EmptyResponse test:', !!commonPb.EmptyResponse);
          console.log('✅ common_pb.EmptyResponse.deserializeBinary test:', typeof commonPb.EmptyResponse?.deserializeBinary === 'function');
          console.log('✅ common_pb.HistoryResponse test:', !!commonPb.HistoryResponse);
          console.log('✅ common_pb.HistoryResponse.deserializeBinary test:', typeof commonPb.HistoryResponse?.deserializeBinary === 'function');
          const pasivPb = window.require('./pasiv-gate-private_pb.js');
          console.log('✅ pasiv-gate-private_pb test:', !!pasivPb.Organization);
          console.log('✅ pasiv-gate-private_pb.OrganizationListRequest test:', !!pasivPb.OrganizationListRequest);
          console.log('✅ pasiv-gate-private_pb.OrganizationListResponse test:', !!pasivPb.OrganizationListResponse);
          console.log('✅ pasiv-gate-private_pb.OrganizationResponse test:', !!pasivPb.OrganizationResponse);
          console.log('✅ pasiv-gate-private_pb.ByIdRequest test:', !!pasivPb.ByIdRequest);
          console.log('✅ pasiv-gate-private_pb.AddressResponse test:', !!pasivPb.AddressResponse);
          console.log('✅ pasiv-gate-private_pb.AddressListResponse test:', !!pasivPb.AddressListResponse);
          console.log('✅ pasiv-gate-private_pb.ByIdWithPaginationRequest test:', !!pasivPb.ByIdWithPaginationRequest);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse test:', !!window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.OrganizationListResponse);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.OrganizationResponse test:', !!window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.OrganizationResponse);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.ByIdRequest test:', !!window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.ByIdRequest);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest test:', !!window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.ByIdWithPaginationRequest);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.AddressResponse test:', !!window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.AddressResponse);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.AddressListResponse test:', !!window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.AddressListResponse);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse test:', !!window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.OrganizationListResponse);
          console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinary test:', typeof window.proto?.ru?.sbertroika?.pasiv?.gate?.v1?.OrganizationListResponse?.deserializeBinary === 'function');
          console.log('✅ proto.ru.sbertroika.common.v1.HistoryResponse test:', !!window.proto?.ru?.sbertroika?.common?.v1?.HistoryResponse);

          // Тестируем gRPC клиент
          try {
            const grpcClient = window.require('./pasiv-gate-private_grpc_web_pb.js');
            console.log('✅ pasiv-gate-private_grpc_web_pb test:', !!grpcClient.PASIVGatePrivateServiceClient);
            console.log('✅ PASIVGatePrivateServiceClient constructor test:', typeof grpcClient.PASIVGatePrivateServiceClient === 'function');
          } catch (e) {
            console.log('❌ pasiv-gate-private_grpc_web_pb test failed:', e.message);
          }
        } catch (e) {
          console.error('❌ Module test failed:', e);
        }
      </script>
</head>

<body>
      <div id="app"></div>
      <script type="module" src="/src/main.ts"></script>
</body>

</html>
