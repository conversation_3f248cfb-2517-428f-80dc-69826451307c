{"name": "pasiv-ui", "version": "4.3.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "type-check:watch": "tsc --noEmit --watch --skipLib<PERSON><PERSON><PERSON>", "type-check:strict": "tsc --noEmit", "lint": "eslint --fix . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "generate-grpc": "echo 'gRPC generation disabled - using REST API'", "generate-proto": "echo 'Proto generation disabled - using REST API'", "generate-proto-js": "echo 'Proto JS generation disabled - using REST API'", "generate-proto-grpc-web": "echo 'Proto gRPC-web generation disabled - using REST API'"}, "dependencies": {"@primeuix/themes": "^1.0.0", "@tkp3/common-ui": "file:../../common/tkp3-common-ui", "chart.js": "3.3.2", "primeicons": "^7.0.0", "primevue": "^4.3.1", "tailwindcss-primeui": "^0.5.0", "vue": "^3.4.34", "vue-router": "^4.4.0"}, "devDependencies": {"@playwright/test": "^1.54.2", "@primevue/auto-import-resolver": "^4.3.1", "@rollup/plugin-commonjs": "^28.0.6", "@rushstack/eslint-patch": "^1.8.0", "@types/node": "^20.19.9", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "postcss": "^8.4.40", "prettier": "^3.2.5", "sass": "^1.55.0", "tailwindcss": "^3.4.6", "typescript": "^5.2.2", "unplugin-vue-components": "^0.27.3", "vite": "^5.3.1"}}