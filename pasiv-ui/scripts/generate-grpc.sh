#!/bin/bash

# Скрипт для генерации gRPC клиента для PASIV-UI
# Автоматически копирует proto файлы из зависимых модулей и генерирует TypeScript/JavaScript клиент

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Переходим в директорию скрипта
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR/.."

print_header "Генерация gRPC клиента для PASIV-UI"

# Проверяем, что мы в правильной директории
if [ ! -f "package.json" ]; then
    print_error "Не найден package.json. Убедитесь, что скрипт запускается из директории pasiv-ui"
    exit 1
fi

# Шаг 1: Извлечение proto файлов из зависимостей
print_info "Извлечение proto файлов из зависимостей..."
cd ../
if ./gradlew :pasiv-api-private:extractProtoFromDeps; then
    print_success "Proto файлы извлечены из зависимостей"
else
    print_error "Ошибка при извлечении proto файлов из зависимостей"
    exit 1
fi

cd pasiv-ui

# Шаг 2: Создание директории proto
print_info "Подготовка директории proto..."
mkdir -p proto
rm -rf proto/*

# Шаг 3: Копирование proto файлов
print_info "Копирование proto файлов..."

# Копируем основные proto файлы из pasiv-api-private
if [ -d "../pasiv-api-private/src/main/proto" ]; then
    cp ../pasiv-api-private/src/main/proto/*.proto proto/ 2>/dev/null || true
    print_success "Скопированы proto файлы из pasiv-api-private"
else
    print_warning "Директория ../pasiv-api-private/src/main/proto не найдена"
fi

# Копируем извлеченные proto файлы из зависимостей
if [ -d "../pasiv-api-private/build/extracted-include-protos/main" ]; then
    cp ../pasiv-api-private/build/extracted-include-protos/main/*.proto proto/ 2>/dev/null || true
    print_success "Скопированы извлеченные proto файлы"
else
    print_warning "Директория ../pasiv-api-private/build/extracted-include-protos/main не найдена"
fi

# Копируем proto файлы из pasiv-api
if [ -d "../pasiv-api/src/main/proto" ]; then
    cp ../pasiv-api/src/main/proto/*.proto proto/ 2>/dev/null || true
    print_success "Скопированы proto файлы из pasiv-api"
else
    print_warning "Директория ../pasiv-api/src/main/proto не найдена"
fi

# Проверяем, что proto файлы скопированы
PROTO_COUNT=$(find proto -name "*.proto" | wc -l)
if [ "$PROTO_COUNT" -eq 0 ]; then
    print_error "Не найдено ни одного proto файла для генерации"
    exit 1
fi

print_info "Найдено $PROTO_COUNT proto файлов:"
ls -la proto/*.proto | awk '{print "  - " $9}' | sed 's|proto/||g'

# Шаг 4: Генерация gRPC клиента
print_info "Генерация gRPC клиента..."
if npm run generate-proto; then
    print_success "gRPC клиент сгенерирован успешно!"
else
    print_error "Ошибка при генерации gRPC клиента"
    exit 1
fi

# Шаг 5: Проверка результатов
GENERATED_COUNT=$(find src/generated -name "*_pb.js" | wc -l)
print_info "Сгенерировано $GENERATED_COUNT JavaScript файлов"

GRPC_COUNT=$(find src/generated -name "*_grpc_pb.d.ts" | wc -l)
print_info "Сгенерировано $GRPC_COUNT gRPC TypeScript файлов"

print_success "Генерация завершена успешно!"
print_info "Сгенерированные файлы находятся в директории src/generated/"
print_info "Основной gRPC клиент: src/generated/pasiv-gate-private_grpc_pb.d.ts"

echo ""
print_header "Готово!"
