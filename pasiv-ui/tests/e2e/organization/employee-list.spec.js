import { test, expect } from '@playwright/test';

test.describe('EmployeeList Component', () => {
    test.beforeEach(async ({ page }) => {
        // Переходим на страницу сотрудников организации
        await page.goto('/organizations/1/employees');

        // Ждем загрузки данных
        await page.waitForSelector('[data-testid="employee-table"]', { timeout: 10000 });
    });

    test('should display employee list page', async ({ page }) => {
        // Проверяем заголовок страницы
        await expect(page.locator('h2')).toContainText('Сотрудники организации');

        // Проверяем наличие кнопки добавления
        await expect(page.locator('button:has-text("Добавить сотрудника")')).toBeVisible();

        // Проверяем наличие таблицы
        await expect(page.locator('[data-testid="employee-table"]')).toBeVisible();
    });

    test('should display employee data in table', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('.p-datatable-tbody tr', { timeout: 10000 });

        // Проверяем наличие данных в таблице
        const rows = page.locator('.p-datatable-tbody tr');
        const rowCount = await rows.count();
        expect(rowCount).toBeGreaterThan(0);

        // Проверяем заголовки колонок
        await expect(page.locator('th:has-text("Фамилия")')).toBeVisible();
        await expect(page.locator('th:has-text("Имя")')).toBeVisible();
        await expect(page.locator('th:has-text("Должность")')).toBeVisible();
        await expect(page.locator('th:has-text("Email")')).toBeVisible();
        await expect(page.locator('th:has-text("Роли")')).toBeVisible();
        await expect(page.locator('th:has-text("Статус")')).toBeVisible();
    });

    test('should open add employee dialog', async ({ page }) => {
        // Кликаем на кнопку добавления сотрудника
        await page.click('button:has-text("Добавить сотрудника")');

        // Проверяем, что диалог открылся
        await expect(page.locator('.p-dialog')).toBeVisible();
        await expect(page.locator('.p-dialog-header:has-text("Добавить сотрудника")')).toBeVisible();

        // Проверяем наличие полей формы
        await expect(page.locator('input[placeholder*="Фамилия"]')).toBeVisible();
        await expect(page.locator('input[placeholder*="Имя"]')).toBeVisible();
        await expect(page.locator('input[placeholder*="Email"]')).toBeVisible();
        await expect(page.locator('input[placeholder*="Должность"]')).toBeVisible();

        // Проверяем кнопки
        await expect(page.locator('button:has-text("Сохранить")')).toBeVisible();
        await expect(page.locator('button:has-text("Отмена")')).toBeVisible();
    });

    test('should validate required fields in add employee form', async ({ page }) => {
        // Открываем диалог добавления
        await page.click('button:has-text("Добавить сотрудника")');
        await page.waitForSelector('.p-dialog');

        // Пытаемся сохранить без заполнения обязательных полей
        await page.click('button:has-text("Сохранить")');

        // Проверяем, что диалог не закрылся (валидация сработала)
        await expect(page.locator('.p-dialog')).toBeVisible();
    });

    test('should fill and submit add employee form', async ({ page }) => {
        // Открываем диалог добавления
        await page.click('button:has-text("Добавить сотрудника")');
        await page.waitForSelector('.p-dialog');

        // Заполняем форму
        await page.fill('input[placeholder*="Фамилия"]', 'Тестов');
        await page.fill('input[placeholder*="Имя"]', 'Тест');
        await page.fill('input[placeholder*="Отчество"]', 'Тестович');
        await page.fill('input[placeholder*="Email"]', '<EMAIL>');
        await page.fill('input[placeholder*="Телефон"]', '+7 (495) 123-45-67');
        await page.fill('input[placeholder*="Должность"]', 'Тестировщик');

        // Выбираем роли
        await page.click('.p-multiselect');
        await page.waitForSelector('.p-multiselect-panel');
        await page.click('.p-multiselect-item:has-text("Администратор ПАСИВ")');
        await page.click('.p-multiselect');

        // Сохраняем
        await page.click('button:has-text("Сохранить")');

        // Проверяем, что диалог закрылся
        await expect(page.locator('.p-dialog')).not.toBeVisible();
    });

    test('should close add employee dialog on cancel', async ({ page }) => {
        // Открываем диалог
        await page.click('button:has-text("Добавить сотрудника")');
        await page.waitForSelector('.p-dialog');

        // Кликаем отмена
        await page.click('button:has-text("Отмена")');

        // Проверяем, что диалог закрылся
        await expect(page.locator('.p-dialog')).not.toBeVisible();
    });

    test('should display employee actions', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('.p-datatable-tbody tr', { timeout: 10000 });

        // Проверяем наличие кнопок действий в первой строке
        const firstRow = page.locator('.p-datatable-tbody tr').first();
        await expect(firstRow.locator('button[title="Редактировать"]')).toBeVisible();
        await expect(firstRow.locator('button[title="Удалить"]')).toBeVisible();
    });

    test('should show employee status tags', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('.p-datatable-tbody tr', { timeout: 10000 });

        // Проверяем наличие тегов статуса
        const statusTags = page.locator('.p-tag');
        const tagCount = await statusTags.count();
        expect(tagCount).toBeGreaterThan(0);
    });

    test('should show employee roles', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('.p-datatable-tbody tr', { timeout: 10000 });

        // Проверяем наличие тегов ролей
        const roleTags = page.locator('td:has-text("Роли") ~ td .p-tag');
        const roleTagCount = await roleTags.count();
        expect(roleTagCount).toBeGreaterThan(0);
    });

    test('should handle empty employee list', async ({ page }) => {
        // Переходим на страницу организации без сотрудников
        await page.goto('/organizations/999/employees');

        // Ждем загрузки
        await page.waitForSelector('[data-testid="employee-table"]', { timeout: 10000 });

        // Проверяем сообщение о пустом списке
        await expect(page.locator('text=Сотрудники не найдены')).toBeVisible();
        await expect(page.locator('button:has-text("Добавить первого сотрудника")')).toBeVisible();
    });

    test('should confirm employee deletion', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('.p-datatable-tbody tr', { timeout: 10000 });

        // Настраиваем обработчик диалога подтверждения
        page.on('dialog', async (dialog) => {
            expect(dialog.type()).toBe('confirm');
            expect(dialog.message()).toContain('Удалить сотрудника');
            await dialog.dismiss(); // Отменяем удаление для теста
        });

        // Кликаем на кнопку удаления
        const deleteButton = page.locator('.p-datatable-tbody tr').first().locator('button[title="Удалить"]');
        await deleteButton.click();
    });
});

test.describe('EmployeeList Component - Error Handling', () => {
    test('should handle loading state', async ({ page }) => {
        // Переходим на страницу
        await page.goto('/organizations/1/employees');

        // Проверяем индикатор загрузки (если он появляется быстро)
        const loadingIndicator = page.locator('.p-datatable-loading');
        if (await loadingIndicator.isVisible()) {
            await expect(loadingIndicator).toBeVisible();
        }

        // Ждем завершения загрузки
        await page.waitForSelector('[data-testid="employee-table"]', { timeout: 10000 });
    });

    test('should handle network errors gracefully', async ({ page }) => {
        // Блокируем сетевые запросы для имитации ошибки
        await page.route('**/api/employees**', (route) => route.abort());

        // Переходим на страницу
        await page.goto('/organizations/1/employees');

        // Проверяем, что страница не сломалась
        await expect(page.locator('h2:has-text("Сотрудники организации")')).toBeVisible();
    });
});
