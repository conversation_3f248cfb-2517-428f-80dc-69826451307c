import { test, expect } from '@playwright/test';

test.describe('FinanceInfo Component', () => {
    test.beforeEach(async ({ page }) => {
        // Переходим на страницу финансовой информации организации
        await page.goto('/organizations/1/finance');
        
        // Ждем загрузки данных
        await page.waitForSelector('.finance-info', { timeout: 10000 });
    });

    test('should display finance info page', async ({ page }) => {
        // Проверяем заголовок страницы
        await expect(page.locator('h2:has-text("Финансовая информация")')).toBeVisible();
        
        // Проверяем наличие селектора периода
        await expect(page.locator('.p-dropdown')).toBeVisible();
    });

    test('should display main financial metrics', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем основные показатели
        await expect(page.locator('text=Общая выручка')).toBeVisible();
        await expect(page.locator('text=Комиссии')).toBeVisible();
        await expect(page.locator('text=Транзакции')).toBeVisible();
        await expect(page.locator('text=Средний чек')).toBeVisible();
        
        // Проверяем наличие карточек статистики
        const statCards = page.locator('.stat-card');
        await expect(statCards).toHaveCount(4);
    });

    test('should display payment methods distribution', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.card', { timeout: 10000 });
        
        // Проверяем заголовок раздела
        await expect(page.locator('h3:has-text("Распределение по способам оплаты")')).toBeVisible();
        
        // Проверяем наличие способов оплаты
        await expect(page.locator('text=Банковские карты')).toBeVisible();
        await expect(page.locator('text=Транспортные карты')).toBeVisible();
        await expect(page.locator('text=Наличные')).toBeVisible();
    });

    test('should display top routes', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.card', { timeout: 10000 });
        
        // Проверяем заголовок раздела
        await expect(page.locator('h3:has-text("Топ маршрутов по выручке")')).toBeVisible();
        
        // Проверяем наличие маршрутов
        const routeItems = page.locator('text=Маршрут №');
        await expect(routeItems).toHaveCountGreaterThan(0);
    });

    test('should change period and reload data', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Запоминаем текущее значение выручки
        const currentRevenue = await page.locator('.stat-card').first().locator('.text-2xl').textContent();
        
        // Меняем период
        await page.click('.p-dropdown');
        await page.waitForSelector('.p-dropdown-panel');
        await page.click('.p-dropdown-item:has-text("Предыдущий месяц")');
        
        // Ждем обновления данных
        await page.waitForTimeout(1000);
        
        // Проверяем, что данные обновились (или остались теми же)
        const newRevenue = await page.locator('.stat-card').first().locator('.text-2xl').textContent();
        expect(newRevenue).toBeDefined();
    });

    test('should display percentage changes with correct styling', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем наличие иконок изменений
        const changeIcons = page.locator('.pi-arrow-up, .pi-arrow-down, .pi-minus');
        await expect(changeIcons).toHaveCountGreaterThan(0);
        
        // Проверяем наличие процентных значений
        const percentages = page.locator('text=/%/');
        await expect(percentages).toHaveCountGreaterThan(0);
    });

    test('should display currency formatting', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем форматирование валюты (рубли)
        const currencyValues = page.locator('text=/₽/');
        await expect(currencyValues).toHaveCountGreaterThan(0);
    });

    test('should display loading state', async ({ page }) => {
        // Переходим на страницу
        await page.goto('/organizations/1/finance');
        
        // Проверяем индикатор загрузки (если он появляется)
        const loadingIndicator = page.locator('.pi-spinner');
        if (await loadingIndicator.isVisible()) {
            await expect(loadingIndicator).toBeVisible();
            await expect(page.locator('text=Загрузка финансовых данных')).toBeVisible();
        }
        
        // Ждем завершения загрузки
        await page.waitForSelector('.finance-info', { timeout: 10000 });
    });

    test('should display payment method colors', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.card', { timeout: 10000 });
        
        // Проверяем наличие цветных индикаторов для способов оплаты
        const colorIndicators = page.locator('.w-1rem.h-1rem.border-round');
        await expect(colorIndicators).toHaveCountGreaterThan(0);
    });

    test('should display route transaction counts', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.card', { timeout: 10000 });
        
        // Проверяем отображение количества транзакций для маршрутов
        const transactionCounts = page.locator('text=транзакций');
        await expect(transactionCounts).toHaveCountGreaterThan(0);
    });

    test('should handle hover effects on stat cards', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Наводим курсор на первую карточку
        const firstCard = page.locator('.stat-card').first();
        await firstCard.hover();
        
        // Проверяем, что карточка реагирует на hover (CSS анимация)
        await expect(firstCard).toBeVisible();
    });

    test('should display responsive grid layout', async ({ page }) => {
        // Ждем загрузки данных
        await page.waitForSelector('.grid', { timeout: 10000 });
        
        // Проверяем наличие grid layout
        await expect(page.locator('.grid')).toBeVisible();
        
        // Проверяем колонки
        const columns = page.locator('.col-12');
        await expect(columns).toHaveCountGreaterThan(0);
    });

    test('should handle period selection dropdown', async ({ page }) => {
        // Проверяем dropdown периода
        const dropdown = page.locator('.p-dropdown');
        await expect(dropdown).toBeVisible();
        
        // Открываем dropdown
        await dropdown.click();
        await page.waitForSelector('.p-dropdown-panel');
        
        // Проверяем опции
        await expect(page.locator('.p-dropdown-item:has-text("Текущий месяц")')).toBeVisible();
        await expect(page.locator('.p-dropdown-item:has-text("Предыдущий месяц")')).toBeVisible();
        await expect(page.locator('.p-dropdown-item:has-text("Текущий квартал")')).toBeVisible();
        await expect(page.locator('.p-dropdown-item:has-text("Текущий год")')).toBeVisible();
        
        // Закрываем dropdown
        await page.keyboard.press('Escape');
    });
});

test.describe('FinanceInfo Component - Error Handling', () => {
    test('should handle loading errors gracefully', async ({ page }) => {
        // Блокируем сетевые запросы для имитации ошибки
        await page.route('**/api/finance/**', route => route.abort());
        
        // Переходим на страницу
        await page.goto('/organizations/1/finance');
        
        // Проверяем, что страница не сломалась
        await expect(page.locator('h2:has-text("Финансовая информация")')).toBeVisible();
    });

    test('should handle empty data gracefully', async ({ page }) => {
        // Мокаем пустой ответ
        await page.route('**/api/finance/**', route => {
            route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify({
                    totalRevenue: 0,
                    revenueChange: 0,
                    totalCommissions: 0,
                    commissionsChange: 0,
                    totalTransactions: 0,
                    transactionsChange: 0,
                    averageTicket: 0,
                    averageTicketChange: 0,
                    paymentMethods: [],
                    topRoutes: []
                })
            });
        });
        
        // Переходим на страницу
        await page.goto('/organizations/1/finance');
        
        // Ждем загрузки
        await page.waitForSelector('.finance-info', { timeout: 10000 });
        
        // Проверяем, что компонент отображается с нулевыми значениями
        await expect(page.locator('.stat-card')).toHaveCount(4);
    });

    test('should handle network timeout', async ({ page }) => {
        // Мокаем медленный ответ
        await page.route('**/api/finance/**', async route => {
            await page.waitForTimeout(5000);
            route.continue();
        });
        
        // Переходим на страницу
        await page.goto('/organizations/1/finance');
        
        // Проверяем, что показывается индикатор загрузки
        await expect(page.locator('h2:has-text("Финансовая информация")')).toBeVisible();
    });
});
