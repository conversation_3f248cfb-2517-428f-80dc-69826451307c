import { test, expect } from '@playwright/test';

test.describe('OrganizationDetail Component', () => {
    test.beforeEach(async ({ page }) => {
        // Переходим на страницу детальной информации об организации
        await page.goto('/organizations/1');
        
        // Ждем загрузки данных
        await page.waitForSelector('.organization-container', { timeout: 10000 });
    });

    test('should display organization detail page', async ({ page }) => {
        // Проверяем заголовок организации
        await expect(page.locator('.organization-header h1')).toBeVisible();
        
        // Проверяем наличие навигационной панели
        await expect(page.locator('.navigation-panel')).toBeVisible();
        await expect(page.locator('h3:has-text("Навигация")')).toBeVisible();
        
        // Проверяем наличие контентной панели
        await expect(page.locator('.content-panel')).toBeVisible();
    });

    test('should display organization header information', async ({ page }) => {
        // Проверяем иконку организации
        await expect(page.locator('.organization-header .pi-building')).toBeVisible();
        
        // Проверяем наличие названия организации
        await expect(page.locator('.organization-header h1')).toContainText('ООО');
        
        // Проверяем наличие тегов статуса
        await expect(page.locator('.organization-header .p-tag')).toHaveCountGreaterThan(0);
        
        // Проверяем кнопки действий
        await expect(page.locator('button:has-text("Назад к списку")')).toBeVisible();
        await expect(page.locator('button:has-text("Действия")')).toBeVisible();
    });

    test('should display navigation menu', async ({ page }) => {
        // Проверяем пункты навигационного меню
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Общая информация")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Сотрудники")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Договоры")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Финансовая информация")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("История операций")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Аудит изменений")')).toBeVisible();
    });

    test('should display organization dashboard by default', async ({ page }) => {
        // Проверяем, что отображается дашборд организации
        await expect(page.locator('.organization-dashboard')).toBeVisible();
        
        // Проверяем основные реквизиты
        await expect(page.locator('text=Основные реквизиты')).toBeVisible();
        await expect(page.locator('text=ИНН')).toBeVisible();
        await expect(page.locator('text=ОГРН')).toBeVisible();
        
        // Проверяем адреса
        await expect(page.locator('text=Адреса')).toBeVisible();
        await expect(page.locator('text=Юридический адрес')).toBeVisible();
        await expect(page.locator('text=Фактический адрес')).toBeVisible();
        
        // Проверяем контакты
        await expect(page.locator('text=Руководство и контакты')).toBeVisible();
        await expect(page.locator('text=Электронная почта')).toBeVisible();
        await expect(page.locator('text=Контактный телефон')).toBeVisible();
    });

    test('should navigate to employees section', async ({ page }) => {
        // Кликаем на пункт меню "Сотрудники"
        await page.click('.navigation-menu .p-menuitem-link:has-text("Сотрудники")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/organizations\/1\/employees/);
        
        // Проверяем, что загрузился компонент сотрудников
        await expect(page.locator('h2:has-text("Сотрудники организации")')).toBeVisible();
    });

    test('should navigate to contracts section', async ({ page }) => {
        // Кликаем на пункт меню "Договоры"
        await page.click('.navigation-menu .p-menuitem-link:has-text("Договоры")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/organizations\/1\/contracts/);
        
        // Проверяем, что загрузился компонент договоров
        await expect(page.locator('h2:has-text("Договоры организации")')).toBeVisible();
    });

    test('should navigate to finance section', async ({ page }) => {
        // Кликаем на пункт меню "Финансовая информация"
        await page.click('.navigation-menu .p-menuitem-link:has-text("Финансовая информация")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/organizations\/1\/finance/);
        
        // Проверяем, что загрузился компонент финансовой информации
        await expect(page.locator('h2:has-text("Финансовая информация")')).toBeVisible();
    });

    test('should navigate to audit section', async ({ page }) => {
        // Кликаем на пункт меню "Аудит изменений"
        await page.click('.navigation-menu .p-menuitem-link:has-text("Аудит изменений")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/organizations\/1\/audit/);
        
        // Проверяем, что загрузился компонент аудита
        await expect(page.locator('h2:has-text("Журнал аудита организации")')).toBeVisible();
    });

    test('should go back to organizations list', async ({ page }) => {
        // Кликаем на кнопку "Назад к списку"
        await page.click('button:has-text("Назад к списку")');
        
        // Проверяем, что перешли на список организаций
        await expect(page).toHaveURL(/\/organizations$/);
    });

    test('should display quick actions menu', async ({ page }) => {
        // Кликаем на кнопку действий
        await page.click('button:has-text("Действия")');
        
        // Проверяем, что появилось меню действий
        await expect(page.locator('.p-splitbutton-menubutton')).toBeVisible();
        
        // Можем проверить пункты меню, если они видны
        // await expect(page.locator('text=Синхронизировать с 1С')).toBeVisible();
        // await expect(page.locator('text=Экспорт данных')).toBeVisible();
    });

    test('should handle organization not found', async ({ page }) => {
        // Переходим на несуществующую организацию
        await page.goto('/organizations/999999');
        
        // Ждем загрузки
        await page.waitForSelector('.organization-container', { timeout: 10000 });
        
        // Проверяем сообщение об ошибке
        await expect(page.locator('text=Организация не найдена')).toBeVisible();
        await expect(page.locator('button:has-text("Вернуться к списку")')).toBeVisible();
    });

    test('should display loading state', async ({ page }) => {
        // Переходим на страницу
        await page.goto('/organizations/1');
        
        // Проверяем индикатор загрузки (если он появляется)
        const loadingIndicator = page.locator('.pi-spinner');
        if (await loadingIndicator.isVisible()) {
            await expect(loadingIndicator).toBeVisible();
            await expect(page.locator('text=Загрузка данных организации')).toBeVisible();
        }
        
        // Ждем завершения загрузки
        await page.waitForSelector('.organization-container', { timeout: 10000 });
    });

    test('should maintain responsive layout', async ({ page }) => {
        // Проверяем наличие Splitter
        await expect(page.locator('.p-splitter')).toBeVisible();
        
        // Проверяем панели
        await expect(page.locator('.p-splitter-panel').first()).toBeVisible(); // Навигация
        await expect(page.locator('.p-splitter-panel').last()).toBeVisible(); // Контент
    });

    test('should display organization type and ownership form', async ({ page }) => {
        // Проверяем отображение типа организации
        const organizationTag = page.locator('.organization-dashboard .p-tag').first();
        await expect(organizationTag).toBeVisible();
        
        // Проверяем форму собственности
        await expect(page.locator('.organization-dashboard .text-xl.font-bold')).toBeVisible();
    });

    test('should display system information', async ({ page }) => {
        // Проверяем системную информацию
        await expect(page.locator('text=Системная информация')).toBeVisible();
        await expect(page.locator('text=Дата создания')).toBeVisible();
        
        // Проверяем дату последней синхронизации (если есть)
        const syncDate = page.locator('text=Последняя синхронизация');
        if (await syncDate.isVisible()) {
            await expect(syncDate).toBeVisible();
        }
    });
});

test.describe('OrganizationDetail Component - Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
        // Блокируем сетевые запросы для имитации ошибки
        await page.route('**/api/organizations/**', route => route.abort());
        
        // Переходим на страницу
        await page.goto('/organizations/1');
        
        // Проверяем, что страница не сломалась
        await expect(page.locator('.organization-container')).toBeVisible();
    });

    test('should handle invalid organization ID', async ({ page }) => {
        // Переходим с невалидным ID
        await page.goto('/organizations/invalid');
        
        // Проверяем, что страница обрабатывает ошибку
        await page.waitForSelector('.organization-container', { timeout: 10000 });
    });
});
