import { test, expect } from '@playwright/test';

test.describe('TransactionList Component', () => {
    test.beforeEach(async ({ page }) => {
        // Переходим на страницу транзакций
        await page.goto('/pasiv/transactions');

        // Ждем загрузки данных
        await page.waitForSelector('.transaction-list', { timeout: 10000 });
    });

    test('should display transaction list page', async ({ page }) => {
        // Проверяем заголовок страницы
        await expect(page.locator('h2:has-text("Транзакции")')).toBeVisible();

        // Проверяем кнопку экспорта
        await expect(page.locator('button:has-text("Экспорт")')).toBeVisible();
    });

    test('should display filters section', async ({ page }) => {
        // Проверяем заголовок фильтров
        await expect(page.locator('h3:has-text("Фильтры")')).toBeVisible();

        // Проверяем все фильтры
        await expect(page.locator('label:has-text("Организация")')).toBeVisible();
        await expect(page.locator('label:has-text("Способ оплаты")')).toBeVisible();
        await expect(page.locator('label:has-text("Статус")')).toBeVisible();
        await expect(page.locator('label:has-text("Дата с")')).toBeVisible();
        await expect(page.locator('label:has-text("Дата по")')).toBeVisible();

        // Проверяем кнопки фильтров
        await expect(page.locator('button:has-text("Применить")')).toBeVisible();
        await expect(page.locator('button:has-text("Сбросить")')).toBeVisible();
    });

    test('should display transaction table', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Проверяем заголовки колонок
        await expect(page.locator('th:has-text("Дата и время")')).toBeVisible();
        await expect(page.locator('th:has-text("Сумма")')).toBeVisible();
        await expect(page.locator('th:has-text("Способ оплаты")')).toBeVisible();
        await expect(page.locator('th:has-text("Маршрут")')).toBeVisible();
        await expect(page.locator('th:has-text("ТС")')).toBeVisible();
        await expect(page.locator('th:has-text("Карта")')).toBeVisible();
        await expect(page.locator('th:has-text("Система")')).toBeVisible();
        await expect(page.locator('th:has-text("Статус")')).toBeVisible();
        await expect(page.locator('th:has-text("Действия")')).toBeVisible();
    });

    test('should filter by organization', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Выбираем организацию - используем getByRole
        await page.getByRole('combobox', { name: 'Выберите организацию' }).click();

        // Ждем появления панели и проверяем наличие опций
        try {
            await page.waitForSelector('.p-select-overlay', { timeout: 5000 });
            const options = page.locator('.p-select-option');
            const optionCount = await options.count();
            if (optionCount > 1) {
                await options.nth(1).click(); // Выбираем вторую опцию (первая обычно "Все")
                // Ждем закрытия dropdown
                await page.waitForSelector('.p-select-overlay', { state: 'hidden', timeout: 3000 });
            }
        } catch (error) {
            console.log('Dropdown panel not found or no options available');
            // Закрываем dropdown если он открыт
            await page.keyboard.press('Escape');
        }

        // Применяем фильтр
        await page.click('button:has-text("Применить")');

        // Ждем обновления данных
        await page.waitForTimeout(1000);

        // Проверяем, что фильтр применился
        await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    });

    test('should filter by payment method', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Выбираем способ оплаты
        await page.getByRole('combobox', { name: 'Выберите способ' }).click();

        // Ждем появления панели и проверяем наличие опций
        try {
            await page.waitForSelector('.p-select-overlay', { timeout: 5000 });
            const options = page.locator('.p-select-option');
            const optionCount = await options.count();
            if (optionCount > 1) {
                await options.nth(1).click(); // Выбираем вторую опцию
                // Ждем закрытия dropdown
                await page.waitForSelector('.p-select-overlay', { state: 'hidden', timeout: 3000 });
            }
        } catch (error) {
            console.log('Dropdown panel not found or no options available');
            // Закрываем dropdown если он открыт
            await page.keyboard.press('Escape');
        }

        // Применяем фильтр
        await page.click('button:has-text("Применить")');

        // Ждем обновления данных
        await page.waitForTimeout(1000);

        // Проверяем, что фильтр применился
        await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    });

    test('should filter by status', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Выбираем статус
        await page.getByRole('combobox', { name: 'Выберите статус' }).click();

        // Ждем появления панели и проверяем наличие опций
        try {
            await page.waitForSelector('.p-select-overlay', { timeout: 5000 });
            const options = page.locator('.p-select-option');
            const optionCount = await options.count();
            if (optionCount > 1) {
                await options.nth(1).click(); // Выбираем вторую опцию
                // Ждем закрытия dropdown
                await page.waitForSelector('.p-select-overlay', { state: 'hidden', timeout: 3000 });
            }
        } catch (error) {
            console.log('Dropdown panel not found or no options available');
            // Закрываем dropdown если он открыт
            await page.keyboard.press('Escape');
        }

        // Применяем фильтр
        await page.click('button:has-text("Применить")');

        // Ждем обновления данных
        await page.waitForTimeout(1000);

        // Проверяем, что фильтр применился
        await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    });

    test('should filter by date range', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Устанавливаем дату "с" - ищем по placeholder
        const dateFromInput = page.locator('combobox[placeholder="дд.мм.гггг"]').first();
        if (await dateFromInput.isVisible()) {
            await dateFromInput.fill('01.02.2024');
        }

        // Устанавливаем дату "по"
        const dateToInput = page.locator('combobox[placeholder="дд.мм.гггг"]').last();
        if (await dateToInput.isVisible()) {
            await dateToInput.fill('29.02.2024');
        }

        // Применяем фильтр
        await page.click('button:has-text("Применить")');

        // Ждем обновления данных
        await page.waitForTimeout(1000);

        // Проверяем, что фильтр применился
        await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    });

    test('should clear all filters', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Устанавливаем фильтры
        await page.getByRole('combobox', { name: 'Выберите организацию' }).click();

        // Ждем появления панели и выбираем опцию если есть
        try {
            await page.waitForSelector('.p-select-overlay', { timeout: 5000 });
            const options = page.locator('.p-select-option');
            const optionCount = await options.count();
            if (optionCount > 1) {
                await options.nth(1).click();
                // Ждем закрытия dropdown
                await page.waitForSelector('.p-select-overlay', { state: 'hidden', timeout: 3000 });
            }
        } catch (error) {
            console.log('Dropdown panel not found or no options available');
            // Закрываем dropdown если он открыт
            await page.keyboard.press('Escape');
        }

        // Сбрасываем фильтры
        await page.click('button:has-text("Сбросить")');

        // Ждем обновления данных
        await page.waitForTimeout(1000);

        // Проверяем, что фильтры сброшены
        await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    });

    test('should display transaction details on view button click', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Проверяем наличие кнопок просмотра
        const viewButtons = page.locator('button[title="Просмотр"], button:has(.pi-eye)');
        const buttonCount = await viewButtons.count();

        if (buttonCount > 0) {
            // Кликаем на первую кнопку просмотра
            await viewButtons.first().click();

            // Здесь можно добавить проверки для модального окна или перехода на страницу деталей
            await page.waitForTimeout(500);
        }
    });

    test('should export transactions', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Кликаем на кнопку экспорта
        await page.click('button:has-text("Экспорт")');

        // Проверяем, что функция экспорта вызвана (в реальном приложении здесь может быть загрузка файла)
        await page.waitForTimeout(500);
    });

    test('should display payment method tags with correct styling', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Проверяем наличие тегов способов оплаты
        const paymentTags = page.locator('.p-tag');
        const tagCount = await paymentTags.count();

        if (tagCount > 0) {
            // Проверяем, что теги видимы
            await expect(paymentTags.first()).toBeVisible();
        }
    });

    test('should display status tags with correct styling', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Проверяем наличие тегов статуса
        const statusTags = page.locator('.p-tag');
        const tagCount = await statusTags.count();

        if (tagCount > 0) {
            // Проверяем, что теги видимы
            await expect(statusTags.first()).toBeVisible();
        }
    });

    test('should display formatted amounts', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Проверяем наличие отформатированных сумм (с символом рубля)
        const amounts = page.locator('text=/₽/');
        const amountCount = await amounts.count();

        if (amountCount > 0) {
            await expect(amounts.first()).toBeVisible();
        }
    });

    test('should display formatted dates', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Проверяем наличие отформатированных дат
        const datePattern = /\d{2}\.\d{2}\.\d{4}/;
        const dates = page.locator(`text=${datePattern}`);
        const dateCount = await dates.count();

        if (dateCount > 0) {
            await expect(dates.first()).toBeVisible();
        }
    });

    test('should handle pagination', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="transaction-table"]', { timeout: 10000 });

        // Проверяем наличие пагинации
        const paginator = page.locator('.p-paginator');
        if (await paginator.isVisible()) {
            // Проверяем элементы пагинации
            await expect(paginator).toBeVisible();
        }
    });

    test('should handle empty state', async ({ page }) => {
        // Переходим на страницу
        await page.goto('/pasiv/transactions');

        // Ждем загрузки
        await page.waitForSelector('.transaction-list', { timeout: 10000 });

        // Проверяем, что таблица загрузилась (даже если пустая)
        await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();

        // Если есть данные, проверяем их отображение, если нет - проверяем пустое состояние
        const rows = page.locator('[data-testid="transaction-table"] tbody tr');
        const rowCount = await rows.count();

        if (rowCount === 0) {
            // Проверяем сообщение о пустом состоянии
            await expect(page.locator('text=Транзакции не найдены')).toBeVisible();
        } else {
            // Если есть данные, проверяем что таблица отображается корректно
            await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
        }
    });
});

test.describe('TransactionList Component - Error Handling', () => {
    test('should handle loading errors gracefully', async ({ page }) => {
        // Блокируем сетевые запросы для имитации ошибки
        await page.route('**/api/transactions**', (route) => route.abort());

        // Переходим на страницу
        await page.goto('/pasiv/transactions');

        // Проверяем, что страница не сломалась
        await expect(page.locator('h2:has-text("Транзакции")')).toBeVisible();
    });

    test('should handle network timeout', async ({ page }) => {
        // Мокаем медленный ответ
        await page.route('**/api/transactions**', async (route) => {
            await page.waitForTimeout(5000);
            route.continue();
        });

        // Переходим на страницу
        await page.goto('/pasiv/transactions');

        // Проверяем, что показывается индикатор загрузки
        await expect(page.locator('h2:has-text("Транзакции")')).toBeVisible();
    });
});
