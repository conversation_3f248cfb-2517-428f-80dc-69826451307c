import { test, expect } from '@playwright/test';

test.describe('SettlementSchemes Component', () => {
    test.beforeEach(async ({ page }) => {
        // Переходим на страницу схем расчетов
        await page.goto('/pasiv/settlement-schemes');
        
        // Ждем загрузки данных
        await page.waitForSelector('.settlement-schemes', { timeout: 10000 });
    });

    test('should display settlement schemes page', async ({ page }) => {
        // Проверяем заголовок страницы
        await expect(page.locator('h2:has-text("Схемы расчетов")')).toBeVisible();
        
        // Проверяем кнопку добавления
        await expect(page.locator('button:has-text("Добавить схему")')).toBeVisible();
    });

    test('should display schemes table', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем заголовки колонок
        await expect(page.locator('th:has-text("Название")')).toBeVisible();
        await expect(page.locator('th:has-text("Тип схемы")')).toBeVisible();
        await expect(page.locator('th:has-text("Комиссия %")')).toBeVisible();
        await expect(page.locator('th:has-text("Период расчетов")')).toBeVisible();
        await expect(page.locator('th:has-text("Организаций")')).toBeVisible();
        await expect(page.locator('th:has-text("Статус")')).toBeVisible();
        await expect(page.locator('th:has-text("Дата создания")')).toBeVisible();
        await expect(page.locator('th:has-text("Действия")')).toBeVisible();
    });

    test('should display scheme data correctly', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем наличие данных схем
        const rows = page.locator('[data-testid="schemes-table"] tbody tr');
        const rowCount = await rows.count();
        
        if (rowCount > 0) {
            // Проверяем первую строку
            const firstRow = rows.first();
            
            // Проверяем наличие названия схемы
            await expect(firstRow.locator('td').first()).toBeVisible();
            
            // Проверяем наличие тегов типа схемы
            await expect(firstRow.locator('.p-tag')).toHaveCountGreaterThan(0);
            
            // Проверяем отображение комиссии
            await expect(firstRow.locator('text=/%/')).toBeVisible();
        }
    });

    test('should open add scheme dialog', async ({ page }) => {
        // Кликаем на кнопку добавления схемы
        await page.click('button:has-text("Добавить схему")');
        
        // Проверяем, что диалог открылся
        await expect(page.locator('.p-dialog-header:has-text("Добавить схему расчетов")')).toBeVisible();
        
        // Проверяем поля формы
        await expect(page.locator('label:has-text("Название схемы")')).toBeVisible();
        await expect(page.locator('label:has-text("Тип схемы")')).toBeVisible();
        await expect(page.locator('label:has-text("Период расчетов")')).toBeVisible();
        await expect(page.locator('label:has-text("Комиссия %")')).toBeVisible();
        await expect(page.locator('label:has-text("Статус")')).toBeVisible();
        await expect(page.locator('label:has-text("Описание")')).toBeVisible();
        
        // Проверяем кнопки
        await expect(page.locator('button:has-text("Отмена")')).toBeVisible();
        await expect(page.locator('button:has-text("Сохранить")')).toBeVisible();
    });

    test('should fill and submit new scheme form', async ({ page }) => {
        // Открываем диалог добавления
        await page.click('button:has-text("Добавить схему")');
        await page.waitForSelector('.p-dialog-header:has-text("Добавить схему расчетов")');
        
        // Заполняем форму
        await page.fill('input[type="text"]', 'Тестовая схема расчетов');
        
        // Выбираем тип схемы
        await page.click('.p-dropdown >> nth=0');
        await page.waitForSelector('.p-dropdown-panel');
        await page.click('.p-dropdown-item:has-text("Через оператора")');
        
        // Выбираем период расчетов
        await page.click('.p-dropdown >> nth=1');
        await page.waitForSelector('.p-dropdown-panel');
        await page.click('.p-dropdown-item:has-text("Ежемесячно")');
        
        // Устанавливаем комиссию
        await page.fill('.p-inputnumber input', '2.5');
        
        // Заполняем описание
        await page.fill('textarea', 'Описание тестовой схемы расчетов');
        
        // Сохраняем
        await page.click('button:has-text("Сохранить")');
        
        // Проверяем, что диалог закрылся
        await expect(page.locator('.p-dialog-header:has-text("Добавить схему расчетов")')).not.toBeVisible();
    });

    test('should cancel scheme creation', async ({ page }) => {
        // Открываем диалог добавления
        await page.click('button:has-text("Добавить схему")');
        await page.waitForSelector('.p-dialog-header:has-text("Добавить схему расчетов")');
        
        // Заполняем часть формы
        await page.fill('input[type="text"]', 'Тестовая схема');
        
        // Отменяем
        await page.click('button:has-text("Отмена")');
        
        // Проверяем, что диалог закрылся
        await expect(page.locator('.p-dialog-header:has-text("Добавить схему расчетов")')).not.toBeVisible();
    });

    test('should edit scheme', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем наличие кнопок редактирования
        const editButtons = page.locator('button:has(.pi-pencil)');
        const buttonCount = await editButtons.count();
        
        if (buttonCount > 0) {
            // Кликаем на первую кнопку редактирования
            await editButtons.first().click();
            
            // Здесь можно добавить проверки для функции редактирования
            await page.waitForTimeout(500);
        }
    });

    test('should toggle scheme status', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем наличие кнопок переключения статуса
        const statusButtons = page.locator('button:has(.pi-pause), button:has(.pi-play)');
        const buttonCount = await statusButtons.count();
        
        if (buttonCount > 0) {
            // Кликаем на первую кнопку переключения статуса
            await statusButtons.first().click();
            
            // Ждем обновления
            await page.waitForTimeout(500);
        }
    });

    test('should delete scheme with confirmation', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем наличие кнопок удаления
        const deleteButtons = page.locator('button:has(.pi-trash)');
        const buttonCount = await deleteButtons.count();
        
        if (buttonCount > 0) {
            // Настраиваем обработчик диалога подтверждения
            page.on('dialog', async dialog => {
                expect(dialog.type()).toBe('confirm');
                await dialog.dismiss(); // Отменяем удаление для теста
            });
            
            // Кликаем на первую кнопку удаления
            await deleteButtons.first().click();
            
            // Ждем обработки
            await page.waitForTimeout(500);
        }
    });

    test('should display scheme type tags with correct styling', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем наличие тегов типов схем
        const typeTags = page.locator('.p-tag');
        const tagCount = await typeTags.count();
        
        if (tagCount > 0) {
            // Проверяем, что теги видимы
            await expect(typeTags.first()).toBeVisible();
            
            // Проверяем различные типы схем
            const possibleTypes = ['Через оператора', 'Прямые расчеты', 'Через расчетный центр', 'Агентская схема'];
            let foundType = false;
            
            for (const type of possibleTypes) {
                const typeTag = page.locator(`.p-tag:has-text("${type}")`);
                if (await typeTag.count() > 0) {
                    foundType = true;
                    break;
                }
            }
            
            expect(foundType).toBe(true);
        }
    });

    test('should display status tags correctly', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем наличие тегов статуса
        const statusTags = page.locator('.p-tag:has-text("Активна"), .p-tag:has-text("Неактивна")');
        const tagCount = await statusTags.count();
        
        if (tagCount > 0) {
            await expect(statusTags.first()).toBeVisible();
        }
    });

    test('should display commission rates correctly', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем отображение комиссий
        const commissionCells = page.locator('text=/%/');
        const commissionCount = await commissionCells.count();
        
        if (commissionCount > 0) {
            await expect(commissionCells.first()).toBeVisible();
        }
    });

    test('should display organization counts', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем отображение количества организаций
        const rows = page.locator('[data-testid="schemes-table"] tbody tr');
        const rowCount = await rows.count();
        
        if (rowCount > 0) {
            // Проверяем, что в колонке "Организаций" есть числовые значения
            const orgCountCells = page.locator('[data-testid="schemes-table"] tbody tr td:nth-child(5)');
            await expect(orgCountCells.first()).toBeVisible();
        }
    });

    test('should display formatted dates', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем отображение дат создания
        const datePattern = /\d{2}\.\d{2}\.\d{4}/;
        const dates = page.locator(`text=${datePattern}`);
        const dateCount = await dates.count();
        
        if (dateCount > 0) {
            await expect(dates.first()).toBeVisible();
        }
    });

    test('should handle empty state', async ({ page }) => {
        // Мокаем пустой ответ
        await page.route('**/api/settlement-schemes**', route => {
            route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify([])
            });
        });
        
        // Переходим на страницу
        await page.goto('/pasiv/settlement-schemes');
        
        // Ждем загрузки
        await page.waitForSelector('.settlement-schemes', { timeout: 10000 });
        
        // Проверяем сообщение о пустом состоянии
        await expect(page.locator('text=Схемы расчетов не найдены')).toBeVisible();
        await expect(page.locator('button:has-text("Добавить первую схему")')).toBeVisible();
    });

    test('should handle pagination', async ({ page }) => {
        // Ждем загрузки таблицы
        await page.waitForSelector('[data-testid="schemes-table"]', { timeout: 10000 });
        
        // Проверяем наличие пагинации
        const paginator = page.locator('.p-paginator');
        if (await paginator.isVisible()) {
            await expect(paginator).toBeVisible();
        }
    });
});

test.describe('SettlementSchemes Component - Error Handling', () => {
    test('should handle loading errors gracefully', async ({ page }) => {
        // Блокируем сетевые запросы для имитации ошибки
        await page.route('**/api/settlement-schemes**', route => route.abort());
        
        // Переходим на страницу
        await page.goto('/pasiv/settlement-schemes');
        
        // Проверяем, что страница не сломалась
        await expect(page.locator('h2:has-text("Схемы расчетов")')).toBeVisible();
    });

    test('should handle form validation', async ({ page }) => {
        // Открываем диалог добавления
        await page.click('button:has-text("Добавить схему")');
        await page.waitForSelector('.p-dialog-header:has-text("Добавить схему расчетов")');
        
        // Пытаемся сохранить пустую форму
        await page.click('button:has-text("Сохранить")');
        
        // Форма должна остаться открытой (валидация не пройдена)
        await expect(page.locator('.p-dialog-header:has-text("Добавить схему расчетов")')).toBeVisible();
    });
});
