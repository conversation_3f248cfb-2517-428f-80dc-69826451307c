import { test, expect } from '@playwright/test';

test.describe('RevenueReport Component', () => {
    test.beforeEach(async ({ page }) => {
        // Переходим на страницу отчета по выручке
        await page.goto('/pasiv/financial-reports/revenue');
        
        // Ждем загрузки страницы
        await page.waitForSelector('.revenue-report', { timeout: 10000 });
    });

    test('should display revenue report page', async ({ page }) => {
        // Проверяем заголовок страницы
        await expect(page.locator('h2:has-text("Отчет по выручке")')).toBeVisible();
        
        // Проверяем кнопки экспорта
        await expect(page.locator('button:has-text("Excel")')).toBeVisible();
        await expect(page.locator('button:has-text("PDF")')).toBeVisible();
    });

    test('should display report parameters form', async ({ page }) => {
        // Проверяем заголовок формы параметров
        await expect(page.locator('h3:has-text("Параметры отчета")')).toBeVisible();
        
        // Проверяем поля формы
        await expect(page.locator('label:has-text("Период с")')).toBeVisible();
        await expect(page.locator('label:has-text("Период по")')).toBeVisible();
        await expect(page.locator('label:has-text("Организация")')).toBeVisible();
        await expect(page.locator('label:has-text("Группировка")')).toBeVisible();
        
        // Проверяем чекбокс детализации
        await expect(page.locator('label:has-text("Включить детализацию")')).toBeVisible();
        
        // Проверяем кнопку формирования отчета
        await expect(page.locator('button:has-text("Сформировать отчет")')).toBeVisible();
    });

    test('should display default parameter values', async ({ page }) => {
        // Проверяем, что поля календаря заполнены
        const dateFromInput = page.locator('.p-calendar input').first();
        const dateToInput = page.locator('.p-calendar input').last();
        
        await expect(dateFromInput).not.toHaveValue('');
        await expect(dateToInput).not.toHaveValue('');
        
        // Проверяем, что чекбокс детализации включен по умолчанию
        const detailsCheckbox = page.locator('input[type="checkbox"]');
        await expect(detailsCheckbox).toBeChecked();
    });

    test('should display summary information', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем заголовок сводной информации
        await expect(page.locator('h3:has-text("Сводная информация")')).toBeVisible();
        
        // Проверяем карточки статистики
        await expect(page.locator('.stat-card:has-text("Общая выручка")')).toBeVisible();
        await expect(page.locator('.stat-card:has-text("Транзакций")')).toBeVisible();
        await expect(page.locator('.stat-card:has-text("Средний чек")')).toBeVisible();
        await expect(page.locator('.stat-card:has-text("Организаций")')).toBeVisible();
        
        // Проверяем отображение значений
        await expect(page.locator('text=/2\.45M ₽|2\.450\.000 ₽/')).toBeVisible();
        await expect(page.locator('text=/15\.420|15,420/')).toBeVisible();
    });

    test('should display detailed breakdown table', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем заголовок детализации
        await expect(page.locator('h3:has-text("Детализация по организациям")')).toBeVisible();
        
        // Проверяем заголовки колонок таблицы
        await expect(page.locator('th:has-text("Организация")')).toBeVisible();
        await expect(page.locator('th:has-text("Выручка")')).toBeVisible();
        await expect(page.locator('th:has-text("Транзакций")')).toBeVisible();
        await expect(page.locator('th:has-text("Средний чек")')).toBeVisible();
        await expect(page.locator('th:has-text("Доля %")')).toBeVisible();
    });

    test('should display organization data in table', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем наличие данных организаций
        await expect(page.locator('text=ООО "Транспорт Плюс"')).toBeVisible();
        await expect(page.locator('text=ИП Иванов И.И.')).toBeVisible();
        await expect(page.locator('text=ООО "Городской транспорт"')).toBeVisible();
        
        // Проверяем отображение процентных долей с прогресс-барами
        const progressBars = page.locator('.bg-blue-500');
        await expect(progressBars).toHaveCountGreaterThan(0);
    });

    test('should change report parameters and regenerate', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Изменяем организацию
        await page.click('.p-dropdown');
        await page.waitForSelector('.p-dropdown-panel');
        await page.click('.p-dropdown-item:has-text("ООО \\"Транспорт Плюс\\"")');
        
        // Изменяем группировку
        await page.click('.p-dropdown >> nth=1');
        await page.waitForSelector('.p-dropdown-panel');
        await page.click('.p-dropdown-item:has-text("По дням")');
        
        // Формируем отчет
        await page.click('button:has-text("Сформировать отчет")');
        
        // Ждем обновления данных
        await page.waitForTimeout(1000);
        
        // Проверяем, что отчет обновился
        await expect(page.locator('.stat-card')).toBeVisible();
    });

    test('should change date range', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Изменяем дату "с"
        await page.fill('.p-calendar input >> nth=0', '01.01.2024');
        
        // Изменяем дату "по"
        await page.fill('.p-calendar input >> nth=1', '31.01.2024');
        
        // Формируем отчет
        await page.click('button:has-text("Сформировать отчет")');
        
        // Ждем обновления данных
        await page.waitForTimeout(1000);
        
        // Проверяем, что период в сводной информации обновился
        await expect(page.locator('text=/01\.01\.2024.*31\.01\.2024/')).toBeVisible();
    });

    test('should toggle details visibility', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем, что детализация видна
        await expect(page.locator('h3:has-text("Детализация по организациям")')).toBeVisible();
        
        // Отключаем детализацию
        await page.uncheck('input[type="checkbox"]');
        
        // Формируем отчет
        await page.click('button:has-text("Сформировать отчет")');
        
        // Ждем обновления
        await page.waitForTimeout(1000);
        
        // Проверяем, что детализация скрыта
        await expect(page.locator('h3:has-text("Детализация по организациям")')).not.toBeVisible();
    });

    test('should export to Excel', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Кликаем на кнопку экспорта в Excel
        await page.click('button:has-text("Excel")');
        
        // Проверяем, что функция экспорта вызвана
        await page.waitForTimeout(500);
    });

    test('should export to PDF', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Кликаем на кнопку экспорта в PDF
        await page.click('button:has-text("PDF")');
        
        // Проверяем, что функция экспорта вызвана
        await page.waitForTimeout(500);
    });

    test('should display loading state during report generation', async ({ page }) => {
        // Переходим на страницу
        await page.goto('/pasiv/financial-reports/revenue');
        
        // Проверяем индикатор загрузки (если он появляется)
        const loadingIndicator = page.locator('.pi-spinner');
        if (await loadingIndicator.isVisible()) {
            await expect(loadingIndicator).toBeVisible();
            await expect(page.locator('text=Формирование отчета')).toBeVisible();
        }
        
        // Ждем завершения загрузки
        await page.waitForSelector('.revenue-report', { timeout: 10000 });
    });

    test('should display formatted currency values', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем форматирование валюты (рубли)
        const currencyValues = page.locator('text=/₽/');
        await expect(currencyValues).toHaveCountGreaterThan(0);
        
        // Проверяем конкретные форматированные значения
        await expect(page.locator('text=/2\.45M ₽|2\.450\.000 ₽/')).toBeVisible();
    });

    test('should display formatted numbers', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем форматирование чисел
        const formattedNumbers = page.locator('text=/15\.420|15,420/');
        await expect(formattedNumbers).toBeVisible();
    });

    test('should display percentage values with progress bars', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем отображение процентных значений
        await expect(page.locator('text=/60\.0%/')).toBeVisible();
        await expect(page.locator('text=/30\.0%/')).toBeVisible();
        await expect(page.locator('text=/10\.0%/')).toBeVisible();
        
        // Проверяем прогресс-бары
        const progressBars = page.locator('.bg-blue-500');
        await expect(progressBars).toHaveCountGreaterThan(0);
    });

    test('should display stat cards with hover effects', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Наводим курсор на первую карточку статистики
        const firstStatCard = page.locator('.stat-card').first();
        await firstStatCard.hover();
        
        // Проверяем, что карточка реагирует на hover
        await expect(firstStatCard).toBeVisible();
        await page.waitForTimeout(300); // Ждем завершения анимации
    });

    test('should handle table sorting', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем наличие сортируемых колонок
        const sortableHeaders = page.locator('th[aria-sort], th.p-sortable-column');
        const headerCount = await sortableHeaders.count();
        
        if (headerCount > 0) {
            // Кликаем на заголовок для сортировки
            await sortableHeaders.first().click();
            
            // Ждем применения сортировки
            await page.waitForTimeout(500);
        }
    });

    test('should maintain responsive layout', async ({ page }) => {
        // Ждем загрузки данных отчета
        await page.waitForSelector('.stat-card', { timeout: 10000 });
        
        // Проверяем адаптивность layout
        await expect(page.locator('.grid')).toBeVisible();
        
        // Проверяем колонки
        const columns = page.locator('.col-12');
        await expect(columns).toHaveCountGreaterThan(0);
    });
});

test.describe('RevenueReport Component - Error Handling', () => {
    test('should handle loading errors gracefully', async ({ page }) => {
        // Блокируем сетевые запросы для имитации ошибки
        await page.route('**/api/reports/revenue**', route => route.abort());
        
        // Переходим на страницу
        await page.goto('/pasiv/financial-reports/revenue');
        
        // Проверяем, что страница не сломалась
        await expect(page.locator('h2:has-text("Отчет по выручке")')).toBeVisible();
        await expect(page.locator('h3:has-text("Параметры отчета")')).toBeVisible();
    });

    test('should handle empty data gracefully', async ({ page }) => {
        // Мокаем пустой ответ
        await page.route('**/api/reports/revenue**', route => {
            route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify({
                    summary: {
                        totalRevenue: 0,
                        totalTransactions: 0,
                        averageTicket: 0,
                        period: '01.02.2024 - 29.02.2024'
                    },
                    details: [],
                    chartData: { labels: [], datasets: [] }
                })
            });
        });
        
        // Переходим на страницу
        await page.goto('/pasiv/financial-reports/revenue');
        
        // Ждем загрузки
        await page.waitForSelector('.revenue-report', { timeout: 10000 });
        
        // Проверяем, что компонент отображается с нулевыми значениями
        await expect(page.locator('.stat-card')).toHaveCount(4);
    });

    test('should handle form validation', async ({ page }) => {
        // Переходим на страницу
        await page.goto('/pasiv/financial-reports/revenue');
        await page.waitForSelector('.revenue-report', { timeout: 10000 });
        
        // Очищаем дату "с"
        await page.fill('.p-calendar input >> nth=0', '');
        
        // Пытаемся сформировать отчет
        await page.click('button:has-text("Сформировать отчет")');
        
        // Проверяем, что форма остается видимой (валидация не прошла)
        await expect(page.locator('h3:has-text("Параметры отчета")')).toBeVisible();
    });
});
