import { test, expect } from '@playwright/test';

test.describe('FinancialReports Component', () => {
    test.beforeEach(async ({ page }) => {
        // Переходим на страницу финансовых отчетов
        await page.goto('/pasiv/financial-reports');
        
        // Ждем загрузки страницы
        await page.waitForSelector('.financial-reports-container', { timeout: 10000 });
    });

    test('should display financial reports page', async ({ page }) => {
        // Проверяем заголовок страницы
        await expect(page.locator('h1:has-text("Финансовые отчеты")')).toBeVisible();
        await expect(page.locator('p:has-text("Отчеты по финансовым показателям системы ПАСИВ")')).toBeVisible();
        
        // Проверяем иконку
        await expect(page.locator('.pi-chart-line')).toBeVisible();
    });

    test('should display navigation panel', async ({ page }) => {
        // Проверяем навигационную панель
        await expect(page.locator('.navigation-panel')).toBeVisible();
        await expect(page.locator('h3:has-text("Отчеты")')).toBeVisible();
        
        // Проверяем пункты меню
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Отчет по выручке")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Отчет по комиссиям")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Сводный финансовый отчет")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Отчет по взаиморасчетам")')).toBeVisible();
        await expect(page.locator('.navigation-menu .p-menuitem-link:has-text("Отчет по агентским вознаграждениям")')).toBeVisible();
    });

    test('should display dashboard with report cards', async ({ page }) => {
        // Проверяем дашборд отчетов
        await expect(page.locator('.reports-dashboard')).toBeVisible();
        
        // Проверяем карточки отчетов
        await expect(page.locator('.report-card:has-text("Отчет по выручке")')).toBeVisible();
        await expect(page.locator('.report-card:has-text("Отчет по комиссиям")')).toBeVisible();
        await expect(page.locator('.report-card:has-text("Сводный отчет")')).toBeVisible();
        await expect(page.locator('.report-card:has-text("Взаиморасчеты")')).toBeVisible();
        await expect(page.locator('.report-card:has-text("Агентские вознаграждения")')).toBeVisible();
        
        // Проверяем описания карточек
        await expect(page.locator('p:has-text("Анализ выручки по организациям и периодам")')).toBeVisible();
        await expect(page.locator('p:has-text("Анализ комиссионных доходов")')).toBeVisible();
        await expect(page.locator('p:has-text("Общие финансовые показатели")')).toBeVisible();
    });

    test('should display quick statistics', async ({ page }) => {
        // Проверяем раздел быстрой статистики
        await expect(page.locator('h3:has-text("Быстрая статистика")')).toBeVisible();
        
        // Проверяем статистические показатели
        await expect(page.locator('text=Общая выручка за месяц')).toBeVisible();
        await expect(page.locator('text=Комиссионные доходы')).toBeVisible();
        await expect(page.locator('text=Транзакций за день')).toBeVisible();
        await expect(page.locator('text=Активных организаций')).toBeVisible();
        
        // Проверяем отображение значений
        await expect(page.locator('text=/2\.45M ₽/')).toBeVisible();
        await expect(page.locator('text=/245K ₽/')).toBeVisible();
        await expect(page.locator('text=/15\.4K/')).toBeVisible();
        await expect(page.locator('text=/12/')).toBeVisible();
    });

    test('should navigate to revenue report via menu', async ({ page }) => {
        // Кликаем на пункт меню "Отчет по выручке"
        await page.click('.navigation-menu .p-menuitem-link:has-text("Отчет по выручке")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/pasiv\/financial-reports\/revenue/);
        
        // Проверяем, что загрузился компонент отчета по выручке
        await expect(page.locator('h2:has-text("Отчет по выручке")')).toBeVisible();
    });

    test('should navigate to revenue report via card click', async ({ page }) => {
        // Кликаем на карточку отчета по выручке
        await page.click('.report-card:has-text("Отчет по выручке")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/pasiv\/financial-reports\/revenue/);
        
        // Проверяем, что загрузился компонент отчета по выручке
        await expect(page.locator('h2:has-text("Отчет по выручке")')).toBeVisible();
    });

    test('should navigate to commission report', async ({ page }) => {
        // Кликаем на карточку отчета по комиссиям
        await page.click('.report-card:has-text("Отчет по комиссиям")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/pasiv\/financial-reports\/commissions/);
    });

    test('should navigate to summary report', async ({ page }) => {
        // Кликаем на карточку сводного отчета
        await page.click('.report-card:has-text("Сводный отчет")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/pasiv\/financial-reports\/summary/);
    });

    test('should navigate to settlements report', async ({ page }) => {
        // Кликаем на карточку отчета по взаиморасчетам
        await page.click('.report-card:has-text("Взаиморасчеты")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/pasiv\/financial-reports\/settlements/);
    });

    test('should navigate to agent rewards report', async ({ page }) => {
        // Кликаем на карточку отчета по агентским вознаграждениям
        await page.click('.report-card:has-text("Агентские вознаграждения")');
        
        // Проверяем, что URL изменился
        await expect(page).toHaveURL(/\/pasiv\/financial-reports\/agent-rewards/);
    });

    test('should display hover effects on report cards', async ({ page }) => {
        // Наводим курсор на первую карточку отчета
        const firstCard = page.locator('.report-card').first();
        await firstCard.hover();
        
        // Проверяем, что карточка реагирует на hover (CSS анимация)
        await expect(firstCard).toBeVisible();
        
        // Можно проверить изменение стилей, если они применяются через классы
        await page.waitForTimeout(300); // Ждем завершения анимации
    });

    test('should display splitter layout correctly', async ({ page }) => {
        // Проверяем наличие Splitter
        await expect(page.locator('.p-splitter')).toBeVisible();
        
        // Проверяем панели
        await expect(page.locator('.p-splitter-panel').first()).toBeVisible(); // Навигация
        await expect(page.locator('.p-splitter-panel').last()).toBeVisible(); // Контент
        
        // Проверяем, что навигационная панель имеет правильный размер
        await expect(page.locator('.navigation-panel')).toBeVisible();
        await expect(page.locator('.content-panel')).toBeVisible();
    });

    test('should toggle navigation menu', async ({ page }) => {
        // Проверяем кнопку переключения навигации
        const toggleButton = page.locator('button:has(.pi-bars)');
        await expect(toggleButton).toBeVisible();
        
        // Кликаем на кнопку
        await toggleButton.click();
        
        // Здесь можно добавить проверки для изменения состояния навигации
        await page.waitForTimeout(300);
    });

    test('should display correct icons for report types', async ({ page }) => {
        // Проверяем иконки в карточках отчетов
        await expect(page.locator('.report-card .pi-chart-line')).toBeVisible(); // Выручка
        await expect(page.locator('.report-card .pi-percentage')).toBeVisible(); // Комиссии
        await expect(page.locator('.report-card .pi-chart-bar')).toBeVisible(); // Сводный
        await expect(page.locator('.report-card .pi-arrows-h')).toBeVisible(); // Взаиморасчеты
        await expect(page.locator('.report-card .pi-gift')).toBeVisible(); // Агентские вознаграждения
    });

    test('should display colored backgrounds for statistics', async ({ page }) => {
        // Проверяем цветные фоны для статистических показателей
        const statItems = page.locator('.stat-item');
        await expect(statItems).toHaveCountGreaterThan(0);
        
        // Проверяем, что статистические элементы видимы
        await expect(statItems.first()).toBeVisible();
    });

    test('should maintain responsive layout', async ({ page }) => {
        // Проверяем адаптивность layout
        await expect(page.locator('.grid')).toBeVisible();
        
        // Проверяем колонки
        const columns = page.locator('.col-12');
        await expect(columns).toHaveCountGreaterThan(0);
    });

    test('should handle back navigation to dashboard', async ({ page }) => {
        // Переходим к отчету по выручке
        await page.click('.report-card:has-text("Отчет по выручке")');
        await expect(page).toHaveURL(/\/pasiv\/financial-reports\/revenue/);
        
        // Возвращаемся к дашборду
        await page.goto('/pasiv/financial-reports');
        
        // Проверяем, что дашборд отображается
        await expect(page.locator('.reports-dashboard')).toBeVisible();
        await expect(page.locator('h3:has-text("Быстрая статистика")')).toBeVisible();
    });

    test('should display menu items with correct icons', async ({ page }) => {
        // Проверяем иконки в навигационном меню
        await expect(page.locator('.navigation-menu .pi-chart-line')).toBeVisible();
        await expect(page.locator('.navigation-menu .pi-percentage')).toBeVisible();
        await expect(page.locator('.navigation-menu .pi-chart-bar')).toBeVisible();
        await expect(page.locator('.navigation-menu .pi-arrows-h')).toBeVisible();
        await expect(page.locator('.navigation-menu .pi-gift')).toBeVisible();
    });
});

test.describe('FinancialReports Component - Error Handling', () => {
    test('should handle component loading errors gracefully', async ({ page }) => {
        // Переходим на страницу
        await page.goto('/pasiv/financial-reports');
        
        // Проверяем, что основные элементы загрузились
        await expect(page.locator('h1:has-text("Финансовые отчеты")')).toBeVisible();
        await expect(page.locator('.navigation-panel')).toBeVisible();
    });

    test('should handle navigation errors', async ({ page }) => {
        // Переходим на несуществующий подотчет
        await page.goto('/pasiv/financial-reports/nonexistent');
        
        // Проверяем, что основная структура остается
        await expect(page.locator('.financial-reports-container')).toBeVisible();
        await expect(page.locator('.navigation-panel')).toBeVisible();
    });

    test('should handle router view errors', async ({ page }) => {
        // Переходим на страницу отчетов
        await page.goto('/pasiv/financial-reports');
        
        // Проверяем, что router-view работает корректно
        await expect(page.locator('.content-panel')).toBeVisible();
        
        // Переходим к отчету и обратно
        await page.click('.report-card:has-text("Отчет по выручке")');
        await page.goBack();
        
        // Проверяем, что дашборд восстановился
        await expect(page.locator('.reports-dashboard')).toBeVisible();
    });
});
