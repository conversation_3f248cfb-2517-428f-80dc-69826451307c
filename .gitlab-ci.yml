stages:
  - aie
  - build
  - testing
  - deploy
  - deploy-prod

variables:
  DOCKER_REPOSITORY_ADDR: $DOCKER_REPOSITORY_ADDR
  GIT_SUBMODULE_STRATEGY: recursive
  KUBEVALURL: "https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz"

.default-tags:
  tags:
    - docker

.tag-generator:
  script:
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi

.aie-template:
  stage: aie
  extends: .default-tags
  rules:
    - if: '$AIE_ENABLE == "true"'
      when: always
    - when: never
  script:
    - docker run --rm
      -v $(pwd)/$CI_PROJECT_NAME:/app:rw
      -u $(id -u):$(id -g)
      -v /var/aisa:/aisa:rw
      --workdir /app
      aisa-linux:latest
      aisa --project-settings-file $CI_PROJECT_NAME.aiproj
      --scan-target ./ --reports "HTML,JSON" --reports-folder ".report"
  artifacts:
    expire_in: 14 days
    paths:
      - $CI_PROJECT_NAME/.report/

.build-template:
  stage: build
  extends:
    - .default-tags
    - .tag-generator
  script:
    - cd $CI_PROJECT_NAME
    - docker build -t $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$TAG --pull -f Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$TAG
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

.deploy-template:
  stage: deploy
  extends:
    - .default-tags
    - .tag-generator
  script:
    - mkdir .kube
    - cat $KUBECONFIG > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd):/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $CI_PROJECT_NAME /apps/charts/$CI_PROJECT_NAME
      --install
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

.deploy-prod-template:
  stage: deploy-prod
  dependencies:
    - build
  extends:
    - .default-tags
    - .tag-generator
  when: manual
  script:
    - mkdir .kube
    - cat "$KUBECONFIG_PROD" > .kube/config
    - docker run 
      -u $(id -u):$(id -g)
      -v $(pwd):/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $CI_PROJECT_NAME /apps/charts/$CI_PROJECT_NAME
      --install
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --set env.keycloak.realm.url=$KEYCLOAK_REALM_URL_PROD
      --set env.keycloak.client_id=$KEYCLOAK_CLIENT_ID_PROD
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

aie-pasive-gate:
  extends: .aie-template
  variables:
    CI_PROJECT_NAME: "pasive-gate"
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^.*$/'

build-pasive-gate:
  extends: .build-template
  variables:
    CI_PROJECT_NAME: "pasive-gate"

deploy-pasive-gate:
  extends: .deploy-template
  variables:
    CI_PROJECT_NAME: "pasive-gate"

deploy-prod-pasive-gate:
  extends: .deploy-prod-template
  variables:
    CI_PROJECT_NAME: "pasive-gate"