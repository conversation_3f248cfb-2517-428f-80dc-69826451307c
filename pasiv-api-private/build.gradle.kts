import com.google.protobuf.gradle.*

plugins {
    idea
    `java-library`
    kotlin("jvm")
    id("com.google.protobuf") version libs.versions.protobufPlugin.get()
    `maven-publish`
}

group = "ru.sbertroika.pasiv"
version = rootProject.version
java.sourceCompatibility = JavaVersion.VERSION_17

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation("ru.sbertroika.common:common-api:1.0.1")
    implementation("ru.sbertroika.common:common-manifest:1.0.1")

    implementation(libs.grpcKotlinStub)
    implementation(libs.grpcProtobuf)
    implementation(libs.grpcStub)
    implementation(libs.protobufKotlin)
    implementation("com.google.protobuf:protobuf-java:${libs.versions.protobuf.get()}")

    //Kotlin
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${libs.versions.protoC.get()}"
    }

    generatedFilesBaseDir = "$projectDir/src/generated"

    plugins {
        id("grpc") { artifact = "io.grpc:protoc-gen-grpc-java:${libs.versions.grpc.get()}" }
        id("grpckt") { artifact = "io.grpc:protoc-gen-grpc-kotlin:${libs.versions.grpcKotlin.get()}:jdk8@jar" }
    }

    generateProtoTasks {
        all().forEach { task ->
            task.plugins {
                id("grpc") {
                    option("lite")
                }
                id("grpckt") {
                    option("lite")
                }
            }
            task.builtins {
                id("kotlin")
            }
        }
    }
}

tasks.named<Delete>("clean") {
    doFirst {
        delete("$projectDir/src/generated")
        delete("$buildDir/extracted-include-protos")
    }
}

// Задача для извлечения proto файлов из зависимостей
tasks.register<Copy>("extractProtoFromDeps") {
    from(configurations.compileClasspath.get().filter {
        it.name.contains("common-api") || it.name.contains("common-manifest")
    }.map { zipTree(it) }) {
        include("**/*.proto")
    }

    into("$buildDir/extracted-include-protos/main")

    doLast {
        println("Extracted proto files to $buildDir/extracted-include-protos/main")
        println("Extracted files:")
        fileTree("$buildDir/extracted-include-protos/main").filter { it.name.endsWith(".proto") }.forEach {
            println("  - ${it.name}")
        }
    }
}
kotlin {
    jvmToolchain(17)
}
publishing {
    repositories {
        maven {
            val releasesRepoUrl = "https://nexus.sbertroika.tech/repository/maven-releases/"
            val snapshotsRepoUrl = "https://nexus.sbertroika.tech/repository/maven-snapshots/"
            url = uri(if (version.toString().endsWith("SNAPSHOT")) snapshotsRepoUrl else releasesRepoUrl)

            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }

    publications {
        create<MavenPublication>("maven") {
            from(components["java"])
        }
    }
}