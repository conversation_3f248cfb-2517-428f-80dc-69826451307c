package ru.sbertroika.pasiv.gate.input.rest

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import ru.sbertroika.pasiv.gate.config.GlobalExceptionHandler
import ru.sbertroika.pasiv.gate.dto.*
import ru.sbertroika.pasiv.gate.output.service.OrganizationService
import ru.sbertroika.pasiv.gate.v1.Organization
import ru.sbertroika.pasiv.gate.v1.OrganizationResult

/**
 * Unit тесты для OrganizationRestController
 */
@WebMvcTest(OrganizationRestController::class)
@ContextConfiguration(classes = [OrganizationRestController::class, GlobalExceptionHandler::class])
class OrganizationRestControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockBean
    private lateinit var organizationService: OrganizationService

    private lateinit var sampleOrganizationDto: OrganizationDto
    private lateinit var sampleAddressDto: AddressDto
    private lateinit var sampleOrganizationWithAddressesDto: OrganizationWithAddressesDto

    @BeforeEach
    fun setUp() {
        // Создание тестовых данных
        sampleAddressDto = AddressDto(
            id = "address-123",
            name = "Головной офис",
            index = 123456,
            country = "Россия",
            region = "Москва",
            city = "Москва",
            street = "ул. Ленина",
            house = "1",
            isDeleted = false
        )

        sampleOrganizationDto = OrganizationDto(
            id = "org-123",
            name = "ООО \"Тестовая организация\"",
            shortName = "Тестовая",
            inn = "1234567890",
            kpp = "123456789",
            ogrn = "1234567890123",
            isDeleted = false
        )

        sampleOrganizationWithAddressesDto = OrganizationWithAddressesDto(
            organization = sampleOrganizationDto,
            addressLegal = sampleAddressDto
        )
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `createOrganization should return 201 when organization is created successfully`() {
        // Arrange
        coEvery { organizationService.createOrganization(any(), any()) } returns Either.Right(Unit)

        // Act & Assert
        mockMvc.perform(
            post("/api/v1/organizations")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sampleOrganizationWithAddressesDto))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.error").doesNotExist())
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `createOrganization should return 400 when service returns error`() {
        // Arrange
        coEvery { organizationService.createOrganization(any(), any()) } returns Either.Left(Error("Service error"))

        // Act & Assert
        mockMvc.perform(
            post("/api/v1/organizations")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sampleOrganizationWithAddressesDto))
        )
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.error.code").value("SERVICE_ERROR"))
            .andExpect(jsonPath("$.error.message").value("Service error"))
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `updateOrganization should return 200 when organization is updated successfully`() {
        // Arrange
        coEvery { organizationService.updateOrganization(any(), any()) } returns Either.Right(Unit)

        // Act & Assert
        mockMvc.perform(
            put("/api/v1/organizations/org-123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sampleOrganizationDto))
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.error").doesNotExist())
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `updateOrganization should return 404 when organization not found`() {
        // Arrange
        coEvery { organizationService.updateOrganization(any(), any()) } returns Either.Left(Error("Organization not found"))

        // Act & Assert
        mockMvc.perform(
            put("/api/v1/organizations/org-123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sampleOrganizationDto))
        )
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.error.code").value("SERVICE_ERROR"))
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `getOrganizations should return 200 with organizations list`() {
        // Arrange
        val grpcOrganization = Organization.newBuilder()
            .setId("org-123")
            .setName("ООО \"Тестовая организация\"")
            .setShortName("Тестовая")
            .setInn("1234567890")
            .setKpp("123456789")
            .setOgrn("1234567890123")
            .setIsDeleted(false)
            .build()

        val organizationResult = OrganizationResult.newBuilder()
            .addOrganization(grpcOrganization)
            .build()

        coEvery { organizationService.organizationList(any()) } returns Either.Right(organizationResult)

        // Act & Assert
        mockMvc.perform(
            get("/api/v1/organizations")
                .param("page", "0")
                .param("size", "20")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.content").isArray)
            .andExpect(jsonPath("$.content[0].id").value("org-123"))
            .andExpect(jsonPath("$.content[0].name").value("ООО \"Тестовая организация\""))
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `getOrganizationById should return 200 when organization found`() {
        // Arrange
        val grpcOrganization = Organization.newBuilder()
            .setId("org-123")
            .setName("ООО \"Тестовая организация\"")
            .setShortName("Тестовая")
            .setInn("1234567890")
            .setKpp("123456789")
            .setOgrn("1234567890123")
            .setIsDeleted(false)
            .build()

        coEvery { organizationService.getOrganization(any()) } returns Either.Right(grpcOrganization)

        // Act & Assert
        mockMvc.perform(get("/api/v1/organizations/org-123"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.id").value("org-123"))
            .andExpect(jsonPath("$.data.name").value("ООО \"Тестовая организация\""))
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `getOrganizationById should return 404 when organization not found`() {
        // Arrange
        coEvery { organizationService.getOrganization(any()) } returns Either.Left(Error("Organization not found"))

        // Act & Assert
        mockMvc.perform(get("/api/v1/organizations/org-123"))
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.error.code").value("SERVICE_ERROR"))
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `deleteOrganization should return 200 when organization deleted successfully`() {
        // Arrange
        coEvery { organizationService.deleteOrganization(any(), any()) } returns Either.Right(Unit)

        // Act & Assert
        mockMvc.perform(delete("/api/v1/organizations/org-123"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.error").doesNotExist())
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `recoverOrganization should return 200 when organization recovered successfully`() {
        // Arrange
        coEvery { organizationService.recoverOrganization(any(), any()) } returns Either.Right(Unit)

        // Act & Assert
        mockMvc.perform(post("/api/v1/organizations/org-123/recover"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.error").doesNotExist())
    }

    @Test
    fun `createOrganization should return 401 when user not authenticated`() {
        // Act & Assert
        mockMvc.perform(
            post("/api/v1/organizations")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sampleOrganizationWithAddressesDto))
        )
            .andExpect(status().isUnauthorized)
    }

    @Test
    @WithMockUser(roles = ["wrong_role"])
    fun `createOrganization should return 403 when user has wrong role`() {
        // Act & Assert
        mockMvc.perform(
            post("/api/v1/organizations")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sampleOrganizationWithAddressesDto))
        )
            .andExpect(status().isForbidden)
    }

    @Test
    @WithMockUser(roles = ["pasiv_console_admin"])
    fun `createOrganization should return 400 when request body is invalid`() {
        // Arrange
        val invalidOrganization = sampleOrganizationWithAddressesDto.copy(
            organization = sampleOrganizationDto.copy(name = "") // Пустое имя
        )

        // Act & Assert
        mockMvc.perform(
            post("/api/v1/organizations")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidOrganization))
        )
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.error.code").value("VALIDATION_ERROR"))
    }
}
