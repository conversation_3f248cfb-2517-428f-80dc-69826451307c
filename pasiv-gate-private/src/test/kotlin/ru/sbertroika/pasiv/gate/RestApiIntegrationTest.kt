package ru.sbertroika.pasiv.gate

import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/**
 * Интеграционный тест для проверки запуска приложения с REST API
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class RestApiIntegrationTest {

    @Test
    fun `application context should load successfully`() {
        // Этот тест проверяет, что Spring контекст загружается без ошибок
        // и все бины создаются корректно
    }
}
