package ru.sbertroika.pasiv.gate.output.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import ru.sbertroika.history.api.*
import java.io.Serializable
import java.sql.Timestamp
import java.util.*


data class AddressPK(
    val id: UUID? = null,
    val version: Int? = null
): Serializable


@Table(value = "address")
data class Address(

    @HistoryId
    @Column("a_id")
    var id: UUID? = null,

    @HistoryVersion
    @Column("a_version")
    var version: Int? = null,


    @HistoryVersionAt
    @Column("a_version_created_at")
    var versionCreatedAt: Timestamp? = null,


    @HistoryVersionBy
    @Column("a_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("a_name")
    var name: String? = null,

    @Column("a_index")
    var index: Int? = null,

    @Column("a_country")
    var country :String? = null,              // страна

    @Column("a_region")
    var region:String? = null,                        // область

    @Column("a_district")
    var district:String? = null,                       // район

    @Column("a_city")
    var city:String? = null,                            // город

    @Column("a_street")
    var street:String? = null,                   // улица

    @Column("a_house")
    var house:String? = null,                      // дом

    @Column("a_building_or_housing")
    var buildingOrHousing:String? = null,    // строение

    @Column("a_office_or_room")
    var  officeOrRoom :String? = null,          // офис комната

    @Column("a_longitude")
    var longitude: Double? = null,         // долгота

    @Column("a_latitude")
    var latitude: Double? = null ,         // широта

    @Column("a_comment")
    var comment :String? = null,             // комментарий

    @Column("a_oktmo")
    var oktmo:Long? =null,               // ОКТМО

    @Column("a_fiac")
    var fiac: String? = null,                 // ФИАС

    @HistoryStatus
    @Column("a_is_deleted")
    var isDeleted:Boolean = false
)
