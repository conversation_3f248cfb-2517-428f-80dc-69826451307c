package ru.sbertroika.pasiv.gate.output.service

import arrow.core.Either
import ru.sbertroika.common.v1.HistoryResult
import ru.sbertroika.pasiv.gate.v1.*

interface ContactService {
    suspend fun createContact(contact: Contact, userId: String): Either<Error, Unit>

    suspend fun updateContact(contact: Contact, userId: String): Either<Error, Unit>

    suspend fun contactList(request: ContactListRequest): Either<Error, ContactListResult>
    suspend fun getContact(request: ByIdRequest): Either<Error, Contact>
    suspend fun getHistory(request: ByIdWithPaginationRequest): Either<Error, HistoryResult>
    suspend fun deleteContact(request: ByIdRequest, userId: String): Either<Error, Unit>
    suspend fun recoverContact(request: ByIdRequest, userId: String): Either<Error, Unit>
}