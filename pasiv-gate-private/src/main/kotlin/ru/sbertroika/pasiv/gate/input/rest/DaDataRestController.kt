package ru.sbertroika.pasiv.gate.input.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.annotation.Secured
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import ru.sbertroika.pasiv.gate.dto.*
import ru.sbertroika.pasiv.gate.mapper.GrpcToRestMapper
import ru.sbertroika.pasiv.gate.output.service.DaDataService
import javax.validation.constraints.NotBlank
import javax.validation.constraints.Pattern

/**
 * REST контроллер для работы с подсказками DaData
 */
@RestController
@RequestMapping("/api/v1/dadata")
@Tag(name = "DaData", description = "API для получения подсказок организаций из DaData")
@Validated
@Secured(value = ["ROLE_pasiv_console_admin"])
class DaDataRestController(
    private val daDataService: DaDataService
) {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    @Operation(
        summary = "Получение подсказки организации по ИНН",
        description = "Возвращает подсказку организации из DaData по указанному ИНН"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Подсказка организации получена успешно",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректный ИНН"
            ),
            ApiResponse(
                responseCode = "404",
                description = "Организация не найдена в DaData"
            ),
            ApiResponse(
                responseCode = "401",
                description = "Не авторизован"
            ),
            ApiResponse(
                responseCode = "403",
                description = "Недостаточно прав доступа"
            )
        ]
    )
    @GetMapping("/organizations/hint")
    fun getOrganizationHintByInn(
        @Parameter(
            description = "ИНН организации (10 или 12 цифр)",
            example = "7733123456"
        )
        @RequestParam
        @NotBlank(message = "ИНН не может быть пустым")
        @Pattern(
            regexp = "^\\d{10}$|^\\d{12}$",
            message = "ИНН должен содержать 10 или 12 цифр"
        )
        inn: String
    ): ResponseEntity<ApiResponseDto<OrganizationHintDto>> = runBlocking {
        log.info("getOrganizationHintByInn: Getting organization hint for INN: $inn")

        val grpcRequest = GrpcToRestMapper.mapOrganizationHintRequestToGrpc(inn)

        daDataService.getOrganizationHint(grpcRequest).fold(
            { error ->
                log.error("getOrganizationHintByInn: Service error: ${error.message}")
                val status = when {
                    error.message.contains("not found", ignoreCase = true) -> HttpStatus.NOT_FOUND
                    error.message.contains("invalid", ignoreCase = true) -> HttpStatus.BAD_REQUEST
                    else -> HttpStatus.BAD_REQUEST
                }
                ResponseEntity.status(status)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { hintList ->
                log.info("getOrganizationHintByInn: Organization hint found for INN: $inn")
                val hint = if (hintList.organizationHintCount > 0) {
                    GrpcToRestMapper.mapOrganizationHintToDto(hintList.getOrganizationHint(0))
                } else {
                    null
                }
                if (hint != null) {
                    ResponseEntity.ok(ApiResponseDto(data = hint))
                } else {
                    ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponseDto(error = OperationErrorDto("NOT_FOUND", "Организация с ИНН $inn не найдена", "")))
                }
            }
        )
    }

    @Operation(
        summary = "Получение списка подсказок организаций",
        description = "Возвращает список подсказок организаций из DaData по списку ИНН"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Список подсказок получен успешно",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса"
            ),
            ApiResponse(
                responseCode = "401",
                description = "Не авторизован"
            ),
            ApiResponse(
                responseCode = "403",
                description = "Недостаточно прав доступа"
            )
        ]
    )
    @PostMapping("/organizations/hints")
    fun getOrganizationHints(
        @Parameter(description = "Список ИНН организаций")
        @RequestBody innList: List<@Pattern(
            regexp = "^\\d{10}$|^\\d{12}$",
            message = "ИНН должен содержать 10 или 12 цифр"
        ) String>
    ): ResponseEntity<ApiResponseDto<OrganizationHintListDto>> = runBlocking {
        log.info("getOrganizationHints: Getting organization hints for ${innList.size} INNs")

        if (innList.isEmpty()) {
            log.warn("getOrganizationHints: Empty INN list provided")
            return@runBlocking ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponseDto(error = OperationErrorDto("VALIDATION_ERROR", "Список ИНН не может быть пустым", "")))
        }

        if (innList.size > 100) {
            log.warn("getOrganizationHints: Too many INNs provided: ${innList.size}")
            return@runBlocking ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponseDto(error = OperationErrorDto("VALIDATION_ERROR", "Максимальное количество ИНН в запросе: 100", "")))
        }

        val hints = mutableListOf<OrganizationHintDto>()
        val errors = mutableListOf<String>()

        innList.forEach { inn ->
            val grpcRequest = GrpcToRestMapper.mapOrganizationHintRequestToGrpc(inn)
            daDataService.getOrganizationHint(grpcRequest).fold(
                { error ->
                    log.warn("getOrganizationHints: Error for INN $inn: ${error.message}")
                    errors.add("ИНН $inn: ${error.message}")
                },
                { hintList ->
                    hintList.organizationHintList.forEach { hint ->
                        hints.add(GrpcToRestMapper.mapOrganizationHintToDto(hint))
                    }
                }
            )
        }

        log.info("getOrganizationHints: Found ${hints.size} hints, ${errors.size} errors")

        val result = OrganizationHintListDto(organizationHints = hints)

        if (errors.isNotEmpty() && hints.isEmpty()) {
            // Все запросы завершились ошибкой
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", "Не удалось получить подсказки: ${errors.joinToString("; ")}", "")))
        } else {
            // Есть успешные результаты (возможно, с частичными ошибками)
            val errorMessage = if (errors.isNotEmpty()) {
                "Частичные ошибки: ${errors.joinToString("; ")}"
            } else null

            ResponseEntity.ok(ApiResponseDto(
                data = result,
                error = errorMessage?.let { OperationErrorDto("PARTIAL_ERROR", it, "") }
            ))
        }
    }

    @Operation(
        summary = "Валидация ИНН",
        description = "Проверяет корректность ИНН организации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Результат валидации",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса"
            )
        ]
    )
    @GetMapping("/organizations/validate-inn")
    fun validateInn(
        @Parameter(
            description = "ИНН для валидации",
            example = "7733123456"
        )
        @RequestParam
        @NotBlank(message = "ИНН не может быть пустым")
        inn: String
    ): ResponseEntity<ApiResponseDto<Map<String, Any>>> = runBlocking {
        log.info("validateInn: Validating INN: $inn")

        val isValid = inn.matches(Regex("^\\d{10}$|^\\d{12}$"))
        val length = inn.length
        val isLegalEntity = length == 10
        val isIndividualEntrepreneur = length == 12

        val result = mapOf(
            "inn" to inn,
            "isValid" to isValid,
            "length" to length,
            "isLegalEntity" to isLegalEntity,
            "isIndividualEntrepreneur" to isIndividualEntrepreneur,
            "errors" to if (!isValid) {
                listOf("ИНН должен содержать 10 цифр для юридических лиц или 12 цифр для ИП")
            } else {
                emptyList<String>()
            }
        )

        log.info("validateInn: INN $inn validation result: valid=$isValid")
        ResponseEntity.ok(ApiResponseDto(data = result))
    }
}
