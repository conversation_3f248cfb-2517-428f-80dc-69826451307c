package ru.sbertroika.pasiv.gate.output.service


import arrow.core.Either
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.sbertroika.common.v1.History
import ru.sbertroika.common.v1.HistoryResult
import ru.sbertroika.common.v1.PaginationResponse
import ru.sbertroika.history.lib.mapHistory
import ru.sbertroika.pasiv.gate.output.model.ProjectOrganization
import ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus
import ru.sbertroika.pasiv.gate.output.repository.AddressRepository
import ru.sbertroika.pasiv.gate.output.repository.OrganizationRepository
import ru.sbertroika.pasiv.gate.output.repository.ProjectOrganizationRepository
import ru.sbertroika.pasiv.gate.util.calcTotalPage
import ru.sbertroika.pasiv.gate.util.timestampNow
import ru.sbertroika.pasiv.gate.v1.*
import java.util.*

@Service
class OrganizationServiceImpl(
    private val repository: OrganizationRepository,
    private val addressRepository: AddressRepository,
    private val projectOrganizationRepository: ProjectOrganizationRepository
) : OrganizationService {

    override suspend fun createOrganization(organizationWithAddresses: OrganizationWithAddresses, userId: String): Either<Error, Unit>  {
        return try {
            val organization = organizationWithAddresses.organization
            val address = organizationWithAddresses.addressLegal

            val legalAddress = saveAddress(address, userId)
            val actualAddress = if(organizationWithAddresses.hasAddressActual()) saveAddress(organizationWithAddresses.addressActual, userId) else null
            val mailingAddress = if(organizationWithAddresses.hasAddressMailing()) saveAddress(organizationWithAddresses.addressMailing, userId) else null

            repository.save(
                ru.sbertroika.pasiv.gate.output.model.Organization(
                    version = 1,
                    versionCreatedAt = timestampNow(),
                    versionCreatedBy = UUID.fromString(userId),
                    oName = organization.name,
                    shortName = organization.shortName,
                    kpp = organization.kpp,
                    inn = organization.inn,
                    addressLegalId = legalAddress.id,
                    parentId = if(organization.parent == null || organization.parent.id.isNullOrEmpty()) null else UUID.fromString(organization.parent.id),
                    addressActualId = actualAddress?.id,
                    addressMailingId = mailingAddress?.id,
                    note = organization.note,
                    okpo = organization.okpo,
                    ogrn = organization.ogrn,
                    managerActionReason = organization.managerActionReason,
                    fioDirector = organization.fioDirector,
                    oktmo = organization.oktmo,
                    okved = organization.okved,
                )
            )
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun updateOrganization(organization: Organization, userId: String): Either<Error, Unit>  {
        return try {
            val org = repository.findById(organization.id.toString())
            if (org != null) {
                repository.save(
                    ru.sbertroika.pasiv.gate.output.model.Organization(
                        id = org.id,
                        version = org.version!! + 1,
                        versionCreatedBy = UUID.fromString(userId),
                        versionCreatedAt = timestampNow(),
                        oName = organization.name,
                        shortName = organization.shortName,
                        kpp = organization.kpp,
                        inn = organization.inn,
                        ogrn = organization.ogrn,
                        addressLegalId = if(organization.addressLegal.isNullOrEmpty()) null else UUID.fromString(organization.addressLegal),
                        parentId = if(organization.parent == null || organization.parent.id.isNullOrEmpty()) null else UUID.fromString(organization.parent.id),
                        addressActualId = if(organization.addressActual.isNullOrEmpty()) null else UUID.fromString(organization.addressActual),
                        addressMailingId = if(organization.addressMailing.isNullOrEmpty()) null else UUID.fromString(organization.addressMailing),
                        note = organization.note,
                        okpo = organization.okpo,
                        managerActionReason = organization.managerActionReason,
                        fioDirector = organization.fioDirector,
                        oktmo = organization.oktmo,
                        okved = organization.okved,
                    )
                )
                Either.Right(Unit)
            } else {
                Either.Left(Error("Organization not found"))
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun organizationList(request: OrganizationListRequest): Either<Error, OrganizationResult> {
        return try {
            if(request.hasPagination()) {
                val pagination = request.pagination
                if(request.hasFilter() &&
                    (request.filter.hasIsDeleted() ||
                            request.filter.hasName() ||
                            request.filter.hasInn() ||
                            request.filter.hasKpp())) {
                    val totalCount = repository.countAll(request.filter)
                    Either.Right(
                        OrganizationResult.newBuilder()
                            .setPagination(
                                PaginationResponse.newBuilder()
                                    .setPage(pagination.page)
                                    .setLimit(pagination.limit)
                                    .setTotalCount(totalCount)
                                    .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                                ).addAllOrganization(repository.findAll(request.filter, pagination.page, pagination.limit)
                           .map { mapOrganizationToGrpc(it) }
                           .toList())
                           .build()
                    )
                } else {
                    val totalCount = repository.countAll()
                    Either.Right(
                        OrganizationResult
                            .newBuilder()
                            .setPagination(
                                PaginationResponse.newBuilder()
                                    .setPage(pagination.page)
                                    .setLimit(pagination.limit)
                                    .setTotalCount(totalCount)
                                    .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                            )
                            .addAllOrganization(repository.findAll(pagination.page, pagination.limit)
                                .map { mapOrganizationToGrpc(it) }
                                .toList())
                            .build()
                    )
                }
            } else {
                if(request.hasFilter() &&
                    (request.filter.hasIsDeleted() ||
                            request.filter.hasName() ||
                            request.filter.hasInn() ||
                            request.filter.hasKpp())) {
                    Either.Right(OrganizationResult.newBuilder().addAllOrganization(repository.findAll(request.filter).map {
                        mapOrganizationToGrpc(it)
                    }.toList()).build())
                } else {
                    Either.Right(OrganizationResult.newBuilder().addAllOrganization(repository.findAll().map {
                        mapOrganizationToGrpc(it)
                    }.toList()).build())
                }
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun organizationListForProject(request: OrganizationListForProjectRequest): Either<Error, OrganizationResult> {
        return try {
            if(request.hasPagination()) {
                val pagination = request.pagination
                val totalCount = repository.countAllByProjectId(UUID.fromString(request.projectId))
                Either.Right(
                    OrganizationResult
                        .newBuilder()
                        .setPagination(
                            PaginationResponse.newBuilder()
                                .setPage(pagination.page)
                                .setLimit(pagination.limit)
                                .setTotalCount(totalCount)
                                .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                        )
                        .addAllOrganization(repository.findAllByProjectId(UUID.fromString(request.projectId), pagination.page * pagination.limit, pagination.limit)
                            .map { mapOrganizationToGrpc(it) }
                            .toList())
                        .build()
                )
            } else {
                Either.Right(OrganizationResult.newBuilder().addAllOrganization(
                    repository.findAllByProjectId(UUID.fromString(request.projectId)).map {
                    mapOrganizationToGrpc(it)
                }.toList()).build())
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }


    override suspend fun getOrganization(request: ByIdRequest): Either<Error, Organization> {
       return try {
           val org = if(request.hasVersion())
               repository.findByIdAndVersion(request.id, request.version)
           else repository.findById(request.id)
           if(org != null)
                Either.Right(mapOrganizationToGrpc(org))
           else
               Either.Left(Error("Organization with id=${request.id} no found"))
       } catch (e: Exception) {
           Either.Left(Error(e))
       }
    }

    override suspend fun addOrganizationInProject(
        request: OrganizationInProjectRequest,
        userId: String
    ): Either<Error, Unit> {
        return try {
            val results = projectOrganizationRepository.findByOrganizationIdAndProjectId(
                UUID.fromString(request.organizationId),
                UUID.fromString(request.projectId)
            ).toList()
            if(results.isNotEmpty()) {
                val result = results.last()
                projectOrganizationRepository.save(
                    result.copy(
                        version = result.version!! + 1,
                        versionCreatedAt = timestampNow(),
                        versionCreatedBy = UUID.fromString(userId),
                        status = ProjectOrganizationStatus.ACTIVE
                    )
                )
            } else {
                projectOrganizationRepository.save(
                    ProjectOrganization(
                        versionCreatedBy = UUID.fromString(userId),
                        status = ProjectOrganizationStatus.ACTIVE,
                        projectId = UUID.fromString(request.projectId),
                        organizationId = UUID.fromString(request.organizationId),
                        activeFrom = timestampNow()
                    )
                )
            }
            Either.Right(Unit)
        }catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun removeOrganizationInProject(
        request: OrganizationInProjectRequest,
        userId: String
    ): Either<Error, Unit> {
        return try {
            val results = projectOrganizationRepository.findByOrganizationIdAndProjectId(
                UUID.fromString(request.organizationId),
                UUID.fromString(request.projectId)
            ).toList()
            if(results.isNotEmpty()) {
                val result = results.last()
                projectOrganizationRepository.save(
                    result.copy(
                        version = result.version!! + 1,
                        versionCreatedAt = timestampNow(),
                        versionCreatedBy = UUID.fromString(userId),
                        status = ProjectOrganizationStatus.IS_DELETED
                    )
                )
            }
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun getHistory(request: ByIdWithPaginationRequest): Either<Error, HistoryResult> {
        return try {
            if(request.hasPagination()) {
                val pagination = request.pagination
                val totalCount = repository.getHistoryCount(request.id)
                val result = mapObjectToHistory(repository.getHistory(request.id, pagination.page * pagination.limit, pagination.limit).toList())
                Either.Right(
                    HistoryResult
                        .newBuilder()
                        .setPagination(
                            PaginationResponse.newBuilder()
                                .setPage(pagination.page)
                                .setLimit(pagination.limit)
                                .setTotalCount(totalCount)
                                .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                        )
                        .addAllHistory(result)
                        .build()
                )
            } else {
                val result = mapObjectToHistory(repository.getHistory(request.id).toList())
                Either.Right(HistoryResult.newBuilder()
                    .addAllHistory(result).build()
                )
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun recoverOrganization(request: ByIdRequest, userId: String): Either<Error, Unit> {
        return try {
            val result = repository.findDeletedById(request.id)
            if(result != null) {
                repository.save(
                    result.copy(
                        version = result.version!! + 1,
                        versionCreatedAt = timestampNow(),
                        versionCreatedBy = UUID.fromString(userId),
                        isDeleted = false
                    )
                )
                Either.Right(Unit)
            } else {
                Either.Left(Error("Organization not found by id ${request.id}"))
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun deleteOrganization(request: ByIdRequest, userId: String): Either<Error, Unit> {
        return try {
            repository.deleted(request.id, UUID.fromString(userId))
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    private suspend fun mapObjectToHistory(result: List<ru.sbertroika.pasiv.gate.output.model.Organization>): List<History> {
        return mapHistory(result) { id, version ->
            return@mapHistory repository.findByIdAndVersion(id, version)
        }
    }

    private fun mapOrganizationToGrpc(entity: ru.sbertroika.pasiv.gate.output.model.Organization ): Organization {
        val builder =  Organization
            .newBuilder()
            .setId(entity.id.toString())
            .setName(entity.oName)
            .setShortName(entity.shortName)
            .setInn(entity.inn)
            .setKpp(entity.kpp)
            .setAddressActual(if(entity.addressActualId!= null) entity.addressActualId.toString() else "")
            .setAddressLegal(if(entity.addressLegalId!= null)entity.addressLegalId.toString() else "")
            .setAddressMailing(if(entity.addressMailingId!= null)entity.addressMailingId.toString() else "")
            .setOkpo(entity.okpo?:"")
            .setOktmo(entity.oktmo?:"")
            .setOkved(entity.okved?:"")
            .setFioDirector(entity.fioDirector?:"")
            .setOgrn(entity.ogrn?:"")
            .setManagerActionReason(entity.managerActionReason?:"")
        if (entity.parentId != null)
            builder.setParent(Organization
                .newBuilder()
                .setName(entity.parentName)
                .setId(
                    entity.parentId.toString()
                ).build())
        return builder.build()
    }

    private suspend fun saveAddress(address: Address, userId: String): ru.sbertroika.pasiv.gate.output.model.Address {
        return if(address.id.isNullOrEmpty())
            addAddress(address, userId)
        else {
            val result = addressRepository.findById(address.id)
            if(result != null)
                addressRepository.save(
                    ru.sbertroika.pasiv.gate.output.model.Address(
                        id = result.id!!,
                        version = result.version!! + 1,
                        versionCreatedAt = timestampNow(),
                        versionCreatedBy = UUID.fromString(userId),
                        name = address.name,
                        longitude = address.longitude,
                        latitude = address.latitude,
                        isDeleted = address.isDeleted,
                        country = address.country,
                        region = address.region,
                        index = address.index,
                        district = address.district,
                        city = address.city,
                        street = address.street,
                        house = address.house,
                        comment = address.comment,
                        buildingOrHousing = address.buildingOrHousing,
                        officeOrRoom = address.officeOrRoom,
                        oktmo = address.oktmo,
                        fiac = address.fiac
                    )
                )
            else
                addAddress(address, userId)
        }
    }

    private suspend fun addAddress(address: Address, userId: String): ru.sbertroika.pasiv.gate.output.model.Address {
        return addressRepository.save(
            ru.sbertroika.pasiv.gate.output.model.Address(
                id = UUID.randomUUID(),
                version = 1,
                versionCreatedAt = timestampNow(),
                versionCreatedBy = UUID.fromString(userId),
                name = address.name,
                longitude = address.longitude,
                latitude = address.latitude,
                isDeleted = address.isDeleted,
                country = address.country,
                region = address.region,
                index = address.index,
                district = address.district,
                city = address.city,
                street = address.street,
                house = address.house,
                comment = address.comment,
                buildingOrHousing = address.buildingOrHousing,
                officeOrRoom = address.officeOrRoom,
                oktmo = address.oktmo,
                fiac = address.fiac
            )
        )
    }

    companion object {
        //TODO вынести в общее пространство
        const val SYSTEM_USER_ID = "00000000-f04d-40dd-8829-598f62ad47f5"
    }

}