package ru.sbertroika.pasiv.gate.output.service

import arrow.core.Either
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.sbertroika.common.manifest.v1.ManifestRequest
import ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv
import ru.sbertroika.common.manifest.v1.pasiv.manifestPasiv
import ru.sbertroika.common.manifest.v1.pasiv.manifestPasivDict
import ru.sbertroika.common.manifest.v1.pasiv.organization
import ru.sbertroika.pasiv.gate.output.model.Address
import ru.sbertroika.pasiv.gate.output.repository.AddressRepository
import ru.sbertroika.pasiv.gate.output.repository.OrganizationCrudRepository
import java.util.*

@Service
class ManifestServiceImpl(
    private val organizationRepository: OrganizationCrudRepository,
    private val addressRepository: AddressRepository,
) : ManifestService {

    override suspend fun getManifest(request: ManifestRequest): Either<Throwable, ManifestPasiv> = Either.catch {
        manifestPasiv {
            dict = manifestPasivDict {
                organization += organizationRepository.findAllByProjectId(UUID.fromString(request.projectId)).map { org ->
                    organization {
                        id = org.id.toString()
                        name = org.oName!!
                        shortName = org.shortName!!
                        inn = org.inn!!
                        kpp = org.kpp!!
                        address = if(org.addressLegalId == null) "" else addressAsString(addressRepository.findById(org.addressLegalId.toString()))
                        paymentPlace = "Разъездная торговля"
                    }
                }.toList()
            }
        }
    }

    private fun addressAsString(address: Address?): String  {
        var returnStr = ""
        if(address != null) {
            returnStr = addressFieldToString(address.country, returnStr)
            returnStr = addressFieldToString(address.region, returnStr)
            returnStr = addressFieldToString(address.district, returnStr)
            returnStr = addressFieldToString(address.city, returnStr)
            returnStr = addressFieldToString(address.street, returnStr)
            returnStr = addressFieldToString(address.house, returnStr)
            returnStr = addressFieldToString(address.buildingOrHousing, returnStr)
            returnStr = addressFieldToString(address.officeOrRoom, returnStr)
        }
        return returnStr
    }

    private fun addressFieldToString(addressField: String?, returnedString: String): String {
        return if(addressField.isNullOrEmpty())
            returnedString
        else if (returnedString.isEmpty())
            addressField
        else "$returnedString, $addressField"
    }
}