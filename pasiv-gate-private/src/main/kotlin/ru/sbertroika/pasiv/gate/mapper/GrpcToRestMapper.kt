package ru.sbertroika.pasiv.gate.mapper

import ru.sbertroika.common.v1.HistoryResult
import ru.sbertroika.common.v1.OperationError
import ru.sbertroika.common.v1.PaginationResponse
import ru.sbertroika.pasiv.gate.dto.*
import ru.sbertroika.pasiv.gate.v1.*

/**
 * Утилиты для конвертации между gRPC моделями и REST DTO
 */
object GrpcToRestMapper {

    /**
     * Конвертация Organization из gRPC в DTO
     */
    fun mapOrganizationToDto(grpcOrganization: Organization): OrganizationDto {
        return OrganizationDto(
            id = grpcOrganization.id.takeIf { it.isNotEmpty() },
            parent = grpcOrganization.parent?.let { mapOrganizationToDto(it) },
            name = grpcOrganization.name,
            shortName = grpcOrganization.shortName,
            inn = grpcOrganization.inn,
            kpp = grpcOrganization.kpp,
            note = grpcOrganization.note.takeIf { it.isNotEmpty() },
            okpo = grpcOrganization.okpo.takeIf { it.isNotEmpty() },
            oktmo = grpcOrganization.oktmo.takeIf { it.isNotEmpty() },
            okved = grpcOrganization.okved.takeIf { it.isNotEmpty() },
            fioDirector = grpcOrganization.fioDirector.takeIf { it.isNotEmpty() },
            ogrn = grpcOrganization.ogrn,
            addressLegal = grpcOrganization.addressLegal.takeIf { it.isNotEmpty() },
            addressActual = grpcOrganization.addressActual.takeIf { it.isNotEmpty() },
            addressMailing = grpcOrganization.addressMailing.takeIf { it.isNotEmpty() },
            managerActionReason = grpcOrganization.managerActionReason.takeIf { it.isNotEmpty() },
            isDeleted = grpcOrganization.isDeleted
        )
    }

    /**
     * Конвертация OrganizationDto в gRPC Organization
     */
    fun mapOrganizationToGrpc(dto: OrganizationDto): Organization {
        val builder = Organization.newBuilder()
            .setName(dto.name)
            .setShortName(dto.shortName)
            .setInn(dto.inn)
            .setKpp(dto.kpp)
            .setOgrn(dto.ogrn)
            .setIsDeleted(dto.isDeleted)

        dto.id?.let { builder.setId(it) }
        dto.parent?.let { builder.setParent(mapOrganizationToGrpc(it)) }
        dto.note?.let { builder.setNote(it) }
        dto.okpo?.let { builder.setOkpo(it) }
        dto.oktmo?.let { builder.setOktmo(it) }
        dto.okved?.let { builder.setOkved(it) }
        dto.fioDirector?.let { builder.setFioDirector(it) }
        dto.addressLegal?.let { builder.setAddressLegal(it) }
        dto.addressActual?.let { builder.setAddressActual(it) }
        dto.addressMailing?.let { builder.setAddressMailing(it) }
        dto.managerActionReason?.let { builder.setManagerActionReason(it) }

        return builder.build()
    }

    /**
     * Конвертация OrganizationWithAddressesDto в gRPC OrganizationWithAddresses
     */
    fun mapOrganizationWithAddressesToGrpc(dto: OrganizationWithAddressesDto): OrganizationWithAddresses {
        val builder = OrganizationWithAddresses.newBuilder()
            .setOrganization(mapOrganizationToGrpc(dto.organization))
            .setAddressLegal(mapAddressToGrpc(dto.addressLegal))

        dto.addressActual?.let { builder.setAddressActual(mapAddressToGrpc(it)) }
        dto.addressMailing?.let { builder.setAddressMailing(mapAddressToGrpc(it)) }

        return builder.build()
    }

    /**
     * Конвертация Address из gRPC в DTO
     */
    fun mapAddressToDto(grpcAddress: Address): AddressDto {
        return AddressDto(
            id = grpcAddress.id.takeIf { it.isNotEmpty() },
            name = grpcAddress.name,
            index = grpcAddress.index.takeIf { it > 0 },
            country = grpcAddress.country.takeIf { it.isNotEmpty() },
            region = grpcAddress.region.takeIf { it.isNotEmpty() },
            district = grpcAddress.district.takeIf { it.isNotEmpty() },
            city = grpcAddress.city.takeIf { it.isNotEmpty() },
            street = grpcAddress.street.takeIf { it.isNotEmpty() },
            house = grpcAddress.house.takeIf { it.isNotEmpty() },
            buildingOrHousing = grpcAddress.buildingOrHousing.takeIf { it.isNotEmpty() },
            officeOrRoom = grpcAddress.officeOrRoom.takeIf { it.isNotEmpty() },
            longitude = grpcAddress.longitude.takeIf { it != 0.0 },
            latitude = grpcAddress.latitude.takeIf { it != 0.0 },
            comment = grpcAddress.comment.takeIf { it.isNotEmpty() },
            oktmo = grpcAddress.oktmo.takeIf { it > 0 },
            fiac = grpcAddress.fiac.takeIf { it.isNotEmpty() },
            isDeleted = grpcAddress.isDeleted
        )
    }

    /**
     * Конвертация AddressDto в gRPC Address
     */
    fun mapAddressToGrpc(dto: AddressDto): Address {
        val builder = Address.newBuilder()
            .setName(dto.name)
            .setIsDeleted(dto.isDeleted)

        dto.id?.let { builder.setId(it) }
        dto.index?.let { builder.setIndex(it) }
        dto.country?.let { builder.setCountry(it) }
        dto.region?.let { builder.setRegion(it) }
        dto.district?.let { builder.setDistrict(it) }
        dto.city?.let { builder.setCity(it) }
        dto.street?.let { builder.setStreet(it) }
        dto.house?.let { builder.setHouse(it) }
        dto.buildingOrHousing?.let { builder.setBuildingOrHousing(it) }
        dto.officeOrRoom?.let { builder.setOfficeOrRoom(it) }
        dto.longitude?.let { builder.setLongitude(it) }
        dto.latitude?.let { builder.setLatitude(it) }
        dto.comment?.let { builder.setComment(it) }
        dto.oktmo?.let { builder.setOktmo(it) }
        dto.fiac?.let { builder.setFiac(it) }

        return builder.build()
    }

    /**
     * Конвертация Contact из gRPC в DTO
     */
    fun mapContactToDto(grpcContact: Contact): ContactDto {
        return ContactDto(
            id = grpcContact.id.takeIf { it.isNotEmpty() },
            organizationId = grpcContact.organizationId,
            type = mapContactTypeToDto(grpcContact.type),
            value = grpcContact.value,
            isDeleted = grpcContact.isDeleted
        )
    }

    /**
     * Конвертация ContactDto в gRPC Contact
     */
    fun mapContactToGrpc(dto: ContactDto): Contact {
        return Contact.newBuilder()
            .apply { dto.id?.let { setId(it) } }
            .setOrganizationId(dto.organizationId)
            .setType(mapContactTypeToGrpc(dto.type))
            .setValue(dto.value)
            .setIsDeleted(dto.isDeleted)
            .build()
    }

    /**
     * Конвертация типа контакта из gRPC в DTO
     */
    fun mapContactTypeToDto(grpcType: ContactType): ContactTypeDto {
        return when (grpcType) {
            ContactType.PHONE -> ContactTypeDto.PHONE
            ContactType.EMAIL -> ContactTypeDto.EMAIL
            ContactType.UNRECOGNIZED -> throw IllegalArgumentException("Неизвестный тип контакта: $grpcType")
        }
    }

    /**
     * Конвертация типа контакта из DTO в gRPC
     */
    fun mapContactTypeToGrpc(dtoType: ContactTypeDto): ContactType {
        return when (dtoType) {
            ContactTypeDto.PHONE -> ContactType.PHONE
            ContactTypeDto.EMAIL -> ContactType.EMAIL
        }
    }

    /**
     * Конвертация PaginationResponse из gRPC в DTO
     */
    fun mapPaginationResponseToDto(grpcPagination: PaginationResponse): PaginationResponseDto {
        return PaginationResponseDto(
            page = grpcPagination.page,
            size = grpcPagination.limit,
            totalElements = grpcPagination.totalCount,
            totalPages = grpcPagination.totalPage
        )
    }

    /**
     * Конвертация PaginationRequestDto в gRPC PaginationRequest
     */
    fun mapPaginationRequestToGrpc(dto: PaginationRequestDto): ru.sbertroika.common.v1.PaginationRequest {
        return ru.sbertroika.common.v1.PaginationRequest.newBuilder()
            .setPage(dto.page)
            .setLimit(dto.size)
            .build()
    }

    /**
     * Конвертация OrganizationFilterDto в gRPC OrganizationFilter
     */
    fun mapOrganizationFilterToGrpc(dto: OrganizationFilterDto): OrganizationFilter {
        val builder = OrganizationFilter.newBuilder()

        dto.name?.let { builder.setName(it) }
        dto.inn?.let { builder.setInn(it) }
        dto.kpp?.let { builder.setKpp(it) }
        dto.isDeleted?.let { builder.setIsDeleted(it) }

        return builder.build()
    }

    /**
     * Конвертация OperationError из gRPC в DTO
     */
    fun mapOperationErrorToDto(grpcError: OperationError): OperationErrorDto {
        return OperationErrorDto(
            code = grpcError.code,
            message = grpcError.message,
            details = grpcError.details
        )
    }

    /**
     * Конвертация HistoryResult из gRPC в DTO
     */
    fun mapHistoryResultToDto(grpcHistory: HistoryResult): HistoryResultDto {
        return HistoryResultDto(
            pagination = grpcHistory.pagination?.let { mapPaginationResponseToDto(it) },
            items = grpcHistory.itemsList.map { item ->
                HistoryItemDto(
                    id = item.id,
                    version = item.version,
                    createdAt = item.createdAt.seconds,
                    createdBy = item.createdBy,
                    data = item.data
                )
            }
        )
    }

    /**
     * Конвертация AddressCreateOrDeleteDto в gRPC AddressCreateOrDelete
     */
    fun mapAddressCreateOrDeleteToGrpc(dto: AddressCreateOrDeleteDto): AddressCreateOrDelete {
        return AddressCreateOrDelete.newBuilder()
            .setAddress(mapAddressToGrpc(dto.address))
            .setType(mapAddressTypeToGrpc(dto.type))
            .setOrganizationId(dto.organizationId)
            .build()
    }

    /**
     * Конвертация типа адреса из DTO в gRPC
     */
    fun mapAddressTypeToGrpc(dtoType: AddressTypeDto): AddressType {
        return when (dtoType) {
            AddressTypeDto.LEGAL -> AddressType.LEGAL
            AddressTypeDto.ACTUAL -> AddressType.ACTUAL
            AddressTypeDto.MAILING -> AddressType.MAILING
        }
    }

    /**
     * Конвертация типа адреса из gRPC в DTO
     */
    fun mapAddressTypeToDto(grpcType: AddressType): AddressTypeDto {
        return when (grpcType) {
            AddressType.LEGAL -> AddressTypeDto.LEGAL
            AddressType.ACTUAL -> AddressTypeDto.ACTUAL
            AddressType.MAILING -> AddressTypeDto.MAILING
            AddressType.UNRECOGNIZED -> throw IllegalArgumentException("Неизвестный тип адреса: $grpcType")
        }
    }

    /**
     * Конвертация AddressListRequest из DTO в gRPC
     */
    fun mapAddressListRequestToGrpc(organizationId: String, pagination: PaginationRequestDto?): AddressListRequest {
        val builder = AddressListRequest.newBuilder()
            .setOrganizationId(organizationId)

        pagination?.let { builder.setPagination(mapPaginationRequestToGrpc(it)) }

        return builder.build()
    }

    /**
     * Конвертация ContactListRequest из DTO в gRPC
     */
    fun mapContactListRequestToGrpc(organizationId: String, pagination: PaginationRequestDto?): ContactListRequest {
        val builder = ContactListRequest.newBuilder()
            .setOrganizationId(organizationId)

        pagination?.let { builder.setPagination(mapPaginationRequestToGrpc(it)) }

        return builder.build()
    }

    /**
     * Конвертация OrganizationHintRequest из DTO в gRPC
     */
    fun mapOrganizationHintRequestToGrpc(inn: String): OrganizationHintRequest {
        return OrganizationHintRequest.newBuilder()
            .setInn(inn)
            .build()
    }

    /**
     * Конвертация OrganizationHintDto из gRPC в DTO
     */
    fun mapOrganizationHintToDto(grpcHint: OrganizationHint): OrganizationHintDto {
        return OrganizationHintDto(
            inn = grpcHint.inn,
            kpp = grpcHint.kpp.takeIf { it.isNotEmpty() },
            name = grpcHint.name,
            shortName = grpcHint.shortName.takeIf { it.isNotEmpty() },
            ogrn = grpcHint.ogrn.takeIf { it.isNotEmpty() },
            okpo = grpcHint.okpo.takeIf { it.isNotEmpty() },
            oktmo = grpcHint.oktmo.takeIf { it.isNotEmpty() },
            okved = grpcHint.okved.takeIf { it.isNotEmpty() },
            fioDirector = grpcHint.fioDirector.takeIf { it.isNotEmpty() },
            managerActionReason = grpcHint.managerActionReason.takeIf { it.isNotEmpty() },
            addressLegalHint = grpcHint.addressLegalHint?.let { mapAddressHintToDto(it) },
            contactHints = grpcHint.contactHintsList.map { mapContactHintToDto(it) }
        )
    }

    /**
     * Конвертация AddressHint из gRPC в DTO
     */
    fun mapAddressHintToDto(grpcHint: AddressHint): AddressHintDto {
        return AddressHintDto(
            index = grpcHint.index.takeIf { it > 0 },
            country = grpcHint.country.takeIf { it.isNotEmpty() },
            region = grpcHint.region,
            district = grpcHint.district.takeIf { it.isNotEmpty() },
            city = grpcHint.city,
            street = grpcHint.street.takeIf { it.isNotEmpty() },
            house = grpcHint.house,
            buildingOrHousing = grpcHint.buildingOrHousing.takeIf { it.isNotEmpty() },
            officeOrRoom = grpcHint.officeOrRoom.takeIf { it.isNotEmpty() },
            longitude = grpcHint.longitude.takeIf { it != 0.0 },
            latitude = grpcHint.latitude.takeIf { it != 0.0 },
            oktmo = grpcHint.oktmo.takeIf { it > 0 },
            fiac = grpcHint.fiac.takeIf { it.isNotEmpty() }
        )
    }

    /**
     * Конвертация ContactHint из gRPC в DTO
     */
    fun mapContactHintToDto(grpcHint: ContactHint): ContactHintDto {
        return ContactHintDto(
            type = mapContactTypeToDto(grpcHint.type),
            value = grpcHint.value
        )
    }
}
