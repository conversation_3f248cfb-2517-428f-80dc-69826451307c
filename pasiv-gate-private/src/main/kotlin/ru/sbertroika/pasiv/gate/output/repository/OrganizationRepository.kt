package ru.sbertroika.pasiv.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.pasiv.gate.output.model.Organization
import ru.sbertroika.pasiv.gate.output.model.OrganizationPK
import ru.sbertroika.pasiv.gate.util.timestampNow
import ru.sbertroika.pasiv.gate.v1.OrganizationFilter
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Repository
class OrganizationRepository(
    override val dbClient: DatabaseClient,
    override val repository: OrganizationCrudRepository
): AbstractRepository<Organization, OrganizationPK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM active_organization o WHERE o.o_is_deleted=false"

    override fun toEntity(t: Readable) = Organization (
        id  = t.get("o_id") as UUID,
        version = t.get("o_version") as Int,
        versionCreatedAt = Timestamp.valueOf(t.get("o_version_created_at") as LocalDateTime),
        versionCreatedBy = t.get("o_version_created_by") as UUID,
        oName = t.get("o_name") as String?,
        parentId = t.get("o_parent_id") as UUID?,
        parentName = if(t.get("o_parent_id") as UUID? != null) try { t.get("o_parent_name") as String? } catch (e: Exception){ null } else null,
        shortName = t.get("o_short_name") as String?,
        kpp = t.get("o_kpp") as String?,
        inn = t.get("o_inn") as String?,
        addressActualId = t.get("o_address_actual_id") as UUID?,
        addressLegalId = t.get("o_address_legal_id") as UUID,
        addressMailingId = t.get("o_address_mailing_id") as UUID?,
        okpo = t.get("o_okpo") as String?,
        ogrn = t.get("o_ogrn") as String?,
        oktmo = t.get("o_oktmo") as String?,
        okved = t.get("o_okved") as String?,
        note = t.get("o_note") as String?,
        fioDirector = t.get("o_fio_director") as String?,
        managerActionReason = t.get("o_manager_action_reason") as String?,
        isDeleted = t.get("o_is_deleted") as Boolean
    )

    override suspend fun findById(id: String): Organization? {
        return dbClient.sql("${getQuery()} AND o.o_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    suspend fun findDeletedById(id: String): Organization? {
        return dbClient.sql("SELECT * FROM active_organization o WHERE o.o_is_deleted=true AND o.o_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    suspend fun findByIdAndVersion(id: String, version: Long): Organization? {
        return dbClient.sql("select  * from  organization o WHERE o.o_id = '$id' AND o.o_version = $version")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Organization> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Organization> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    suspend fun findAllByProjectId(project: UUID): Flow<Organization> {
        return repository.findAllByProjectId(project)
    }

    suspend fun findAllByProjectId(project: UUID, offset: Int, limit: Int): Flow<Organization> {
        return repository.findAllByProjectId(project, offset, limit)
    }
    suspend fun countAllByProjectId(project: UUID): Int {
        return repository.countAllByProjectId(project)
    }

    suspend fun getHistory(id: String): Flow<Organization> {
        return dbClient.sql("select * from organization where o_id = '$id'")
            .map(::toEntity).flow()
    }

    suspend fun getHistoryCount(id: String):Int {
        return dbClient.sql("select count(*) from organization where o_id = '$id'").map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    suspend fun getHistory(id: String, offset: Int, limit: Int): Flow<Organization> {
        return dbClient.sql("select * from organization where o_id = '$id' OFFSET $offset LIMIT $limit")
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && !entity.isDeleted) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                isDeleted = true
            ))
        }
    }

    suspend fun findAll(filter: OrganizationFilter, page: Int, limit: Int): Flow<Organization> {
        val query = "${buildRequestByFilter(filter)} OFFSET ${page * limit} LIMIT $limit"
        return dbClient.sql(query).map(::toEntity).flow()
    }

    suspend fun countAll(filter: OrganizationFilter): Int {
        val query = buildRequestByFilter(filter, true)
        return dbClient.sql(query).map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    suspend fun findAll(filter: OrganizationFilter): Flow<Organization> {
        val query = buildRequestByFilter(filter)
        return dbClient.sql(query).map(::toEntity).flow()
    }

    private fun buildRequestByFilter(filter: OrganizationFilter, isCount: Boolean  = false): String {
        var query = if (filter.hasIsDeleted() && filter.isDeleted) {
            (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM active_organization o WHERE o.o_is_deleted=true"
        } else getQuery(isCount)
        if(filter.hasName() && !filter.name.isNullOrEmpty())
            query += " AND o.o_name ILIKE '%${filter.name}%'"
        if(filter.hasInn() && !filter.inn.isNullOrEmpty())
            query += " AND o.o_inn = '${filter.inn}'"
        if(filter.hasKpp() && !filter.kpp.isNullOrEmpty())
            query += " AND o.o_kpp = '${filter.kpp}'"

        return query
    }
}