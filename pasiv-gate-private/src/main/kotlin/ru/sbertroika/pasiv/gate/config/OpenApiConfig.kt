package ru.sbertroika.pasiv.gate.config

import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Contact
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.info.License
import io.swagger.v3.oas.models.security.SecurityRequirement
import io.swagger.v3.oas.models.security.SecurityScheme
import io.swagger.v3.oas.models.servers.Server
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class OpenApiConfig {

    @Bean
    fun customOpenAPI(): OpenAPI {
        return OpenAPI()
            .info(
                Info()
                    .title("PASIV Gate Private API")
                    .description("REST API для управления организациями, адресами, контактами, договорами и способами оплаты в системе ПАСИВ")
                    .version("1.0.0")
                    .contact(
                        Contact()
                            .name("СберТройка")
                            .email("<EMAIL>")
                            .url("https://sbertroika.tech")
                    )
                    .license(
                        License()
                            .name("Proprietary")
                            .url("https://sbertroika.tech/license")
                    )
            )
            .addServersItem(
                Server()
                    .url("http://localhost:8080")
                    .description("Development server")
            )
            .addServersItem(
                Server()
                    .url("https://dev-pasiv-gate-private.sbertroika.tech")
                    .description("Development environment")
            )
            .addServersItem(
                Server()
                    .url("https://pasiv-gate-private.sbertroika.tech")
                    .description("Production environment")
            )
            .addSecurityItem(
                SecurityRequirement().addList("bearerAuth")
            )
            .components(
                io.swagger.v3.oas.models.Components()
                    .addSecuritySchemes(
                        "bearerAuth",
                        SecurityScheme()
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT")
                            .description("JWT токен для аутентификации")
                    )
            )
    }
}
