package ru.sbertroika.pasiv.gate.util

import java.util.regex.Pattern


object LogUtil {
    const val HIDDEN_STRING = "***HIDDEN***"
    private val PATTERN_FOR_HEADERS = Pattern.compile(
        "^((([a-zA-Z_0-9-]*)credit([a-zA-Z_0-9-]*))|(([a-zA-Z_0-9-]*)(secret|key|token))|(credit|passwd|pwd|sign|set-cookie|authorization): )(.*)$",
        Pattern.CASE_INSENSITIVE
    )
    private val PATTERN_FOR_PARAMS = Pattern.compile(
        "((^|&)((([a-zA-Z_0-9-]*)credit([a-zA-Z_0-9-]*))|(([a-zA-Z_0-9-]*)(secret|key|token))|(credit|passwd|pwd|sign|set-cookie|authorization))=)([^&]*)",
        Pattern.CASE_INSENSITIVE
    )
    private val PATTERN_FOR_BODY = Pattern.compile(
        "(\"((([a-zA-Z_0-9-]*)credit([a-zA-Z_0-9-]*))|(([a-zA-Z_0-9-]*)(password|secret|key|userName))|(credit|passwd|pwd|sign|set-cookie|authorization))\":\")((?:\\\\\"|[^\"]){0,1000})",
        Pattern.CASE_INSENSITIVE
    )
    private val PATTERN_FOR_BODY_TOKEN = Pattern.compile(
        "(\"(([a-zA-Z_0-9-]*)token)\":\")([^\"]{0,10000})",
        Pattern.CASE_INSENSITIVE
    )

    fun sanitizeLog(str: String): String {
        return try {
            if (str.startsWith("{") && str.endsWith("}")) {
                sanitizeBody(str)
            } else {
                sanitizeParams(sanitizeHeaders(str))
            }
        } catch (e: Throwable) {
            println("Cannot sanitize logs:$e")
            str
        }
    }

    private fun sanitizeHeaders(str: String): String {
        return PATTERN_FOR_HEADERS.matcher(str).replaceAll("$1" + HIDDEN_STRING)
    }

    private fun sanitizeParams(str: String): String {
        return PATTERN_FOR_PARAMS.matcher(str).replaceAll("$1" + HIDDEN_STRING)
    }

    private fun sanitizeBody(str: String): String {
        val result = PATTERN_FOR_BODY.matcher(str).replaceAll("$1" + HIDDEN_STRING)
        return PATTERN_FOR_BODY_TOKEN.matcher(result).replaceAll("$1" + HIDDEN_STRING)
    }
}
