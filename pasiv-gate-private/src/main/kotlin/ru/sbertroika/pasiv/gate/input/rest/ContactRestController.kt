package ru.sbertroika.pasiv.gate.input.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.annotation.Secured
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import ru.sbertroika.pasiv.gate.dto.*
import ru.sbertroika.pasiv.gate.mapper.GrpcToRestMapper
import ru.sbertroika.pasiv.gate.output.service.ContactService
import ru.sbertroika.pasiv.gate.v1.ByIdRequest
import ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest
import ru.sbertroika.tkp3.security.userId
import ru.sbertroika.tkp3.security.validateUser
import javax.validation.Valid
import javax.validation.constraints.NotBlank

/**
 * REST контроллер для управления контактами
 */
@RestController
@RequestMapping("/api/v1/contacts")
@Tag(name = "Contacts", description = "API для управления контактами организаций")
@Validated
@Secured(value = ["ROLE_pasiv_console_admin"])
class ContactRestController(
    private val contactService: ContactService
) {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    @Operation(
        summary = "Создание контакта",
        description = "Создает новый контакт для организации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "201",
                description = "Контакт успешно создан",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Не авторизован"
            ),
            ApiResponse(
                responseCode = "403",
                description = "Недостаточно прав доступа"
            )
        ]
    )
    @PostMapping
    fun createContact(
        @Valid @RequestBody request: ContactDto
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("createContact: Creating contact for organization: ${request.organizationId}")

        validateUser().fold(
            { error ->
                log.warn("createContact: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = GrpcToRestMapper.mapContactToGrpc(request)
                contactService.createContact(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("createContact: Service error: ${error.message}")
                        ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("createContact: Contact created successfully")
                        ResponseEntity.status(HttpStatus.CREATED)
                            .body(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Обновление контакта",
        description = "Обновляет существующий контакт"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Контакт успешно обновлен",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса"
            ),
            ApiResponse(
                responseCode = "404",
                description = "Контакт не найден"
            )
        ]
    )
    @PutMapping("/{id}")
    fun updateContact(
        @Parameter(description = "ID контакта", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String,
        @Valid @RequestBody request: ContactDto
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("updateContact: Updating contact with id: $id")

        validateUser().fold(
            { error ->
                log.warn("updateContact: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val contactWithId = request.copy(id = id)
                val grpcRequest = GrpcToRestMapper.mapContactToGrpc(contactWithId)
                contactService.updateContact(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("updateContact: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("updateContact: Contact updated successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Получение списка контактов организации",
        description = "Возвращает список контактов для указанной организации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Список контактов получен успешно",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные параметры запроса"
            )
        ]
    )
    @GetMapping
    fun getContacts(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @RequestParam @NotBlank organizationId: String,
        @Parameter(description = "Номер страницы (начиная с 0)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Размер страницы")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponseDto<List<ContactDto>>> = runBlocking {
        log.info("getContacts: Getting contacts for organization: $organizationId")

        val pagination = PaginationRequestDto(page = page, size = size)
        val grpcRequest = GrpcToRestMapper.mapContactListRequestToGrpc(organizationId, pagination)

        contactService.contactList(grpcRequest).fold(
            { error ->
                log.error("getContacts: Service error: ${error.message}")
                ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { result ->
                log.info("getContacts: Found ${result.contactCount} contacts")
                val contacts = result.contactList.map { GrpcToRestMapper.mapContactToDto(it) }
                ResponseEntity.ok(ApiResponseDto(data = contacts))
            }
        )
    }

    @Operation(
        summary = "Получение контакта по ID",
        description = "Возвращает контакт по указанному идентификатору"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Контакт найден",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Контакт не найден"
            )
        ]
    )
    @GetMapping("/{id}")
    fun getContactById(
        @Parameter(description = "ID контакта", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String
    ): ResponseEntity<ApiResponseDto<ContactDto>> = runBlocking {
        log.info("getContactById: Getting contact with id: $id")

        val grpcRequest = ByIdRequest.newBuilder().setId(id).build()

        contactService.getContact(grpcRequest).fold(
            { error ->
                log.error("getContactById: Service error: ${error.message}")
                val status = if (error.message.contains("not found", ignoreCase = true)) {
                    HttpStatus.NOT_FOUND
                } else {
                    HttpStatus.BAD_REQUEST
                }
                ResponseEntity.status(status)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { contact ->
                log.info("getContactById: Contact found")
                ResponseEntity.ok(ApiResponseDto(data = GrpcToRestMapper.mapContactToDto(contact)))
            }
        )
    }

    @Operation(
        summary = "Удаление контакта",
        description = "Помечает контакт как удаленный (мягкое удаление)"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Контакт успешно удален",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Контакт не найден"
            )
        ]
    )
    @DeleteMapping("/{id}")
    fun deleteContact(
        @Parameter(description = "ID контакта", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("deleteContact: Deleting contact with id: $id")

        validateUser().fold(
            { error ->
                log.warn("deleteContact: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = ByIdRequest.newBuilder().setId(id).build()
                contactService.deleteContact(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("deleteContact: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("deleteContact: Contact deleted successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Восстановление контакта",
        description = "Восстанавливает ранее удаленный контакт"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Контакт успешно восстановлен",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Контакт не найден"
            )
        ]
    )
    @PostMapping("/{id}/recover")
    fun recoverContact(
        @Parameter(description = "ID контакта", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("recoverContact: Recovering contact with id: $id")

        validateUser().fold(
            { error ->
                log.warn("recoverContact: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = ByIdRequest.newBuilder().setId(id).build()
                contactService.recoverContact(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("recoverContact: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("recoverContact: Contact recovered successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Получение истории изменений контакта",
        description = "Возвращает историю изменений контакта с поддержкой пагинации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "История изменений получена успешно",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Контакт не найден"
            )
        ]
    )
    @GetMapping("/{id}/history")
    fun getContactHistory(
        @Parameter(description = "ID контакта", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String,
        @Parameter(description = "Номер страницы (начиная с 0)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Размер страницы")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponseDto<HistoryResultDto>> = runBlocking {
        log.info("getContactHistory: Getting history for contact with id: $id")

        val pagination = PaginationRequestDto(page = page, size = size)
        val grpcRequest = ByIdWithPaginationRequest.newBuilder()
            .setId(id)
            .setPagination(GrpcToRestMapper.mapPaginationRequestToGrpc(pagination))
            .build()

        contactService.getHistory(grpcRequest).fold(
            { error ->
                log.error("getContactHistory: Service error: ${error.message}")
                val status = if (error.message.contains("not found", ignoreCase = true)) {
                    HttpStatus.NOT_FOUND
                } else {
                    HttpStatus.BAD_REQUEST
                }
                ResponseEntity.status(status)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { history ->
                log.info("getContactHistory: History found with ${history.itemsCount} items")
                ResponseEntity.ok(ApiResponseDto(data = GrpcToRestMapper.mapHistoryResultToDto(history)))
            }
        )
    }
}
