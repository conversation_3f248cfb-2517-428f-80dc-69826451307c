package ru.sbertroika.pasiv.gate.config

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException
import org.springframework.validation.FieldError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException
import ru.sbertroika.pasiv.gate.dto.ApiResponseDto
import ru.sbertroika.pasiv.gate.dto.OperationErrorDto
import javax.validation.ConstraintViolationException

/**
 * Глобальный обработчик ошибок для REST API
 */
@RestControllerAdvice
class GlobalExceptionHandler {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    /**
     * Обработка ошибок валидации полей запроса
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationExceptions(
        ex: MethodArgumentNotValidException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("Validation error: ${ex.message}")

        val errors = ex.bindingResult.allErrors.map { error ->
            when (error) {
                is FieldError -> "${error.field}: ${error.defaultMessage}"
                else -> error.defaultMessage ?: "Неизвестная ошибка валидации"
            }
        }

        val errorMessage = "Ошибки валидации: ${errors.joinToString(", ")}"

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "VALIDATION_ERROR",
                    message = errorMessage,
                    details = errors.joinToString("; ")
                )
            ))
    }

    /**
     * Обработка ошибок валидации параметров запроса
     */
    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(
        ex: ConstraintViolationException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("Constraint violation: ${ex.message}")

        val errors = ex.constraintViolations.map { violation ->
            "${violation.propertyPath}: ${violation.message}"
        }

        val errorMessage = "Ошибки валидации параметров: ${errors.joinToString(", ")}"

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "VALIDATION_ERROR",
                    message = errorMessage,
                    details = errors.joinToString("; ")
                )
            ))
    }

    /**
     * Обработка ошибок отсутствующих обязательных параметров
     */
    @ExceptionHandler(MissingServletRequestParameterException::class)
    fun handleMissingServletRequestParameter(
        ex: MissingServletRequestParameterException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("Missing request parameter: ${ex.message}")

        val errorMessage = "Отсутствует обязательный параметр: ${ex.parameterName}"

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "MISSING_PARAMETER",
                    message = errorMessage,
                    details = "Параметр '${ex.parameterName}' типа '${ex.parameterType}' обязателен"
                )
            ))
    }

    /**
     * Обработка ошибок неправильного типа параметров
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException::class)
    fun handleMethodArgumentTypeMismatch(
        ex: MethodArgumentTypeMismatchException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("Method argument type mismatch: ${ex.message}")

        val errorMessage = "Неправильный тип параметра '${ex.name}': ожидается ${ex.requiredType?.simpleName}"

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "TYPE_MISMATCH",
                    message = errorMessage,
                    details = "Значение '${ex.value}' не может быть преобразовано к типу ${ex.requiredType?.simpleName}"
                )
            ))
    }

    /**
     * Обработка ошибок парсинга JSON
     */
    @ExceptionHandler(HttpMessageNotReadableException::class)
    fun handleHttpMessageNotReadable(
        ex: HttpMessageNotReadableException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("HTTP message not readable: ${ex.message}")

        val errorMessage = "Некорректный формат JSON в теле запроса"

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "JSON_PARSE_ERROR",
                    message = errorMessage,
                    details = ex.localizedMessage
                )
            ))
    }

    /**
     * Обработка ошибок аутентификации
     */
    @ExceptionHandler(AuthenticationCredentialsNotFoundException::class)
    fun handleAuthenticationCredentialsNotFound(
        ex: AuthenticationCredentialsNotFoundException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("Authentication credentials not found: ${ex.message}")

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "AUTHENTICATION_ERROR",
                    message = "Требуется аутентификация",
                    details = ex.message
                )
            ))
    }

    /**
     * Обработка ошибок авторизации
     */
    @ExceptionHandler(AccessDeniedException::class)
    fun handleAccessDenied(
        ex: AccessDeniedException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("Access denied: ${ex.message}")

        return ResponseEntity.status(HttpStatus.FORBIDDEN)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "ACCESS_DENIED",
                    message = "Недостаточно прав для выполнения операции",
                    details = ex.message
                )
            ))
    }

    /**
     * Обработка ошибок IllegalArgumentException
     */
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgument(
        ex: IllegalArgumentException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.warn("Illegal argument: ${ex.message}")

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "ILLEGAL_ARGUMENT",
                    message = ex.message ?: "Некорректные аргументы",
                    details = ex.localizedMessage
                )
            ))
    }

    /**
     * Обработка ошибок IllegalStateException
     */
    @ExceptionHandler(IllegalStateException::class)
    fun handleIllegalState(
        ex: IllegalStateException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.error("Illegal state: ${ex.message}", ex)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "ILLEGAL_STATE",
                    message = "Внутренняя ошибка сервера",
                    details = ex.message ?: "Неизвестная ошибка состояния"
                )
            ))
    }

    /**
     * Обработка всех остальных исключений
     */
    @ExceptionHandler(Exception::class)
    fun handleGenericException(
        ex: Exception
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.error("Unexpected error: ${ex.message}", ex)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponseDto(
                error = OperationErrorDto(
                    code = "INTERNAL_ERROR",
                    message = "Внутренняя ошибка сервера",
                    details = "Обратитесь к администратору системы"
                )
            ))
    }

    /**
     * Обработка ошибок RuntimeException
     */
    @ExceptionHandler(RuntimeException::class)
    fun handleRuntimeException(
        ex: RuntimeException
    ): ResponseEntity<ApiResponseDto<Unit>> {
        log.error("Runtime error: ${ex.message}", ex)

        // Проверяем, не является ли это известной ошибкой
        return when {
            ex.message?.contains("not found", ignoreCase = true) == true -> {
                ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponseDto(
                        error = OperationErrorDto(
                            code = "NOT_FOUND",
                            message = "Ресурс не найден",
                            details = ex.message
                        )
                    ))
            }
            ex.message?.contains("already exists", ignoreCase = true) == true -> {
                ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponseDto(
                        error = OperationErrorDto(
                            code = "CONFLICT",
                            message = "Ресурс уже существует",
                            details = ex.message
                        )
                    ))
            }
            else -> {
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponseDto(
                        error = OperationErrorDto(
                            code = "RUNTIME_ERROR",
                            message = "Ошибка выполнения",
                            details = ex.message ?: "Неизвестная ошибка выполнения"
                        )
                    ))
            }
        }
    }
}
