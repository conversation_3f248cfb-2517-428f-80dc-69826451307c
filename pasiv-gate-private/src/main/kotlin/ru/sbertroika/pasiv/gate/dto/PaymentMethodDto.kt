package ru.sbertroika.pasiv.gate.dto

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import javax.validation.constraints.NotBlank
import javax.validation.constraints.Size

/**
 * DTO для работы со способами оплаты
 */

@Schema(description = "Тип способа оплаты")
enum class PaymentMethodTypeDto {
    @Schema(description = "Банковская карта")
    BANK_CARD,
    
    @Schema(description = "Наличные денежные средства")
    CASH,
    
    @Schema(description = "Транспортная карта \"Тройка\" (разовые поездки)")
    TROIKA_SINGLE,
    
    @Schema(description = "Транспортная карта \"Тройка\" (абонемент)")
    TROIKA_SUBSCRIPTION,
    
    @Schema(description = "МПК Дисконт")
    MPC_DISCOUNT,
    
    @Schema(description = "МПК Социальная карта")
    MPC_SOCIAL,
    
    @Schema(description = "МПК \"Карта Школьника\"")
    MPC_SCHOOL,
    
    @Schema(description = "МПК \"Карта Студента\" (разовые поездки)")
    MPC_STUDENT_SINGLE,
    
    @Schema(description = "МПК \"Карта Студента\" (абонемент)")
    MPC_STUDENT_SUBSCRIPTION,
    
    @Schema(description = "ТК Карта жителя")
    TC_RESIDENT,
    
    @Schema(description = "Мобильное приложение БК")
    MOBILE_BC,
    
    @Schema(description = "Мобильное приложение Виртуальная ТК")
    MOBILE_VIRTUAL_TC,
    
    @Schema(description = "Мобильное приложение СБП")
    MOBILE_SBP,
    
    @Schema(description = "Транспортная карта региона")
    REGIONAL_TC,
    
    @Schema(description = "Социальная транспортная карта")
    SOCIAL_TC,
    
    @Schema(description = "Иные карты, предусмотренные договором")
    OTHER_CARDS
}

@Schema(description = "Способ оплаты по договору")
data class ContractPaymentMethodDto(
    @Schema(description = "ID способа оплаты", example = "123e4567-e89b-12d3-a456-************")
    val id: String? = null,
    
    @Schema(description = "ID договора", example = "123e4567-e89b-12d3-a456-************")
    @field:NotBlank(message = "ID договора не может быть пустым")
    val contractId: String,
    
    @Schema(description = "Тип способа оплаты")
    val methodType: PaymentMethodTypeDto,
    
    @Schema(description = "Код типа (для совместимости)", example = "BANK_CARD")
    @field:NotBlank(message = "Код типа не может быть пустым")
    @field:Size(max = 50, message = "Код типа не может превышать 50 символов")
    val code: String,
    
    @Schema(description = "Название способа оплаты", example = "Банковские карты Visa/MasterCard")
    @field:NotBlank(message = "Название способа оплаты не может быть пустым")
    @field:Size(max = 200, message = "Название способа оплаты не может превышать 200 символов")
    val name: String,
    
    @Schema(description = "Описание", example = "Оплата банковскими картами международных платежных систем")
    @field:Size(max = 500, message = "Описание не может превышать 500 символов")
    val description: String? = null,
    
    @Schema(description = "Активен ли способ оплаты")
    val isActive: Boolean = true,
    
    @Schema(description = "Удален ли способ оплаты")
    val isDeleted: Boolean = false,
    
    @Schema(description = "Дата создания", example = "2024-01-01T12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val createdDate: LocalDateTime,
    
    @Schema(description = "Дата последней синхронизации", example = "2024-01-01T12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val lastSyncDate: LocalDateTime? = null,
    
    @Schema(description = "Внешний ID", example = "EXT_PAYMENT_123")
    @field:Size(max = 100, message = "Внешний ID не может превышать 100 символов")
    val externalId: String? = null
)

@Schema(description = "Фильтр для поиска способов оплаты")
data class PaymentMethodFilterDto(
    @Schema(description = "Показывать удаленные способы оплаты")
    val isDeleted: Boolean? = null,
    
    @Schema(description = "ID договора", example = "123e4567-e89b-12d3-a456-************")
    val contractId: String? = null,
    
    @Schema(description = "Тип способа оплаты")
    val methodType: PaymentMethodTypeDto? = null,
    
    @Schema(description = "Код типа", example = "BANK_CARD")
    @field:Size(max = 50, message = "Код типа не может превышать 50 символов")
    val code: String? = null,
    
    @Schema(description = "Показывать только активные способы оплаты")
    val isActive: Boolean? = null,
    
    @Schema(description = "Поиск по названию", example = "карта")
    @field:Size(max = 100, message = "Поиск по названию не может превышать 100 символов")
    val name: String? = null
)

@Schema(description = "Запрос списка способов оплаты")
data class PaymentMethodListRequestDto(
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,
    
    @Schema(description = "Фильтры поиска")
    val filter: PaymentMethodFilterDto? = null
)

@Schema(description = "Результат списка способов оплаты")
data class PaymentMethodListResultDto(
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,
    
    @Schema(description = "Примененные фильтры")
    val filter: PaymentMethodFilterDto? = null,
    
    @Schema(description = "Список способов оплаты")
    val paymentMethods: List<ContractPaymentMethodDto>
)

@Schema(description = "Запрос способов оплаты по договору")
data class PaymentMethodsByContractRequestDto(
    @Schema(description = "ID договора", example = "123e4567-e89b-12d3-a456-************")
    @field:NotBlank(message = "ID договора не может быть пустым")
    val contractId: String,
    
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,
    
    @Schema(description = "Включить неактивные способы оплаты")
    val includeInactive: Boolean? = false
)

@Schema(description = "Запрос создания способа оплаты")
data class CreatePaymentMethodRequestDto(
    @Schema(description = "ID договора", example = "123e4567-e89b-12d3-a456-************")
    @field:NotBlank(message = "ID договора не может быть пустым")
    val contractId: String,
    
    @Schema(description = "Тип способа оплаты")
    val methodType: PaymentMethodTypeDto,
    
    @Schema(description = "Код типа (для совместимости)", example = "BANK_CARD")
    @field:NotBlank(message = "Код типа не может быть пустым")
    @field:Size(max = 50, message = "Код типа не может превышать 50 символов")
    val code: String,
    
    @Schema(description = "Название способа оплаты", example = "Банковские карты Visa/MasterCard")
    @field:NotBlank(message = "Название способа оплаты не может быть пустым")
    @field:Size(max = 200, message = "Название способа оплаты не может превышать 200 символов")
    val name: String,
    
    @Schema(description = "Описание", example = "Оплата банковскими картами международных платежных систем")
    @field:Size(max = 500, message = "Описание не может превышать 500 символов")
    val description: String? = null,
    
    @Schema(description = "Активен ли способ оплаты")
    val isActive: Boolean = true,
    
    @Schema(description = "Внешний ID", example = "EXT_PAYMENT_123")
    @field:Size(max = 100, message = "Внешний ID не может превышать 100 символов")
    val externalId: String? = null
)

@Schema(description = "Запрос обновления способа оплаты")
data class UpdatePaymentMethodRequestDto(
    @Schema(description = "ID способа оплаты", example = "123e4567-e89b-12d3-a456-************")
    @field:NotBlank(message = "ID способа оплаты не может быть пустым")
    val id: String,
    
    @Schema(description = "ID договора", example = "123e4567-e89b-12d3-a456-************")
    @field:NotBlank(message = "ID договора не может быть пустым")
    val contractId: String,
    
    @Schema(description = "Тип способа оплаты")
    val methodType: PaymentMethodTypeDto,
    
    @Schema(description = "Код типа (для совместимости)", example = "BANK_CARD")
    @field:NotBlank(message = "Код типа не может быть пустым")
    @field:Size(max = 50, message = "Код типа не может превышать 50 символов")
    val code: String,
    
    @Schema(description = "Название способа оплаты", example = "Банковские карты Visa/MasterCard")
    @field:NotBlank(message = "Название способа оплаты не может быть пустым")
    @field:Size(max = 200, message = "Название способа оплаты не может превышать 200 символов")
    val name: String,
    
    @Schema(description = "Описание", example = "Оплата банковскими картами международных платежных систем")
    @field:Size(max = 500, message = "Описание не может превышать 500 символов")
    val description: String? = null,
    
    @Schema(description = "Активен ли способ оплаты")
    val isActive: Boolean = true,
    
    @Schema(description = "Внешний ID", example = "EXT_PAYMENT_123")
    @field:Size(max = 100, message = "Внешний ID не может превышать 100 символов")
    val externalId: String? = null
)
