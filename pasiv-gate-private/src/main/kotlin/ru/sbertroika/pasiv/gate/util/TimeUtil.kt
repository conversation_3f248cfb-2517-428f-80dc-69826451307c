package ru.sbertroika.pasiv.gate.util

import java.sql.Timestamp
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset

fun timestampNow() = Timestamp(Instant.now().toEpochMilli())

fun fromGoogleTimestampUTC(googleTimestamp: com.google.protobuf.Timestamp): LocalDateTime? {
    return Instant.ofEpochSecond(googleTimestamp.seconds, googleTimestamp.nanos.toLong())
        .atOffset(ZoneOffset.UTC)
        .toLocalDateTime()
}

fun timestampProtoToSQL(protoTimestamp: com.google.protobuf.Timestamp): Timestamp? = Timestamp.valueOf(fromGoogleTimestampUTC(protoTimestamp))

fun timestampSQLToProto(sqlTimestamp: Timestamp): com.google.protobuf.Timestamp = toGoogleTimestampUTC(sqlTimestamp.toLocalDateTime())

fun toGoogleTimestampUTC(localDateTime: LocalDateTime): com.google.protobuf.Timestamp = com.google.protobuf.Timestamp.newBuilder()
        .setSeconds(localDateTime.toEpochSecond(ZoneOffset.UTC))
        .setNanos(localDateTime.nano)
        .build()

