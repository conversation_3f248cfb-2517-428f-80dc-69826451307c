package ru.sbertroika.pasiv.gate.config

import okhttp3.ConnectionPool
import okhttp3.Dispatcher
import okhttp3.OkHttpClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import ru.sbertroika.pasiv.gate.output.dadata.DaDataClient
import ru.sbertroika.pasiv.gate.output.dadata.LoggingInterceptor
import ru.sbertroika.pasiv.gate.output.dadata.TokenInterceptor
import ru.sbertroika.pasiv.gate.output.dadata.TokenStorage
import java.security.SecureRandom
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.X509TrustManager

@Configuration
class DaDataConfig (
    private val tokenStorage: TokenStorage
){

    private val baseUrl = "http://suggestions.dadata.ru/"

    @Bean
    fun daDataClient(): DaDataClient {
        val httpClient = getUnsafeOkHttpClient()
        return Retrofit.Builder()
            .client(httpClient)
            .baseUrl(baseUrl)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(DaDataClient::class.java)
    }

    fun tokenInterceptor(): TokenInterceptor {
        return TokenInterceptor(tokenStorage)
    }

    private fun getUnsafeOkHttpClient(): OkHttpClient {
        return try {
            val trustAllCerts = arrayOf<X509TrustManager>(
                object : X509TrustManager {
                    override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
                    override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
                    override fun getAcceptedIssuers(): Array<X509Certificate> {
                        return arrayOf()
                    }
                }
            )
            val sslContext = SSLContext.getInstance("SSL")
            sslContext.init(null, trustAllCerts, SecureRandom())
            val sslSocketFactory = sslContext.socketFactory
            val maxIdleConnections = 30
            val dispatcher = Dispatcher()
            dispatcher.maxRequests = maxIdleConnections
            dispatcher.maxRequestsPerHost = maxIdleConnections
            val builder: OkHttpClient.Builder = OkHttpClient.Builder()
                .dispatcher(dispatcher)
                .addInterceptor(tokenInterceptor())
                .connectionPool(ConnectionPool(maxIdleConnections, 5, TimeUnit.MINUTES))
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .callTimeout(2, TimeUnit.MINUTES)
                .addInterceptor(LoggingInterceptor())
                .sslSocketFactory(sslSocketFactory, trustAllCerts[0])
                .hostnameVerifier { _, _ -> true }
            builder.build()
        } catch (e: Exception) {
            throw RuntimeException(e)
        }
    }
}