package ru.sbertroika.pasiv.gate.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.pasiv.gate.output.model.Organization
import ru.sbertroika.pasiv.gate.output.model.OrganizationPK
import java.util.*

interface OrganizationCrudRepository : CoroutineCrudRepository<Organization, OrganizationPK> {

    @Query("SELECT * FROM active_organization_by_project where project_id = :projectId")
    suspend fun findAllByProjectId(projectId: UUID): Flow<Organization>

    @Query("SELECT * FROM active_organization_by_project where project_id = :projectId OFFSET :offset LIMIT :limit")
    suspend fun findAllByProjectId(projectId: UUID, offset: Int, limit: Int): Flow<Organization>


    @Query("SELECT count(*) FROM active_organization_by_project where project_id = :projectId")
    suspend fun countAllByProjectId(projectId: UUID): Int
}