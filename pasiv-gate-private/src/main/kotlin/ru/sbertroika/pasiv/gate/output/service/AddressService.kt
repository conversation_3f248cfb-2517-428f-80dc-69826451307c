package ru.sbertroika.pasiv.gate.output.service

import arrow.core.Either
import ru.sbertroika.common.v1.HistoryResult
import ru.sbertroika.pasiv.gate.v1.*

interface AddressService {
    suspend fun createAddress(addressCreateOrDelete: AddressCreateOrDelete, userId: String): Either<Error, Unit>

    suspend fun updateAddress(addressCreateOrDelete: AddressCreateOrDelete, userId: String): Either<Error, Unit>

    suspend fun addressList(request: AddressListRequest): Either<Error, AddressListResult>
    suspend fun getAddress(request: ByIdRequest): Either<Error, Address>
    suspend fun getHistory(request: ByIdWithPaginationRequest): Either<Error, HistoryResult>
    suspend fun deleteAddress(request: ByIdRequest, userId: String): Either<Error, Unit>
    suspend fun recoverAddress(request: ByIdRequest, userId: String): Either<Error, Unit>
}