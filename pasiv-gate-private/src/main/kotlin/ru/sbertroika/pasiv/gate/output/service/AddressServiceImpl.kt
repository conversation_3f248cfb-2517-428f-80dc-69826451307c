package ru.sbertroika.pasiv.gate.output.service

import arrow.core.Either
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.sbertroika.common.v1.History
import ru.sbertroika.common.v1.HistoryResult
import ru.sbertroika.common.v1.PaginationResponse
import ru.sbertroika.history.lib.mapHistory
import ru.sbertroika.pasiv.gate.output.model.Organization
import ru.sbertroika.pasiv.gate.output.repository.AddressRepository
import ru.sbertroika.pasiv.gate.output.repository.OrganizationRepository
import ru.sbertroika.pasiv.gate.util.calcTotalPage
import ru.sbertroika.pasiv.gate.util.timestampNow
import ru.sbertroika.pasiv.gate.v1.*
import java.util.*

@Service
class AddressServiceImpl(
    private val repository: AddressRepository,
    private val organizationRepository: OrganizationRepository
) : AddressService {
    override suspend fun createAddress(addressCreateOrDelete: AddressCreateOrDelete, userId: String): Either<Error, Unit> {
        return try {
            val organization = organizationRepository.findById(addressCreateOrDelete.organizationId)
            if(organization  != null) {
                val address = addressCreateOrDelete.address
                val result = repository.save(
                    ru.sbertroika.pasiv.gate.output.model.Address(
                        id = UUID.randomUUID(),
                        version = 1,
                        versionCreatedAt = timestampNow(),
                        versionCreatedBy = UUID.fromString(userId),
                        name = address.name,
                        longitude = address.longitude,
                        latitude = address.latitude,
                        isDeleted = address.isDeleted,
                        country = address.country,
                        region = address.region,
                        index = address.index,
                        district = address.district,
                        city = address.city,
                        street = address.street,
                        house = address.house,
                        comment = address.comment,
                        buildingOrHousing = address.buildingOrHousing,
                        officeOrRoom = address.officeOrRoom,
                        oktmo = address.oktmo,
                        fiac = address.fiac
                    )
                )
                setAddressToOrganization(organization, userId, addressCreateOrDelete, result)
            }
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun updateAddress(addressCreateOrDelete: AddressCreateOrDelete, userId: String): Either<Error, Unit> {
        return try {
            val organization = organizationRepository.findById(addressCreateOrDelete.organizationId)
            if(organization  != null) {
                val address = addressCreateOrDelete.address
            val addressFromDB = repository.findById(address.id)
            if (addressFromDB != null) {
                val result = repository.save(
                    addressFromDB.copy(
                        version = addressFromDB.version!! + 1,
                        versionCreatedBy = UUID.fromString(userId),
                        versionCreatedAt = timestampNow(),
                        name = address.name,
                        longitude = address.longitude,
                        latitude = address.latitude,
                        isDeleted = address.isDeleted,
                        country = address.country,
                        region = address.region,
                        index = address.index,
                        district = address.district,
                        city = address.city,
                        street = address.street,
                        house = address.house,
                        comment = address.comment,
                        buildingOrHousing = address.buildingOrHousing,
                        officeOrRoom = address.officeOrRoom,
                        oktmo = address.oktmo,
                        fiac = address.fiac
                    )
                )
                setAddressToOrganization(organization, userId, addressCreateOrDelete, result)
            }
                Either.Right(Unit)
            } else {
                Either.Left(Error("Address not found"))
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun addressList(request: AddressListRequest): Either<Error, AddressListResult> {
        return try {
            if(request.hasPagination()) {
                val pagination = request.pagination
                if (request.hasFilters() && (request.filters.hasCity() || request.filters.hasStreet())) {
                    val totalCount = repository.countAll(request.filters)
                    Either.Right(
                        AddressListResult
                            .newBuilder()
                            .setPagination(
                                PaginationResponse.newBuilder()
                                    .setPage(pagination.page)
                                    .setLimit(pagination.limit)
                                    .setTotalCount(totalCount)
                                    .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                            )
                            .addAllAddress(repository.findAll(request.filters, pagination.page, pagination.limit)
                                .map { mapAddressToGrps(it) }
                                .toList())
                            .build()
                    )
                } else {
                    val totalCount = repository.countAll()
                    Either.Right(
                        AddressListResult
                            .newBuilder()
                            .setPagination(
                                PaginationResponse.newBuilder()
                                    .setPage(pagination.page)
                                    .setLimit(pagination.limit)
                                    .setTotalCount(totalCount)
                                    .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                            )
                            .addAllAddress(repository.findAll(pagination.page, pagination.limit)
                                .map { mapAddressToGrps(it) }
                                .toList())
                            .build()
                    )
                }
            } else {
                if(request.hasFilters() && request.filters.hasOrganizationId()) {
                    val organization =  organizationRepository.findById(request.filters.organizationId)
                    val ids = mutableListOf<UUID>()
                    if(organization?.addressLegalId != null)
                        ids.add(organization.addressLegalId!!)
                    if(organization?.addressActualId != null)
                        ids.add(organization.addressActualId!!)
                    if(organization?.addressMailingId != null)
                        ids.add(organization.addressMailingId!!)
                    Either.Right(AddressListResult.newBuilder().addAllAddress(repository.findWhereIdIN(ids).map {
                        mapAddressToGrps(it)
                    }.toList()).build())
                } else if (request.hasFilters() && (request.filters.hasCity() || request.filters.hasStreet()))
                    Either.Right(AddressListResult.newBuilder().addAllAddress(repository.findAll(request.filters).map {
                        mapAddressToGrps(it)
                    }.toList()).build())
                else
                    Either.Right(AddressListResult.newBuilder().addAllAddress(repository.findAll().map {
                        mapAddressToGrps(it)
                    }.toList()).build())
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun getAddress(request: ByIdRequest): Either<Error, Address> {
        return try {
            val address = if(request.hasVersion())
                repository.findByIdAndVersion(request.id, request.version)
            else repository.findById(request.id)
            if(address != null)
                Either.Right(mapAddressToGrps(address))
            else
                Either.Left(Error("Address with id=${request.id} no found"))
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun getHistory(request: ByIdWithPaginationRequest): Either<Error, HistoryResult> {
        return try {
            if(request.hasPagination()) {
                val pagination = request.pagination
                val totalCount = repository.getHistoryCount(request.id)
                val result = mapObjectToHistory(repository.getHistory(request.id, pagination.page * pagination.limit, pagination.limit).toList())
                Either.Right(
                    HistoryResult
                        .newBuilder()
                        .setPagination(
                            PaginationResponse.newBuilder()
                                .setPage(pagination.page)
                                .setLimit(pagination.limit)
                                .setTotalCount(totalCount)
                                .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                        )
                        .addAllHistory(result)
                        .build()
                )
            } else {
                val result = mapObjectToHistory(repository.getHistory(request.id).toList())
                Either.Right(HistoryResult.newBuilder()
                    .addAllHistory(result).build()
                )
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun deleteAddress(request: ByIdRequest, userId: String): Either<Error, Unit> {
        return try {
            repository.deleted(request.id, UUID.fromString(userId))
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun recoverAddress(request: ByIdRequest, userId: String): Either<Error, Unit> {
        return try {
            val result = repository.findDeletedById(request.id)
            if(result != null) {
                repository.save(
                    result.copy(
                        version = result.version!! + 1,
                        versionCreatedAt = timestampNow(),
                        versionCreatedBy = UUID.fromString(userId),
                        isDeleted = false
                    )
                )
                Either.Right(Unit)
            } else {
                Either.Left(Error("Address not found by id ${request.id}"))
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    private suspend fun mapObjectToHistory(result: List<ru.sbertroika.pasiv.gate.output.model.Address>): List<History> {
        return mapHistory(result) { id, version ->
            return@mapHistory repository.findByIdAndVersion(id, version)
        }
    }

    private suspend fun setAddressToOrganization(
        organization: Organization,
        userId: String,
        addressCreateOrDelete: AddressCreateOrDelete,
        result: ru.sbertroika.pasiv.gate.output.model.Address
    ) {
        var change = false
        val org = organization.copy(
            version = organization.version!! + 1,
            versionCreatedBy = UUID.fromString(userId),
            versionCreatedAt = timestampNow(),
        )
        when (addressCreateOrDelete.type) {
            AddressType.AT_LEGAL -> {
                if (org.addressLegalId != result.id) {
                    if(org.addressLegalId != null)
                        repository.deleted(org.addressLegalId.toString(), UUID.fromString(userId))
                    org.addressLegalId = result.id
                    change = true
                }
            }

            AddressType.AT_ACTUAL -> {
                if (org.addressActualId != result.id) {
                    if(org.addressActualId != null)
                        repository.deleted(org.addressActualId.toString(), UUID.fromString(userId))
                    org.addressActualId = result.id
                    change = true
                }
            }

            AddressType.AT_MAILING -> {
                if (org.addressMailingId != result.id) {
                    if(org.addressMailingId != null)
                    repository.deleted(org.addressMailingId.toString(), UUID.fromString(userId))
                    org.addressMailingId = result.id
                    change = true
                }
            }

            else -> {
                if (org.addressActualId != result.id) {
                    if(org.addressActualId != null)
                        repository.deleted(org.addressActualId.toString(), UUID.fromString(userId))
                    org.addressActualId = result.id
                    change = true
                }
            }
        }
        if (change)
            organizationRepository.save(org)
    }

    private fun mapAddressToGrps(entity: ru.sbertroika.pasiv.gate.output.model.Address ): Address {
        return Address
            .newBuilder()
            .setId(entity.id.toString())
            .setName(entity.name)
            .setIndex(entity.index?:0)
            .setCity(entity.city?:"")
            .setComment(entity.comment?:"")
            .setCountry(entity.country?:"")
            .setRegion(entity.region?:"")
            .setDistrict(entity.district?:"")
            .setIsDeleted(entity.isDeleted)
            .setBuildingOrHousing(entity.buildingOrHousing?:"")
            .setOfficeOrRoom(entity.officeOrRoom?:"")
            .setOktmo(entity.oktmo?:0)
            .setFiac(entity.fiac?:"")
            .setLatitude(entity.latitude?:0.0)
            .setLongitude(entity.longitude?:0.0)
            .build()
    }
}