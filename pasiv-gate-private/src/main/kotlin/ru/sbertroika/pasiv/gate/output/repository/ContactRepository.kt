package ru.sbertroika.pasiv.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.pasiv.gate.output.model.Contact
import ru.sbertroika.pasiv.gate.output.model.ContactPK
import ru.sbertroika.pasiv.gate.output.model.ContactType
import ru.sbertroika.pasiv.gate.util.timestampNow
import ru.sbertroika.pasiv.gate.v1.ContactFilter
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Repository
class ContactRepository(
    override val dbClient: DatabaseClient,
    override val repository: ContactCrudRepository
): AbstractRepository<Contact, ContactPK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM active_contact o WHERE o.c_is_deleted = false"

    override fun toEntity(t: Readable) = Contact(
        id  = t.get("c_id") as UUID,
        versionCreatedAt = Timestamp.valueOf(t.get("c_version_created_at") as LocalDateTime),
        versionCreatedBy = t.get("c_version_created_by") as UUID,
        version = t.get("c_version") as Int,
        isDeleted = t.get("c_is_deleted") as Boolean,
        organizationId = t.get("c_organization_id") as UUID,
        value = t.get("c_value") as String?,
        type = t.get("c_type") as ContactType
    )

    override suspend fun findById(id: String): Contact? {
        return dbClient.sql("${getQuery()} AND  o.c_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    suspend fun findDeletedById(id: String): Contact? {
        return dbClient.sql("SELECT * FROM active_contact o WHERE o.c_is_deleted=true AND o.c_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    suspend fun findByIdAndVersion(id: String, version: Long): Contact? {
        return dbClient.sql("select  * from  contact o WHERE o.c_id = '$id' AND o.c_version = $version")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Contact> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Contact> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    suspend fun getHistory(id: String): Flow<Contact> {
        return dbClient.sql("select * from contact where c_id = '$id'")
            .map(::toEntity).flow()
    }

    suspend fun getHistoryCount(id: String):Int {
        return dbClient.sql("select count(*) from contact where c_id = '$id'").map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    suspend fun getHistory(id: String, offset: Int, limit: Int): Flow<Contact> {
        return dbClient.sql("select * from contact where c_id = '$id' OFFSET $offset LIMIT $limit")
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && !entity.isDeleted) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                isDeleted = true
            ))
        }
    }

    suspend fun findAll(filter: ContactFilter, page: Int, limit: Int): Flow<Contact> {
        val query = "${buildRequestByFilter(filter)} OFFSET ${page * limit} LIMIT $limit"
        return dbClient.sql(query).map(::toEntity).flow()
    }

    suspend fun findAll(filter: ContactFilter): Flow<Contact> {
        val query = buildRequestByFilter(filter)
        return dbClient.sql(query).map(::toEntity).flow()
    }

    suspend fun countAll(filter: ContactFilter): Int {
        val query = buildRequestByFilter(filter, true)
        return dbClient.sql(query).map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    private fun buildRequestByFilter(filter: ContactFilter, isCount: Boolean  = false): String {
        var query = if (filter.hasIsDeleted() && filter.isDeleted) {
            (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM active_contact o WHERE o.c_is_deleted=true"
        } else getQuery(isCount)
        if(filter.hasOrganizationId() && !filter.organizationId.isNullOrEmpty())
            query += " AND o.c_organization_id ='${filter.organizationId}'"
        return query
    }

}
