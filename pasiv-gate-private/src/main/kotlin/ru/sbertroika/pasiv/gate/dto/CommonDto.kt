package ru.sbertroika.pasiv.gate.dto

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import javax.validation.constraints.Min
import javax.validation.constraints.NotNull

/**
 * Базовые DTO классы для REST API
 */

@Schema(description = "Запрос пагинации")
data class PaginationRequestDto(
    @Schema(description = "Номер страницы (начиная с 0)", example = "0", minimum = "0")
    @field:Min(0, message = "Номер страницы должен быть больше или равен 0")
    val page: Int = 0,
    
    @Schema(description = "Размер страницы", example = "20", minimum = "1", maximum = "100")
    @field:Min(1, message = "Размер страницы должен быть больше 0")
    val size: Int = 20
)

@Schema(description = "Ответ с пагинацией")
data class PaginationResponseDto(
    @Schema(description = "Номер текущей страницы", example = "0")
    val page: Int,
    
    @Schema(description = "Размер страницы", example = "20")
    val size: Int,
    
    @Schema(description = "Общее количество элементов", example = "150")
    val totalElements: Long,
    
    @Schema(description = "Общее количество страниц", example = "8")
    val totalPages: Int,
    
    @Schema(description = "Является ли текущая страница первой")
    val first: Boolean,
    
    @Schema(description = "Является ли текущая страница последней")
    val last: Boolean
)

@Schema(description = "Запрос по ID")
data class ByIdRequestDto(
    @Schema(description = "Идентификатор", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotNull(message = "ID не может быть пустым")
    val id: String,
    
    @Schema(description = "Версия записи", example = "1")
    val version: Long? = null
)

@Schema(description = "Запрос по ID с пагинацией")
data class ByIdWithPaginationRequestDto(
    @Schema(description = "Идентификатор", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotNull(message = "ID не может быть пустым")
    val id: String,
    
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null
)

@Schema(description = "Ошибка операции")
data class OperationErrorDto(
    @Schema(description = "Код ошибки", example = "VALIDATION_ERROR")
    val code: String,
    
    @Schema(description = "Сообщение об ошибке", example = "Некорректные данные")
    val message: String,
    
    @Schema(description = "Детали ошибки")
    val details: Map<String, Any>? = null,
    
    @Schema(description = "Время возникновения ошибки")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val timestamp: LocalDateTime = LocalDateTime.now()
)

@Schema(description = "Стандартный ответ API")
data class ApiResponseDto<T>(
    @Schema(description = "Данные ответа")
    val data: T? = null,
    
    @Schema(description = "Ошибка (если есть)")
    val error: OperationErrorDto? = null,
    
    @Schema(description = "Успешность операции")
    val success: Boolean = error == null
)

@Schema(description = "Ответ с пагинированными данными")
data class PagedResponseDto<T>(
    @Schema(description = "Данные")
    val content: List<T>,
    
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto,
    
    @Schema(description = "Ошибка (если есть)")
    val error: OperationErrorDto? = null,
    
    @Schema(description = "Успешность операции")
    val success: Boolean = error == null
)

@Schema(description = "Ответ создания записи")
data class CreateResponseDto(
    @Schema(description = "ID созданной записи", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String,
    
    @Schema(description = "Ошибка (если есть)")
    val error: OperationErrorDto? = null,
    
    @Schema(description = "Успешность операции")
    val success: Boolean = error == null
)

@Schema(description = "Пустой ответ")
data class EmptyResponseDto(
    @Schema(description = "Ошибка (если есть)")
    val error: OperationErrorDto? = null,

    @Schema(description = "Успешность операции")
    val success: Boolean = error == null
)

@Schema(description = "Элемент истории")
data class HistoryItemDto(
    @Schema(description = "Идентификатор записи", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String,

    @Schema(description = "Версия записи", example = "1")
    val version: Long,

    @Schema(description = "Время создания (timestamp)", example = "1640995200")
    val createdAt: Long,

    @Schema(description = "Пользователь, создавший запись", example = "user123")
    val createdBy: String,

    @Schema(description = "Данные записи в JSON формате")
    val data: String
)

@Schema(description = "Результат истории изменений")
data class HistoryResultDto(
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,

    @Schema(description = "Элементы истории")
    val items: List<HistoryItemDto>
)

@Schema(description = "Запись истории изменений")
data class HistoryRecordDto(
    @Schema(description = "ID записи истории")
    val id: String,
    
    @Schema(description = "ID сущности")
    val entityId: String,
    
    @Schema(description = "Тип операции", example = "CREATE")
    val operationType: String,
    
    @Schema(description = "Данные до изменения")
    val dataBefore: Map<String, Any>? = null,
    
    @Schema(description = "Данные после изменения")
    val dataAfter: Map<String, Any>? = null,
    
    @Schema(description = "ID пользователя, выполнившего операцию")
    val userId: String,
    
    @Schema(description = "Время операции")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val timestamp: LocalDateTime
)

@Schema(description = "Ответ с историей изменений")
data class HistoryResponseDto(
    @Schema(description = "Записи истории")
    val records: List<HistoryRecordDto>,
    
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,
    
    @Schema(description = "Ошибка (если есть)")
    val error: OperationErrorDto? = null,
    
    @Schema(description = "Успешность операции")
    val success: Boolean = error == null
)
