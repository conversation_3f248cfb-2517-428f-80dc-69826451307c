package ru.sbertroika.pasiv.gate.input.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.annotation.Secured
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import ru.sbertroika.pasiv.gate.dto.*
import ru.sbertroika.pasiv.gate.mapper.GrpcToRestMapper
import ru.sbertroika.pasiv.gate.output.service.OrganizationService
import ru.sbertroika.pasiv.gate.v1.*
import ru.sbertroika.tkp3.security.userId
import ru.sbertroika.tkp3.security.validateUser
import javax.validation.Valid
import javax.validation.constraints.NotBlank

/**
 * REST контроллер для управления организациями
 */
@RestController
@RequestMapping("/api/v1/organizations")
@Tag(name = "Organizations", description = "API для управления организациями")
@Validated
@Secured(value = ["ROLE_pasiv_console_admin"])
class OrganizationRestController(
    private val organizationService: OrganizationService
) {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    @Operation(
        summary = "Создание организации с адресами",
        description = "Создает новую организацию с обязательным юридическим адресом и опциональными фактическим и почтовым адресами"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "201",
                description = "Организация успешно создана",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Не авторизован"
            ),
            ApiResponse(
                responseCode = "403",
                description = "Недостаточно прав доступа"
            )
        ]
    )
    @PostMapping
    fun createOrganization(
        @Valid @RequestBody request: OrganizationWithAddressesDto
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("createOrganization: Creating organization with name: ${request.organization.name}")

        validateUser().fold(
            { error ->
                log.warn("createOrganization: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = GrpcToRestMapper.mapOrganizationWithAddressesToGrpc(request)
                organizationService.createOrganization(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("createOrganization: Service error: ${error.message}")
                        ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("createOrganization: Organization created successfully")
                        ResponseEntity.status(HttpStatus.CREATED)
                            .body(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Обновление организации",
        description = "Обновляет существующую организацию"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Организация успешно обновлена",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса"
            ),
            ApiResponse(
                responseCode = "404",
                description = "Организация не найдена"
            )
        ]
    )
    @PutMapping("/{id}")
    fun updateOrganization(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String,
        @Valid @RequestBody request: OrganizationDto
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("updateOrganization: Updating organization with id: $id")

        validateUser().fold(
            { error ->
                log.warn("updateOrganization: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val organizationWithId = request.copy(id = id)
                val grpcRequest = GrpcToRestMapper.mapOrganizationToGrpc(organizationWithId)
                organizationService.updateOrganization(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("updateOrganization: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("updateOrganization: Organization updated successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Получение списка организаций",
        description = "Возвращает список организаций с поддержкой пагинации и фильтрации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Список организаций получен успешно",
                content = [Content(schema = Schema(implementation = PagedResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные параметры запроса"
            )
        ]
    )
    @GetMapping
    fun getOrganizations(
        @Parameter(description = "Номер страницы (начиная с 0)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Размер страницы")
        @RequestParam(defaultValue = "20") size: Int,
        @Parameter(description = "Поиск по наименованию")
        @RequestParam(required = false) name: String?,
        @Parameter(description = "Поиск по ИНН")
        @RequestParam(required = false) inn: String?,
        @Parameter(description = "Поиск по КПП")
        @RequestParam(required = false) kpp: String?,
        @Parameter(description = "Показывать удаленные организации")
        @RequestParam(defaultValue = "false") includeDeleted: Boolean
    ): ResponseEntity<PagedResponseDto<OrganizationDto>> = runBlocking {
        log.info("getOrganizations: Getting organizations list with page=$page, size=$size")

        val pagination = PaginationRequestDto(page = page, size = size)
        val filter = OrganizationFilterDto(
            name = name,
            inn = inn,
            kpp = kpp,
            isDeleted = if (includeDeleted) null else false
        )

        val grpcRequest = OrganizationListRequest.newBuilder()
            .setPagination(GrpcToRestMapper.mapPaginationRequestToGrpc(pagination))
            .setFilter(GrpcToRestMapper.mapOrganizationFilterToGrpc(filter))
            .build()

        organizationService.organizationList(grpcRequest).fold(
            { error ->
                log.error("getOrganizations: Service error: ${error.message}")
                ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(PagedResponseDto(
                        content = emptyList(),
                        pagination = PaginationResponseDto(page, size, 0, 0),
                        error = OperationErrorDto("SERVICE_ERROR", error.message, "")
                    ))
            },
            { result ->
                log.info("getOrganizations: Found ${result.organizationCount} organizations")
                val organizations = result.organizationList.map { GrpcToRestMapper.mapOrganizationToDto(it) }
                val paginationResponse = result.pagination?.let { GrpcToRestMapper.mapPaginationResponseToDto(it) }
                    ?: PaginationResponseDto(page, size, organizations.size.toLong(), 1)

                ResponseEntity.ok(PagedResponseDto(
                    content = organizations,
                    pagination = paginationResponse
                ))
            }
        )
    }

    @Operation(
        summary = "Получение организации по ID",
        description = "Возвращает организацию по указанному идентификатору"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Организация найдена",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Организация не найдена"
            )
        ]
    )
    @GetMapping("/{id}")
    fun getOrganizationById(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String
    ): ResponseEntity<ApiResponseDto<OrganizationDto>> = runBlocking {
        log.info("getOrganizationById: Getting organization with id: $id")

        val grpcRequest = ByIdRequest.newBuilder().setId(id).build()

        organizationService.getOrganization(grpcRequest).fold(
            { error ->
                log.error("getOrganizationById: Service error: ${error.message}")
                val status = if (error.message.contains("not found", ignoreCase = true)) {
                    HttpStatus.NOT_FOUND
                } else {
                    HttpStatus.BAD_REQUEST
                }
                ResponseEntity.status(status)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { organization ->
                log.info("getOrganizationById: Organization found")
                ResponseEntity.ok(ApiResponseDto(data = GrpcToRestMapper.mapOrganizationToDto(organization)))
            }
        )
    }

    @Operation(
        summary = "Удаление организации",
        description = "Помечает организацию как удаленную (мягкое удаление)"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Организация успешно удалена",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Организация не найдена"
            )
        ]
    )
    @DeleteMapping("/{id}")
    fun deleteOrganization(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("deleteOrganization: Deleting organization with id: $id")

        validateUser().fold(
            { error ->
                log.warn("deleteOrganization: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = ByIdRequest.newBuilder().setId(id).build()
                organizationService.deleteOrganization(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("deleteOrganization: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("deleteOrganization: Organization deleted successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Восстановление организации",
        description = "Восстанавливает ранее удаленную организацию"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Организация успешно восстановлена",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Организация не найдена"
            )
        ]
    )
    @PostMapping("/{id}/recover")
    fun recoverOrganization(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("recoverOrganization: Recovering organization with id: $id")

        validateUser().fold(
            { error ->
                log.warn("recoverOrganization: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = ByIdRequest.newBuilder().setId(id).build()
                organizationService.recoverOrganization(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("recoverOrganization: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("recoverOrganization: Organization recovered successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Получение истории изменений организации",
        description = "Возвращает историю изменений организации с поддержкой пагинации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "История изменений получена успешно",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Организация не найдена"
            )
        ]
    )
    @GetMapping("/{id}/history")
    fun getOrganizationHistory(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank id: String,
        @Parameter(description = "Номер страницы (начиная с 0)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Размер страницы")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponseDto<HistoryResultDto>> = runBlocking {
        log.info("getOrganizationHistory: Getting history for organization with id: $id")

        val pagination = PaginationRequestDto(page = page, size = size)
        val grpcRequest = ByIdWithPaginationRequest.newBuilder()
            .setId(id)
            .setPagination(GrpcToRestMapper.mapPaginationRequestToGrpc(pagination))
            .build()

        organizationService.getHistory(grpcRequest).fold(
            { error ->
                log.error("getOrganizationHistory: Service error: ${error.message}")
                val status = if (error.message.contains("not found", ignoreCase = true)) {
                    HttpStatus.NOT_FOUND
                } else {
                    HttpStatus.BAD_REQUEST
                }
                ResponseEntity.status(status)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { history ->
                log.info("getOrganizationHistory: History found with ${history.itemsCount} items")
                ResponseEntity.ok(ApiResponseDto(data = GrpcToRestMapper.mapHistoryResultToDto(history)))
            }
        )
    }

    @Operation(
        summary = "Добавление организации в проект",
        description = "Добавляет организацию в указанный проект"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Организация успешно добавлена в проект",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса"
            )
        ]
    )
    @PostMapping("/{organizationId}/projects/{projectId}")
    fun addOrganizationToProject(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank organizationId: String,
        @Parameter(description = "ID проекта", example = "project-123")
        @PathVariable @NotBlank projectId: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("addOrganizationToProject: Adding organization $organizationId to project $projectId")

        validateUser().fold(
            { error ->
                log.warn("addOrganizationToProject: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = OrganizationInProjectRequest.newBuilder()
                    .setOrganizationId(organizationId)
                    .setProjectId(projectId)
                    .build()

                organizationService.addOrganizationInProject(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("addOrganizationToProject: Service error: ${error.message}")
                        ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("addOrganizationToProject: Organization added to project successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Удаление организации из проекта",
        description = "Удаляет организацию из указанного проекта"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Организация успешно удалена из проекта",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса"
            )
        ]
    )
    @DeleteMapping("/{organizationId}/projects/{projectId}")
    fun removeOrganizationFromProject(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable @NotBlank organizationId: String,
        @Parameter(description = "ID проекта", example = "project-123")
        @PathVariable @NotBlank projectId: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("removeOrganizationFromProject: Removing organization $organizationId from project $projectId")

        validateUser().fold(
            { error ->
                log.warn("removeOrganizationFromProject: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = OrganizationInProjectRequest.newBuilder()
                    .setOrganizationId(organizationId)
                    .setProjectId(projectId)
                    .build()

                organizationService.removeOrganizationInProject(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("removeOrganizationFromProject: Service error: ${error.message}")
                        ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("removeOrganizationFromProject: Organization removed from project successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Получение списка организаций для проекта",
        description = "Возвращает список организаций, связанных с указанным проектом"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Список организаций получен успешно",
                content = [Content(schema = Schema(implementation = PagedResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные параметры запроса"
            )
        ]
    )
    @GetMapping("/projects/{projectId}")
    fun getOrganizationsForProject(
        @Parameter(description = "ID проекта", example = "project-123")
        @PathVariable @NotBlank projectId: String,
        @Parameter(description = "Номер страницы (начиная с 0)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Размер страницы")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<PagedResponseDto<OrganizationDto>> = runBlocking {
        log.info("getOrganizationsForProject: Getting organizations for project $projectId")

        val pagination = PaginationRequestDto(page = page, size = size)
        val grpcRequest = OrganizationListForProjectRequest.newBuilder()
            .setProjectId(projectId)
            .setPagination(GrpcToRestMapper.mapPaginationRequestToGrpc(pagination))
            .build()

        organizationService.organizationListForProject(grpcRequest).fold(
            { error ->
                log.error("getOrganizationsForProject: Service error: ${error.message}")
                ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(PagedResponseDto(
                        content = emptyList(),
                        pagination = PaginationResponseDto(page, size, 0, 0),
                        error = OperationErrorDto("SERVICE_ERROR", error.message, "")
                    ))
            },
            { result ->
                log.info("getOrganizationsForProject: Found ${result.organizationCount} organizations")
                val organizations = result.organizationList.map { GrpcToRestMapper.mapOrganizationToDto(it) }
                val paginationResponse = result.pagination?.let { GrpcToRestMapper.mapPaginationResponseToDto(it) }
                    ?: PaginationResponseDto(page, size, organizations.size.toLong(), 1)

                ResponseEntity.ok(PagedResponseDto(
                    content = organizations,
                    pagination = paginationResponse
                ))
            }
        )
    }
}
