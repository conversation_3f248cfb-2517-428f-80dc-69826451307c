package ru.sbertroika.pasiv.gate.input.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.annotation.Secured
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import ru.sbertroika.pasiv.gate.dto.*
import ru.sbertroika.pasiv.gate.mapper.GrpcToRestMapper
import ru.sbertroika.pasiv.gate.output.service.AddressService
import ru.sbertroika.pasiv.gate.v1.ByIdRequest
import ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest
import ru.sbertroika.tkp3.security.userId
import ru.sbertroika.tkp3.security.validateUser

/**
 * REST контроллер для управления адресами
 */
@RestController
@RequestMapping("/api/v1/addresses")
@Tag(name = "Addresses", description = "API для управления адресами организаций")
@Validated
@Secured(value = ["ROLE_pasiv_console_admin"])
class AddressRestController(
    private val addressService: AddressService
) {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    @Operation(
        summary = "Создание адреса",
        description = "Создает новый адрес для организации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "201",
                description = "Адрес успешно создан",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Не авторизован"
            ),
            ApiResponse(
                responseCode = "403",
                description = "Недостаточно прав доступа"
            )
        ]
    )
    @PostMapping
    fun createAddress(
        @RequestBody request: AddressCreateOrDeleteDto
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("createAddress: Creating address for organization: ${request.organizationId}")

        validateUser().fold(
            { error ->
                log.warn("createAddress: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = GrpcToRestMapper.mapAddressCreateOrDeleteToGrpc(request)
                addressService.createAddress(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("createAddress: Service error: ${error.message}")
                        ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("createAddress: Address created successfully")
                        ResponseEntity.status(HttpStatus.CREATED)
                            .body(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Обновление адреса",
        description = "Обновляет существующий адрес"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Адрес успешно обновлен",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные данные запроса"
            ),
            ApiResponse(
                responseCode = "404",
                description = "Адрес не найден"
            )
        ]
    )
    @PutMapping("/{id}")
    fun updateAddress(
        @Parameter(description = "ID адреса", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable id: String,
        @RequestBody request: AddressCreateOrDeleteDto
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("updateAddress: Updating address with id: $id")

        validateUser().fold(
            { error ->
                log.warn("updateAddress: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val addressWithId = request.copy(address = request.address.copy(id = id))
                val grpcRequest = GrpcToRestMapper.mapAddressCreateOrDeleteToGrpc(addressWithId)
                addressService.updateAddress(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("updateAddress: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("updateAddress: Address updated successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Получение списка адресов организации",
        description = "Возвращает список адресов для указанной организации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Список адресов получен успешно",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Некорректные параметры запроса"
            )
        ]
    )
    @GetMapping
    fun getAddresses(
        @Parameter(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
        @RequestParam organizationId: String,
        @Parameter(description = "Номер страницы (начиная с 0)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Размер страницы")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponseDto<List<AddressDto>>> = runBlocking {
        log.info("getAddresses: Getting addresses for organization: $organizationId")

        val pagination = PaginationRequestDto(page = page, size = size)
        val grpcRequest = GrpcToRestMapper.mapAddressListRequestToGrpc(organizationId, pagination)

        addressService.addressList(grpcRequest).fold(
            { error ->
                log.error("getAddresses: Service error: ${error.message}")
                ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { result ->
                log.info("getAddresses: Found ${result.addressCount} addresses")
                val addresses = result.addressList.map { GrpcToRestMapper.mapAddressToDto(it) }
                ResponseEntity.ok(ApiResponseDto(data = addresses))
            }
        )
    }

    @Operation(
        summary = "Получение адреса по ID",
        description = "Возвращает адрес по указанному идентификатору"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Адрес найден",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Адрес не найден"
            )
        ]
    )
    @GetMapping("/{id}")
    fun getAddressById(
        @Parameter(description = "ID адреса", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable id: String
    ): ResponseEntity<ApiResponseDto<AddressDto>> = runBlocking {
        log.info("getAddressById: Getting address with id: $id")

        val grpcRequest = ByIdRequest.newBuilder().setId(id).build()

        addressService.getAddress(grpcRequest).fold(
            { error ->
                log.error("getAddressById: Service error: ${error.message}")
                val status = if (error.message.contains("not found", ignoreCase = true)) {
                    HttpStatus.NOT_FOUND
                } else {
                    HttpStatus.BAD_REQUEST
                }
                ResponseEntity.status(status)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { address ->
                log.info("getAddressById: Address found")
                ResponseEntity.ok(ApiResponseDto(data = GrpcToRestMapper.mapAddressToDto(address)))
            }
        )
    }

    @Operation(
        summary = "Удаление адреса",
        description = "Помечает адрес как удаленный (мягкое удаление)"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Адрес успешно удален",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Адрес не найден"
            )
        ]
    )
    @DeleteMapping("/{id}")
    fun deleteAddress(
        @Parameter(description = "ID адреса", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable id: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("deleteAddress: Deleting address with id: $id")

        validateUser().fold(
            { error ->
                log.warn("deleteAddress: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = ByIdRequest.newBuilder().setId(id).build()
                addressService.deleteAddress(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("deleteAddress: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("deleteAddress: Address deleted successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Восстановление адреса",
        description = "Восстанавливает ранее удаленный адрес"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Адрес успешно восстановлен",
                content = [Content(schema = Schema(implementation = EmptyResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Адрес не найден"
            )
        ]
    )
    @PostMapping("/{id}/recover")
    fun recoverAddress(
        @Parameter(description = "ID адреса", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable id: String
    ): ResponseEntity<ApiResponseDto<Unit>> = runBlocking {
        log.info("recoverAddress: Recovering address with id: $id")

        validateUser().fold(
            { error ->
                log.warn("recoverAddress: User validation failed: ${error.message}")
                ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto(error = OperationErrorDto("AUTH_ERROR", error.message, "")))
            },
            { auth ->
                val grpcRequest = ByIdRequest.newBuilder().setId(id).build()
                addressService.recoverAddress(grpcRequest, auth.userId()).fold(
                    { error ->
                        log.error("recoverAddress: Service error: ${error.message}")
                        val status = if (error.message.contains("not found", ignoreCase = true)) {
                            HttpStatus.NOT_FOUND
                        } else {
                            HttpStatus.BAD_REQUEST
                        }
                        ResponseEntity.status(status)
                            .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
                    },
                    {
                        log.info("recoverAddress: Address recovered successfully")
                        ResponseEntity.ok(ApiResponseDto(data = Unit))
                    }
                )
            }
        )
    }

    @Operation(
        summary = "Получение истории изменений адреса",
        description = "Возвращает историю изменений адреса с поддержкой пагинации"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "История изменений получена успешно",
                content = [Content(schema = Schema(implementation = ApiResponseDto::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Адрес не найден"
            )
        ]
    )
    @GetMapping("/{id}/history")
    fun getAddressHistory(
        @Parameter(description = "ID адреса", example = "123e4567-e89b-12d3-a456-426614174000")
        @PathVariable id: String,
        @Parameter(description = "Номер страницы (начиная с 0)")
        @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "Размер страницы")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponseDto<HistoryResultDto>> = runBlocking {
        log.info("getAddressHistory: Getting history for address with id: $id")

        val pagination = PaginationRequestDto(page = page, size = size)
        val grpcRequest = ByIdWithPaginationRequest.newBuilder()
            .setId(id)
            .setPagination(GrpcToRestMapper.mapPaginationRequestToGrpc(pagination))
            .build()

        addressService.getHistory(grpcRequest).fold(
            { error ->
                log.error("getAddressHistory: Service error: ${error.message}")
                val status = if (error.message.contains("not found", ignoreCase = true)) {
                    HttpStatus.NOT_FOUND
                } else {
                    HttpStatus.BAD_REQUEST
                }
                ResponseEntity.status(status)
                    .body(ApiResponseDto(error = OperationErrorDto("SERVICE_ERROR", error.message, "")))
            },
            { history ->
                log.info("getAddressHistory: History found with ${history.itemsCount} items")
                ResponseEntity.ok(ApiResponseDto(data = GrpcToRestMapper.mapHistoryResultToDto(history)))
            }
        )
    }
}
