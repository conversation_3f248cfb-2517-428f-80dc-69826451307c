spring:
  application:
    name: pasiv-gate
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_REALM_URL:https://dev-auth.sbertroika.tech/realms/test-asop}
    client_id: ${CLIENT_ID:crm-ui}

  r2dbc:
    url: r2dbc:pool:${DB_R2URL:postgresql://pasiv:pasiv@localhost:5432/pasiv}
    username: ${DB_USER:pasiv}
    password: ${DB_PASSWORD:pasiv}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
#    url: jdbc:${DB_URL:postgresql://postgresql:5432/pasiv} # start in docker
    url: jdbc:${DB_URL:postgresql://localhost:5432/pasiv}
    user: ${DB_USER:pasiv}
    password: ${DB_PASSWORD:pasiv}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

grpc:
  port: 5000
server:
  port: 8080