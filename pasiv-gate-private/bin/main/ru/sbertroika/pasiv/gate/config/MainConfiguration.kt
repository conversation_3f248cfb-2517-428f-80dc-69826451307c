package ru.sbertroika.pasiv.gate.config


import io.r2dbc.postgresql.codec.EnumCodec
import io.r2dbc.spi.ConnectionFactoryOptions
import io.r2dbc.spi.Option
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryOptionsBuilderCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.convert.CustomConversions
import org.springframework.data.r2dbc.config.EnableR2dbcAuditing
import org.springframework.data.r2dbc.convert.R2dbcCustomConversions
import org.springframework.data.r2dbc.dialect.DialectResolver
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.transaction.annotation.EnableTransactionManagement
import ru.sbertroika.pasiv.gate.output.model.ContactType
import ru.sbertroika.pasiv.gate.output.model.ContactTypeConverter
import ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus
import ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatusConverter


@Configuration
@EnableR2dbcRepositories(
    basePackages = ["ru.sbertroika.pasiv.gate.output.repository"]
)
@EnableTransactionManagement
@EnableR2dbcAuditing
class MainConfiguration(
    @Value("\${spring.r2dbc.url}")
    private val url: String
){
    @Bean
    fun connectionFactoryOptionsBuilderCustomizer(): ConnectionFactoryOptionsBuilderCustomizer {
        return ConnectionFactoryOptionsBuilderCustomizer { builder: ConnectionFactoryOptions.Builder ->
            builder.option(
                Option.valueOf("extensions"),
                listOf(
                    EnumCodec.builder()
                        .withEnum("project_organization_status", ProjectOrganizationStatus::class.java)
                        .withEnum("contact_type", ContactType::class.java)
                        .withRegistrationPriority(EnumCodec.Builder.RegistrationPriority.FIRST)
                        .build()
                )
            )
        }
    }

    @Bean
    fun r2dbcCustomConversions(databaseClient: DatabaseClient): R2dbcCustomConversions? {
        val dialect = DialectResolver.getDialect(databaseClient.connectionFactory)
        val converters: MutableList<Any?> = ArrayList(dialect.converters)
        converters.addAll(R2dbcCustomConversions.STORE_CONVERTERS)
        return R2dbcCustomConversions(
            CustomConversions.StoreConversions.of(dialect.simpleTypeHolder, converters),
            listOf<Any?>(
                ProjectOrganizationStatusConverter(),
                ContactTypeConverter()
            )
        )
    }

}