package ru.sbertroika.pasiv.gate.output.model

import org.springframework.data.convert.WritingConverter
import org.springframework.data.r2dbc.convert.EnumWriteSupport
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*


data class ProjectOrganizationPK(
    val poId: UUID? = null,
    val poVersion: Int? = null
): Serializable



@Table(value = "project_organization")
data class ProjectOrganization(
    @Column("po_id")
    var id: UUID? = null,

    @Column("po_version")
    var version: Int? = null,

    @Column("po_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("po_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("po_project_id")
    var projectId: UUID? = null,

    @Column("po_organization_id")
    var organizationId: UUID? = null,

    @Column("po_active_from")
    var activeFrom: Timestamp? = null,

    @Column("po_active_till")
    var activeTill: Timestamp? = null,

    @Column("po_status")
    var status: ProjectOrganizationStatus? = null,
)

enum class ProjectOrganizationStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}

@WritingConverter
class ProjectOrganizationStatusConverter: EnumWriteSupport<ProjectOrganizationStatus>()