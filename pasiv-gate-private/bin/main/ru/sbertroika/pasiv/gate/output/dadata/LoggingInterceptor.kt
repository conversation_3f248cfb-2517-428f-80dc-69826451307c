package ru.sbertroika.pasiv.gate.output.dadata

import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import ru.sbertroika.pasiv.gate.util.LogUtil
import java.io.IOException


class LoggingInterceptor : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val helper = Helper()
        return try {
            helper.intercept(chain)
        } finally {
            println("RESPONSE: " + helper.logString)
        }
    }

    private class Helper : Interceptor {
        private val httpLoggingInterceptor = HttpLoggingInterceptor { value: String ->
            log(
                value
            )
        }
        private val logBuilder = StringBuilder()

        @Throws(IOException::class)
        override fun intercept(chain: Interceptor.Chain): Response {
            val path: String = chain.request().url.toUrl().path

            // TODO рефактор может из конфига подтягивать или сделать статик лист
            if (path.contains("Blank")) {
                httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.NONE)
            } else {
                httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY)
            }
            return httpLoggingInterceptor.intercept(chain)
        }

        val logString: String
            get() = logBuilder.toString()

        private fun log(value: String) {
            if (logBuilder.isNotEmpty()) logBuilder.append("\n")
            logBuilder.append(LogUtil.sanitizeLog(value))
            if (value.startsWith("--> END")) {
                println("REQUEST: $logString")
                logBuilder.setLength(0)
            }
        }
    }
}
