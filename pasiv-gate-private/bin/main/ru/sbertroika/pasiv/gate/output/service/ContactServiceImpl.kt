package ru.sbertroika.pasiv.gate.output.service

import arrow.core.Either
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.sbertroika.common.v1.History
import ru.sbertroika.common.v1.HistoryResult
import ru.sbertroika.common.v1.PaginationResponse
import ru.sbertroika.history.lib.mapHistory
import ru.sbertroika.pasiv.gate.output.model.ContactType
import ru.sbertroika.pasiv.gate.output.repository.ContactRepository
import ru.sbertroika.pasiv.gate.util.calcTotalPage
import ru.sbertroika.pasiv.gate.util.timestampNow
import ru.sbertroika.pasiv.gate.v1.*
import java.util.*

@Service
class ContactServiceImpl(
    private val repository: ContactRepository
) : ContactService {
    override suspend fun createContact(contact: Contact, userId: String): Either<Error, Unit> {
        return try {
            repository.save(
                ru.sbertroika.pasiv.gate.output.model.Contact(
                    version = 1,
                    versionCreatedAt = timestampNow(),
                    versionCreatedBy = UUID.fromString(userId),
                    isDeleted = contact.isDeleted,
                    value = contact.value,
                    organizationId = if(contact.organizationId.isNullOrEmpty()) null else UUID.fromString(contact.organizationId),
                    type = mapContactTypeToGrps(contact.type)
                )
            )
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun updateContact(contact: Contact, userId: String): Either<Error, Unit> {
        return try {
            val org = repository.findById(contact.id)
            if (org != null) {
                repository.save(
                    org.copy(
                        version = org.version!! + 1,
                        versionCreatedBy = UUID.fromString(userId),
                        versionCreatedAt = timestampNow(),
                        isDeleted = contact.isDeleted,
                        value = contact.value,
                        organizationId = if(contact.organizationId.isNullOrEmpty()) null else UUID.fromString(contact.organizationId),
                        type = mapContactTypeToGrps(contact.type)
                    )
                )
                Either.Right(Unit)
            } else {
                Either.Left(Error("Contact not found"))
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun contactList(request: ContactListRequest): Either<Error, ContactListResult> {
        return try {
            if(request.hasPagination()) {
                val pagination = request.pagination
                if(request.hasFilters() &&
                    (request.filters.hasIsDeleted() ||
                            request.filters.hasOrganizationId())) {
                    val totalCount = repository.countAll(request.filters)
                    Either.Right(
                        ContactListResult
                            .newBuilder()
                            .addAllContacts(repository.findAll(request.filters, pagination.page, pagination.limit)
                                .map { mapContactToGrps(it) }
                                .toList())
                            .setPagination(
                                PaginationResponse.newBuilder()
                                    .setPage(pagination.page)
                                    .setLimit(pagination.limit)
                                    .setTotalCount(totalCount)
                                    .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                            )
                            .build())
                } else {
                    val totalCount = repository.countAll()
                    Either.Right(
                        ContactListResult
                            .newBuilder()
                            .addAllContacts(repository.findAll(pagination.page, pagination.limit)
                                .map { mapContactToGrps(it) }
                                .toList())
                            .setPagination(
                                PaginationResponse.newBuilder()
                                    .setPage(pagination.page)
                                    .setLimit(pagination.limit)
                                    .setTotalCount(totalCount)
                                    .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                            )
                            .build()
                    )
                }
            } else {
                if(request.hasFilters() &&
                    (request.filters.hasIsDeleted() ||
                            request.filters.hasOrganizationId())) {
                    Either.Right(ContactListResult.newBuilder().addAllContacts(repository.findAll(request.filters).map {
                        mapContactToGrps(it)
                    }.toList()).build())
                } else {
                    Either.Right(ContactListResult.newBuilder().addAllContacts(repository.findAll().map {
                        mapContactToGrps(it)
                    }.toList()).build())
                }
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun getContact(request: ByIdRequest): Either<Error, Contact> {
        return try {
            val contact = if(request.hasVersion())
                repository.findByIdAndVersion(request.id, request.version)
            else repository.findById(request.id)
            if(contact != null)
                Either.Right(mapContactToGrps(contact))
            else
                Either.Left(Error("Contact with id=${request.id} no found"))
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun getHistory(request: ByIdWithPaginationRequest): Either<Error, HistoryResult> {
        return try {
            if(request.hasPagination()) {
                val pagination = request.pagination
                val totalCount = repository.getHistoryCount(request.id)
                val result = mapObjectToHistory(repository.getHistory(request.id, pagination.page * pagination.limit, pagination.limit).toList())
                Either.Right(
                    HistoryResult
                        .newBuilder()
                        .setPagination(
                            PaginationResponse.newBuilder()
                                .setPage(pagination.page)
                                .setLimit(pagination.limit)
                                .setTotalCount(totalCount)
                                .setTotalPage(calcTotalPage(totalCount, request.pagination.limit))
                        )
                        .addAllHistory(result)
                        .build()
                )
            } else {
                val result = mapObjectToHistory(repository.getHistory(request.id).toList())
                Either.Right(HistoryResult.newBuilder()
                    .addAllHistory(result).build()
                )
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun deleteContact(request: ByIdRequest, userId: String): Either<Error, Unit> {
        return try {
            repository.deleted(request.id, UUID.fromString(userId))
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    override suspend fun recoverContact(request: ByIdRequest, userId: String): Either<Error, Unit> {
        return try {
            val result = repository.findDeletedById(request.id)
            if(result != null) {
                repository.save(
                    result.copy(
                        version = result.version!! + 1,
                        versionCreatedAt = timestampNow(),
                        versionCreatedBy = UUID.fromString(userId),
                        isDeleted = false
                    )
                )
                Either.Right(Unit)
            } else {
                Either.Left(Error("Contact not found by id ${request.id}"))
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    private suspend fun mapObjectToHistory(result: List<ru.sbertroika.pasiv.gate.output.model.Contact>): List<History> {
        return mapHistory(result) { id, version ->
            return@mapHistory repository.findByIdAndVersion(id, version)
        }
    }

    private fun mapContactToGrps(entity: ru.sbertroika.pasiv.gate.output.model.Contact ): Contact {
        return Contact
            .newBuilder()
            .setId(entity.id.toString())
            .setValue(entity.value)
            .setType(mapContactTypeToEntity(entity.type!!))
            .setOrganizationId(entity.organizationId.toString())
            .build()
    }

    private fun mapContactTypeToGrps(type: ru.sbertroika.pasiv.gate.v1.ContactType): ContactType {
        return when(type) {
            ru.sbertroika.pasiv.gate.v1.ContactType.CT_PHONE -> ContactType.PHONE
            ru.sbertroika.pasiv.gate.v1.ContactType.CT_EMAIL -> ContactType.EMAIL
            else -> ContactType.EMAIL
        }
    }

    private fun mapContactTypeToEntity(type: ContactType): ru.sbertroika.pasiv.gate.v1.ContactType {
        return when(type) {
            ContactType.EMAIL ->  ru.sbertroika.pasiv.gate.v1.ContactType.CT_EMAIL
            ContactType.PHONE ->  ru.sbertroika.pasiv.gate.v1.ContactType.CT_PHONE
        }
    }
}