package ru.sbertroika.pasiv.gate.output.service

import arrow.core.Either
import org.springframework.stereotype.Service
import ru.sbertroika.pasiv.gate.output.dadata.DaDataClient
import ru.sbertroika.pasiv.gate.output.dadata.model.OrganizationSuggestionRequest
import ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData
import ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData
import ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions
import ru.sbertroika.pasiv.gate.v1.*


@Service
class DaDataServiceImpl(
    private val client: DaDataClient
) : DaDataService {
    override fun getOrganizationHint(request: OrganizationHintRequest): Either<Error, OrganizationHintList> = try{
        val result = client.organizationByInn(OrganizationSuggestionRequest(
            request.inn,
            request.kpp
        )).execute()
        if (result.isSuccessful)
            Either.Right(map(result.body()?.suggestions!!))
        else
            Either.Left(Error(result.errorBody().toString()))
    } catch (e: Exception) {
        Either.Left(Error(e))
    }

    private fun map(list: ArrayList<Suggestions>): OrganizationHintList {
        val result = list.map { it.data }.filterNotNull().map {
            organizationHint {
                name = it.name?.full?:""
                shortName = it.name?.short?:""
                kpp = it.kpp?:""
                inn = it.inn?:""
                okpo = it.okpo?:""
                oktmo = it.oktmo?:""
                okved = it.okved?:""
                fioDirector = it.management?.name?:""
                managerActionReason = it.management?.post?:""
                ogrn = it.ogrn?:""
                addressLegalHint = if(it.address != null && it.address!!.data != null) addressHint{
                    index = if(getAddress(it).postalCode == null) 0 else getAddress(it).postalCode!!.toInt()
                    country = getAddress(it).country?:""
                    region = getAddress(it).region?:""
                    district = getAddress(it).federalDistrict?:""
                    city = getAddress(it).cityWithType?:""
                    street = getAddress(it).streetWithType?:""
                    house = "${getAddress(it).houseType} ${getAddress(it).house}"
                    buildingOrHousing = if(getAddress(it).block == null) "" else "${getAddress(it).blockType} ${getAddress(it).block}"
                    officeOrRoom = getOfficeOrRoom(it.address?.data)
                    oktmo = if(getAddress(it).oktmo == null) 0L else getAddress(it).oktmo!!.toLong()
                    fiac = getAddress(it).fiasCode?:""
                    longitude = if(getAddress(it).geoLon == null) 0.0 else getAddress(it).geoLon!!.toDouble()
                    latitude = if(getAddress(it).geoLat == null) 0.0 else getAddress(it).geoLat!!.toDouble()
                } else addressHint{}
                contactHints += getContactsHints(it)
            }
        }
        return organizationHintList {
            organizationHint += result
        }
    }

    private fun getContactsHints(it: OrgData): List<ContactHint> {
        val result = mutableListOf<ContactHint>()
        if (it.phones != null) {
            for (phone in it.phones!!) {
                if (phone.value != null)
                    result.add(
                        contactHint {
                            type = ContactType.CT_PHONE
                            value = phone.value!!
                        }
                    )
            }
        }
        if (it.emails != null) {
            for (email in it.emails!!) {
                if (email.value != null)
                    result.add(
                        contactHint {
                            type = ContactType.CT_EMAIL
                            value = email.value!!
                        }
                    )
            }
        }
        return result
    }

    private fun getAddress(org: OrgData): AddressData {
        return org.address?.data!!
    }

    private fun getOfficeOrRoom(data: AddressData?): String {
        val builder = StringBuilder()
        if (data?.floor != null)
            builder.append("ЭТАЖ ${data.floor} ")
        if(data?.flat != null)
            builder.append("${data.flatType} ${data.flat} ")
        if(data?.room != null)
            builder.append("${data.roomType} ${data.room}")
        return builder.toString()
    }
}