package ru.sbertroika.pasiv.gate.output.dadata

import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST
import ru.sbertroika.pasiv.gate.output.dadata.model.OrganizationSuggestionRequest
import ru.sbertroika.pasiv.gate.output.dadata.model.response.OrganizationSuggestionResponse

interface DaDataClient {

    @POST("suggestions/api/4_1/rs/findById/party")
    fun organizationByInn(@Body request: OrganizationSuggestionRequest): Call<OrganizationSuggestionResponse>
}