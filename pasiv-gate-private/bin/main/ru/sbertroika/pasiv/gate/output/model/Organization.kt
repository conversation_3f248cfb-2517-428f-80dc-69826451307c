package ru.sbertroika.pasiv.gate.output.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import ru.sbertroika.history.api.*
import ru.sbertroika.pasiv.gate.util.*
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class OrganizationPK(
    val id: UUID? = null,
    val version: Int? = null
): Serializable

@Table(value = "organization")
data class Organization(

    @HistoryId
    @Column("o_id")
    var id: UUID? = null,

    @Column("o_parent_id")
    var parentId: UUID? = null,

    @Transient
    var parentName: String? = null,

    @HistoryVersion
    @Column("o_version")
    var version: Int? = null,

    @HistoryVersionAt
    @Column("o_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @HistoryVersionBy
    @Column("o_version_created_by")
    var versionCreatedBy: UUID? = null,

    /**
     * Полное наименование
     */
    @HistoryName(name = "name")
    @Column("o_name")
    var oName: String? = null,

    /**
     * Сокращенное наименование
     */
    @Column("o_short_name")
    var shortName: String? = null,

    @Column("o_kpp")
    var kpp: String? = null,

    @Column("o_inn")
    var inn: String? = null,

    @Column("o_note")
    var note: String? = null,

    @Column("o_okpo")
    var okpo: String? = null,

    @Column("o_oktmo")
    var oktmo: String? = null,

    @Column("o_ogrn")
    var ogrn: String? = null,

    @Column("o_okved")
    var okved: String? = null,

    @Column("o_fio_director")
    var fioDirector: String? = null,

    @Column("o_manager_action_reason")
    var managerActionReason: String? = null,

    @HistoryName(name = "addressLegal")
    @Column("o_address_legal_id")
    var addressLegalId: UUID? = null,

    @HistoryName(name = "addressActual")
    @Column("o_address_actual_id")
    var addressActualId: UUID? = null,

    @HistoryName(name = "addressMailing")
    @Column("o_address_mailing_id")
    var addressMailingId: UUID? = null,

    @HistoryStatus
    @Column("o_is_deleted")
    var isDeleted: Boolean = false,


)

