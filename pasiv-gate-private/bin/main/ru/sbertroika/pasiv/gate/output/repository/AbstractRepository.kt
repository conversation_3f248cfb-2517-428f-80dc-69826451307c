package ru.sbertroika.pasiv.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import java.util.*

abstract class AbstractRepository<E, K>(
    open val dbClient: DatabaseClient,
    open val repository: CoroutineCrudRepository<E, K>
) {
    abstract fun getQuery(isCount: Boolean = false): String
    abstract fun toEntity(t: Readable): E
    protected fun getPageRequest(page: Int, limit: Int): String = "${getQuery()} OFFSET ${page * limit} LIMIT $limit"
    abstract suspend fun findById(id: String): E?
    abstract fun findAll(page: Int, limit: Int): Flow<E>
    abstract fun findAll(): Flow<E>
    suspend fun countAll(): Int {
        return dbClient.sql(getQuery(true)).map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    abstract suspend fun deleted(id: String, userId: UUID)

    suspend fun save(entity: E) = repository.save(entity)

    suspend fun getCountByQuery(query: String): Int {
        return dbClient.sql(query).map { t -> (t.get(0) as Long).toInt() }.awaitOne()
    }

    fun getByQueryPageRequest(query: String, page: Int, limit: Int): String = "$query OFFSET ${page * limit} LIMIT $limit"
}

