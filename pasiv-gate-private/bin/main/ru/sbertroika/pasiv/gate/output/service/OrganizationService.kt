package ru.sbertroika.pasiv.gate.output.service

import arrow.core.Either
import ru.sbertroika.common.v1.HistoryResult
import ru.sbertroika.pasiv.gate.v1.*

interface OrganizationService {

    suspend fun createOrganization(organizationWithAddresses: OrganizationWithAddresses, userId: String): Either<Error, Unit>

    suspend fun updateOrganization(organization: Organization, userId: String): Either<Error, Unit>

    suspend fun organizationList(request: OrganizationListRequest): Either<Error, OrganizationResult>
    suspend fun organizationListForProject(request: OrganizationListForProjectRequest): Either<Error, OrganizationResult>
    suspend fun addOrganizationInProject(request: OrganizationInProjectRequest, userId: String): Either<Error, Unit>
    suspend fun removeOrganizationInProject(request: OrganizationInProjectRequest, userId: String): Either<Error, Unit>

    suspend fun getOrganization(request: ByIdRequest): Either<Error, Organization>

    suspend fun getHistory(request: ByIdWithPaginationRequest): Either<Error, HistoryResult>
    suspend fun deleteOrganization(request: ByIdRequest, userId: String): Either<Error, Unit>
    suspend fun recoverOrganization(request: ByIdRequest, userId: String): Either<Error, Unit>


}