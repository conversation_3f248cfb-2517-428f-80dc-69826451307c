package ru.sbertroika.pasiv.gate.input

import org.lognet.springboot.grpc.GRpcService
import org.slf4j.LoggerFactory
import org.springframework.security.access.annotation.Secured
import ru.sbertroika.common.toOperationError
import ru.sbertroika.common.v1.EmptyResponse
import ru.sbertroika.common.v1.HistoryResponse
import ru.sbertroika.common.v1.emptyResponse
import ru.sbertroika.common.v1.historyResponse
import ru.sbertroika.pasiv.gate.output.service.AddressService
import ru.sbertroika.pasiv.gate.output.service.ContactService
import ru.sbertroika.pasiv.gate.output.service.DaDataService
import ru.sbertroika.pasiv.gate.output.service.OrganizationService
import ru.sbertroika.pasiv.gate.v1.*
import ru.sbertroika.tkp3.security.userId
import ru.sbertroika.tkp3.security.validateUser

@GRpcService(port = 5000)
@Secured(value = ["ROLE_pasiv_console_admin"])
class PASIVGatePrivateServiceGrpc(
    private val organizationService: OrganizationService,
    private val addressService: AddressService,
    private val contactService: ContactService,
    private val daDataService: DaDataService
) : PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineImplBase() {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    override suspend fun createOrganization(request: OrganizationWithAddresses): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                organizationService.createOrganization(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun updateOrganization(request: Organization): EmptyResponse {
        log.info("updateOrganization")
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                organizationService.updateOrganization(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    {
                        emptyResponse {}
                    }
                )
            }
        )
    }

    override suspend fun organizationList(request: OrganizationListRequest): OrganizationListResponse {
        log.info("organizationList")
        return  organizationService.organizationList(request).fold(
            {  err ->
                organizationListResponse {
                    error = toOperationError(err)
                }
            }, { res ->
                organizationListResponse {
                    result = res
                }
            })
    }

    override suspend fun organizationListForProject(request: OrganizationListForProjectRequest): OrganizationListResponse {
        log.info("organizationListForProject")
        return  organizationService.organizationListForProject(request).fold(
            {  err ->
                organizationListResponse {
                    error = toOperationError(err)
                }
            }, { res ->
                organizationListResponse {
                    result = res
                }
            })
    }

    override suspend fun addOrganizationInProject(request: OrganizationInProjectRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                organizationService.addOrganizationInProject(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    {
                        emptyResponse {}
                    }
                )
            }
        )
    }

    override suspend fun removeOrganizationInProject(request: OrganizationInProjectRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                organizationService.removeOrganizationInProject(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    {
                        emptyResponse {}
                    }
                )
            }
        )
    }

    override suspend fun addressList(request: AddressListRequest): AddressListResponse {
        return  addressService.addressList(request).fold(
            {  err ->
                addressListResponse {
                    error = toOperationError(err)
                }
            }, { res ->
                addressListResponse {
                    result = res
                }
            })
    }

    override suspend fun createAddress(request: AddressCreateOrDelete): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                addressService.createAddress(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun updateAddress(request: AddressCreateOrDelete): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                addressService.updateAddress(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    {
                        emptyResponse {}
                    }
                )
            }
        )
    }

    override suspend fun contactList(request: ContactListRequest): ContactListResponse {
        return  contactService.contactList(request).fold(
            {  err ->
                contactListResponse {
                    error = toOperationError(err)
                }
            }, { res ->
                contactListResponse {
                    result = res
                }
            })
    }

    override suspend fun createContact(request: Contact): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                contactService.createContact(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun updateContact(request: Contact): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                contactService.updateContact(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    {
                        emptyResponse {}
                    }
                )
            }
        )
    }

    override suspend fun addressById(request: ByIdRequest): AddressResponse {
        return  addressService.getAddress(request).fold(
            { err ->
                addressResponse {
                    error = toOperationError(err)
                }
            },
            { res ->
                addressResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun contactById(request: ByIdRequest): ContactResponse {
        return  contactService.getContact(request).fold(
            { err ->
                contactResponse {
                    error = toOperationError(err)
                }
            },
            { res ->
                contactResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun organizationById(request: ByIdRequest): OrganizationResponse {
        return  organizationService.getOrganization(request).fold(
            { err ->
                organizationResponse {
                    error = toOperationError(err)
                }
            },
            { res ->
                organizationResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun addressHistoryById(request: ByIdWithPaginationRequest): HistoryResponse {
        return addressService.getHistory(request).fold(
            { err ->
                historyResponse {
                    error = toOperationError(err)
                }
            },
            { res ->
                historyResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun contactHistoryById(request: ByIdWithPaginationRequest): HistoryResponse {
        return contactService.getHistory(request).fold(
            { err ->
                historyResponse {
                    error = toOperationError(err)
                }
            },
            { res ->
                historyResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun organizationHistoryById(request: ByIdWithPaginationRequest): HistoryResponse {
        return organizationService.getHistory(request).fold(
            { err ->
                historyResponse {
                    error = toOperationError(err)
                }
            },
            { res ->
                historyResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun deleteAddress(request: ByIdRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                addressService.deleteAddress(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun recoverAddress(request: ByIdRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                addressService.recoverAddress(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun deleteContact(request: ByIdRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                contactService.deleteContact(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun recoverContact(request: ByIdRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                contactService.recoverContact(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun deleteOrganization(request: ByIdRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                organizationService.deleteOrganization(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun recoverOrganization(request: ByIdRequest): EmptyResponse {
        return validateUser().fold(
            { err ->
                emptyResponse {
                    error = toOperationError(err)
                }
            },
            { auth ->
                organizationService.recoverOrganization(request, auth.userId()).fold(
                    { err ->
                        emptyResponse {
                            error = toOperationError(err)
                        }
                    },
                    { emptyResponse {} }
                )
            }
        )
    }

    override suspend fun organizationHintByINN(request: OrganizationHintRequest): OrganizationHintResponse {
        return  daDataService.getOrganizationHint(request).fold(
            { err ->
                organizationHintResponse {
                    error = toOperationError(err)
                }
            },
            { res ->
                organizationHintResponse {
                    result = res
                }
            }
        )
    }
}