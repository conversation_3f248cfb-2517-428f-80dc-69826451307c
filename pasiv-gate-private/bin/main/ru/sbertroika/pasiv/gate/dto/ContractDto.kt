package ru.sbertroika.pasiv.gate.dto

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import javax.validation.Valid
import javax.validation.constraints.*

/**
 * DTO для работы с договорами
 */

@Schema(description = "Статус договора")
enum class ContractStatusDto {
    @Schema(description = "Черновик")
    DRAFT,
    
    @Schema(description = "Активный")
    ACTIVE,
    
    @Schema(description = "Истекает")
    EXPIRING,
    
    @Schema(description = "Завершен")
    COMPLETED,
    
    @Schema(description = "Расторгнут")
    TERMINATED
}

@Schema(description = "Тип договора")
enum class ContractTypeDto {
    @Schema(description = "Правила системы")
    SYSTEM_RULES,
    
    @Schema(description = "Сервисный договор")
    SERVICE,
    
    @Schema(description = "Транспортный договор")
    TRANSPORT,
    
    @Schema(description = "Процессинговый договор")
    PROCESSING
}

@Schema(description = "Тип проекта")
enum class ProjectTypeDto {
    @Schema(description = "Транспортная система")
    TRANSPORT_SYSTEM,
    
    @Schema(description = "Система метро")
    METRO_SYSTEM,
    
    @Schema(description = "Автобусная система")
    BUS_SYSTEM,
    
    @Schema(description = "Система такси")
    TAXI_SYSTEM
}

@Schema(description = "Роль организации в договоре")
enum class OrganizationRoleDto {
    @Schema(description = "Оператор")
    OPERATOR,
    
    @Schema(description = "Перевозчик")
    CARRIER,
    
    @Schema(description = "Процессинговый центр")
    PROCESSING_CENTER,
    
    @Schema(description = "Контрагент")
    CONTRACTOR,
    
    @Schema(description = "Партнер")
    PARTNER
}

@Schema(description = "Договор")
data class ContractDto(
    @Schema(description = "ID договора", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String? = null,
    
    @Schema(description = "Код проекта", example = "MSK_METRO")
    @field:NotBlank(message = "Код проекта не может быть пустым")
    @field:Size(max = 50, message = "Код проекта не может превышать 50 символов")
    val projectCode: String,
    
    @Schema(description = "Название проекта", example = "Московский метрополитен")
    @field:NotBlank(message = "Название проекта не может быть пустым")
    @field:Size(max = 200, message = "Название проекта не может превышать 200 символов")
    val projectName: String,
    
    @Schema(description = "Тип проекта")
    val projectType: ProjectTypeDto,
    
    @Schema(description = "Тип договора")
    val contractType: ContractTypeDto,
    
    @Schema(description = "Название договора", example = "Договор на оказание транспортных услуг")
    @field:NotBlank(message = "Название договора не может быть пустым")
    @field:Size(max = 300, message = "Название договора не может превышать 300 символов")
    val contractName: String,
    
    @Schema(description = "Номер договора", example = "ДОГ-2024-001")
    @field:NotBlank(message = "Номер договора не может быть пустым")
    @field:Size(max = 100, message = "Номер договора не может превышать 100 символов")
    val contractNumber: String,
    
    @Schema(description = "Дата подписания", example = "2024-01-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val signatureDate: LocalDateTime,
    
    @Schema(description = "Дата заключения", example = "2024-01-10T09:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val conclusionDate: LocalDateTime,
    
    @Schema(description = "Дата завершения", example = "2024-12-31T23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val completionDate: LocalDateTime,
    
    @Schema(description = "Статус договора")
    val status: ContractStatusDto,
    
    @Schema(description = "Внешний ID в 1С", example = "1C_CONTRACT_123")
    @field:Size(max = 100, message = "Внешний ID в 1С не может превышать 100 символов")
    val externalId1C: String? = null,
    
    @Schema(description = "Описание договора", example = "Договор на оказание услуг по перевозке пассажиров")
    @field:Size(max = 1000, message = "Описание договора не может превышать 1000 символов")
    val description: String? = null,
    
    @Schema(description = "Общая сумма договора", example = "1000000.50")
    @field:DecimalMin(value = "0.0", message = "Сумма договора не может быть отрицательной")
    val totalAmount: Double? = null,
    
    @Schema(description = "Валюта", example = "RUB")
    @field:Size(max = 3, message = "Код валюты должен содержать 3 символа")
    @field:Pattern(regexp = "[A-Z]{3}", message = "Код валюты должен содержать 3 заглавные буквы")
    val currency: String? = "RUB",
    
    @Schema(description = "Условия оплаты (дни)", example = "30")
    @field:Min(0, message = "Условия оплаты не могут быть отрицательными")
    @field:Max(365, message = "Условия оплаты не могут превышать 365 дней")
    val paymentTerms: Int? = null,
    
    @Schema(description = "Ставка НДС (%)", example = "20.0")
    @field:DecimalMin(value = "0.0", message = "Ставка НДС не может быть отрицательной")
    @field:DecimalMax(value = "100.0", message = "Ставка НДС не может превышать 100%")
    val vatRate: Double? = null,
    
    @Schema(description = "Удален ли договор")
    val isDeleted: Boolean = false,
    
    @Schema(description = "Дата создания", example = "2024-01-01T12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val createdDate: LocalDateTime,
    
    @Schema(description = "Дата последней синхронизации", example = "2024-01-01T12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val lastSyncDate: LocalDateTime? = null
)

@Schema(description = "Организация в договоре")
data class ContractOrganizationDto(
    @Schema(description = "ID связи", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String? = null,
    
    @Schema(description = "ID договора", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID договора не может быть пустым")
    val contractId: String,
    
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID организации не может быть пустым")
    val organizationId: String,
    
    @Schema(description = "Название организации", example = "ООО \"Ромашка\"")
    @field:NotBlank(message = "Название организации не может быть пустым")
    @field:Size(max = 500, message = "Название организации не может превышать 500 символов")
    val organizationName: String,
    
    @Schema(description = "Роль организации")
    val role: OrganizationRoleDto,
    
    @Schema(description = "Описание роли", example = "Основной перевозчик на маршруте №1")
    @field:Size(max = 500, message = "Описание роли не может превышать 500 символов")
    val roleDescription: String? = null,
    
    @Schema(description = "Активна с", example = "2024-01-01T00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val activeFrom: LocalDateTime,
    
    @Schema(description = "Активна до", example = "2024-12-31T23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val activeTill: LocalDateTime? = null,
    
    @Schema(description = "Удалена ли связь")
    val isDeleted: Boolean = false
)

@Schema(description = "Договор с организациями")
data class ContractWithOrganizationsDto(
    @Schema(description = "Договор")
    @field:Valid
    val contract: ContractDto,

    @Schema(description = "Организации в договоре")
    @field:Valid
    val organizations: List<ContractOrganizationDto> = emptyList()
)

@Schema(description = "Фильтр для поиска договоров")
data class ContractFilterDto(
    @Schema(description = "Показывать удаленные договоры")
    val isDeleted: Boolean? = null,

    @Schema(description = "Код проекта", example = "MSK_METRO")
    @field:Size(max = 50, message = "Код проекта не может превышать 50 символов")
    val projectCode: String? = null,

    @Schema(description = "Номер договора", example = "ДОГ-2024")
    @field:Size(max = 100, message = "Поиск по номеру договора не может превышать 100 символов")
    val contractNumber: String? = null,

    @Schema(description = "Название договора", example = "транспорт")
    @field:Size(max = 200, message = "Поиск по названию договора не может превышать 200 символов")
    val contractName: String? = null,

    @Schema(description = "Статус договора")
    val status: ContractStatusDto? = null,

    @Schema(description = "Тип договора")
    val contractType: ContractTypeDto? = null,

    @Schema(description = "Тип проекта")
    val projectType: ProjectTypeDto? = null,

    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    val organizationId: String? = null
)

@Schema(description = "Запрос списка договоров")
data class ContractListRequestDto(
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,

    @Schema(description = "Фильтры поиска")
    val filter: ContractFilterDto? = null
)

@Schema(description = "Результат списка договоров")
data class ContractListResultDto(
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,

    @Schema(description = "Примененные фильтры")
    val filter: ContractFilterDto? = null,

    @Schema(description = "Список договоров")
    val contracts: List<ContractDto>
)

@Schema(description = "Запрос договоров по проекту")
data class ContractsByProjectRequestDto(
    @Schema(description = "Код проекта", example = "MSK_METRO")
    @field:NotBlank(message = "Код проекта не может быть пустым")
    @field:Size(max = 50, message = "Код проекта не может превышать 50 символов")
    val projectCode: String,

    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null
)

@Schema(description = "Фильтр для поиска договоров")
data class ContractFilterDto(
    @Schema(description = "Показывать удаленные договоры")
    val isDeleted: Boolean? = null,

    @Schema(description = "Код проекта", example = "MSK_METRO")
    @field:Size(max = 50, message = "Код проекта не может превышать 50 символов")
    val projectCode: String? = null,

    @Schema(description = "Номер договора", example = "ДОГ-2024")
    @field:Size(max = 100, message = "Поиск по номеру договора не может превышать 100 символов")
    val contractNumber: String? = null,

    @Schema(description = "Название договора", example = "транспорт")
    @field:Size(max = 200, message = "Поиск по названию договора не может превышать 200 символов")
    val contractName: String? = null,

    @Schema(description = "Статус договора")
    val status: ContractStatusDto? = null,

    @Schema(description = "Тип договора")
    val contractType: ContractTypeDto? = null,

    @Schema(description = "Тип проекта")
    val projectType: ProjectTypeDto? = null,

    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    val organizationId: String? = null
)

@Schema(description = "Запрос списка договоров")
data class ContractListRequestDto(
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,

    @Schema(description = "Фильтры поиска")
    val filter: ContractFilterDto? = null
)

@Schema(description = "Результат списка договоров")
data class ContractListResultDto(
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,

    @Schema(description = "Примененные фильтры")
    val filter: ContractFilterDto? = null,

    @Schema(description = "Список договоров")
    val contracts: List<ContractDto>
)

@Schema(description = "Запрос договоров по проекту")
data class ContractsByProjectRequestDto(
    @Schema(description = "Код проекта", example = "MSK_METRO")
    @field:NotBlank(message = "Код проекта не может быть пустым")
    @field:Size(max = 50, message = "Код проекта не может превышать 50 символов")
    val projectCode: String,

    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null
)
