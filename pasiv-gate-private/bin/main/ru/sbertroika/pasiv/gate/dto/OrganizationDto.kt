package ru.sbertroika.pasiv.gate.dto

import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.Valid
import javax.validation.constraints.NotBlank
import javax.validation.constraints.Pattern
import javax.validation.constraints.Size

/**
 * DTO для работы с организациями
 */

@Schema(description = "Организация")
data class OrganizationDto(
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String? = null,
    
    @Schema(description = "Родительская организация")
    val parent: OrganizationDto? = null,
    
    @Schema(description = "Наименование организации", example = "ООО \"Ромашка\"")
    @field:NotBlank(message = "Наименование организации не может быть пустым")
    @field:Size(max = 500, message = "Наименование организации не может превышать 500 символов")
    val name: String,
    
    @Schema(description = "Сокращенное наименование", example = "Ромашка")
    @field:NotBlank(message = "Сокращенное наименование не может быть пустым")
    @field:Size(max = 200, message = "Сокращенное наименование не может превышать 200 символов")
    val shortName: String,
    
    @Schema(description = "КПП", example = "773301001")
    @field:NotBlank(message = "КПП не может быть пустым")
    @field:Pattern(regexp = "\\d{9}", message = "КПП должен содержать 9 цифр")
    val kpp: String,
    
    @Schema(description = "ИНН", example = "7733123456")
    @field:NotBlank(message = "ИНН не может быть пустым")
    @field:Pattern(regexp = "\\d{10}|\\d{12}", message = "ИНН должен содержать 10 или 12 цифр")
    val inn: String,
    
    @Schema(description = "ОГРН", example = "1027739123456")
    @field:NotBlank(message = "ОГРН не может быть пустым")
    @field:Pattern(regexp = "\\d{13}|\\d{15}", message = "ОГРН должен содержать 13 или 15 цифр")
    val ogrn: String,
    
    @Schema(description = "Заметка", example = "Дополнительная информация")
    @field:Size(max = 1000, message = "Заметка не может превышать 1000 символов")
    val note: String? = null,
    
    @Schema(description = "ОКПО", example = "12345678")
    @field:Pattern(regexp = "\\d{8}|\\d{10}", message = "ОКПО должен содержать 8 или 10 цифр")
    val okpo: String? = null,
    
    @Schema(description = "ОКТМО", example = "45123000")
    @field:Pattern(regexp = "\\d{8}|\\d{11}", message = "ОКТМО должен содержать 8 или 11 цифр")
    val oktmo: String? = null,
    
    @Schema(description = "ОКВЭД", example = "62.01")
    @field:Size(max = 20, message = "ОКВЭД не может превышать 20 символов")
    val okved: String? = null,
    
    @Schema(description = "ФИО руководителя", example = "Иванов Иван Иванович")
    @field:Size(max = 200, message = "ФИО руководителя не может превышать 200 символов")
    val fioDirector: String? = null,
    
    @Schema(description = "Юридический адрес", example = "г. Москва, ул. Ленина, д. 1")
    @field:Size(max = 500, message = "Юридический адрес не может превышать 500 символов")
    val addressLegal: String? = null,
    
    @Schema(description = "Фактический адрес", example = "г. Москва, ул. Ленина, д. 1")
    @field:Size(max = 500, message = "Фактический адрес не может превышать 500 символов")
    val addressActual: String? = null,
    
    @Schema(description = "Почтовый адрес", example = "г. Москва, ул. Ленина, д. 1")
    @field:Size(max = 500, message = "Почтовый адрес не может превышать 500 символов")
    val addressMailing: String? = null,
    
    @Schema(description = "Основание действия руководителя", example = "Устав")
    @field:Size(max = 200, message = "Основание действия руководителя не может превышать 200 символов")
    val managerActionReason: String? = null,
    
    @Schema(description = "Удалена ли организация")
    val isDeleted: Boolean = false
)

@Schema(description = "Организация с адресами")
data class OrganizationWithAddressesDto(
    @Schema(description = "Организация")
    @field:Valid
    val organization: OrganizationDto,
    
    @Schema(description = "Юридический адрес")
    @field:Valid
    val addressLegal: AddressDto,
    
    @Schema(description = "Фактический адрес")
    @field:Valid
    val addressActual: AddressDto? = null,
    
    @Schema(description = "Почтовый адрес")
    @field:Valid
    val addressMailing: AddressDto? = null
)

@Schema(description = "Фильтр для поиска организаций")
data class OrganizationFilterDto(
    @Schema(description = "Показывать удаленные организации")
    val isDeleted: Boolean? = null,
    
    @Schema(description = "Поиск по наименованию", example = "Ромашка")
    @field:Size(max = 200, message = "Поисковый запрос не может превышать 200 символов")
    val name: String? = null,
    
    @Schema(description = "Поиск по ИНН", example = "7733123456")
    @field:Pattern(regexp = "\\d{0,12}", message = "ИНН должен содержать только цифры")
    val inn: String? = null,
    
    @Schema(description = "Поиск по КПП", example = "773301001")
    @field:Pattern(regexp = "\\d{0,9}", message = "КПП должен содержать только цифры")
    val kpp: String? = null
)

@Schema(description = "Запрос списка организаций")
data class OrganizationListRequestDto(
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,
    
    @Schema(description = "Фильтры поиска")
    val filter: OrganizationFilterDto? = null
)

@Schema(description = "Результат списка организаций")
data class OrganizationListResultDto(
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,
    
    @Schema(description = "Примененные фильтры")
    val filter: OrganizationFilterDto? = null,
    
    @Schema(description = "Список организаций")
    val organizations: List<OrganizationDto>
)

@Schema(description = "Запрос списка организаций для проекта")
data class OrganizationListForProjectRequestDto(
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,
    
    @Schema(description = "ID проекта", example = "project-123")
    @field:NotBlank(message = "ID проекта не может быть пустым")
    val projectId: String
)

@Schema(description = "Запрос добавления/удаления организации в проект")
data class OrganizationInProjectRequestDto(
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID организации не может быть пустым")
    val organizationId: String,
    
    @Schema(description = "ID проекта", example = "project-123")
    @field:NotBlank(message = "ID проекта не может быть пустым")
    val projectId: String
)

@Schema(description = "Запрос подсказки по ИНН")
data class OrganizationHintRequestDto(
    @Schema(description = "ИНН", example = "7733123456")
    @field:NotBlank(message = "ИНН не может быть пустым")
    @field:Pattern(regexp = "\\d{10}|\\d{12}", message = "ИНН должен содержать 10 или 12 цифр")
    val inn: String,
    
    @Schema(description = "КПП", example = "773301001")
    @field:Pattern(regexp = "\\d{9}", message = "КПП должен содержать 9 цифр")
    val kpp: String? = null
)

@Schema(description = "Подсказка организации")
data class OrganizationHintDto(
    @Schema(description = "Наименование организации")
    val name: String,
    
    @Schema(description = "Сокращенное наименование")
    val shortName: String,
    
    @Schema(description = "КПП")
    val kpp: String,
    
    @Schema(description = "ИНН")
    val inn: String,
    
    @Schema(description = "ОГРН")
    val ogrn: String,
    
    @Schema(description = "Заметка")
    val note: String? = null,
    
    @Schema(description = "ОКПО")
    val okpo: String? = null,
    
    @Schema(description = "ОКТМО")
    val oktmo: String? = null,
    
    @Schema(description = "ОКВЭД")
    val okved: String? = null,
    
    @Schema(description = "ФИО руководителя")
    val fioDirector: String? = null,
    
    @Schema(description = "Основание действия руководителя")
    val managerActionReason: String? = null,
    
    @Schema(description = "Подсказка юридического адреса")
    val addressLegalHint: AddressHintDto? = null,
    
    @Schema(description = "Подсказки контактов")
    val contactHints: List<ContactHintDto> = emptyList()
)

@Schema(description = "Список подсказок организаций")
data class OrganizationHintListDto(
    @Schema(description = "Подсказки организаций")
    val organizationHints: List<OrganizationHintDto>
)
