package ru.sbertroika.pasiv.gate.dto

import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.constraints.*

/**
 * DTO для работы с адресами
 */

@Schema(description = "Тип адреса")
enum class AddressTypeDto {
    @Schema(description = "Юридический адрес")
    LEGAL,
    
    @Schema(description = "Фактический адрес")
    ACTUAL,
    
    @Schema(description = "Почтовый адрес")
    MAILING
}

@Schema(description = "Адрес")
data class AddressDto(
    @Schema(description = "ID адреса", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String? = null,
    
    @Schema(description = "Наименование адреса", example = "Головной офис")
    @field:NotBlank(message = "Наименование адреса не может быть пустым")
    @field:Size(max = 200, message = "Наименование адреса не может превышать 200 символов")
    val name: String,
    
    @Schema(description = "Почтовый индекс", example = "123456")
    @field:Min(100000, message = "Почтовый индекс должен быть 6-значным числом")
    @field:Max(999999, message = "Почтовый индекс должен быть 6-значным числом")
    val index: Int? = null,
    
    @Schema(description = "Страна", example = "Россия")
    @field:Size(max = 100, message = "Название страны не может превышать 100 символов")
    val country: String? = null,
    
    @Schema(description = "Регион/область", example = "Московская область")
    @field:NotBlank(message = "Регион не может быть пустым")
    @field:Size(max = 100, message = "Название региона не может превышать 100 символов")
    val region: String,
    
    @Schema(description = "Район", example = "Подольский район")
    @field:Size(max = 100, message = "Название района не может превышать 100 символов")
    val district: String? = null,
    
    @Schema(description = "Город", example = "Москва")
    @field:NotBlank(message = "Город не может быть пустым")
    @field:Size(max = 100, message = "Название города не может превышать 100 символов")
    val city: String,
    
    @Schema(description = "Улица", example = "ул. Ленина")
    @field:Size(max = 200, message = "Название улицы не может превышать 200 символов")
    val street: String? = null,
    
    @Schema(description = "Дом", example = "д. 1")
    @field:NotBlank(message = "Номер дома не может быть пустым")
    @field:Size(max = 50, message = "Номер дома не может превышать 50 символов")
    val house: String,
    
    @Schema(description = "Строение/корпус", example = "стр. 1")
    @field:Size(max = 50, message = "Строение не может превышать 50 символов")
    val buildingOrHousing: String? = null,
    
    @Schema(description = "Офис/комната", example = "оф. 101")
    @field:Size(max = 50, message = "Номер офиса не может превышать 50 символов")
    val officeOrRoom: String? = null,
    
    @Schema(description = "Долгота", example = "37.6173")
    @field:DecimalMin(value = "-180.0", message = "Долгота должна быть от -180 до 180")
    @field:DecimalMax(value = "180.0", message = "Долгота должна быть от -180 до 180")
    val longitude: Double? = null,
    
    @Schema(description = "Широта", example = "55.7558")
    @field:DecimalMin(value = "-90.0", message = "Широта должна быть от -90 до 90")
    @field:DecimalMax(value = "90.0", message = "Широта должна быть от -90 до 90")
    val latitude: Double? = null,
    
    @Schema(description = "Комментарий", example = "Дополнительная информация")
    @field:Size(max = 500, message = "Комментарий не может превышать 500 символов")
    val comment: String? = null,
    
    @Schema(description = "ОКТМО", example = "45123000001")
    @field:Min(0, message = "ОКТМО должен быть положительным числом")
    val oktmo: Long? = null,
    
    @Schema(description = "ФИАС", example = "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
    @field:Size(max = 36, message = "ФИАС не может превышать 36 символов")
    val fiac: String? = null,
    
    @Schema(description = "Удален ли адрес")
    val isDeleted: Boolean = false
)

@Schema(description = "Подсказка адреса")
data class AddressHintDto(
    @Schema(description = "Почтовый индекс")
    val index: Int? = null,
    
    @Schema(description = "Страна")
    val country: String? = null,
    
    @Schema(description = "Регион/область")
    val region: String,
    
    @Schema(description = "Район")
    val district: String? = null,
    
    @Schema(description = "Город")
    val city: String,
    
    @Schema(description = "Улица")
    val street: String? = null,
    
    @Schema(description = "Дом")
    val house: String,
    
    @Schema(description = "Строение/корпус")
    val buildingOrHousing: String? = null,
    
    @Schema(description = "Офис/комната")
    val officeOrRoom: String? = null,
    
    @Schema(description = "Долгота")
    val longitude: Double? = null,
    
    @Schema(description = "Широта")
    val latitude: Double? = null,
    
    @Schema(description = "ОКТМО")
    val oktmo: Long? = null,
    
    @Schema(description = "ФИАС")
    val fiac: String? = null
)

@Schema(description = "Создание или удаление адреса")
data class AddressCreateOrDeleteDto(
    @Schema(description = "Адрес")
    val address: AddressDto,
    
    @Schema(description = "Тип адреса")
    val type: AddressTypeDto,
    
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID организации не может быть пустым")
    val organizationId: String
)

@Schema(description = "Фильтр для поиска адресов")
data class AddressFilterDto(
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    val organizationId: String? = null,
    
    @Schema(description = "Поиск по городу", example = "Москва")
    @field:Size(max = 100, message = "Поисковый запрос по городу не может превышать 100 символов")
    val city: String? = null,
    
    @Schema(description = "Поиск по улице", example = "Ленина")
    @field:Size(max = 200, message = "Поисковый запрос по улице не может превышать 200 символов")
    val street: String? = null,
    
    @Schema(description = "Показывать удаленные адреса")
    val isDeleted: Boolean? = null
)

@Schema(description = "Запрос списка адресов")
data class AddressListRequestDto(
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,
    
    @Schema(description = "Фильтры поиска")
    val filters: AddressFilterDto? = null
)

@Schema(description = "Результат списка адресов")
data class AddressListResultDto(
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,
    
    @Schema(description = "Примененные фильтры")
    val filters: AddressFilterDto? = null,
    
    @Schema(description = "Список адресов")
    val addresses: List<AddressDto>
)
