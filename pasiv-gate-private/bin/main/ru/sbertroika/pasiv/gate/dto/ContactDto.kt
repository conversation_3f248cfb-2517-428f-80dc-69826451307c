package ru.sbertroika.pasiv.gate.dto

import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.constraints.Email
import javax.validation.constraints.NotBlank
import javax.validation.constraints.Pattern
import javax.validation.constraints.Size

/**
 * DTO для работы с контактами
 */

@Schema(description = "Тип контакта")
enum class ContactTypeDto {
    @Schema(description = "Телефон")
    PHONE,
    
    @Schema(description = "Email")
    EMAIL
}

@Schema(description = "Контакт")
data class ContactDto(
    @Schema(description = "ID контакта", example = "123e4567-e89b-12d3-a456-426614174000")
    val id: String? = null,
    
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID организации не может быть пустым")
    val organizationId: String,
    
    @Schema(description = "Тип контакта")
    val type: ContactTypeDto,
    
    @Schema(description = "Значение контакта", example = "+7 (495) 123-45-67")
    @field:NotBlank(message = "Значение контакта не может быть пустым")
    @field:Size(max = 200, message = "Значение контакта не может превышать 200 символов")
    val value: String,
    
    @Schema(description = "Удален ли контакт")
    val isDeleted: Boolean = false
) {
    init {
        // Валидация в зависимости от типа контакта
        when (type) {
            ContactTypeDto.PHONE -> {
                require(value.matches(Regex("^\\+?[1-9]\\d{1,14}$|^\\+?[1-9]\\d{0,3}[\\s\\-\\(\\)\\d]{4,14}$"))) {
                    "Некорректный формат телефона"
                }
            }
            ContactTypeDto.EMAIL -> {
                require(value.matches(Regex("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"))) {
                    "Некорректный формат email"
                }
            }
        }
    }
}

@Schema(description = "Подсказка контакта")
data class ContactHintDto(
    @Schema(description = "Тип контакта")
    val type: ContactTypeDto,
    
    @Schema(description = "Значение контакта")
    val value: String
)

@Schema(description = "Фильтр для поиска контактов")
data class ContactFilterDto(
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    val organizationId: String? = null,
    
    @Schema(description = "Тип контакта")
    val type: ContactTypeDto? = null,
    
    @Schema(description = "Поиск по значению", example = "+7495")
    @field:Size(max = 50, message = "Поисковый запрос не может превышать 50 символов")
    val value: String? = null,
    
    @Schema(description = "Показывать удаленные контакты")
    val isDeleted: Boolean? = null
)

@Schema(description = "Запрос списка контактов")
data class ContactListRequestDto(
    @Schema(description = "Параметры пагинации")
    val pagination: PaginationRequestDto? = null,
    
    @Schema(description = "Фильтры поиска")
    val filters: ContactFilterDto? = null
)

@Schema(description = "Результат списка контактов")
data class ContactListResultDto(
    @Schema(description = "Информация о пагинации")
    val pagination: PaginationResponseDto? = null,
    
    @Schema(description = "Примененные фильтры")
    val filters: ContactFilterDto? = null,
    
    @Schema(description = "Список контактов")
    val contacts: List<ContactDto>
)

@Schema(description = "Запрос создания контакта с валидацией")
data class CreateContactRequestDto(
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID организации не может быть пустым")
    val organizationId: String,
    
    @Schema(description = "Тип контакта")
    val type: ContactTypeDto,
    
    @Schema(description = "Значение контакта")
    @field:NotBlank(message = "Значение контакта не может быть пустым")
    val value: String
) {
    
    @Schema(description = "Валидированное значение контакта")
    val validatedValue: String
        get() = when (type) {
            ContactTypeDto.PHONE -> validatePhone(value)
            ContactTypeDto.EMAIL -> validateEmail(value)
        }
    
    private fun validatePhone(phone: String): String {
        val cleanPhone = phone.replace(Regex("[^+\\d]"), "")
        
        if (!cleanPhone.matches(Regex("^\\+?[1-9]\\d{10,14}$"))) {
            throw IllegalArgumentException("Некорректный формат телефона. Ожидается формат: +7XXXXXXXXXX")
        }
        
        return if (cleanPhone.startsWith("8") && cleanPhone.length == 11) {
            "+7${cleanPhone.substring(1)}"
        } else if (cleanPhone.startsWith("7") && cleanPhone.length == 11) {
            "+$cleanPhone"
        } else if (!cleanPhone.startsWith("+")) {
            "+$cleanPhone"
        } else {
            cleanPhone
        }
    }
    
    private fun validateEmail(email: String): String {
        val trimmedEmail = email.trim().lowercase()
        
        if (!trimmedEmail.matches(Regex("^[a-z0-9+_.-]+@[a-z0-9.-]+\\.[a-z]{2,}$"))) {
            throw IllegalArgumentException("Некорректный формат email")
        }
        
        return trimmedEmail
    }
}

@Schema(description = "Запрос обновления контакта")
data class UpdateContactRequestDto(
    @Schema(description = "ID контакта", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID контакта не может быть пустым")
    val id: String,
    
    @Schema(description = "ID организации", example = "123e4567-e89b-12d3-a456-426614174000")
    @field:NotBlank(message = "ID организации не может быть пустым")
    val organizationId: String,
    
    @Schema(description = "Тип контакта")
    val type: ContactTypeDto,
    
    @Schema(description = "Значение контакта")
    @field:NotBlank(message = "Значение контакта не может быть пустым")
    val value: String
) {
    
    fun toContactDto(): ContactDto {
        val createRequest = CreateContactRequestDto(organizationId, type, value)
        return ContactDto(
            id = id,
            organizationId = organizationId,
            type = type,
            value = createRequest.validatedValue,
            isDeleted = false
        )
    }
}
