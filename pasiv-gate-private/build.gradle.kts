import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    idea
    kotlin("jvm")
    kotlin("plugin.spring")
    id("org.springframework.boot") version "3.0.1"
    id("io.spring.dependency-management") version "1.1.0"
    id("org.jetbrains.kotlin.kapt")
    id("com.google.protobuf")
    id("org.openapi.generator") version "7.0.1"
    `maven-publish`
}

group = "ru.sbertroika.pasiv"
version = rootProject.version
java.sourceCompatibility = JavaVersion.VERSION_17

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

dependencies {
    implementation(project(":pasiv-api"))
    implementation("ru.sbertroika.common:common-manifest:1.0.1")
    implementation("ru.sbertroika.common:history-api:1.0.1")
    implementation("ru.sbertroika.common:lib-history:1.0.1")
    implementation(project(":pasiv-api-private"))
    implementation("ru.sbertroika.common:common-api:1.0.1")
    implementation("ru.sbertroika.common:grpc-starter:1.0.1")
    implementation("ru.sbertroika.common:security-starter:1.0.1")

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-data-r2dbc")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.security:spring-security-oauth2-jose")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")

    // REST API support
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-validation")

    // OpenAPI/Swagger support
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0")
    implementation("org.springdoc:springdoc-openapi-starter-common:2.2.0")
    implementation("io.swagger.core.v3:swagger-annotations:2.2.15")

    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.squareup.okhttp3:okhttp")
    implementation("com.squareup.okhttp3:okhttp-tls")
    implementation("com.squareup.okhttp3:logging-interceptor")

    //Kotlin
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.14.0")

    //Kotlin-ext
    implementation("io.arrow-kt:arrow-core:1.0.1")

    // MapStruct
    compileOnly("org.mapstruct:mapstruct:1.5.3.Final")
    kapt("org.mapstruct:mapstruct-processor:1.5.3.Final")

    //gRPC
    implementation(libs.grpcKotlinStub)
    implementation(libs.grpcProtobuf)
    implementation(libs.grpcStub)
    implementation(libs.protobufKotlin)
    implementation("io.grpc:grpc-netty:1.52.1")
    implementation("io.grpc:grpc-okhttp:1.52.1")

    runtimeOnly("org.postgresql:r2dbc-postgresql")
    runtimeOnly("io.r2dbc:r2dbc-pool")

    implementation("org.flywaydb:flyway-core:9.14.1")
    runtimeOnly("org.postgresql:postgresql:42.5.2")
    implementation("io.r2dbc:r2dbc-postgresql:0.8.13.RELEASE")
    implementation("org.postgresql:r2dbc-postgresql:1.0.4.RELEASE")

    //Test
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly("org.projectlombok:lombok")
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
    testImplementation("com.squareup.okhttp3:okhttp:4.10.0")
    testImplementation("com.squareup.okhttp3:logging-interceptor:4.10.0")
}

tasks.named("bootJar") {
    enabled = true
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<Test>().configureEach {
    useJUnitPlatform()
    testLogging {
        showStandardStreams = true
    }
}

tasks.named<Test>("test") {
    useJUnitPlatform()
}

tasks.named<Jar>("jar") {
    enabled = false
}

// OpenAPI generation configuration
val openApiSpecFile = "$buildDir/openapi/openapi.yaml"
val generatedClientDir = "$buildDir/generated-client"

//// Task to generate OpenAPI spec from running application
//tasks.register<Exec>("generateOpenApiSpec") {
//    dependsOn("bootJar")
//
//    description = "Generate OpenAPI specification from running application"
//    group = "openapi"
//
//    // Start the application in background and extract OpenAPI spec
//    commandLine("bash", "-c", """
//        # Start application in background
//        java -jar build/libs/${project.name}-${version}.jar --server.port=8081 &
//        APP_PID=$!
//
//        # Wait for application to start
//        sleep 30
//
//        # Create output directory
//        mkdir -p build/openapi
//
//        # Download OpenAPI spec
//        curl -s http://localhost:8081/v3/api-docs.yaml > $openApiSpecFile || echo "Failed to download OpenAPI spec"
//
//        # Stop application
//        kill $APP_PID || true
//
//        echo "OpenAPI spec generated at $openApiSpecFile"
//    """.trimIndent())
//
//    outputs.file(openApiSpecFile)
//}
//
//// Task to generate TypeScript client for pasiv-ui
//tasks.register("generateTypeScriptClient") {
//    dependsOn("generateOpenApiSpec")
//
//    description = "Generate TypeScript client from OpenAPI specification"
//    group = "openapi"
//
//    doLast {
//        exec {
//            commandLine(
//                "npx", "@openapitools/openapi-generator-cli", "generate",
//                "-i", openApiSpecFile,
//                "-g", "typescript-axios",
//                "-o", "$generatedClientDir/typescript",
//                "--additional-properties=npmName=pasiv-gate-private-client,npmVersion=${version}",
//                "--type-mappings=DateTime=string"
//            )
//        }
//
//        // Copy generated client to pasiv-ui
//        copy {
//            from("$generatedClientDir/typescript")
//            into("../pasiv-ui/src/generated/rest-client")
//        }
//
//        println("TypeScript client generated and copied to pasiv-ui")
//    }
//
//    inputs.file(openApiSpecFile)
//    outputs.dir("$generatedClientDir/typescript")
//}

publishing {
    repositories {
        maven {
            val releasesRepoUrl = "https://nexus.sbertroika.tech/repository/maven-releases/"
            val snapshotsRepoUrl = "https://nexus.sbertroika.tech/repository/maven-snapshots/"
            url = uri(if (version.toString().endsWith("SNAPSHOT")) snapshotsRepoUrl else releasesRepoUrl)

            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }

    publications {
        create<MavenPublication>("maven") {
            from(components["java"])
        }
    }
}