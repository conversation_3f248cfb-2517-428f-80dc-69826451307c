package ru.sbertroika.pasiv.gate.output.service;

@org.springframework.stereotype.Service()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0017\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\"\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\t2\u0006\u0010\f\u001a\u00020\rH\u0096@\u00a2\u0006\u0002\u0010\u000eJ\u0012\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0012J\u001a\u0010\u0013\u001a\u00020\u00102\b\u0010\u0014\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0015\u001a\u00020\u0010H\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/ManifestServiceImpl;", "Lru/sbertroika/pasiv/gate/output/service/ManifestService;", "organizationRepository", "Lru/sbertroika/pasiv/gate/output/repository/OrganizationCrudRepository;", "addressRepository", "Lru/sbertroika/pasiv/gate/output/repository/AddressRepository;", "<init>", "(Lru/sbertroika/pasiv/gate/output/repository/OrganizationCrudRepository;Lru/sbertroika/pasiv/gate/output/repository/AddressRepository;)V", "getManifest", "Larrow/core/Either;", "", "Lru/sbertroika/common/manifest/v1/pasiv/ManifestPasiv;", "request", "Lru/sbertroika/common/manifest/v1/ManifestRequest;", "(Lru/sbertroika/common/manifest/v1/ManifestRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addressAsString", "", "address", "Lru/sbertroika/pasiv/gate/output/model/Address;", "addressFieldToString", "addressField", "returnedString", "pasiv-gate-private"})
public class ManifestServiceImpl implements ru.sbertroika.pasiv.gate.output.service.ManifestService {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.OrganizationCrudRepository organizationRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.AddressRepository addressRepository = null;
    
    public ManifestServiceImpl(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.OrganizationCrudRepository organizationRepository, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.AddressRepository addressRepository) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getManifest(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.common.manifest.v1.ManifestRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Throwable, ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv>> $completion) {
        return null;
    }
    
    private java.lang.String addressAsString(ru.sbertroika.pasiv.gate.output.model.Address address) {
        return null;
    }
    
    private java.lang.String addressFieldToString(java.lang.String addressField, java.lang.String returnedString) {
        return null;
    }
}