package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;", "", "<init>", "(Ljava/lang/String;I)V", "SYSTEM_RULES", "SERVICE", "TRANSPORT", "PROCESSING", "pasiv-gate-private"})
public enum ContractTypeDto {
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0440\u0430\u0432\u0438\u043b\u0430 \u0441\u0438\u0441\u0442\u0435\u043c\u044b")
    /*public static final*/ SYSTEM_RULES /* = new SYSTEM_RULES() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0435\u0440\u0432\u0438\u0441\u043d\u044b\u0439 \u0434\u043e\u0433\u043e\u0432\u043e\u0440")
    /*public static final*/ SERVICE /* = new SERVICE() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u044b\u0439 \u0434\u043e\u0433\u043e\u0432\u043e\u0440")
    /*public static final*/ TRANSPORT /* = new TRANSPORT() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0440\u043e\u0446\u0435\u0441\u0441\u0438\u043d\u0433\u043e\u0432\u044b\u0439 \u0434\u043e\u0433\u043e\u0432\u043e\u0440")
    /*public static final*/ PROCESSING /* = new PROCESSING() */;
    
    ContractTypeDto() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.dto.ContractTypeDto> getEntries() {
        return null;
    }
}