package ru.sbertroika.pasiv.gate.output.dadata;

@org.springframework.stereotype.Component()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0017\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0007H\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lru/sbertroika/pasiv/gate/output/dadata/TokenStorageImpl;", "Lru/sbertroika/pasiv/gate/output/dadata/TokenStorage;", "repository", "Lru/sbertroika/pasiv/gate/output/repository/DaDataValueRepository;", "<init>", "(Lru/sbertroika/pasiv/gate/output/repository/DaDataValueRepository;)V", "get", "", "key", "pasiv-gate-private"})
public class TokenStorageImpl implements ru.sbertroika.pasiv.gate.output.dadata.TokenStorage {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.DaDataValueRepository repository = null;
    
    public TokenStorageImpl(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.DaDataValueRepository repository) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String get(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return null;
    }
}