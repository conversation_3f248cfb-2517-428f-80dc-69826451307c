package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u043e\u043b\u044c \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438 \u0432 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0435")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/OrganizationRoleDto;", "", "<init>", "(Ljava/lang/String;I)V", "OPERATOR", "CARRIER", "PROCESSING_CENTER", "CONTRACTOR", "PARTNER", "pasiv-gate-private"})
public enum OrganizationRoleDto {
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u043f\u0435\u0440\u0430\u0442\u043e\u0440")
    /*public static final*/ OPERATOR /* = new OPERATOR() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0435\u0440\u0435\u0432\u043e\u0437\u0447\u0438\u043a")
    /*public static final*/ CARRIER /* = new CARRIER() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0440\u043e\u0446\u0435\u0441\u0441\u0438\u043d\u0433\u043e\u0432\u044b\u0439 \u0446\u0435\u043d\u0442\u0440")
    /*public static final*/ PROCESSING_CENTER /* = new PROCESSING_CENTER() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u043d\u0442\u0440\u0430\u0433\u0435\u043d\u0442")
    /*public static final*/ CONTRACTOR /* = new CONTRACTOR() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0430\u0440\u0442\u043d\u0435\u0440")
    /*public static final*/ PARTNER /* = new PARTNER() */;
    
    OrganizationRoleDto() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.dto.OrganizationRoleDto> getEntries() {
        return null;
    }
}