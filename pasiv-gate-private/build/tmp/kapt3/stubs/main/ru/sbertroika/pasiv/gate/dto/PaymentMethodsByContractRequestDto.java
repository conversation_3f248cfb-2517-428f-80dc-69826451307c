package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0440\u043e\u0441 \u0441\u043f\u043e\u0441\u043e\u0431\u043e\u0432 \u043e\u043f\u043b\u0430\u0442\u044b \u043f\u043e \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0443")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B)\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000fJ0\u0010\u0014\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0015J\u0013\u0010\u0016\u001a\u00020\u00072\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010\u0010\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001b"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/PaymentMethodsByContractRequestDto;", "", "contractId", "", "pagination", "Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;", "includeInactive", "", "<init>", "(Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;Ljava/lang/Boolean;)V", "getContractId", "()Ljava/lang/String;", "getPagination", "()Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;", "getIncludeInactive", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "component3", "copy", "(Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;Ljava/lang/Boolean;)Lru/sbertroika/pasiv/gate/dto/PaymentMethodsByContractRequestDto;", "equals", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class PaymentMethodsByContractRequestDto {
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String contractId = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean includeInactive = null;
    
    public PaymentMethodsByContractRequestDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u043a\u043b\u044e\u0447\u0438\u0442\u044c \u043d\u0435\u0430\u043a\u0442\u0438\u0432\u043d\u044b\u0435 \u0441\u043f\u043e\u0441\u043e\u0431\u044b \u043e\u043f\u043b\u0430\u0442\u044b")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean includeInactive) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getContractId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationRequestDto getPagination() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean getIncludeInactive() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationRequestDto component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.PaymentMethodsByContractRequestDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u043a\u043b\u044e\u0447\u0438\u0442\u044c \u043d\u0435\u0430\u043a\u0442\u0438\u0432\u043d\u044b\u0435 \u0441\u043f\u043e\u0441\u043e\u0431\u044b \u043e\u043f\u043b\u0430\u0442\u044b")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean includeInactive) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}