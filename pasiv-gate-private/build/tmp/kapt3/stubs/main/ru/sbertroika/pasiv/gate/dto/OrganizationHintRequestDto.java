package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0440\u043e\u0441 \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0438 \u043f\u043e \u0418\u041d\u041d")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u001d\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u000b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u001f\u0010\f\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\b\u00a8\u0006\u0013"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/OrganizationHintRequestDto;", "", "inn", "", "kpp", "<init>", "(Ljava/lang/String;Ljava/lang/String;)V", "getInn", "()Ljava/lang/String;", "getKpp", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class OrganizationHintRequestDto {
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String inn = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String kpp = null;
    
    public OrganizationHintRequestDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u041d\u041d", example = "7733123456")
    @org.jetbrains.annotations.NotNull()
    java.lang.String inn, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u041f\u041f", example = "773301001")
    @org.jetbrains.annotations.Nullable()
    java.lang.String kpp) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getInn() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getKpp() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationHintRequestDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u041d\u041d", example = "7733123456")
    @org.jetbrains.annotations.NotNull()
    java.lang.String inn, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u041f\u041f", example = "773301001")
    @org.jetbrains.annotations.Nullable()
    java.lang.String kpp) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}