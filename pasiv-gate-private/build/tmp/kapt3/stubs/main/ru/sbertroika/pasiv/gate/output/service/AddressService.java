package ru.sbertroika.pasiv.gate.output.service;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bf\u0018\u00002\u00020\u0001J.\u0010\u0002\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u000bJ.\u0010\f\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u000bJ&\u0010\r\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u000e0\u00032\u0006\u0010\u000f\u001a\u00020\u0010H\u00a6@\u00a2\u0006\u0002\u0010\u0011J&\u0010\u0012\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00130\u00032\u0006\u0010\u000f\u001a\u00020\u0014H\u00a6@\u00a2\u0006\u0002\u0010\u0015J&\u0010\u0016\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00170\u00032\u0006\u0010\u000f\u001a\u00020\u0018H\u00a6@\u00a2\u0006\u0002\u0010\u0019J.\u0010\u001a\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u000f\u001a\u00020\u00142\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u001bJ.\u0010\u001c\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u000f\u001a\u00020\u00142\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u001b\u00a8\u0006\u001d"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/AddressService;", "", "createAddress", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "", "addressCreateOrDelete", "Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;", "userId", "", "(Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAddress", "addressList", "Lru/sbertroika/pasiv/gate/v1/AddressListResult;", "request", "Lru/sbertroika/pasiv/gate/v1/AddressListRequest;", "(Lru/sbertroika/pasiv/gate/v1/AddressListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAddress", "Lru/sbertroika/pasiv/gate/v1/Address;", "Lru/sbertroika/pasiv/gate/v1/ByIdRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHistory", "Lru/sbertroika/common/v1/HistoryResult;", "Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAddress", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recoverAddress", "pasiv-gate-private"})
public abstract interface AddressService {
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete addressCreateOrDelete, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete addressCreateOrDelete, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addressList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.AddressListResult>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.Address>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getHistory(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.common.v1.HistoryResult>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object recoverAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
}