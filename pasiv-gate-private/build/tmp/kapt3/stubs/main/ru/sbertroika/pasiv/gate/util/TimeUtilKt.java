package ru.sbertroika.pasiv.gate.util;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u0016\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\u001a\u0006\u0010\u0000\u001a\u00020\u0001\u001a\u0010\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0004\u001a\u00020\u0005\u001a\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u00012\u0006\u0010\u0007\u001a\u00020\u0005\u001a\u000e\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\u0001\u001a\u000e\u0010\n\u001a\u00020\u00052\u0006\u0010\u000b\u001a\u00020\u0003\u00a8\u0006\f"}, d2 = {"timestampNow", "Ljava/sql/Timestamp;", "fromGoogleTimestampUTC", "Ljava/time/LocalDateTime;", "googleTimestamp", "Lcom/google/protobuf/Timestamp;", "timestampProtoToSQL", "protoTimestamp", "timestampSQLToProto", "sqlTimestamp", "toGoogleTimestampUTC", "localDateTime", "pasiv-gate-private"})
public final class TimeUtilKt {
    
    @org.jetbrains.annotations.NotNull()
    public static final java.sql.Timestamp timestampNow() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final java.time.LocalDateTime fromGoogleTimestampUTC(@org.jetbrains.annotations.NotNull()
    com.google.protobuf.Timestamp googleTimestamp) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final java.sql.Timestamp timestampProtoToSQL(@org.jetbrains.annotations.NotNull()
    com.google.protobuf.Timestamp protoTimestamp) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.google.protobuf.Timestamp timestampSQLToProto(@org.jetbrains.annotations.NotNull()
    java.sql.Timestamp sqlTimestamp) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.google.protobuf.Timestamp toGoogleTimestampUTC(@org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime localDateTime) {
        return null;
    }
}