package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0440\u043e\u0441 \u043f\u043e ID")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u001d\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ$\u0010\u000f\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0010J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0017"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ByIdRequestDto;", "", "id", "", "version", "", "<init>", "(Ljava/lang/String;Ljava/lang/Long;)V", "getId", "()Ljava/lang/String;", "getVersion", "()Ljava/lang/Long;", "Ljava/lang/Long;", "component1", "component2", "copy", "(Ljava/lang/String;Ljava/lang/Long;)Lru/sbertroika/pasiv/gate/dto/ByIdRequestDto;", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class ByIdRequestDto {
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long version = null;
    
    public ByIdRequestDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0435\u0440\u0441\u0438\u044f \u0437\u0430\u043f\u0438\u0441\u0438", example = "1")
    @org.jetbrains.annotations.Nullable()
    java.lang.Long version) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getVersion() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ByIdRequestDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0435\u0440\u0441\u0438\u044f \u0437\u0430\u043f\u0438\u0441\u0438", example = "1")
    @org.jetbrains.annotations.Nullable()
    java.lang.Long version) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}