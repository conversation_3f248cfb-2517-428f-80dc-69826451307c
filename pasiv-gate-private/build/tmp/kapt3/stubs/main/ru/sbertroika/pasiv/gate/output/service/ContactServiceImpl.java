package ru.sbertroika.pasiv.gate.output.service;

@org.springframework.stereotype.Service()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0017\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J.\u0010\u0006\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\n0\u00072\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0002\u0010\u000fJ.\u0010\u0010\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\n0\u00072\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0002\u0010\u000fJ&\u0010\u0011\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\u00120\u00072\u0006\u0010\u0013\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0002\u0010\u0015J&\u0010\u0016\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\f0\u00072\u0006\u0010\u0013\u001a\u00020\u0017H\u0096@\u00a2\u0006\u0002\u0010\u0018J&\u0010\u0019\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\u001a0\u00072\u0006\u0010\u0013\u001a\u00020\u001bH\u0096@\u00a2\u0006\u0002\u0010\u001cJ.\u0010\u001d\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\n0\u00072\u0006\u0010\u0013\u001a\u00020\u00172\u0006\u0010\r\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0002\u0010\u001eJ.\u0010\u001f\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\n0\u00072\u0006\u0010\u0013\u001a\u00020\u00172\u0006\u0010\r\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0002\u0010\u001eJ\"\u0010 \u001a\b\u0012\u0004\u0012\u00020\"0!2\f\u0010#\u001a\b\u0012\u0004\u0012\u00020$0!H\u0092@\u00a2\u0006\u0002\u0010%J\u0010\u0010&\u001a\u00020\f2\u0006\u0010\'\u001a\u00020$H\u0012J\u0010\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020+H\u0012J\u0010\u0010,\u001a\u00020+2\u0006\u0010*\u001a\u00020)H\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/ContactServiceImpl;", "Lru/sbertroika/pasiv/gate/output/service/ContactService;", "repository", "Lru/sbertroika/pasiv/gate/output/repository/ContactRepository;", "<init>", "(Lru/sbertroika/pasiv/gate/output/repository/ContactRepository;)V", "createContact", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "", "contact", "Lru/sbertroika/pasiv/gate/v1/Contact;", "userId", "", "(Lru/sbertroika/pasiv/gate/v1/Contact;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateContact", "contactList", "Lru/sbertroika/pasiv/gate/v1/ContactListResult;", "request", "Lru/sbertroika/pasiv/gate/v1/ContactListRequest;", "(Lru/sbertroika/pasiv/gate/v1/ContactListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getContact", "Lru/sbertroika/pasiv/gate/v1/ByIdRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHistory", "Lru/sbertroika/common/v1/HistoryResult;", "Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteContact", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recoverContact", "mapObjectToHistory", "", "Lru/sbertroika/common/v1/History;", "result", "Lru/sbertroika/pasiv/gate/output/model/Contact;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "mapContactToGrps", "entity", "mapContactTypeToGrps", "Lru/sbertroika/pasiv/gate/output/model/ContactType;", "type", "Lru/sbertroika/pasiv/gate/v1/ContactType;", "mapContactTypeToEntity", "pasiv-gate-private"})
public class ContactServiceImpl implements ru.sbertroika.pasiv.gate.output.service.ContactService {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.ContactRepository repository = null;
    
    public ContactServiceImpl(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.ContactRepository repository) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object createContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Contact contact, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Contact contact, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object contactList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ContactListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.ContactListResult>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.Contact>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getHistory(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.common.v1.HistoryResult>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object recoverContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    private java.lang.Object mapObjectToHistory(java.util.List<ru.sbertroika.pasiv.gate.output.model.Contact> result, kotlin.coroutines.Continuation<? super java.util.List<ru.sbertroika.common.v1.History>> $completion) {
        return null;
    }
    
    private ru.sbertroika.pasiv.gate.v1.Contact mapContactToGrps(ru.sbertroika.pasiv.gate.output.model.Contact entity) {
        return null;
    }
    
    private ru.sbertroika.pasiv.gate.output.model.ContactType mapContactTypeToGrps(ru.sbertroika.pasiv.gate.v1.ContactType type) {
        return null;
    }
    
    private ru.sbertroika.pasiv.gate.v1.ContactType mapContactTypeToEntity(ru.sbertroika.pasiv.gate.output.model.ContactType type) {
        return null;
    }
}