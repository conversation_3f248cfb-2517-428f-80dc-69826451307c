package ru.sbertroika.pasiv.gate.mapper;

/**
 * Утилиты для конвертации между gRPC моделями и REST DTO
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u00d8\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000e\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007J\u000e\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u0005J\u000e\u0010\n\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\fJ\u000e\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010J\u000e\u0010\u0011\u001a\u00020\u00102\u0006\u0010\t\u001a\u00020\u000eJ\u000e\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015J\u000e\u0010\u0016\u001a\u00020\u00152\u0006\u0010\t\u001a\u00020\u0013J\u000e\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u0018J\u000e\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 J\u000e\u0010!\u001a\u00020\"2\u0006\u0010\t\u001a\u00020#J\u000e\u0010$\u001a\u00020%2\u0006\u0010\t\u001a\u00020&J\u000e\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020*J\u000e\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020.J\u000e\u0010/\u001a\u0002002\u0006\u0010\t\u001a\u000201J\u000e\u00102\u001a\u0002032\u0006\u0010\u001c\u001a\u000204J\u000e\u00105\u001a\u0002042\u0006\u0010\u0019\u001a\u000203J\u0018\u00106\u001a\u0002072\u0006\u00108\u001a\u0002092\b\u0010:\u001a\u0004\u0018\u00010#J\u0018\u0010;\u001a\u00020<2\u0006\u00108\u001a\u0002092\b\u0010:\u001a\u0004\u0018\u00010#J\u000e\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u000209J\u000e\u0010@\u001a\u00020A2\u0006\u0010B\u001a\u00020CJ\u000e\u0010D\u001a\u00020E2\u0006\u0010B\u001a\u00020FJ\u000e\u0010G\u001a\u00020H2\u0006\u0010B\u001a\u00020I\u00a8\u0006J"}, d2 = {"Lru/sbertroika/pasiv/gate/mapper/GrpcToRestMapper;", "", "<init>", "()V", "mapOrganizationToDto", "Lru/sbertroika/pasiv/gate/dto/OrganizationDto;", "grpcOrganization", "Lru/sbertroika/pasiv/gate/v1/Organization;", "mapOrganizationToGrpc", "dto", "mapOrganizationWithAddressesToGrpc", "Lru/sbertroika/pasiv/gate/v1/OrganizationWithAddresses;", "Lru/sbertroika/pasiv/gate/dto/OrganizationWithAddressesDto;", "mapAddressToDto", "Lru/sbertroika/pasiv/gate/dto/AddressDto;", "grpcAddress", "Lru/sbertroika/pasiv/gate/v1/Address;", "mapAddressToGrpc", "mapContactToDto", "Lru/sbertroika/pasiv/gate/dto/ContactDto;", "grpcContact", "Lru/sbertroika/pasiv/gate/v1/Contact;", "mapContactToGrpc", "mapContactTypeToDto", "Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "grpcType", "Lru/sbertroika/pasiv/gate/v1/ContactType;", "mapContactTypeToGrpc", "dtoType", "mapPaginationResponseToDto", "Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;", "grpcPagination", "Lru/sbertroika/common/v1/PaginationResponse;", "mapPaginationRequestToGrpc", "Lru/sbertroika/common/v1/PaginationRequest;", "Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;", "mapOrganizationFilterToGrpc", "Lru/sbertroika/pasiv/gate/v1/OrganizationFilter;", "Lru/sbertroika/pasiv/gate/dto/OrganizationFilterDto;", "mapOperationErrorToDto", "Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;", "grpcError", "Lru/sbertroika/common/v1/OperationError;", "mapHistoryResultToDto", "Lru/sbertroika/pasiv/gate/dto/HistoryResultDto;", "grpcHistory", "Lru/sbertroika/common/v1/HistoryResult;", "mapAddressCreateOrDeleteToGrpc", "Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;", "Lru/sbertroika/pasiv/gate/dto/AddressCreateOrDeleteDto;", "mapAddressTypeToGrpc", "Lru/sbertroika/pasiv/gate/v1/AddressType;", "Lru/sbertroika/pasiv/gate/dto/AddressTypeDto;", "mapAddressTypeToDto", "mapAddressListRequestToGrpc", "Lru/sbertroika/pasiv/gate/v1/AddressListRequest;", "organizationId", "", "pagination", "mapContactListRequestToGrpc", "Lru/sbertroika/pasiv/gate/v1/ContactListRequest;", "mapOrganizationHintRequestToGrpc", "Lru/sbertroika/pasiv/gate/v1/OrganizationHintRequest;", "inn", "mapOrganizationHintToDto", "Lru/sbertroika/pasiv/gate/dto/OrganizationHintDto;", "grpcHint", "Lru/sbertroika/pasiv/gate/v1/OrganizationHint;", "mapAddressHintToDto", "Lru/sbertroika/pasiv/gate/dto/AddressHintDto;", "Lru/sbertroika/pasiv/gate/v1/AddressHint;", "mapContactHintToDto", "Lru/sbertroika/pasiv/gate/dto/ContactHintDto;", "Lru/sbertroika/pasiv/gate/v1/ContactHint;", "pasiv-gate-private"})
public final class GrpcToRestMapper {
    @org.jetbrains.annotations.NotNull()
    public static final ru.sbertroika.pasiv.gate.mapper.GrpcToRestMapper INSTANCE = null;
    
    private GrpcToRestMapper() {
        super();
    }
    
    /**
     * Конвертация Organization из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationDto mapOrganizationToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Organization grpcOrganization) {
        return null;
    }
    
    /**
     * Конвертация OrganizationDto в gRPC Organization
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.Organization mapOrganizationToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.OrganizationDto dto) {
        return null;
    }
    
    /**
     * Конвертация OrganizationWithAddressesDto в gRPC OrganizationWithAddresses
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses mapOrganizationWithAddressesToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.OrganizationWithAddressesDto dto) {
        return null;
    }
    
    /**
     * Конвертация Address из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto mapAddressToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Address grpcAddress) {
        return null;
    }
    
    /**
     * Конвертация AddressDto в gRPC Address
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.Address mapAddressToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressDto dto) {
        return null;
    }
    
    /**
     * Конвертация Contact из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactDto mapContactToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Contact grpcContact) {
        return null;
    }
    
    /**
     * Конвертация ContactDto в gRPC Contact
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.Contact mapContactToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactDto dto) {
        return null;
    }
    
    /**
     * Конвертация типа контакта из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactTypeDto mapContactTypeToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ContactType grpcType) {
        return null;
    }
    
    /**
     * Конвертация типа контакта из DTO в gRPC
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.ContactType mapContactTypeToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactTypeDto dtoType) {
        return null;
    }
    
    /**
     * Конвертация PaginationResponse из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto mapPaginationResponseToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.common.v1.PaginationResponse grpcPagination) {
        return null;
    }
    
    /**
     * Конвертация PaginationRequestDto в gRPC PaginationRequest
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.common.v1.PaginationRequest mapPaginationRequestToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.PaginationRequestDto dto) {
        return null;
    }
    
    /**
     * Конвертация OrganizationFilterDto в gRPC OrganizationFilter
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.OrganizationFilter mapOrganizationFilterToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.OrganizationFilterDto dto) {
        return null;
    }
    
    /**
     * Конвертация OperationError из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OperationErrorDto mapOperationErrorToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.common.v1.OperationError grpcError) {
        return null;
    }
    
    /**
     * Конвертация HistoryResult из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.HistoryResultDto mapHistoryResultToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.common.v1.HistoryResult grpcHistory) {
        return null;
    }
    
    /**
     * Конвертация AddressCreateOrDeleteDto в gRPC AddressCreateOrDelete
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete mapAddressCreateOrDeleteToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressCreateOrDeleteDto dto) {
        return null;
    }
    
    /**
     * Конвертация типа адреса из DTO в gRPC
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.AddressType mapAddressTypeToGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressTypeDto dtoType) {
        return null;
    }
    
    /**
     * Конвертация типа адреса из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressTypeDto mapAddressTypeToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressType grpcType) {
        return null;
    }
    
    /**
     * Конвертация AddressListRequest из DTO в gRPC
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.AddressListRequest mapAddressListRequestToGrpc(@org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination) {
        return null;
    }
    
    /**
     * Конвертация ContactListRequest из DTO в gRPC
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.ContactListRequest mapContactListRequestToGrpc(@org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination) {
        return null;
    }
    
    /**
     * Конвертация OrganizationHintRequest из DTO в gRPC
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest mapOrganizationHintRequestToGrpc(@org.jetbrains.annotations.NotNull()
    java.lang.String inn) {
        return null;
    }
    
    /**
     * Конвертация OrganizationHintDto из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationHintDto mapOrganizationHintToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationHint grpcHint) {
        return null;
    }
    
    /**
     * Конвертация AddressHint из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressHintDto mapAddressHintToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressHint grpcHint) {
        return null;
    }
    
    /**
     * Конвертация ContactHint из gRPC в DTO
     */
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactHintDto mapContactHintToDto(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ContactHint grpcHint) {
        return null;
    }
}