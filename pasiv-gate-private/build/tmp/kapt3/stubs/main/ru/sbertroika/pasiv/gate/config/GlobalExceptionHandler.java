package ru.sbertroika.pasiv.gate.config;

/**
 * Глобальный обработчик ошибок для REST API
 */
@org.springframework.web.bind.annotation.RestControllerAdvice()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0017\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u001c\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\fH\u0017J!\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\u000eH\u0017\u00a2\u0006\u0002\u0010\u000fJ\u001c\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\u0011H\u0017J\u001c\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\u0013H\u0017J\u001c\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\u0015H\u0017J\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\u0017H\u0017J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\u0019H\u0017J \u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\n\u0010\u000b\u001a\u00060\u001bj\u0002`\u001cH\u0017J \u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\n\u0010\u000b\u001a\u00060\u001ej\u0002`\u001fH\u0017J \u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\n\u0010\u000b\u001a\u00060!j\u0002`\"H\u0017J \u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\n\u0010\u000b\u001a\u00060$j\u0002`%H\u0017R\u0016\u0010\u0004\u001a\n \u0006*\u0004\u0018\u00010\u00050\u0005X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lru/sbertroika/pasiv/gate/config/GlobalExceptionHandler;", "", "<init>", "()V", "log", "Lorg/slf4j/Logger;", "kotlin.jvm.PlatformType", "handleValidationExceptions", "Lorg/springframework/http/ResponseEntity;", "Lru/sbertroika/pasiv/gate/dto/ApiResponseDto;", "", "ex", "Lorg/springframework/web/bind/MethodArgumentNotValidException;", "handleConstraintViolationException", "error/NonExistentClass", "(Lerror/NonExistentClass;)Lorg/springframework/http/ResponseEntity;", "handleMissingServletRequestParameter", "Lorg/springframework/web/bind/MissingServletRequestParameterException;", "handleMethodArgumentTypeMismatch", "Lorg/springframework/web/method/annotation/MethodArgumentTypeMismatchException;", "handleHttpMessageNotReadable", "Lorg/springframework/http/converter/HttpMessageNotReadableException;", "handleAuthenticationCredentialsNotFound", "Lorg/springframework/security/authentication/AuthenticationCredentialsNotFoundException;", "handleAccessDenied", "Lorg/springframework/security/access/AccessDeniedException;", "handleIllegalArgument", "Ljava/lang/IllegalArgumentException;", "Lkotlin/IllegalArgumentException;", "handleIllegalState", "Ljava/lang/IllegalStateException;", "Lkotlin/IllegalStateException;", "handleGenericException", "Ljava/lang/Exception;", "Lkotlin/Exception;", "handleRuntimeException", "Ljava/lang/RuntimeException;", "Lkotlin/RuntimeException;", "pasiv-gate-private"})
public class GlobalExceptionHandler {
    private final org.slf4j.Logger log = null;
    
    public GlobalExceptionHandler() {
        super();
    }
    
    /**
     * Обработка ошибок валидации полей запроса
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {org.springframework.web.bind.MethodArgumentNotValidException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleValidationExceptions(@org.jetbrains.annotations.NotNull()
    org.springframework.web.bind.MethodArgumentNotValidException ex) {
        return null;
    }
    
    /**
     * Обработка ошибок валидации параметров запроса
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {ConstraintViolationException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleConstraintViolationException(@org.jetbrains.annotations.NotNull()
    error.NonExistentClass ex) {
        return null;
    }
    
    /**
     * Обработка ошибок отсутствующих обязательных параметров
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {org.springframework.web.bind.MissingServletRequestParameterException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleMissingServletRequestParameter(@org.jetbrains.annotations.NotNull()
    org.springframework.web.bind.MissingServletRequestParameterException ex) {
        return null;
    }
    
    /**
     * Обработка ошибок неправильного типа параметров
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {org.springframework.web.method.annotation.MethodArgumentTypeMismatchException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleMethodArgumentTypeMismatch(@org.jetbrains.annotations.NotNull()
    org.springframework.web.method.annotation.MethodArgumentTypeMismatchException ex) {
        return null;
    }
    
    /**
     * Обработка ошибок парсинга JSON
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {org.springframework.http.converter.HttpMessageNotReadableException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleHttpMessageNotReadable(@org.jetbrains.annotations.NotNull()
    org.springframework.http.converter.HttpMessageNotReadableException ex) {
        return null;
    }
    
    /**
     * Обработка ошибок аутентификации
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {org.springframework.security.authentication.AuthenticationCredentialsNotFoundException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleAuthenticationCredentialsNotFound(@org.jetbrains.annotations.NotNull()
    org.springframework.security.authentication.AuthenticationCredentialsNotFoundException ex) {
        return null;
    }
    
    /**
     * Обработка ошибок авторизации
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {org.springframework.security.access.AccessDeniedException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleAccessDenied(@org.jetbrains.annotations.NotNull()
    org.springframework.security.access.AccessDeniedException ex) {
        return null;
    }
    
    /**
     * Обработка ошибок IllegalArgumentException
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {java.lang.IllegalArgumentException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleIllegalArgument(@org.jetbrains.annotations.NotNull()
    java.lang.IllegalArgumentException ex) {
        return null;
    }
    
    /**
     * Обработка ошибок IllegalStateException
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {java.lang.IllegalStateException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleIllegalState(@org.jetbrains.annotations.NotNull()
    java.lang.IllegalStateException ex) {
        return null;
    }
    
    /**
     * Обработка всех остальных исключений
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {java.lang.Exception.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleGenericException(@org.jetbrains.annotations.NotNull()
    java.lang.Exception ex) {
        return null;
    }
    
    /**
     * Обработка ошибок RuntimeException
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = {java.lang.RuntimeException.class})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> handleRuntimeException(@org.jetbrains.annotations.NotNull()
    java.lang.RuntimeException ex) {
        return null;
    }
}