package ru.sbertroika.pasiv.gate.dto;

/**
 * DTO для работы со способами оплаты
 */
@io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0441\u043f\u043e\u0441\u043e\u0431\u0430 \u043e\u043f\u043b\u0430\u0442\u044b")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0013\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013\u00a8\u0006\u0014"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/PaymentMethodTypeDto;", "", "<init>", "(Ljava/lang/String;I)V", "BANK_CARD", "CASH", "TROIKA_SINGLE", "TROIKA_SUBSCRIPTION", "MPC_DISCOUNT", "MPC_SOCIAL", "MPC_SCHOOL", "MPC_STUDENT_SINGLE", "MPC_STUDENT_SUBSCRIPTION", "TC_RESIDENT", "MOBILE_BC", "MOBILE_VIRTUAL_TC", "MOBILE_SBP", "REGIONAL_TC", "SOCIAL_TC", "OTHER_CARDS", "pasiv-gate-private"})
public enum PaymentMethodTypeDto {
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0411\u0430\u043d\u043a\u043e\u0432\u0441\u043a\u0430\u044f \u043a\u0430\u0440\u0442\u0430")
    /*public static final*/ BANK_CARD /* = new BANK_CARD() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u043b\u0438\u0447\u043d\u044b\u0435 \u0434\u0435\u043d\u0435\u0436\u043d\u044b\u0435 \u0441\u0440\u0435\u0434\u0441\u0442\u0432\u0430")
    /*public static final*/ CASH /* = new CASH() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u0430\u044f \u043a\u0430\u0440\u0442\u0430 \"\u0422\u0440\u043e\u0439\u043a\u0430\" (\u0440\u0430\u0437\u043e\u0432\u044b\u0435 \u043f\u043e\u0435\u0437\u0434\u043a\u0438)")
    /*public static final*/ TROIKA_SINGLE /* = new TROIKA_SINGLE() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u0430\u044f \u043a\u0430\u0440\u0442\u0430 \"\u0422\u0440\u043e\u0439\u043a\u0430\" (\u0430\u0431\u043e\u043d\u0435\u043c\u0435\u043d\u0442)")
    /*public static final*/ TROIKA_SUBSCRIPTION /* = new TROIKA_SUBSCRIPTION() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u041f\u041a \u0414\u0438\u0441\u043a\u043e\u043d\u0442")
    /*public static final*/ MPC_DISCOUNT /* = new MPC_DISCOUNT() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u041f\u041a \u0421\u043e\u0446\u0438\u0430\u043b\u044c\u043d\u0430\u044f \u043a\u0430\u0440\u0442\u0430")
    /*public static final*/ MPC_SOCIAL /* = new MPC_SOCIAL() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u041f\u041a \"\u041a\u0430\u0440\u0442\u0430 \u0428\u043a\u043e\u043b\u044c\u043d\u0438\u043a\u0430\"")
    /*public static final*/ MPC_SCHOOL /* = new MPC_SCHOOL() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u041f\u041a \"\u041a\u0430\u0440\u0442\u0430 \u0421\u0442\u0443\u0434\u0435\u043d\u0442\u0430\" (\u0440\u0430\u0437\u043e\u0432\u044b\u0435 \u043f\u043e\u0435\u0437\u0434\u043a\u0438)")
    /*public static final*/ MPC_STUDENT_SINGLE /* = new MPC_STUDENT_SINGLE() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u041f\u041a \"\u041a\u0430\u0440\u0442\u0430 \u0421\u0442\u0443\u0434\u0435\u043d\u0442\u0430\" (\u0430\u0431\u043e\u043d\u0435\u043c\u0435\u043d\u0442)")
    /*public static final*/ MPC_STUDENT_SUBSCRIPTION /* = new MPC_STUDENT_SUBSCRIPTION() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u041a \u041a\u0430\u0440\u0442\u0430 \u0436\u0438\u0442\u0435\u043b\u044f")
    /*public static final*/ TC_RESIDENT /* = new TC_RESIDENT() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u043e\u0431\u0438\u043b\u044c\u043d\u043e\u0435 \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0411\u041a")
    /*public static final*/ MOBILE_BC /* = new MOBILE_BC() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u043e\u0431\u0438\u043b\u044c\u043d\u043e\u0435 \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0412\u0438\u0440\u0442\u0443\u0430\u043b\u044c\u043d\u0430\u044f \u0422\u041a")
    /*public static final*/ MOBILE_VIRTUAL_TC /* = new MOBILE_VIRTUAL_TC() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041c\u043e\u0431\u0438\u043b\u044c\u043d\u043e\u0435 \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0421\u0411\u041f")
    /*public static final*/ MOBILE_SBP /* = new MOBILE_SBP() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u0430\u044f \u043a\u0430\u0440\u0442\u0430 \u0440\u0435\u0433\u0438\u043e\u043d\u0430")
    /*public static final*/ REGIONAL_TC /* = new REGIONAL_TC() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043e\u0446\u0438\u0430\u043b\u044c\u043d\u0430\u044f \u0442\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u0430\u044f \u043a\u0430\u0440\u0442\u0430")
    /*public static final*/ SOCIAL_TC /* = new SOCIAL_TC() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u043d\u044b\u0435 \u043a\u0430\u0440\u0442\u044b, \u043f\u0440\u0435\u0434\u0443\u0441\u043c\u043e\u0442\u0440\u0435\u043d\u043d\u044b\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u043e\u043c")
    /*public static final*/ OTHER_CARDS /* = new OTHER_CARDS() */;
    
    PaymentMethodTypeDto() {
    }
    
    /**
     * DTO для работы со способами оплаты
     */
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.dto.PaymentMethodTypeDto> getEntries() {
        return null;
    }
}