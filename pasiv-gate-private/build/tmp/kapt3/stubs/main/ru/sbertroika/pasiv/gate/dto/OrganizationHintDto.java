package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0430 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b!\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u009d\u0001\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0007\u001a\u00020\u0003\u0012\n\b\u0003\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\r\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u0012\u000e\b\u0003\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011\u00a2\u0006\u0004\b\u0013\u0010\u0014J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0003H\u00c6\u0003J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010-\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010/\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00100\u001a\u0004\u0018\u00010\u000fH\u00c6\u0003J\u000f\u00101\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011H\u00c6\u0003J\u009f\u0001\u00102\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00032\b\b\u0003\u0010\u0005\u001a\u00020\u00032\b\b\u0003\u0010\u0006\u001a\u00020\u00032\b\b\u0003\u0010\u0007\u001a\u00020\u00032\n\b\u0003\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\r\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\u000e\b\u0003\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011H\u00c6\u0001J\u0013\u00103\u001a\u0002042\b\u00105\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00106\u001a\u000207H\u00d6\u0001J\t\u00108\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0016R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0016R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0016R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0016R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0016R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0016R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0016R\u0013\u0010\r\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0016R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$\u00a8\u00069"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/OrganizationHintDto;", "", "name", "", "shortName", "kpp", "inn", "ogrn", "note", "okpo", "oktmo", "okved", "fioDirector", "managerActionReason", "addressLegalHint", "Lru/sbertroika/pasiv/gate/dto/AddressHintDto;", "contactHints", "", "Lru/sbertroika/pasiv/gate/dto/ContactHintDto;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/AddressHintDto;Ljava/util/List;)V", "getName", "()Ljava/lang/String;", "getShortName", "getKpp", "getInn", "getOgrn", "getNote", "getOkpo", "getOktmo", "getOkved", "getFioDirector", "getManagerActionReason", "getAddressLegalHint", "()Lru/sbertroika/pasiv/gate/dto/AddressHintDto;", "getContactHints", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class OrganizationHintDto {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String shortName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String kpp = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String inn = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ogrn = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String note = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String okpo = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String oktmo = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String okved = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String fioDirector = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String managerActionReason = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.AddressHintDto addressLegalHint = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<ru.sbertroika.pasiv.gate.dto.ContactHintDto> contactHints = null;
    
    public OrganizationHintDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043e\u043a\u0440\u0430\u0449\u0435\u043d\u043d\u043e\u0435 \u043d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435")
    @org.jetbrains.annotations.NotNull()
    java.lang.String shortName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u041f\u041f")
    @org.jetbrains.annotations.NotNull()
    java.lang.String kpp, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u041d\u041d")
    @org.jetbrains.annotations.NotNull()
    java.lang.String inn, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0413\u0420\u041d")
    @org.jetbrains.annotations.NotNull()
    java.lang.String ogrn, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043c\u0435\u0442\u043a\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String note, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u041f\u041e")
    @org.jetbrains.annotations.Nullable()
    java.lang.String okpo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0422\u041c\u041e")
    @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0412\u042d\u0414")
    @org.jetbrains.annotations.Nullable()
    java.lang.String okved, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0418\u041e \u0440\u0443\u043a\u043e\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String fioDirector, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0441\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044f \u0440\u0443\u043a\u043e\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String managerActionReason, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0430 \u044e\u0440\u0438\u0434\u0438\u0447\u0435\u0441\u043a\u043e\u0433\u043e \u0430\u0434\u0440\u0435\u0441\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.AddressHintDto addressLegalHint, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0438 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u043e\u0432")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.ContactHintDto> contactHints) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getShortName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getKpp() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getInn() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOgrn() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getNote() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkpo() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOktmo() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkved() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFioDirector() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getManagerActionReason() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.AddressHintDto getAddressLegalHint() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.ContactHintDto> getContactHints() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.AddressHintDto component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.ContactHintDto> component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationHintDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043e\u043a\u0440\u0430\u0449\u0435\u043d\u043d\u043e\u0435 \u043d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435")
    @org.jetbrains.annotations.NotNull()
    java.lang.String shortName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u041f\u041f")
    @org.jetbrains.annotations.NotNull()
    java.lang.String kpp, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u041d\u041d")
    @org.jetbrains.annotations.NotNull()
    java.lang.String inn, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0413\u0420\u041d")
    @org.jetbrains.annotations.NotNull()
    java.lang.String ogrn, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043c\u0435\u0442\u043a\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String note, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u041f\u041e")
    @org.jetbrains.annotations.Nullable()
    java.lang.String okpo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0422\u041c\u041e")
    @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0412\u042d\u0414")
    @org.jetbrains.annotations.Nullable()
    java.lang.String okved, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0418\u041e \u0440\u0443\u043a\u043e\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String fioDirector, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0441\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044f \u0440\u0443\u043a\u043e\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String managerActionReason, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0430 \u044e\u0440\u0438\u0434\u0438\u0447\u0435\u0441\u043a\u043e\u0433\u043e \u0430\u0434\u0440\u0435\u0441\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.AddressHintDto addressLegalHint, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0438 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u043e\u0432")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.ContactHintDto> contactHints) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}