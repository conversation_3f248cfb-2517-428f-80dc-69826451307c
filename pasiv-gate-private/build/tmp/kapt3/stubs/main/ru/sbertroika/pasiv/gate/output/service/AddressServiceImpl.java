package ru.sbertroika.pasiv.gate.output.service;

@org.springframework.stereotype.Service()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0017\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J.\u0010\b\u001a\u0012\u0012\b\u0012\u00060\nj\u0002`\u000b\u0012\u0004\u0012\u00020\f0\t2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0096@\u00a2\u0006\u0002\u0010\u0011J.\u0010\u0012\u001a\u0012\u0012\b\u0012\u00060\nj\u0002`\u000b\u0012\u0004\u0012\u00020\f0\t2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0096@\u00a2\u0006\u0002\u0010\u0011J&\u0010\u0013\u001a\u0012\u0012\b\u0012\u00060\nj\u0002`\u000b\u0012\u0004\u0012\u00020\u00140\t2\u0006\u0010\u0015\u001a\u00020\u0016H\u0096@\u00a2\u0006\u0002\u0010\u0017J&\u0010\u0018\u001a\u0012\u0012\b\u0012\u00060\nj\u0002`\u000b\u0012\u0004\u0012\u00020\u00190\t2\u0006\u0010\u0015\u001a\u00020\u001aH\u0096@\u00a2\u0006\u0002\u0010\u001bJ&\u0010\u001c\u001a\u0012\u0012\b\u0012\u00060\nj\u0002`\u000b\u0012\u0004\u0012\u00020\u001d0\t2\u0006\u0010\u0015\u001a\u00020\u001eH\u0096@\u00a2\u0006\u0002\u0010\u001fJ.\u0010 \u001a\u0012\u0012\b\u0012\u00060\nj\u0002`\u000b\u0012\u0004\u0012\u00020\f0\t2\u0006\u0010\u0015\u001a\u00020\u001a2\u0006\u0010\u000f\u001a\u00020\u0010H\u0096@\u00a2\u0006\u0002\u0010!J.\u0010\"\u001a\u0012\u0012\b\u0012\u00060\nj\u0002`\u000b\u0012\u0004\u0012\u00020\f0\t2\u0006\u0010\u0015\u001a\u00020\u001a2\u0006\u0010\u000f\u001a\u00020\u0010H\u0096@\u00a2\u0006\u0002\u0010!J\"\u0010#\u001a\b\u0012\u0004\u0012\u00020%0$2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\'0$H\u0092@\u00a2\u0006\u0002\u0010(J.\u0010)\u001a\u00020\f2\u0006\u0010*\u001a\u00020+2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010&\u001a\u00020\'H\u0092@\u00a2\u0006\u0002\u0010,J\u0010\u0010-\u001a\u00020\u00192\u0006\u0010.\u001a\u00020\'H\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/AddressServiceImpl;", "Lru/sbertroika/pasiv/gate/output/service/AddressService;", "repository", "Lru/sbertroika/pasiv/gate/output/repository/AddressRepository;", "organizationRepository", "Lru/sbertroika/pasiv/gate/output/repository/OrganizationRepository;", "<init>", "(Lru/sbertroika/pasiv/gate/output/repository/AddressRepository;Lru/sbertroika/pasiv/gate/output/repository/OrganizationRepository;)V", "createAddress", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "", "addressCreateOrDelete", "Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;", "userId", "", "(Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAddress", "addressList", "Lru/sbertroika/pasiv/gate/v1/AddressListResult;", "request", "Lru/sbertroika/pasiv/gate/v1/AddressListRequest;", "(Lru/sbertroika/pasiv/gate/v1/AddressListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAddress", "Lru/sbertroika/pasiv/gate/v1/Address;", "Lru/sbertroika/pasiv/gate/v1/ByIdRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHistory", "Lru/sbertroika/common/v1/HistoryResult;", "Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAddress", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recoverAddress", "mapObjectToHistory", "", "Lru/sbertroika/common/v1/History;", "result", "Lru/sbertroika/pasiv/gate/output/model/Address;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAddressToOrganization", "organization", "Lru/sbertroika/pasiv/gate/output/model/Organization;", "(Lru/sbertroika/pasiv/gate/output/model/Organization;Ljava/lang/String;Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;Lru/sbertroika/pasiv/gate/output/model/Address;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "mapAddressToGrps", "entity", "pasiv-gate-private"})
public class AddressServiceImpl implements ru.sbertroika.pasiv.gate.output.service.AddressService {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.AddressRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.OrganizationRepository organizationRepository = null;
    
    public AddressServiceImpl(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.AddressRepository repository, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.OrganizationRepository organizationRepository) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object createAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete addressCreateOrDelete, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete addressCreateOrDelete, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object addressList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.AddressListResult>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.Address>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getHistory(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.common.v1.HistoryResult>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object recoverAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    private java.lang.Object mapObjectToHistory(java.util.List<ru.sbertroika.pasiv.gate.output.model.Address> result, kotlin.coroutines.Continuation<? super java.util.List<ru.sbertroika.common.v1.History>> $completion) {
        return null;
    }
    
    private java.lang.Object setAddressToOrganization(ru.sbertroika.pasiv.gate.output.model.Organization organization, java.lang.String userId, ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete addressCreateOrDelete, ru.sbertroika.pasiv.gate.output.model.Address result, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private ru.sbertroika.pasiv.gate.v1.Address mapAddressToGrps(ru.sbertroika.pasiv.gate.output.model.Address entity) {
        return null;
    }
}