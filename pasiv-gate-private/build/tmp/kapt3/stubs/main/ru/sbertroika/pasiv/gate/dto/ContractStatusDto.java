package ru.sbertroika.pasiv.gate.dto;

/**
 * DTO для работы с договорами
 */
@io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u0442\u0443\u0441 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;", "", "<init>", "(Ljava/lang/String;I)V", "DRAFT", "ACTIVE", "EXPIRING", "COMPLETED", "TERMINATED", "pasiv-gate-private"})
public enum ContractStatusDto {
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0427\u0435\u0440\u043d\u043e\u0432\u0438\u043a")
    /*public static final*/ DRAFT /* = new DRAFT() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u043a\u0442\u0438\u0432\u043d\u044b\u0439")
    /*public static final*/ ACTIVE /* = new ACTIVE() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u0441\u0442\u0435\u043a\u0430\u0435\u0442")
    /*public static final*/ EXPIRING /* = new EXPIRING() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u0432\u0435\u0440\u0448\u0435\u043d")
    /*public static final*/ COMPLETED /* = new COMPLETED() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0430\u0441\u0442\u043e\u0440\u0433\u043d\u0443\u0442")
    /*public static final*/ TERMINATED /* = new TERMINATED() */;
    
    ContractStatusDto() {
    }
    
    /**
     * DTO для работы с договорами
     */
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.dto.ContractStatusDto> getEntries() {
        return null;
    }
}