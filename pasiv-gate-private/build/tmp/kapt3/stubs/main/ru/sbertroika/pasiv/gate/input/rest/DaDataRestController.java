package ru.sbertroika.pasiv.gate.input.rest;

/**
 * REST контроллер для работы с подсказками DaData
 */
@org.springframework.web.bind.annotation.RestController()
@org.springframework.web.bind.annotation.RequestMapping(value = {"/api/v1/dadata"})
@io.swagger.v3.oas.annotations.tags.Tag(name = "DaData", description = "API \u0434\u043b\u044f \u043f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u044f \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043e\u043a \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439 \u0438\u0437 DaData")
@org.springframework.validation.annotation.Validated()
@org.springframework.security.access.annotation.Secured(value = {"ROLE_pasiv_console_admin"})
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010$\n\u0000\b\u0017\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001e\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\b\b\u0001\u0010\r\u001a\u00020\u000eH\u0017J$\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000b0\n2\u000e\b\u0001\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0012H\u0017J*\u0010\u0013\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\u00140\u000b0\n2\b\b\u0001\u0010\r\u001a\u00020\u000eH\u0017R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lru/sbertroika/pasiv/gate/input/rest/DaDataRestController;", "", "daDataService", "Lru/sbertroika/pasiv/gate/output/service/DaDataService;", "<init>", "(Lru/sbertroika/pasiv/gate/output/service/DaDataService;)V", "log", "Lorg/slf4j/Logger;", "kotlin.jvm.PlatformType", "getOrganizationHintByInn", "Lorg/springframework/http/ResponseEntity;", "Lru/sbertroika/pasiv/gate/dto/ApiResponseDto;", "Lru/sbertroika/pasiv/gate/dto/OrganizationHintDto;", "inn", "", "getOrganizationHints", "Lru/sbertroika/pasiv/gate/dto/OrganizationHintListDto;", "innList", "", "validateInn", "", "pasiv-gate-private"})
public class DaDataRestController {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.service.DaDataService daDataService = null;
    private final org.slf4j.Logger log = null;
    
    public DaDataRestController(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.service.DaDataService daDataService) {
        super();
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u041f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u0435 \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0438 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438 \u043f\u043e \u0418\u041d\u041d", description = "\u0412\u043e\u0437\u0432\u0440\u0430\u0449\u0430\u0435\u0442 \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0443 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438 \u0438\u0437 DaData \u043f\u043e \u0443\u043a\u0430\u0437\u0430\u043d\u043d\u043e\u043c\u0443 \u0418\u041d\u041d")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0430 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438 \u043f\u043e\u043b\u0443\u0447\u0435\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.ApiResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u043a\u043e\u0440\u0440\u0435\u043a\u0442\u043d\u044b\u0439 \u0418\u041d\u041d", responseCode = "400"), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u044f \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d\u0430 \u0432 DaData", responseCode = "404"), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u043e\u0432\u0430\u043d", responseCode = "401"), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u0434\u043e\u0441\u0442\u0430\u0442\u043e\u0447\u043d\u043e \u043f\u0440\u0430\u0432 \u0434\u043e\u0441\u0442\u0443\u043f\u0430", responseCode = "403")})
    @org.springframework.web.bind.annotation.GetMapping(value = {"/organizations/hint"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<ru.sbertroika.pasiv.gate.dto.OrganizationHintDto>> getOrganizationHintByInn(@io.swagger.v3.oas.annotations.Parameter(description = "\u0418\u041d\u041d \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438 (10 \u0438\u043b\u0438 12 \u0446\u0438\u0444\u0440)", example = "7733123456")
    @org.springframework.web.bind.annotation.RequestParam()
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String inn) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u041f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u0435 \u0441\u043f\u0438\u0441\u043a\u0430 \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043e\u043a \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439", description = "\u0412\u043e\u0437\u0432\u0440\u0430\u0449\u0430\u0435\u0442 \u0441\u043f\u0438\u0441\u043e\u043a \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043e\u043a \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439 \u0438\u0437 DaData \u043f\u043e \u0441\u043f\u0438\u0441\u043a\u0443 \u0418\u041d\u041d")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u0421\u043f\u0438\u0441\u043e\u043a \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043e\u043a \u043f\u043e\u043b\u0443\u0447\u0435\u043d \u0443\u0441\u043f\u0435\u0448\u043d\u043e", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.ApiResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u043a\u043e\u0440\u0440\u0435\u043a\u0442\u043d\u044b\u0435 \u0434\u0430\u043d\u043d\u044b\u0435 \u0437\u0430\u043f\u0440\u043e\u0441\u0430", responseCode = "400"), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u043e\u0432\u0430\u043d", responseCode = "401"), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u0434\u043e\u0441\u0442\u0430\u0442\u043e\u0447\u043d\u043e \u043f\u0440\u0430\u0432 \u0434\u043e\u0441\u0442\u0443\u043f\u0430", responseCode = "403")})
    @org.springframework.web.bind.annotation.PostMapping(value = {"/organizations/hints"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<ru.sbertroika.pasiv.gate.dto.OrganizationHintListDto>> getOrganizationHints(@io.swagger.v3.oas.annotations.Parameter(description = "\u0421\u043f\u0438\u0441\u043e\u043a \u0418\u041d\u041d \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
    @org.springframework.web.bind.annotation.RequestBody()
    @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> innList) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u0412\u0430\u043b\u0438\u0434\u0430\u0446\u0438\u044f \u0418\u041d\u041d", description = "\u041f\u0440\u043e\u0432\u0435\u0440\u044f\u0435\u0442 \u043a\u043e\u0440\u0440\u0435\u043a\u0442\u043d\u043e\u0441\u0442\u044c \u0418\u041d\u041d \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u0432\u0430\u043b\u0438\u0434\u0430\u0446\u0438\u0438", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.ApiResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u043a\u043e\u0440\u0440\u0435\u043a\u0442\u043d\u044b\u0435 \u0434\u0430\u043d\u043d\u044b\u0435 \u0437\u0430\u043f\u0440\u043e\u0441\u0430", responseCode = "400")})
    @org.springframework.web.bind.annotation.GetMapping(value = {"/organizations/validate-inn"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<java.util.Map<java.lang.String, java.lang.Object>>> validateInn(@io.swagger.v3.oas.annotations.Parameter(description = "\u0418\u041d\u041d \u0434\u043b\u044f \u0432\u0430\u043b\u0438\u0434\u0430\u0446\u0438\u0438", example = "7733123456")
    @org.springframework.web.bind.annotation.RequestParam()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String inn) {
        return null;
    }
}