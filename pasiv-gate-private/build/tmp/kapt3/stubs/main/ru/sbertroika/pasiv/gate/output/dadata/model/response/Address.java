package ru.sbertroika.pasiv.gate.output.dadata.model.response;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J9\u0010\u001a\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001J\t\u0010 \u001a\u00020\u0003H\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR \u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000b\"\u0004\b\u000f\u0010\rR \u0010\u0005\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u000b\"\u0004\b\u0011\u0010\rR \u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0013\"\u0004\b\u0014\u0010\u0015\u00a8\u0006!"}, d2 = {"Lru/sbertroika/pasiv/gate/output/dadata/model/response/Address;", "", "value", "", "unrestrictedValue", "invalidity", "data", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/AddressData;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/output/dadata/model/response/AddressData;)V", "getValue", "()Ljava/lang/String;", "setValue", "(Ljava/lang/String;)V", "getUnrestrictedValue", "setUnrestrictedValue", "getInvalidity", "setInvalidity", "getData", "()Lru/sbertroika/pasiv/gate/output/dadata/model/response/AddressData;", "setData", "(Lru/sbertroika/pasiv/gate/output/dadata/model/response/AddressData;)V", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class Address {
    @com.google.gson.annotations.SerializedName(value = "value")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String value;
    @com.google.gson.annotations.SerializedName(value = "unrestricted_value")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String unrestrictedValue;
    @com.google.gson.annotations.SerializedName(value = "invalidity")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String invalidity;
    @com.google.gson.annotations.SerializedName(value = "data")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData data;
    
    public Address(@org.jetbrains.annotations.Nullable()
    java.lang.String value, @org.jetbrains.annotations.Nullable()
    java.lang.String unrestrictedValue, @org.jetbrains.annotations.Nullable()
    java.lang.String invalidity, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData data) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getValue() {
        return null;
    }
    
    public final void setValue(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUnrestrictedValue() {
        return null;
    }
    
    public final void setUnrestrictedValue(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getInvalidity() {
        return null;
    }
    
    public final void setInvalidity(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData getData() {
        return null;
    }
    
    public final void setData(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData p0) {
    }
    
    public Address() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Address copy(@org.jetbrains.annotations.Nullable()
    java.lang.String value, @org.jetbrains.annotations.Nullable()
    java.lang.String unrestrictedValue, @org.jetbrains.annotations.Nullable()
    java.lang.String invalidity, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData data) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}