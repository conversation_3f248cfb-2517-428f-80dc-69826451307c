package ru.sbertroika.pasiv.gate.output.model;

@org.springframework.data.relational.core.mapping.Table(value = "organization")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\bR\b\u0087\b\u0018\u00002\u00020\u0001B\u0081\u0002\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u001b\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u000b\u0010R\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010S\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010T\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u0010\u0010U\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010)J\u000b\u0010V\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010W\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010X\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010Y\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\\\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010]\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010^\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010_\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010`\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010a\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010b\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010c\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010d\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010f\u001a\u00020\u001bH\u00c6\u0003J\u0088\u0002\u0010g\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u001a\u001a\u00020\u001bH\u00c6\u0001\u00a2\u0006\u0002\u0010hJ\u0013\u0010i\u001a\u00020\u001b2\b\u0010j\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010k\u001a\u00020\bH\u00d6\u0001J\t\u0010l\u001a\u00020\u0006H\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!R \u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010\u001f\"\u0004\b#\u0010!R\u001c\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010%\"\u0004\b&\u0010\'R\"\u0010\u0007\u001a\u0004\u0018\u00010\b8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010,\u001a\u0004\b(\u0010)\"\u0004\b*\u0010+R \u0010\t\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b-\u0010.\"\u0004\b/\u00100R \u0010\u000b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010\u001f\"\u0004\b2\u0010!R \u0010\f\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010%\"\u0004\b4\u0010\'R \u0010\r\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u0010%\"\u0004\b6\u0010\'R \u0010\u000e\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b7\u0010%\"\u0004\b8\u0010\'R \u0010\u000f\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b9\u0010%\"\u0004\b:\u0010\'R \u0010\u0010\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b;\u0010%\"\u0004\b<\u0010\'R \u0010\u0011\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b=\u0010%\"\u0004\b>\u0010\'R \u0010\u0012\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b?\u0010%\"\u0004\b@\u0010\'R \u0010\u0013\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bA\u0010%\"\u0004\bB\u0010\'R \u0010\u0014\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bC\u0010%\"\u0004\bD\u0010\'R \u0010\u0015\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bE\u0010%\"\u0004\bF\u0010\'R \u0010\u0016\u001a\u0004\u0018\u00010\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bG\u0010%\"\u0004\bH\u0010\'R \u0010\u0017\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bI\u0010\u001f\"\u0004\bJ\u0010!R \u0010\u0018\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bK\u0010\u001f\"\u0004\bL\u0010!R \u0010\u0019\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010\u001f\"\u0004\bN\u0010!R\u001e\u0010\u001a\u001a\u00020\u001b8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010O\"\u0004\bP\u0010Q\u00a8\u0006m"}, d2 = {"Lru/sbertroika/pasiv/gate/output/model/Organization;", "", "id", "Ljava/util/UUID;", "parentId", "parentName", "", "version", "", "versionCreatedAt", "Ljava/sql/Timestamp;", "versionCreatedBy", "oName", "shortName", "kpp", "inn", "note", "okpo", "oktmo", "ogrn", "okved", "fioDirector", "managerActionReason", "addressLegalId", "addressActualId", "addressMailingId", "isDeleted", "", "<init>", "(Ljava/util/UUID;Ljava/util/UUID;Ljava/lang/String;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/UUID;Ljava/util/UUID;Ljava/util/UUID;Z)V", "getId", "()Ljava/util/UUID;", "setId", "(Ljava/util/UUID;)V", "getParentId", "setParentId", "getParentName", "()Ljava/lang/String;", "setParentName", "(Ljava/lang/String;)V", "getVersion", "()Ljava/lang/Integer;", "setVersion", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "getVersionCreatedAt", "()Ljava/sql/Timestamp;", "setVersionCreatedAt", "(Ljava/sql/Timestamp;)V", "getVersionCreatedBy", "setVersionCreatedBy", "getOName", "setOName", "getShortName", "setShortName", "getKpp", "setKpp", "getInn", "setInn", "getNote", "setNote", "getOkpo", "setOkpo", "getOktmo", "setOktmo", "getOgrn", "setOgrn", "getOkved", "setOkved", "getFioDirector", "setFioDirector", "getManagerActionReason", "setManagerActionReason", "getAddressLegalId", "setAddressLegalId", "getAddressActualId", "setAddressActualId", "getAddressMailingId", "setAddressMailingId", "()Z", "setDeleted", "(Z)V", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component20", "component21", "copy", "(Ljava/util/UUID;Ljava/util/UUID;Ljava/lang/String;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/UUID;Ljava/util/UUID;Ljava/util/UUID;Z)Lru/sbertroika/pasiv/gate/output/model/Organization;", "equals", "other", "hashCode", "toString", "pasiv-gate-private"})
public final class Organization {
    @ru.sbertroika.history.api.HistoryId()
    @org.springframework.data.relational.core.mapping.Column(value = "o_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID id;
    @org.springframework.data.relational.core.mapping.Column(value = "o_parent_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID parentId;
    @kotlin.jvm.Transient()
    @org.jetbrains.annotations.Nullable()
    private transient java.lang.String parentName;
    @ru.sbertroika.history.api.HistoryVersion()
    @org.springframework.data.relational.core.mapping.Column(value = "o_version")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer version;
    @ru.sbertroika.history.api.HistoryVersionAt()
    @org.springframework.data.relational.core.mapping.Column(value = "o_version_created_at")
    @org.jetbrains.annotations.Nullable()
    private java.sql.Timestamp versionCreatedAt;
    @ru.sbertroika.history.api.HistoryVersionBy()
    @org.springframework.data.relational.core.mapping.Column(value = "o_version_created_by")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID versionCreatedBy;
    
    /**
     * Полное наименование
     */
    @ru.sbertroika.history.api.HistoryName(name = "name")
    @org.springframework.data.relational.core.mapping.Column(value = "o_name")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String oName;
    
    /**
     * Сокращенное наименование
     */
    @org.springframework.data.relational.core.mapping.Column(value = "o_short_name")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String shortName;
    @org.springframework.data.relational.core.mapping.Column(value = "o_kpp")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String kpp;
    @org.springframework.data.relational.core.mapping.Column(value = "o_inn")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String inn;
    @org.springframework.data.relational.core.mapping.Column(value = "o_note")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String note;
    @org.springframework.data.relational.core.mapping.Column(value = "o_okpo")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okpo;
    @org.springframework.data.relational.core.mapping.Column(value = "o_oktmo")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String oktmo;
    @org.springframework.data.relational.core.mapping.Column(value = "o_ogrn")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String ogrn;
    @org.springframework.data.relational.core.mapping.Column(value = "o_okved")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okved;
    @org.springframework.data.relational.core.mapping.Column(value = "o_fio_director")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fioDirector;
    @org.springframework.data.relational.core.mapping.Column(value = "o_manager_action_reason")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String managerActionReason;
    @ru.sbertroika.history.api.HistoryName(name = "addressLegal")
    @org.springframework.data.relational.core.mapping.Column(value = "o_address_legal_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID addressLegalId;
    @ru.sbertroika.history.api.HistoryName(name = "addressActual")
    @org.springframework.data.relational.core.mapping.Column(value = "o_address_actual_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID addressActualId;
    @ru.sbertroika.history.api.HistoryName(name = "addressMailing")
    @org.springframework.data.relational.core.mapping.Column(value = "o_address_mailing_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID addressMailingId;
    @ru.sbertroika.history.api.HistoryStatus()
    @org.springframework.data.relational.core.mapping.Column(value = "o_is_deleted")
    private boolean isDeleted;
    
    public Organization(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.util.UUID parentId, @org.jetbrains.annotations.Nullable()
    java.lang.String parentName, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.lang.String oName, @org.jetbrains.annotations.Nullable()
    java.lang.String shortName, @org.jetbrains.annotations.Nullable()
    java.lang.String kpp, @org.jetbrains.annotations.Nullable()
    java.lang.String inn, @org.jetbrains.annotations.Nullable()
    java.lang.String note, @org.jetbrains.annotations.Nullable()
    java.lang.String okpo, @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String ogrn, @org.jetbrains.annotations.Nullable()
    java.lang.String okved, @org.jetbrains.annotations.Nullable()
    java.lang.String fioDirector, @org.jetbrains.annotations.Nullable()
    java.lang.String managerActionReason, @org.jetbrains.annotations.Nullable()
    java.util.UUID addressLegalId, @org.jetbrains.annotations.Nullable()
    java.util.UUID addressActualId, @org.jetbrains.annotations.Nullable()
    java.util.UUID addressMailingId, boolean isDeleted) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getId() {
        return null;
    }
    
    public final void setId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getParentId() {
        return null;
    }
    
    public final void setParentId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getParentName() {
        return null;
    }
    
    public final void setParentName(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getVersion() {
        return null;
    }
    
    public final void setVersion(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp getVersionCreatedAt() {
        return null;
    }
    
    public final void setVersionCreatedAt(@org.jetbrains.annotations.Nullable()
    java.sql.Timestamp p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getVersionCreatedBy() {
        return null;
    }
    
    public final void setVersionCreatedBy(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    /**
     * Полное наименование
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOName() {
        return null;
    }
    
    /**
     * Полное наименование
     */
    public final void setOName(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    /**
     * Сокращенное наименование
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getShortName() {
        return null;
    }
    
    /**
     * Сокращенное наименование
     */
    public final void setShortName(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getKpp() {
        return null;
    }
    
    public final void setKpp(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getInn() {
        return null;
    }
    
    public final void setInn(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getNote() {
        return null;
    }
    
    public final void setNote(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkpo() {
        return null;
    }
    
    public final void setOkpo(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOktmo() {
        return null;
    }
    
    public final void setOktmo(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOgrn() {
        return null;
    }
    
    public final void setOgrn(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkved() {
        return null;
    }
    
    public final void setOkved(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFioDirector() {
        return null;
    }
    
    public final void setFioDirector(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getManagerActionReason() {
        return null;
    }
    
    public final void setManagerActionReason(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getAddressLegalId() {
        return null;
    }
    
    public final void setAddressLegalId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getAddressActualId() {
        return null;
    }
    
    public final void setAddressActualId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getAddressMailingId() {
        return null;
    }
    
    public final void setAddressMailingId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    public final void setDeleted(boolean p0) {
    }
    
    public Organization() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component19() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component20() {
        return null;
    }
    
    public final boolean component21() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.model.Organization copy(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.util.UUID parentId, @org.jetbrains.annotations.Nullable()
    java.lang.String parentName, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.lang.String oName, @org.jetbrains.annotations.Nullable()
    java.lang.String shortName, @org.jetbrains.annotations.Nullable()
    java.lang.String kpp, @org.jetbrains.annotations.Nullable()
    java.lang.String inn, @org.jetbrains.annotations.Nullable()
    java.lang.String note, @org.jetbrains.annotations.Nullable()
    java.lang.String okpo, @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String ogrn, @org.jetbrains.annotations.Nullable()
    java.lang.String okved, @org.jetbrains.annotations.Nullable()
    java.lang.String fioDirector, @org.jetbrains.annotations.Nullable()
    java.lang.String managerActionReason, @org.jetbrains.annotations.Nullable()
    java.util.UUID addressLegalId, @org.jetbrains.annotations.Nullable()
    java.util.UUID addressActualId, @org.jetbrains.annotations.Nullable()
    java.util.UUID addressMailingId, boolean isDeleted) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}