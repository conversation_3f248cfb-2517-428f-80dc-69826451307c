package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0438\u043b\u044c\u0442\u0440 \u0434\u043b\u044f \u043f\u043e\u0438\u0441\u043a\u0430 \u0430\u0434\u0440\u0435\u0441\u043e\u0432")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ>\u0010\u0014\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0015J\u0013\u0010\u0016\u001a\u00020\u00072\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\u0006\u0010\u000e\u00a8\u0006\u001b"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/AddressFilterDto;", "", "organizationId", "", "city", "street", "isDeleted", "", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)V", "getOrganizationId", "()Ljava/lang/String;", "getCity", "getStreet", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)Lru/sbertroika/pasiv/gate/dto/AddressFilterDto;", "equals", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class AddressFilterDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String organizationId = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String city = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String street = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean isDeleted = null;
    
    public AddressFilterDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0438\u0441\u043a \u043f\u043e \u0433\u043e\u0440\u043e\u0434\u0443", example = "\u041c\u043e\u0441\u043a\u0432\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String city, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0438\u0441\u043a \u043f\u043e \u0443\u043b\u0438\u0446\u0435", example = "\u041b\u0435\u043d\u0438\u043d\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String street, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0443\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0435 \u0430\u0434\u0440\u0435\u0441\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isDeleted) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOrganizationId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCity() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreet() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean isDeleted() {
        return null;
    }
    
    public AddressFilterDto() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressFilterDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0438\u0441\u043a \u043f\u043e \u0433\u043e\u0440\u043e\u0434\u0443", example = "\u041c\u043e\u0441\u043a\u0432\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String city, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0438\u0441\u043a \u043f\u043e \u0443\u043b\u0438\u0446\u0435", example = "\u041b\u0435\u043d\u0438\u043d\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String street, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0443\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0435 \u0430\u0434\u0440\u0435\u0441\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isDeleted) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}