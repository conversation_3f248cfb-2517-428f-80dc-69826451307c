package ru.sbertroika.pasiv.gate.output.repository;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\bf\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u001c\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ,\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\n2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\u000e"}, d2 = {"Lru/sbertroika/pasiv/gate/output/repository/OrganizationCrudRepository;", "Lorg/springframework/data/repository/kotlin/CoroutineCrudRepository;", "Lru/sbertroika/pasiv/gate/output/model/Organization;", "Lru/sbertroika/pasiv/gate/output/model/OrganizationPK;", "findAllByProjectId", "Lkotlinx/coroutines/flow/Flow;", "projectId", "Ljava/util/UUID;", "(Ljava/util/UUID;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "offset", "", "limit", "(Ljava/util/UUID;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "countAllByProjectId", "pasiv-gate-private"})
public abstract interface OrganizationCrudRepository extends org.springframework.data.repository.kotlin.CoroutineCrudRepository<ru.sbertroika.pasiv.gate.output.model.Organization, ru.sbertroika.pasiv.gate.output.model.OrganizationPK> {
    
    @org.springframework.data.r2dbc.repository.Query(value = "SELECT * FROM active_organization_by_project where project_id = :projectId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object findAllByProjectId(@org.jetbrains.annotations.NotNull()
    java.util.UUID projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion);
    
    @org.springframework.data.r2dbc.repository.Query(value = "SELECT * FROM active_organization_by_project where project_id = :projectId OFFSET :offset LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object findAllByProjectId(@org.jetbrains.annotations.NotNull()
    java.util.UUID projectId, int offset, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion);
    
    @org.springframework.data.r2dbc.repository.Query(value = "SELECT count(*) FROM active_organization_by_project where project_id = :projectId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object countAllByProjectId(@org.jetbrains.annotations.NotNull()
    java.util.UUID projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
}