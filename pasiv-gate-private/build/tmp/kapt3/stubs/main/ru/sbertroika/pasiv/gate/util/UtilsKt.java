package ru.sbertroika.pasiv.gate.util;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000L\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0006\u0010\u0004\u001a\u00020\u0005\u001a2\u0010\u0006\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u0002H\n0\u0007\"\b\b\u0000\u0010\n*\u00020\u000b*\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u0002H\n0\u000e\u001aG\u0010\u000f\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u0002H\u00100\u0007\"\u0004\b\u0000\u0010\u0011\"\u0004\b\u0001\u0010\u0010*\u0004\u0018\u0001H\u00112\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0011\u0012\u0004\u0012\u0002H\u00100\u0013H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0014\u001a\u0016\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016\u001a\n\u0010\u0019\u001a\u00020\u001a*\u00020\u001b\u001a\n\u0010\u001c\u001a\u00020\u001b*\u00020\u001a\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\u0082\u0002\u0007\n\u0005\b\u009920\u0001\u00a8\u0006\u001d"}, d2 = {"DT_FORMAT", "Ljava/time/format/DateTimeFormatter;", "getDT_FORMAT", "()Ljava/time/format/DateTimeFormatter;", "mapper", "Lcom/fasterxml/jackson/databind/ObjectMapper;", "param", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "E", "Lkotlin/coroutines/CoroutineContext$Element;", "Lkotlin/coroutines/CoroutineContext;", "key", "Lkotlin/coroutines/CoroutineContext$Key;", "notNull", "R", "T", "block", "Lkotlin/Function1;", "(Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Larrow/core/Either;", "calcTotalPage", "", "totalElements", "limit", "toZonedDateTime", "Ljava/time/ZonedDateTime;", "Lcom/google/protobuf/Timestamp;", "toTimestamp", "pasiv-gate-private"})
public final class UtilsKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.time.format.DateTimeFormatter DT_FORMAT = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final java.time.format.DateTimeFormatter getDT_FORMAT() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.fasterxml.jackson.databind.ObjectMapper mapper() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final <E extends kotlin.coroutines.CoroutineContext.Element>arrow.core.Either<java.lang.Error, E> param(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.CoroutineContext $this$param, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.CoroutineContext.Key<E> key) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final <T extends java.lang.Object, R extends java.lang.Object>arrow.core.Either<java.lang.Error, R> notNull(@org.jetbrains.annotations.Nullable()
    T $this$notNull, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super T, ? extends R> block) {
        return null;
    }
    
    public static final int calcTotalPage(int totalElements, int limit) {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.time.ZonedDateTime toZonedDateTime(@org.jetbrains.annotations.NotNull()
    com.google.protobuf.Timestamp $this$toZonedDateTime) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.google.protobuf.Timestamp toTimestamp(@org.jetbrains.annotations.NotNull()
    java.time.ZonedDateTime $this$toTimestamp) {
        return null;
    }
}