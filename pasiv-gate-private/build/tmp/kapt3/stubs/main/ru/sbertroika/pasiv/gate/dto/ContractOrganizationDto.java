package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u044f \u0432 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0435")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u001b\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001Bg\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0007\u001a\u00020\b\u0012\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\n\u001a\u00020\u000b\u0012\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\u000b\u0012\b\b\u0003\u0010\r\u001a\u00020\u000e\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\t\u0010%\u001a\u00020\u000eH\u00c6\u0003Ji\u0010&\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00032\b\b\u0003\u0010\u0005\u001a\u00020\u00032\b\b\u0003\u0010\u0006\u001a\u00020\u00032\b\b\u0003\u0010\u0007\u001a\u00020\b2\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\n\u001a\u00020\u000b2\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\u000b2\b\b\u0003\u0010\r\u001a\u00020\u000eH\u00c6\u0001J\u0013\u0010\'\u001a\u00020\u000e2\b\u0010(\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010)\u001a\u00020*H\u00d6\u0001J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0013\u0010\f\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001aR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u001c\u00a8\u0006,"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContractOrganizationDto;", "", "id", "", "contractId", "organizationId", "organizationName", "role", "Lru/sbertroika/pasiv/gate/dto/OrganizationRoleDto;", "roleDescription", "activeFrom", "Ljava/time/LocalDateTime;", "activeTill", "isDeleted", "", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/OrganizationRoleDto;Ljava/lang/String;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Z)V", "getId", "()Ljava/lang/String;", "getContractId", "getOrganizationId", "getOrganizationName", "getRole", "()Lru/sbertroika/pasiv/gate/dto/OrganizationRoleDto;", "getRoleDescription", "getActiveFrom", "()Ljava/time/LocalDateTime;", "getActiveTill", "()Z", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class ContractOrganizationDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String id = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String contractId = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String organizationId = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String organizationName = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.OrganizationRoleDto role = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String roleDescription = null;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime activeFrom = null;
    @org.jetbrains.annotations.Nullable()
    private final java.time.LocalDateTime activeTill = null;
    private final boolean isDeleted = false;
    
    public ContractOrganizationDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0441\u0432\u044f\u0437\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractId, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "\u041e\u041e\u041e \"\u0420\u043e\u043c\u0430\u0448\u043a\u0430\"")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u043e\u043b\u044c \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.OrganizationRoleDto role, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0440\u043e\u043b\u0438", example = "\u041e\u0441\u043d\u043e\u0432\u043d\u043e\u0439 \u043f\u0435\u0440\u0435\u0432\u043e\u0437\u0447\u0438\u043a \u043d\u0430 \u043c\u0430\u0440\u0448\u0440\u0443\u0442\u0435 \u21161")
    @org.jetbrains.annotations.Nullable()
    java.lang.String roleDescription, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u043a\u0442\u0438\u0432\u043d\u0430 \u0441", example = "2024-01-01T00:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime activeFrom, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u043a\u0442\u0438\u0432\u043d\u0430 \u0434\u043e", example = "2024-12-31T23:59:59")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime activeTill, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d\u0430 \u043b\u0438 \u0441\u0432\u044f\u0437\u044c")
    boolean isDeleted) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getContractId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrganizationId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrganizationName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationRoleDto getRole() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRoleDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getActiveFrom() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime getActiveTill() {
        return null;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationRoleDto component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractOrganizationDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0441\u0432\u044f\u0437\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractId, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "\u041e\u041e\u041e \"\u0420\u043e\u043c\u0430\u0448\u043a\u0430\"")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u043e\u043b\u044c \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.OrganizationRoleDto role, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0440\u043e\u043b\u0438", example = "\u041e\u0441\u043d\u043e\u0432\u043d\u043e\u0439 \u043f\u0435\u0440\u0435\u0432\u043e\u0437\u0447\u0438\u043a \u043d\u0430 \u043c\u0430\u0440\u0448\u0440\u0443\u0442\u0435 \u21161")
    @org.jetbrains.annotations.Nullable()
    java.lang.String roleDescription, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u043a\u0442\u0438\u0432\u043d\u0430 \u0441", example = "2024-01-01T00:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime activeFrom, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u043a\u0442\u0438\u0432\u043d\u0430 \u0434\u043e", example = "2024-12-31T23:59:59")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime activeTill, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d\u0430 \u043b\u0438 \u0441\u0432\u044f\u0437\u044c")
    boolean isDeleted) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}