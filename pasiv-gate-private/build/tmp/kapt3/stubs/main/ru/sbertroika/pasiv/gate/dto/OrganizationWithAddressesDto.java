package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u044f \u0441 \u0430\u0434\u0440\u0435\u0441\u0430\u043c\u0438")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B3\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J5\u0010\u0014\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00052\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u001c"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/OrganizationWithAddressesDto;", "", "organization", "Lru/sbertroika/pasiv/gate/dto/OrganizationDto;", "addressLegal", "Lru/sbertroika/pasiv/gate/dto/AddressDto;", "addressActual", "addressMailing", "<init>", "(Lru/sbertroika/pasiv/gate/dto/OrganizationDto;Lru/sbertroika/pasiv/gate/dto/AddressDto;Lru/sbertroika/pasiv/gate/dto/AddressDto;Lru/sbertroika/pasiv/gate/dto/AddressDto;)V", "getOrganization", "()Lru/sbertroika/pasiv/gate/dto/OrganizationDto;", "getAddressLegal", "()Lru/sbertroika/pasiv/gate/dto/AddressDto;", "getAddressActual", "getAddressMailing", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class OrganizationWithAddressesDto {
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.OrganizationDto organization = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.AddressDto addressLegal = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.AddressDto addressActual = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.AddressDto addressMailing = null;
    
    public OrganizationWithAddressesDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u044f")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.OrganizationDto organization, @io.swagger.v3.oas.annotations.media.Schema(description = "\u042e\u0440\u0438\u0434\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0430\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressDto addressLegal, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0430\u043a\u0442\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0430\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.AddressDto addressActual, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0447\u0442\u043e\u0432\u044b\u0439 \u0430\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.AddressDto addressMailing) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationDto getOrganization() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto getAddressLegal() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto getAddressActual() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto getAddressMailing() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationDto component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationWithAddressesDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u044f")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.OrganizationDto organization, @io.swagger.v3.oas.annotations.media.Schema(description = "\u042e\u0440\u0438\u0434\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0430\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressDto addressLegal, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0430\u043a\u0442\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0430\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.AddressDto addressActual, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0447\u0442\u043e\u0432\u044b\u0439 \u0430\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.AddressDto addressMailing) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}