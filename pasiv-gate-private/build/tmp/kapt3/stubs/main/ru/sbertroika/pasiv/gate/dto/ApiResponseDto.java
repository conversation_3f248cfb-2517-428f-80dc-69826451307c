package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u044b\u0439 \u043e\u0442\u0432\u0435\u0442 API")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002B)\u0012\n\b\u0003\u0010\u0003\u001a\u0004\u0018\u00018\u0000\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0003\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u0010\u0010\u0011\u001a\u0004\u0018\u00018\u0000H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0007H\u00c6\u0003J6\u0010\u0014\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\n\b\u0003\u0010\u0003\u001a\u0004\u0018\u00018\u00002\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\b\u0003\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0015J\u0013\u0010\u0016\u001a\u00020\u00072\b\u0010\u0017\u001a\u0004\u0018\u00010\u0002H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001R\u0015\u0010\u0003\u001a\u0004\u0018\u00018\u0000\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001c"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ApiResponseDto;", "T", "", "data", "error", "Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;", "success", "", "<init>", "(Ljava/lang/Object;Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;Z)V", "getData", "()Ljava/lang/Object;", "Ljava/lang/Object;", "getError", "()Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;", "getSuccess", "()Z", "component1", "component2", "component3", "copy", "(Ljava/lang/Object;Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;Z)Lru/sbertroika/pasiv/gate/dto/ApiResponseDto;", "equals", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class ApiResponseDto<T extends java.lang.Object> {
    @org.jetbrains.annotations.Nullable()
    private final T data = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.OperationErrorDto error = null;
    private final boolean success = false;
    
    public ApiResponseDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u043d\u043d\u044b\u0435 \u043e\u0442\u0432\u0435\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    T data, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0448\u0438\u0431\u043a\u0430 (\u0435\u0441\u043b\u0438 \u0435\u0441\u0442\u044c)")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OperationErrorDto error, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043f\u0435\u0448\u043d\u043e\u0441\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438")
    boolean success) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final T getData() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OperationErrorDto getError() {
        return null;
    }
    
    public final boolean getSuccess() {
        return false;
    }
    
    public ApiResponseDto() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final T component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OperationErrorDto component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ApiResponseDto<T> copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u043d\u043d\u044b\u0435 \u043e\u0442\u0432\u0435\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    T data, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0448\u0438\u0431\u043a\u0430 (\u0435\u0441\u043b\u0438 \u0435\u0441\u0442\u044c)")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OperationErrorDto error, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043f\u0435\u0448\u043d\u043e\u0441\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438")
    boolean success) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}