package ru.sbertroika.pasiv.gate.output.model;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatus;", "", "<init>", "(Ljava/lang/String;I)V", "ACTIVE", "DISABLED", "BLOCKED", "IS_DELETED", "pasiv-gate-private"})
public enum ProjectOrganizationStatus {
    /*public static final*/ ACTIVE /* = new ACTIVE() */,
    /*public static final*/ DISABLED /* = new DISABLED() */,
    /*public static final*/ BLOCKED /* = new BLOCKED() */,
    /*public static final*/ IS_DELETED /* = new IS_DELETED() */;
    
    ProjectOrganizationStatus() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus> getEntries() {
        return null;
    }
}