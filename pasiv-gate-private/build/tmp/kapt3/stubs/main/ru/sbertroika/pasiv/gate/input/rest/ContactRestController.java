package ru.sbertroika.pasiv.gate.input.rest;

/**
 * REST контроллер для управления контактами
 */
@org.springframework.web.bind.annotation.RestController()
@org.springframework.web.bind.annotation.RequestMapping(value = {"/api/v1/contacts"})
@io.swagger.v3.oas.annotations.tags.Tag(name = "Contacts", description = "API \u0434\u043b\u044f \u0443\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u044f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430\u043c\u0438 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
@org.springframework.validation.annotation.Validated()
@org.springframework.security.access.annotation.Secured(value = {"ROLE_pasiv_console_admin"})
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\b\u0017\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001e\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\b\b\u0001\u0010\r\u001a\u00020\u000eH\u0017J(\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\b\b\u0001\u0010\u0010\u001a\u00020\u00112\b\b\u0001\u0010\r\u001a\u00020\u000eH\u0017J8\u0010\u0012\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00130\u000b0\n2\b\b\u0001\u0010\u0014\u001a\u00020\u00112\b\b\u0001\u0010\u0015\u001a\u00020\u00162\b\b\u0001\u0010\u0017\u001a\u00020\u0016H\u0017J\u001e\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u000b0\n2\b\b\u0001\u0010\u0010\u001a\u00020\u0011H\u0017J\u001e\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\b\b\u0001\u0010\u0010\u001a\u00020\u0011H\u0017J\u001e\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\b\b\u0001\u0010\u0010\u001a\u00020\u0011H\u0017J2\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001c0\u000b0\n2\b\b\u0001\u0010\u0010\u001a\u00020\u00112\b\b\u0001\u0010\u0015\u001a\u00020\u00162\b\b\u0001\u0010\u0017\u001a\u00020\u0016H\u0017R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lru/sbertroika/pasiv/gate/input/rest/ContactRestController;", "", "contactService", "Lru/sbertroika/pasiv/gate/output/service/ContactService;", "<init>", "(Lru/sbertroika/pasiv/gate/output/service/ContactService;)V", "log", "Lorg/slf4j/Logger;", "kotlin.jvm.PlatformType", "createContact", "Lorg/springframework/http/ResponseEntity;", "Lru/sbertroika/pasiv/gate/dto/ApiResponseDto;", "", "request", "Lru/sbertroika/pasiv/gate/dto/ContactDto;", "updateContact", "id", "", "getContacts", "", "organizationId", "page", "", "size", "getContactById", "deleteContact", "recoverContact", "getContactHistory", "Lru/sbertroika/pasiv/gate/dto/HistoryResultDto;", "pasiv-gate-private"})
public class ContactRestController {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.service.ContactService contactService = null;
    private final org.slf4j.Logger log = null;
    
    public ContactRestController(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.service.ContactService contactService) {
        super();
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", description = "\u0421\u043e\u0437\u0434\u0430\u0435\u0442 \u043d\u043e\u0432\u044b\u0439 \u043a\u043e\u043d\u0442\u0430\u043a\u0442 \u0434\u043b\u044f \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0441\u043e\u0437\u0434\u0430\u043d", responseCode = "201", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.EmptyResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u043a\u043e\u0440\u0440\u0435\u043a\u0442\u043d\u044b\u0435 \u0434\u0430\u043d\u043d\u044b\u0435 \u0437\u0430\u043f\u0440\u043e\u0441\u0430", responseCode = "400", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.ApiResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u043e\u0432\u0430\u043d", responseCode = "401"), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u0434\u043e\u0441\u0442\u0430\u0442\u043e\u0447\u043d\u043e \u043f\u0440\u0430\u0432 \u0434\u043e\u0441\u0442\u0443\u043f\u0430", responseCode = "403")})
    @org.springframework.web.bind.annotation.PostMapping()
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> createContact(@error.NonExistentClass()
    @org.springframework.web.bind.annotation.RequestBody()
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactDto request) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u041e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", description = "\u041e\u0431\u043d\u043e\u0432\u043b\u044f\u0435\u0442 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044e\u0449\u0438\u0439 \u043a\u043e\u043d\u0442\u0430\u043a\u0442")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.EmptyResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u043a\u043e\u0440\u0440\u0435\u043a\u0442\u043d\u044b\u0435 \u0434\u0430\u043d\u043d\u044b\u0435 \u0437\u0430\u043f\u0440\u043e\u0441\u0430", responseCode = "400"), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d", responseCode = "404")})
    @org.springframework.web.bind.annotation.PutMapping(value = {"/{id}"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> updateContact(@io.swagger.v3.oas.annotations.Parameter(description = "ID \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.springframework.web.bind.annotation.PathVariable()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @error.NonExistentClass()
    @org.springframework.web.bind.annotation.RequestBody()
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactDto request) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u041f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u0435 \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u043e\u0432 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", description = "\u0412\u043e\u0437\u0432\u0440\u0430\u0449\u0430\u0435\u0442 \u0441\u043f\u0438\u0441\u043e\u043a \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u043e\u0432 \u0434\u043b\u044f \u0443\u043a\u0430\u0437\u0430\u043d\u043d\u043e\u0439 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u0421\u043f\u0438\u0441\u043e\u043a \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u043e\u0432 \u043f\u043e\u043b\u0443\u0447\u0435\u043d \u0443\u0441\u043f\u0435\u0448\u043d\u043e", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.ApiResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041d\u0435\u043a\u043e\u0440\u0440\u0435\u043a\u0442\u043d\u044b\u0435 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u0437\u0430\u043f\u0440\u043e\u0441\u0430", responseCode = "400")})
    @org.springframework.web.bind.annotation.GetMapping()
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<java.util.List<ru.sbertroika.pasiv.gate.dto.ContactDto>>> getContacts(@io.swagger.v3.oas.annotations.Parameter(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.springframework.web.bind.annotation.RequestParam()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.Parameter(description = "\u041d\u043e\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b (\u043d\u0430\u0447\u0438\u043d\u0430\u044f \u0441 0)")
    @org.springframework.web.bind.annotation.RequestParam(defaultValue = "0")
    int page, @io.swagger.v3.oas.annotations.Parameter(description = "\u0420\u0430\u0437\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b")
    @org.springframework.web.bind.annotation.RequestParam(defaultValue = "20")
    int size) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u041f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430 \u043f\u043e ID", description = "\u0412\u043e\u0437\u0432\u0440\u0430\u0449\u0430\u0435\u0442 \u043a\u043e\u043d\u0442\u0430\u043a\u0442 \u043f\u043e \u0443\u043a\u0430\u0437\u0430\u043d\u043d\u043e\u043c\u0443 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440\u0443")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u043d\u0430\u0439\u0434\u0435\u043d", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.ApiResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d", responseCode = "404")})
    @org.springframework.web.bind.annotation.GetMapping(value = {"/{id}"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<ru.sbertroika.pasiv.gate.dto.ContactDto>> getContactById(@io.swagger.v3.oas.annotations.Parameter(description = "ID \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.springframework.web.bind.annotation.PathVariable()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String id) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", description = "\u041f\u043e\u043c\u0435\u0447\u0430\u0435\u0442 \u043a\u043e\u043d\u0442\u0430\u043a\u0442 \u043a\u0430\u043a \u0443\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0439 (\u043c\u044f\u0433\u043a\u043e\u0435 \u0443\u0434\u0430\u043b\u0435\u043d\u0438\u0435)")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0443\u0434\u0430\u043b\u0435\u043d", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.EmptyResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d", responseCode = "404")})
    @org.springframework.web.bind.annotation.DeleteMapping(value = {"/{id}"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> deleteContact(@io.swagger.v3.oas.annotations.Parameter(description = "ID \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.springframework.web.bind.annotation.PathVariable()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String id) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", description = "\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u0430\u0432\u043b\u0438\u0432\u0430\u0435\u0442 \u0440\u0430\u043d\u0435\u0435 \u0443\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0439 \u043a\u043e\u043d\u0442\u0430\u043a\u0442")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0432\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.EmptyResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d", responseCode = "404")})
    @org.springframework.web.bind.annotation.PostMapping(value = {"/{id}/recover"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<kotlin.Unit>> recoverContact(@io.swagger.v3.oas.annotations.Parameter(description = "ID \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.springframework.web.bind.annotation.PathVariable()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String id) {
        return null;
    }
    
    @io.swagger.v3.oas.annotations.Operation(summary = "\u041f\u043e\u043b\u0443\u0447\u0435\u043d\u0438\u0435 \u0438\u0441\u0442\u043e\u0440\u0438\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", description = "\u0412\u043e\u0437\u0432\u0440\u0430\u0449\u0430\u0435\u0442 \u0438\u0441\u0442\u043e\u0440\u0438\u044e \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430 \u0441 \u043f\u043e\u0434\u0434\u0435\u0440\u0436\u043a\u043e\u0439 \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {@io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u0418\u0441\u0442\u043e\u0440\u0438\u044f \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u043f\u043e\u043b\u0443\u0447\u0435\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e", responseCode = "200", content = {@io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ru.sbertroika.pasiv.gate.dto.ApiResponseDto.class))}), @io.swagger.v3.oas.annotations.responses.ApiResponse(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d", responseCode = "404")})
    @org.springframework.web.bind.annotation.GetMapping(value = {"/{id}/history"})
    @org.jetbrains.annotations.NotNull()
    public org.springframework.http.ResponseEntity<ru.sbertroika.pasiv.gate.dto.ApiResponseDto<ru.sbertroika.pasiv.gate.dto.HistoryResultDto>> getContactHistory(@io.swagger.v3.oas.annotations.Parameter(description = "ID \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.springframework.web.bind.annotation.PathVariable()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @io.swagger.v3.oas.annotations.Parameter(description = "\u041d\u043e\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b (\u043d\u0430\u0447\u0438\u043d\u0430\u044f \u0441 0)")
    @org.springframework.web.bind.annotation.RequestParam(defaultValue = "0")
    int page, @io.swagger.v3.oas.annotations.Parameter(description = "\u0420\u0430\u0437\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b")
    @org.springframework.web.bind.annotation.RequestParam(defaultValue = "20")
    int size) {
        return null;
    }
}