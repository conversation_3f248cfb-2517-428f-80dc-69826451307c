package ru.sbertroika.pasiv.gate.config;

@org.springframework.context.annotation.Configuration()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0017\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\b\u0010\b\u001a\u00020\tH\u0017J\b\u0010\n\u001a\u00020\u000bH\u0016J\b\u0010\f\u001a\u00020\rH\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0092D\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lru/sbertroika/pasiv/gate/config/DaDataConfig;", "", "tokenStorage", "Lru/sbertroika/pasiv/gate/output/dadata/TokenStorage;", "<init>", "(Lru/sbertroika/pasiv/gate/output/dadata/TokenStorage;)V", "baseUrl", "", "daDataClient", "Lru/sbertroika/pasiv/gate/output/dadata/DaDataClient;", "tokenInterceptor", "Lru/sbertroika/pasiv/gate/output/dadata/TokenInterceptor;", "getUnsafeOkHttpClient", "Lokhttp3/OkHttpClient;", "pasiv-gate-private"})
public class DaDataConfig {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.dadata.TokenStorage tokenStorage = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String baseUrl = "http://suggestions.dadata.ru/";
    
    public DaDataConfig(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.dadata.TokenStorage tokenStorage) {
        super();
    }
    
    @org.springframework.context.annotation.Bean()
    @org.jetbrains.annotations.NotNull()
    public ru.sbertroika.pasiv.gate.output.dadata.DaDataClient daDataClient() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public ru.sbertroika.pasiv.gate.output.dadata.TokenInterceptor tokenInterceptor() {
        return null;
    }
    
    private okhttp3.OkHttpClient getUnsafeOkHttpClient() {
        return null;
    }
}