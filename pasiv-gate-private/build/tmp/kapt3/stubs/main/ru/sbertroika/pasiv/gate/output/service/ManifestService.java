package ru.sbertroika.pasiv.gate.output.service;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\"\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\t"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/ManifestService;", "", "getManifest", "Larrow/core/Either;", "", "Lru/sbertroika/common/manifest/v1/pasiv/ManifestPasiv;", "request", "Lru/sbertroika/common/manifest/v1/ManifestRequest;", "(Lru/sbertroika/common/manifest/v1/ManifestRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "pasiv-gate-private"})
public abstract interface ManifestService {
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getManifest(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.common.manifest.v1.ManifestRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Throwable, ru.sbertroika.common.manifest.v1.pasiv.ManifestPasiv>> $completion);
}