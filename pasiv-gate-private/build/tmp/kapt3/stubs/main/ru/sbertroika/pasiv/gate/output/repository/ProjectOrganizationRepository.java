package ru.sbertroika.pasiv.gate.output.repository;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\"\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00020\u00052\b\b\u0001\u0010\u0006\u001a\u00020\u00072\b\b\u0001\u0010\b\u001a\u00020\u0007H\'\u00a8\u0006\t"}, d2 = {"Lru/sbertroika/pasiv/gate/output/repository/ProjectOrganizationRepository;", "Lorg/springframework/data/repository/kotlin/CoroutineCrudRepository;", "Lru/sbertroika/pasiv/gate/output/model/ProjectOrganization;", "Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationPK;", "findByOrganizationIdAndProjectId", "Lkotlinx/coroutines/flow/Flow;", "organization", "Ljava/util/UUID;", "project", "pasiv-gate-private"})
public abstract interface ProjectOrganizationRepository extends org.springframework.data.repository.kotlin.CoroutineCrudRepository<ru.sbertroika.pasiv.gate.output.model.ProjectOrganization, ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationPK> {
    
    @org.springframework.data.r2dbc.repository.Query(value = "SELECT * FROM project_organization o INNER JOIN (    SELECT po_id, MAX(po_version) vers     FROM project_organization     GROUP BY po_id ) o2 ON o.po_id = o2.po_id AND o.po_version = o2.vers  AND o.po_organization_id = :organization AND o.po_project_id = :project LIMIT 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.ProjectOrganization> findByOrganizationIdAndProjectId(@org.springframework.data.repository.query.Param(value = "organization")
    @org.jetbrains.annotations.NotNull()
    java.util.UUID organization, @org.springframework.data.repository.query.Param(value = "project")
    @org.jetbrains.annotations.NotNull()
    java.util.UUID project);
}