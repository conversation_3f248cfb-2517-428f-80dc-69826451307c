package ru.sbertroika.pasiv.gate.output.dadata.model.response;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00003\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\bX\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0003\b\u00b3\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u00a1\t\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010 \u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010)\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010-\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010/\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00100\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00101\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00102\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00103\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00104\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00105\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00106\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00107\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00108\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u00109\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010:\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010;\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010<\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010=\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010>\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010?\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010@\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010A\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010B\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010C\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010D\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010E\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010F\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010G\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010H\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010I\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010J\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010K\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010L\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010M\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010N\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010O\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010Q\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010R\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010S\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010T\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010U\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010V\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010W\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010X\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010Y\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010Z\u001a\u0004\u0018\u00010\u0003\u0012\u0018\b\u0002\u0010[\u001a\u0012\u0012\u0004\u0012\u00020]0\\j\b\u0012\u0004\u0012\u00020]`^\u0012\n\b\u0002\u0010_\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010`\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010a\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010c\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010d\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\bg\u0010hJ\f\u0010\u00af\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b0\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b1\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b2\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b3\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b4\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b5\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b6\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b7\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b8\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00b9\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ba\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00bb\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00bc\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00bd\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00be\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00bf\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c0\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c1\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c2\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c3\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c4\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c5\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c6\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c7\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c8\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00c9\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ca\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00cb\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00cc\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00cd\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ce\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00cf\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d0\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d1\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d2\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d3\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d4\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d5\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d6\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d7\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d8\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00d9\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00da\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00db\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00dc\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00dd\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00de\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00df\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e0\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e1\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e2\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e3\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e4\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e5\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e6\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e7\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e8\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00e9\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ea\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00eb\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ec\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ed\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ee\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ef\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f0\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f1\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f2\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f3\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f4\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f5\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f6\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f7\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f8\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00f9\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00fa\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00fb\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00fc\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00fd\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00fe\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u00ff\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0080\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0081\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0084\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0085\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0086\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u001a\u0010\u0087\u0003\u001a\u0012\u0012\u0004\u0012\u00020]0\\j\b\u0012\u0004\u0012\u00020]`^H\u00c6\u0003J\f\u0010\u0088\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0089\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008a\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008b\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008c\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008d\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008e\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008f\u0003\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u00a4\t\u0010\u0090\u0003\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010 \u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010)\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010-\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010/\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00100\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00101\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00102\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00103\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00104\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00105\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00106\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00107\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00108\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u00109\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010:\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010;\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010<\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010=\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010>\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010?\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010@\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010A\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010B\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010C\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010D\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010E\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010F\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010G\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010H\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010I\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010J\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010K\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010L\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010M\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010N\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010O\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010Q\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010R\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010S\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010T\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010U\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010V\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010W\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010X\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010Y\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010Z\u001a\u0004\u0018\u00010\u00032\u0018\b\u0002\u0010[\u001a\u0012\u0012\u0004\u0012\u00020]0\\j\b\u0012\u0004\u0012\u00020]`^2\n\b\u0002\u0010_\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010`\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010a\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010c\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010d\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010e\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0016\u0010\u0091\u0003\u001a\u00030\u0092\u00032\t\u0010\u0093\u0003\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000b\u0010\u0094\u0003\u001a\u00030\u0095\u0003H\u00d6\u0001J\n\u0010\u0096\u0003\u001a\u00020\u0003H\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bi\u0010j\"\u0004\bk\u0010lR \u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bm\u0010j\"\u0004\bn\u0010lR \u0010\u0005\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bo\u0010j\"\u0004\bp\u0010lR \u0010\u0006\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bq\u0010j\"\u0004\br\u0010lR \u0010\u0007\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bs\u0010j\"\u0004\bt\u0010lR \u0010\b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bu\u0010j\"\u0004\bv\u0010lR \u0010\t\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bw\u0010j\"\u0004\bx\u0010lR \u0010\n\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\by\u0010j\"\u0004\bz\u0010lR \u0010\u000b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b{\u0010j\"\u0004\b|\u0010lR \u0010\f\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b}\u0010j\"\u0004\b~\u0010lR!\u0010\r\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000f\n\u0000\u001a\u0004\b\u007f\u0010j\"\u0005\b\u0080\u0001\u0010lR\"\u0010\u000e\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0081\u0001\u0010j\"\u0005\b\u0082\u0001\u0010lR\"\u0010\u000f\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0083\u0001\u0010j\"\u0005\b\u0084\u0001\u0010lR\"\u0010\u0010\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0085\u0001\u0010j\"\u0005\b\u0086\u0001\u0010lR\"\u0010\u0011\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0087\u0001\u0010j\"\u0005\b\u0088\u0001\u0010lR\"\u0010\u0012\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0089\u0001\u0010j\"\u0005\b\u008a\u0001\u0010lR\"\u0010\u0013\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u008b\u0001\u0010j\"\u0005\b\u008c\u0001\u0010lR\"\u0010\u0014\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u008d\u0001\u0010j\"\u0005\b\u008e\u0001\u0010lR\"\u0010\u0015\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u008f\u0001\u0010j\"\u0005\b\u0090\u0001\u0010lR\"\u0010\u0016\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0091\u0001\u0010j\"\u0005\b\u0092\u0001\u0010lR\"\u0010\u0017\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0093\u0001\u0010j\"\u0005\b\u0094\u0001\u0010lR\"\u0010\u0018\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0095\u0001\u0010j\"\u0005\b\u0096\u0001\u0010lR\"\u0010\u0019\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0097\u0001\u0010j\"\u0005\b\u0098\u0001\u0010lR\"\u0010\u001a\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0099\u0001\u0010j\"\u0005\b\u009a\u0001\u0010lR\"\u0010\u001b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u009b\u0001\u0010j\"\u0005\b\u009c\u0001\u0010lR\"\u0010\u001c\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u009d\u0001\u0010j\"\u0005\b\u009e\u0001\u0010lR\"\u0010\u001d\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u009f\u0001\u0010j\"\u0005\b\u00a0\u0001\u0010lR\"\u0010\u001e\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a1\u0001\u0010j\"\u0005\b\u00a2\u0001\u0010lR\"\u0010\u001f\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a3\u0001\u0010j\"\u0005\b\u00a4\u0001\u0010lR\"\u0010 \u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a5\u0001\u0010j\"\u0005\b\u00a6\u0001\u0010lR\"\u0010!\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a7\u0001\u0010j\"\u0005\b\u00a8\u0001\u0010lR\"\u0010\"\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a9\u0001\u0010j\"\u0005\b\u00aa\u0001\u0010lR\"\u0010#\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00ab\u0001\u0010j\"\u0005\b\u00ac\u0001\u0010lR\"\u0010$\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00ad\u0001\u0010j\"\u0005\b\u00ae\u0001\u0010lR\"\u0010%\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00af\u0001\u0010j\"\u0005\b\u00b0\u0001\u0010lR\"\u0010&\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00b1\u0001\u0010j\"\u0005\b\u00b2\u0001\u0010lR\"\u0010\'\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00b3\u0001\u0010j\"\u0005\b\u00b4\u0001\u0010lR\"\u0010(\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00b5\u0001\u0010j\"\u0005\b\u00b6\u0001\u0010lR\"\u0010)\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00b7\u0001\u0010j\"\u0005\b\u00b8\u0001\u0010lR\"\u0010*\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00b9\u0001\u0010j\"\u0005\b\u00ba\u0001\u0010lR\"\u0010+\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00bb\u0001\u0010j\"\u0005\b\u00bc\u0001\u0010lR\"\u0010,\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00bd\u0001\u0010j\"\u0005\b\u00be\u0001\u0010lR\"\u0010-\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00bf\u0001\u0010j\"\u0005\b\u00c0\u0001\u0010lR\"\u0010.\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00c1\u0001\u0010j\"\u0005\b\u00c2\u0001\u0010lR\"\u0010/\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00c3\u0001\u0010j\"\u0005\b\u00c4\u0001\u0010lR\"\u00100\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00c5\u0001\u0010j\"\u0005\b\u00c6\u0001\u0010lR\"\u00101\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00c7\u0001\u0010j\"\u0005\b\u00c8\u0001\u0010lR\"\u00102\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00c9\u0001\u0010j\"\u0005\b\u00ca\u0001\u0010lR\"\u00103\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00cb\u0001\u0010j\"\u0005\b\u00cc\u0001\u0010lR\"\u00104\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00cd\u0001\u0010j\"\u0005\b\u00ce\u0001\u0010lR\"\u00105\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00cf\u0001\u0010j\"\u0005\b\u00d0\u0001\u0010lR\"\u00106\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00d1\u0001\u0010j\"\u0005\b\u00d2\u0001\u0010lR\"\u00107\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00d3\u0001\u0010j\"\u0005\b\u00d4\u0001\u0010lR\"\u00108\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00d5\u0001\u0010j\"\u0005\b\u00d6\u0001\u0010lR\"\u00109\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00d7\u0001\u0010j\"\u0005\b\u00d8\u0001\u0010lR\"\u0010:\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00d9\u0001\u0010j\"\u0005\b\u00da\u0001\u0010lR\"\u0010;\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00db\u0001\u0010j\"\u0005\b\u00dc\u0001\u0010lR\"\u0010<\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00dd\u0001\u0010j\"\u0005\b\u00de\u0001\u0010lR\"\u0010=\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00df\u0001\u0010j\"\u0005\b\u00e0\u0001\u0010lR\"\u0010>\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00e1\u0001\u0010j\"\u0005\b\u00e2\u0001\u0010lR\"\u0010?\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00e3\u0001\u0010j\"\u0005\b\u00e4\u0001\u0010lR\"\u0010@\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00e5\u0001\u0010j\"\u0005\b\u00e6\u0001\u0010lR\"\u0010A\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00e7\u0001\u0010j\"\u0005\b\u00e8\u0001\u0010lR\"\u0010B\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00e9\u0001\u0010j\"\u0005\b\u00ea\u0001\u0010lR\"\u0010C\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00eb\u0001\u0010j\"\u0005\b\u00ec\u0001\u0010lR\"\u0010D\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00ed\u0001\u0010j\"\u0005\b\u00ee\u0001\u0010lR\"\u0010E\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00ef\u0001\u0010j\"\u0005\b\u00f0\u0001\u0010lR\"\u0010F\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00f1\u0001\u0010j\"\u0005\b\u00f2\u0001\u0010lR\"\u0010G\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00f3\u0001\u0010j\"\u0005\b\u00f4\u0001\u0010lR\"\u0010H\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00f5\u0001\u0010j\"\u0005\b\u00f6\u0001\u0010lR\"\u0010I\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00f7\u0001\u0010j\"\u0005\b\u00f8\u0001\u0010lR\"\u0010J\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00f9\u0001\u0010j\"\u0005\b\u00fa\u0001\u0010lR\"\u0010K\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00fb\u0001\u0010j\"\u0005\b\u00fc\u0001\u0010lR\"\u0010L\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00fd\u0001\u0010j\"\u0005\b\u00fe\u0001\u0010lR\"\u0010M\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00ff\u0001\u0010j\"\u0005\b\u0080\u0002\u0010lR\"\u0010N\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0081\u0002\u0010j\"\u0005\b\u0082\u0002\u0010lR\"\u0010O\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0083\u0002\u0010j\"\u0005\b\u0084\u0002\u0010lR\"\u0010P\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0085\u0002\u0010j\"\u0005\b\u0086\u0002\u0010lR\"\u0010Q\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0087\u0002\u0010j\"\u0005\b\u0088\u0002\u0010lR\"\u0010R\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0089\u0002\u0010j\"\u0005\b\u008a\u0002\u0010lR\"\u0010S\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u008b\u0002\u0010j\"\u0005\b\u008c\u0002\u0010lR\"\u0010T\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u008d\u0002\u0010j\"\u0005\b\u008e\u0002\u0010lR\"\u0010U\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u008f\u0002\u0010j\"\u0005\b\u0090\u0002\u0010lR\"\u0010V\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0091\u0002\u0010j\"\u0005\b\u0092\u0002\u0010lR\"\u0010W\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0093\u0002\u0010j\"\u0005\b\u0094\u0002\u0010lR\"\u0010X\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0095\u0002\u0010j\"\u0005\b\u0096\u0002\u0010lR\"\u0010Y\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0097\u0002\u0010j\"\u0005\b\u0098\u0002\u0010lR\"\u0010Z\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u0099\u0002\u0010j\"\u0005\b\u009a\u0002\u0010lR2\u0010[\u001a\u0012\u0012\u0004\u0012\u00020]0\\j\b\u0012\u0004\u0012\u00020]`^8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0012\n\u0000\u001a\u0006\b\u009b\u0002\u0010\u009c\u0002\"\u0006\b\u009d\u0002\u0010\u009e\u0002R\"\u0010_\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u009f\u0002\u0010j\"\u0005\b\u00a0\u0002\u0010lR\"\u0010`\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a1\u0002\u0010j\"\u0005\b\u00a2\u0002\u0010lR\"\u0010a\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a3\u0002\u0010j\"\u0005\b\u00a4\u0002\u0010lR\"\u0010b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a5\u0002\u0010j\"\u0005\b\u00a6\u0002\u0010lR\"\u0010c\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a7\u0002\u0010j\"\u0005\b\u00a8\u0002\u0010lR\"\u0010d\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00a9\u0002\u0010j\"\u0005\b\u00aa\u0002\u0010lR\"\u0010e\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00ab\u0002\u0010j\"\u0005\b\u00ac\u0002\u0010lR\"\u0010f\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0000\u001a\u0005\b\u00ad\u0002\u0010j\"\u0005\b\u00ae\u0002\u0010l\u00a8\u0006\u0097\u0003"}, d2 = {"Lru/sbertroika/pasiv/gate/output/dadata/model/response/AddressData;", "", "postalCode", "", "country", "countryIsoCode", "federalDistrict", "regionFiasId", "regionKladrId", "regionIsoCode", "regionWithType", "regionType", "regionTypeFull", "region", "areaFiasId", "areaKladrId", "areaWithType", "areaType", "areaTypeFull", "area", "cityFiasId", "cityKladrId", "cityWithType", "cityType", "cityTypeFull", "city", "cityArea", "cityDistrictFiasId", "cityDistrictKladrId", "cityDistrictWithType", "cityDistrictType", "cityDistrictTypeFull", "cityDistrict", "settlementFiasId", "settlementKladrId", "settlementWithType", "settlementType", "settlementTypeFull", "settlement", "streetFiasId", "streetKladrId", "streetWithType", "streetType", "streetTypeFull", "street", "steadFiasId", "steadCadnum", "steadType", "steadTypeFull", "stead", "houseFiasId", "houseKladrId", "houseCadnum", "houseType", "houseTypeFull", "house", "blockType", "blockTypeFull", "block", "entrance", "floor", "flatFiasId", "flatCadnum", "flatType", "flatTypeFull", "flat", "flatArea", "squareMeterPrice", "flatPrice", "roomFiasId", "roomCadnum", "roomType", "roomTypeFull", "room", "postalBox", "fiasId", "fiasCode", "fiasLevel", "fiasActualityState", "kladrId", "geonameId", "capitalMarker", "okato", "oktmo", "taxOffice", "taxOfficeLegal", "timezone", "geoLat", "geoLon", "beltwayHit", "beltwayDistance", "metro", "Ljava/util/ArrayList;", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Metro;", "Lkotlin/collections/ArrayList;", "divisions", "qcGeo", "qcComplete", "qcHouse", "historyValues", "unparsedParts", "source", "qc", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/ArrayList;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getPostalCode", "()Ljava/lang/String;", "setPostalCode", "(Ljava/lang/String;)V", "getCountry", "setCountry", "getCountryIsoCode", "setCountryIsoCode", "getFederalDistrict", "setFederalDistrict", "getRegionFiasId", "setRegionFiasId", "getRegionKladrId", "setRegionKladrId", "getRegionIsoCode", "setRegionIsoCode", "getRegionWithType", "setRegionWithType", "getRegionType", "setRegionType", "getRegionTypeFull", "setRegionTypeFull", "getRegion", "setRegion", "getAreaFiasId", "setAreaFiasId", "getAreaKladrId", "setAreaKladrId", "getAreaWithType", "setAreaWithType", "getAreaType", "setAreaType", "getAreaTypeFull", "setAreaTypeFull", "getArea", "setArea", "getCityFiasId", "setCityFiasId", "getCityKladrId", "setCityKladrId", "getCityWithType", "setCityWithType", "getCityType", "setCityType", "getCityTypeFull", "setCityTypeFull", "getCity", "setCity", "getCityArea", "setCityArea", "getCityDistrictFiasId", "setCityDistrictFiasId", "getCityDistrictKladrId", "setCityDistrictKladrId", "getCityDistrictWithType", "setCityDistrictWithType", "getCityDistrictType", "setCityDistrictType", "getCityDistrictTypeFull", "setCityDistrictTypeFull", "getCityDistrict", "setCityDistrict", "getSettlementFiasId", "setSettlementFiasId", "getSettlementKladrId", "setSettlementKladrId", "getSettlementWithType", "setSettlementWithType", "getSettlementType", "setSettlementType", "getSettlementTypeFull", "setSettlementTypeFull", "getSettlement", "setSettlement", "getStreetFiasId", "setStreetFiasId", "getStreetKladrId", "setStreetKladrId", "getStreetWithType", "setStreetWithType", "getStreetType", "setStreetType", "getStreetTypeFull", "setStreetTypeFull", "getStreet", "setStreet", "getSteadFiasId", "setSteadFiasId", "getSteadCadnum", "setSteadCadnum", "getSteadType", "setSteadType", "getSteadTypeFull", "setSteadTypeFull", "getStead", "setStead", "getHouseFiasId", "setHouseFiasId", "getHouseKladrId", "setHouseKladrId", "getHouseCadnum", "setHouseCadnum", "getHouseType", "setHouseType", "getHouseTypeFull", "setHouseTypeFull", "getHouse", "setHouse", "getBlockType", "setBlockType", "getBlockTypeFull", "setBlockTypeFull", "getBlock", "setBlock", "getEntrance", "setEntrance", "getFloor", "setFloor", "getFlatFiasId", "setFlatFiasId", "getFlatCadnum", "setFlatCadnum", "getFlatType", "setFlatType", "getFlatTypeFull", "setFlatTypeFull", "getFlat", "setFlat", "getFlatArea", "setFlatArea", "getSquareMeterPrice", "setSquareMeterPrice", "getFlatPrice", "setFlatPrice", "getRoomFiasId", "setRoomFiasId", "getRoomCadnum", "setRoomCadnum", "getRoomType", "setRoomType", "getRoomTypeFull", "setRoomTypeFull", "getRoom", "setRoom", "getPostalBox", "setPostalBox", "getFiasId", "setFiasId", "getFiasCode", "setFiasCode", "getFiasLevel", "setFiasLevel", "getFiasActualityState", "setFiasActualityState", "getKladrId", "setKladrId", "getGeonameId", "setGeonameId", "getCapitalMarker", "setCapitalMarker", "getOkato", "setOkato", "getOktmo", "setOktmo", "getTaxOffice", "setTaxOffice", "getTaxOfficeLegal", "setTaxOfficeLegal", "getTimezone", "setTimezone", "getGeoLat", "setGeoLat", "getGeoLon", "setGeoLon", "getBeltwayHit", "setBeltwayHit", "getBeltwayDistance", "setBeltwayDistance", "getMetro", "()Ljava/util/ArrayList;", "setMetro", "(Ljava/util/ArrayList;)V", "getDivisions", "setDivisions", "getQcGeo", "setQcGeo", "getQcComplete", "setQcComplete", "getQcHouse", "setQcHouse", "getHistoryValues", "setHistoryValues", "getUnparsedParts", "setUnparsedParts", "getSource", "setSource", "getQc", "setQc", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component37", "component38", "component39", "component40", "component41", "component42", "component43", "component44", "component45", "component46", "component47", "component48", "component49", "component50", "component51", "component52", "component53", "component54", "component55", "component56", "component57", "component58", "component59", "component60", "component61", "component62", "component63", "component64", "component65", "component66", "component67", "component68", "component69", "component70", "component71", "component72", "component73", "component74", "component75", "component76", "component77", "component78", "component79", "component80", "component81", "component82", "component83", "component84", "component85", "component86", "component87", "component88", "component89", "component90", "component91", "component92", "component93", "component94", "component95", "component96", "component97", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class AddressData {
    @com.google.gson.annotations.SerializedName(value = "postal_code")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String postalCode;
    @com.google.gson.annotations.SerializedName(value = "country")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String country;
    @com.google.gson.annotations.SerializedName(value = "country_iso_code")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String countryIsoCode;
    @com.google.gson.annotations.SerializedName(value = "federal_district")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String federalDistrict;
    @com.google.gson.annotations.SerializedName(value = "region_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String regionFiasId;
    @com.google.gson.annotations.SerializedName(value = "region_kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String regionKladrId;
    @com.google.gson.annotations.SerializedName(value = "region_iso_code")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String regionIsoCode;
    @com.google.gson.annotations.SerializedName(value = "region_with_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String regionWithType;
    @com.google.gson.annotations.SerializedName(value = "region_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String regionType;
    @com.google.gson.annotations.SerializedName(value = "region_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String regionTypeFull;
    @com.google.gson.annotations.SerializedName(value = "region")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String region;
    @com.google.gson.annotations.SerializedName(value = "area_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String areaFiasId;
    @com.google.gson.annotations.SerializedName(value = "area_kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String areaKladrId;
    @com.google.gson.annotations.SerializedName(value = "area_with_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String areaWithType;
    @com.google.gson.annotations.SerializedName(value = "area_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String areaType;
    @com.google.gson.annotations.SerializedName(value = "area_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String areaTypeFull;
    @com.google.gson.annotations.SerializedName(value = "area")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String area;
    @com.google.gson.annotations.SerializedName(value = "city_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityFiasId;
    @com.google.gson.annotations.SerializedName(value = "city_kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityKladrId;
    @com.google.gson.annotations.SerializedName(value = "city_with_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityWithType;
    @com.google.gson.annotations.SerializedName(value = "city_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityType;
    @com.google.gson.annotations.SerializedName(value = "city_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityTypeFull;
    @com.google.gson.annotations.SerializedName(value = "city")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String city;
    @com.google.gson.annotations.SerializedName(value = "city_area")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityArea;
    @com.google.gson.annotations.SerializedName(value = "city_district_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityDistrictFiasId;
    @com.google.gson.annotations.SerializedName(value = "city_district_kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityDistrictKladrId;
    @com.google.gson.annotations.SerializedName(value = "city_district_with_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityDistrictWithType;
    @com.google.gson.annotations.SerializedName(value = "city_district_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityDistrictType;
    @com.google.gson.annotations.SerializedName(value = "city_district_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityDistrictTypeFull;
    @com.google.gson.annotations.SerializedName(value = "city_district")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String cityDistrict;
    @com.google.gson.annotations.SerializedName(value = "settlement_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String settlementFiasId;
    @com.google.gson.annotations.SerializedName(value = "settlement_kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String settlementKladrId;
    @com.google.gson.annotations.SerializedName(value = "settlement_with_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String settlementWithType;
    @com.google.gson.annotations.SerializedName(value = "settlement_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String settlementType;
    @com.google.gson.annotations.SerializedName(value = "settlement_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String settlementTypeFull;
    @com.google.gson.annotations.SerializedName(value = "settlement")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String settlement;
    @com.google.gson.annotations.SerializedName(value = "street_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String streetFiasId;
    @com.google.gson.annotations.SerializedName(value = "street_kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String streetKladrId;
    @com.google.gson.annotations.SerializedName(value = "street_with_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String streetWithType;
    @com.google.gson.annotations.SerializedName(value = "street_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String streetType;
    @com.google.gson.annotations.SerializedName(value = "street_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String streetTypeFull;
    @com.google.gson.annotations.SerializedName(value = "street")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String street;
    @com.google.gson.annotations.SerializedName(value = "stead_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String steadFiasId;
    @com.google.gson.annotations.SerializedName(value = "stead_cadnum")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String steadCadnum;
    @com.google.gson.annotations.SerializedName(value = "stead_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String steadType;
    @com.google.gson.annotations.SerializedName(value = "stead_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String steadTypeFull;
    @com.google.gson.annotations.SerializedName(value = "stead")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String stead;
    @com.google.gson.annotations.SerializedName(value = "house_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String houseFiasId;
    @com.google.gson.annotations.SerializedName(value = "house_kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String houseKladrId;
    @com.google.gson.annotations.SerializedName(value = "house_cadnum")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String houseCadnum;
    @com.google.gson.annotations.SerializedName(value = "house_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String houseType;
    @com.google.gson.annotations.SerializedName(value = "house_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String houseTypeFull;
    @com.google.gson.annotations.SerializedName(value = "house")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String house;
    @com.google.gson.annotations.SerializedName(value = "block_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String blockType;
    @com.google.gson.annotations.SerializedName(value = "block_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String blockTypeFull;
    @com.google.gson.annotations.SerializedName(value = "block")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String block;
    @com.google.gson.annotations.SerializedName(value = "entrance")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String entrance;
    @com.google.gson.annotations.SerializedName(value = "floor")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String floor;
    @com.google.gson.annotations.SerializedName(value = "flat_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String flatFiasId;
    @com.google.gson.annotations.SerializedName(value = "flat_cadnum")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String flatCadnum;
    @com.google.gson.annotations.SerializedName(value = "flat_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String flatType;
    @com.google.gson.annotations.SerializedName(value = "flat_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String flatTypeFull;
    @com.google.gson.annotations.SerializedName(value = "flat")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String flat;
    @com.google.gson.annotations.SerializedName(value = "flat_area")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String flatArea;
    @com.google.gson.annotations.SerializedName(value = "square_meter_price")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String squareMeterPrice;
    @com.google.gson.annotations.SerializedName(value = "flat_price")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String flatPrice;
    @com.google.gson.annotations.SerializedName(value = "room_fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String roomFiasId;
    @com.google.gson.annotations.SerializedName(value = "room_cadnum")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String roomCadnum;
    @com.google.gson.annotations.SerializedName(value = "room_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String roomType;
    @com.google.gson.annotations.SerializedName(value = "room_type_full")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String roomTypeFull;
    @com.google.gson.annotations.SerializedName(value = "room")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String room;
    @com.google.gson.annotations.SerializedName(value = "postal_box")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String postalBox;
    @com.google.gson.annotations.SerializedName(value = "fias_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fiasId;
    @com.google.gson.annotations.SerializedName(value = "fias_code")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fiasCode;
    @com.google.gson.annotations.SerializedName(value = "fias_level")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fiasLevel;
    @com.google.gson.annotations.SerializedName(value = "fias_actuality_state")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fiasActualityState;
    @com.google.gson.annotations.SerializedName(value = "kladr_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String kladrId;
    @com.google.gson.annotations.SerializedName(value = "geoname_id")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String geonameId;
    @com.google.gson.annotations.SerializedName(value = "capital_marker")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String capitalMarker;
    @com.google.gson.annotations.SerializedName(value = "okato")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okato;
    @com.google.gson.annotations.SerializedName(value = "oktmo")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String oktmo;
    @com.google.gson.annotations.SerializedName(value = "tax_office")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String taxOffice;
    @com.google.gson.annotations.SerializedName(value = "tax_office_legal")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String taxOfficeLegal;
    @com.google.gson.annotations.SerializedName(value = "timezone")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String timezone;
    @com.google.gson.annotations.SerializedName(value = "geo_lat")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String geoLat;
    @com.google.gson.annotations.SerializedName(value = "geo_lon")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String geoLon;
    @com.google.gson.annotations.SerializedName(value = "beltway_hit")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String beltwayHit;
    @com.google.gson.annotations.SerializedName(value = "beltway_distance")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String beltwayDistance;
    @com.google.gson.annotations.SerializedName(value = "metro")
    @org.jetbrains.annotations.NotNull()
    private java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Metro> metro;
    @com.google.gson.annotations.SerializedName(value = "divisions")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String divisions;
    @com.google.gson.annotations.SerializedName(value = "qc_geo")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String qcGeo;
    @com.google.gson.annotations.SerializedName(value = "qc_complete")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String qcComplete;
    @com.google.gson.annotations.SerializedName(value = "qc_house")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String qcHouse;
    @com.google.gson.annotations.SerializedName(value = "history_values")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String historyValues;
    @com.google.gson.annotations.SerializedName(value = "unparsed_parts")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String unparsedParts;
    @com.google.gson.annotations.SerializedName(value = "source")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String source;
    @com.google.gson.annotations.SerializedName(value = "qc")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String qc;
    
    public AddressData(@org.jetbrains.annotations.Nullable()
    java.lang.String postalCode, @org.jetbrains.annotations.Nullable()
    java.lang.String country, @org.jetbrains.annotations.Nullable()
    java.lang.String countryIsoCode, @org.jetbrains.annotations.Nullable()
    java.lang.String federalDistrict, @org.jetbrains.annotations.Nullable()
    java.lang.String regionFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String regionKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String regionIsoCode, @org.jetbrains.annotations.Nullable()
    java.lang.String regionWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String regionType, @org.jetbrains.annotations.Nullable()
    java.lang.String regionTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String region, @org.jetbrains.annotations.Nullable()
    java.lang.String areaFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String areaKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String areaWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String areaType, @org.jetbrains.annotations.Nullable()
    java.lang.String areaTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String area, @org.jetbrains.annotations.Nullable()
    java.lang.String cityFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String city, @org.jetbrains.annotations.Nullable()
    java.lang.String cityArea, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrict, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementType, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String settlement, @org.jetbrains.annotations.Nullable()
    java.lang.String streetFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String streetKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String streetWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String streetType, @org.jetbrains.annotations.Nullable()
    java.lang.String streetTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String street, @org.jetbrains.annotations.Nullable()
    java.lang.String steadFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String steadCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String steadType, @org.jetbrains.annotations.Nullable()
    java.lang.String steadTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String stead, @org.jetbrains.annotations.Nullable()
    java.lang.String houseFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String houseKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String houseCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String houseType, @org.jetbrains.annotations.Nullable()
    java.lang.String houseTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String house, @org.jetbrains.annotations.Nullable()
    java.lang.String blockType, @org.jetbrains.annotations.Nullable()
    java.lang.String blockTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String block, @org.jetbrains.annotations.Nullable()
    java.lang.String entrance, @org.jetbrains.annotations.Nullable()
    java.lang.String floor, @org.jetbrains.annotations.Nullable()
    java.lang.String flatFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String flatCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String flatType, @org.jetbrains.annotations.Nullable()
    java.lang.String flatTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String flat, @org.jetbrains.annotations.Nullable()
    java.lang.String flatArea, @org.jetbrains.annotations.Nullable()
    java.lang.String squareMeterPrice, @org.jetbrains.annotations.Nullable()
    java.lang.String flatPrice, @org.jetbrains.annotations.Nullable()
    java.lang.String roomFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String roomCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String roomType, @org.jetbrains.annotations.Nullable()
    java.lang.String roomTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String room, @org.jetbrains.annotations.Nullable()
    java.lang.String postalBox, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasCode, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasLevel, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasActualityState, @org.jetbrains.annotations.Nullable()
    java.lang.String kladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String geonameId, @org.jetbrains.annotations.Nullable()
    java.lang.String capitalMarker, @org.jetbrains.annotations.Nullable()
    java.lang.String okato, @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String taxOffice, @org.jetbrains.annotations.Nullable()
    java.lang.String taxOfficeLegal, @org.jetbrains.annotations.Nullable()
    java.lang.String timezone, @org.jetbrains.annotations.Nullable()
    java.lang.String geoLat, @org.jetbrains.annotations.Nullable()
    java.lang.String geoLon, @org.jetbrains.annotations.Nullable()
    java.lang.String beltwayHit, @org.jetbrains.annotations.Nullable()
    java.lang.String beltwayDistance, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Metro> metro, @org.jetbrains.annotations.Nullable()
    java.lang.String divisions, @org.jetbrains.annotations.Nullable()
    java.lang.String qcGeo, @org.jetbrains.annotations.Nullable()
    java.lang.String qcComplete, @org.jetbrains.annotations.Nullable()
    java.lang.String qcHouse, @org.jetbrains.annotations.Nullable()
    java.lang.String historyValues, @org.jetbrains.annotations.Nullable()
    java.lang.String unparsedParts, @org.jetbrains.annotations.Nullable()
    java.lang.String source, @org.jetbrains.annotations.Nullable()
    java.lang.String qc) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPostalCode() {
        return null;
    }
    
    public final void setPostalCode(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCountry() {
        return null;
    }
    
    public final void setCountry(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCountryIsoCode() {
        return null;
    }
    
    public final void setCountryIsoCode(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFederalDistrict() {
        return null;
    }
    
    public final void setFederalDistrict(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegionFiasId() {
        return null;
    }
    
    public final void setRegionFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegionKladrId() {
        return null;
    }
    
    public final void setRegionKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegionIsoCode() {
        return null;
    }
    
    public final void setRegionIsoCode(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegionWithType() {
        return null;
    }
    
    public final void setRegionWithType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegionType() {
        return null;
    }
    
    public final void setRegionType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegionTypeFull() {
        return null;
    }
    
    public final void setRegionTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegion() {
        return null;
    }
    
    public final void setRegion(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAreaFiasId() {
        return null;
    }
    
    public final void setAreaFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAreaKladrId() {
        return null;
    }
    
    public final void setAreaKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAreaWithType() {
        return null;
    }
    
    public final void setAreaWithType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAreaType() {
        return null;
    }
    
    public final void setAreaType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAreaTypeFull() {
        return null;
    }
    
    public final void setAreaTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getArea() {
        return null;
    }
    
    public final void setArea(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityFiasId() {
        return null;
    }
    
    public final void setCityFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityKladrId() {
        return null;
    }
    
    public final void setCityKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityWithType() {
        return null;
    }
    
    public final void setCityWithType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityType() {
        return null;
    }
    
    public final void setCityType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityTypeFull() {
        return null;
    }
    
    public final void setCityTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCity() {
        return null;
    }
    
    public final void setCity(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityArea() {
        return null;
    }
    
    public final void setCityArea(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityDistrictFiasId() {
        return null;
    }
    
    public final void setCityDistrictFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityDistrictKladrId() {
        return null;
    }
    
    public final void setCityDistrictKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityDistrictWithType() {
        return null;
    }
    
    public final void setCityDistrictWithType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityDistrictType() {
        return null;
    }
    
    public final void setCityDistrictType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityDistrictTypeFull() {
        return null;
    }
    
    public final void setCityDistrictTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCityDistrict() {
        return null;
    }
    
    public final void setCityDistrict(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSettlementFiasId() {
        return null;
    }
    
    public final void setSettlementFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSettlementKladrId() {
        return null;
    }
    
    public final void setSettlementKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSettlementWithType() {
        return null;
    }
    
    public final void setSettlementWithType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSettlementType() {
        return null;
    }
    
    public final void setSettlementType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSettlementTypeFull() {
        return null;
    }
    
    public final void setSettlementTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSettlement() {
        return null;
    }
    
    public final void setSettlement(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreetFiasId() {
        return null;
    }
    
    public final void setStreetFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreetKladrId() {
        return null;
    }
    
    public final void setStreetKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreetWithType() {
        return null;
    }
    
    public final void setStreetWithType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreetType() {
        return null;
    }
    
    public final void setStreetType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreetTypeFull() {
        return null;
    }
    
    public final void setStreetTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreet() {
        return null;
    }
    
    public final void setStreet(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSteadFiasId() {
        return null;
    }
    
    public final void setSteadFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSteadCadnum() {
        return null;
    }
    
    public final void setSteadCadnum(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSteadType() {
        return null;
    }
    
    public final void setSteadType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSteadTypeFull() {
        return null;
    }
    
    public final void setSteadTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStead() {
        return null;
    }
    
    public final void setStead(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHouseFiasId() {
        return null;
    }
    
    public final void setHouseFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHouseKladrId() {
        return null;
    }
    
    public final void setHouseKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHouseCadnum() {
        return null;
    }
    
    public final void setHouseCadnum(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHouseType() {
        return null;
    }
    
    public final void setHouseType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHouseTypeFull() {
        return null;
    }
    
    public final void setHouseTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHouse() {
        return null;
    }
    
    public final void setHouse(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBlockType() {
        return null;
    }
    
    public final void setBlockType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBlockTypeFull() {
        return null;
    }
    
    public final void setBlockTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBlock() {
        return null;
    }
    
    public final void setBlock(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getEntrance() {
        return null;
    }
    
    public final void setEntrance(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFloor() {
        return null;
    }
    
    public final void setFloor(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFlatFiasId() {
        return null;
    }
    
    public final void setFlatFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFlatCadnum() {
        return null;
    }
    
    public final void setFlatCadnum(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFlatType() {
        return null;
    }
    
    public final void setFlatType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFlatTypeFull() {
        return null;
    }
    
    public final void setFlatTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFlat() {
        return null;
    }
    
    public final void setFlat(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFlatArea() {
        return null;
    }
    
    public final void setFlatArea(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSquareMeterPrice() {
        return null;
    }
    
    public final void setSquareMeterPrice(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFlatPrice() {
        return null;
    }
    
    public final void setFlatPrice(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRoomFiasId() {
        return null;
    }
    
    public final void setRoomFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRoomCadnum() {
        return null;
    }
    
    public final void setRoomCadnum(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRoomType() {
        return null;
    }
    
    public final void setRoomType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRoomTypeFull() {
        return null;
    }
    
    public final void setRoomTypeFull(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRoom() {
        return null;
    }
    
    public final void setRoom(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPostalBox() {
        return null;
    }
    
    public final void setPostalBox(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFiasId() {
        return null;
    }
    
    public final void setFiasId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFiasCode() {
        return null;
    }
    
    public final void setFiasCode(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFiasLevel() {
        return null;
    }
    
    public final void setFiasLevel(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFiasActualityState() {
        return null;
    }
    
    public final void setFiasActualityState(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getKladrId() {
        return null;
    }
    
    public final void setKladrId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGeonameId() {
        return null;
    }
    
    public final void setGeonameId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCapitalMarker() {
        return null;
    }
    
    public final void setCapitalMarker(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkato() {
        return null;
    }
    
    public final void setOkato(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOktmo() {
        return null;
    }
    
    public final void setOktmo(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTaxOffice() {
        return null;
    }
    
    public final void setTaxOffice(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTaxOfficeLegal() {
        return null;
    }
    
    public final void setTaxOfficeLegal(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTimezone() {
        return null;
    }
    
    public final void setTimezone(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGeoLat() {
        return null;
    }
    
    public final void setGeoLat(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGeoLon() {
        return null;
    }
    
    public final void setGeoLon(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBeltwayHit() {
        return null;
    }
    
    public final void setBeltwayHit(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBeltwayDistance() {
        return null;
    }
    
    public final void setBeltwayDistance(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Metro> getMetro() {
        return null;
    }
    
    public final void setMetro(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Metro> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDivisions() {
        return null;
    }
    
    public final void setDivisions(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getQcGeo() {
        return null;
    }
    
    public final void setQcGeo(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getQcComplete() {
        return null;
    }
    
    public final void setQcComplete(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getQcHouse() {
        return null;
    }
    
    public final void setQcHouse(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHistoryValues() {
        return null;
    }
    
    public final void setHistoryValues(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUnparsedParts() {
        return null;
    }
    
    public final void setUnparsedParts(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSource() {
        return null;
    }
    
    public final void setSource(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getQc() {
        return null;
    }
    
    public final void setQc(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    public AddressData() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component22() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component23() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component24() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component25() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component26() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component27() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component28() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component29() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component30() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component31() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component32() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component33() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component34() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component35() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component36() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component37() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component38() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component39() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component40() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component41() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component42() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component43() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component44() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component45() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component46() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component47() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component48() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component49() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component50() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component51() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component52() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component53() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component54() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component55() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component56() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component57() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component58() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component59() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component60() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component61() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component62() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component63() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component64() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component65() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component66() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component67() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component68() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component69() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component70() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component71() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component72() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component73() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component74() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component75() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component76() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component77() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component78() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component79() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component80() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component81() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component82() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component83() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component84() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component85() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component86() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component87() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component88() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Metro> component89() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component90() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component91() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component92() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component93() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component94() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component95() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component96() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component97() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData copy(@org.jetbrains.annotations.Nullable()
    java.lang.String postalCode, @org.jetbrains.annotations.Nullable()
    java.lang.String country, @org.jetbrains.annotations.Nullable()
    java.lang.String countryIsoCode, @org.jetbrains.annotations.Nullable()
    java.lang.String federalDistrict, @org.jetbrains.annotations.Nullable()
    java.lang.String regionFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String regionKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String regionIsoCode, @org.jetbrains.annotations.Nullable()
    java.lang.String regionWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String regionType, @org.jetbrains.annotations.Nullable()
    java.lang.String regionTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String region, @org.jetbrains.annotations.Nullable()
    java.lang.String areaFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String areaKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String areaWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String areaType, @org.jetbrains.annotations.Nullable()
    java.lang.String areaTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String area, @org.jetbrains.annotations.Nullable()
    java.lang.String cityFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String city, @org.jetbrains.annotations.Nullable()
    java.lang.String cityArea, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictType, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrictTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String cityDistrict, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementType, @org.jetbrains.annotations.Nullable()
    java.lang.String settlementTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String settlement, @org.jetbrains.annotations.Nullable()
    java.lang.String streetFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String streetKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String streetWithType, @org.jetbrains.annotations.Nullable()
    java.lang.String streetType, @org.jetbrains.annotations.Nullable()
    java.lang.String streetTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String street, @org.jetbrains.annotations.Nullable()
    java.lang.String steadFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String steadCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String steadType, @org.jetbrains.annotations.Nullable()
    java.lang.String steadTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String stead, @org.jetbrains.annotations.Nullable()
    java.lang.String houseFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String houseKladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String houseCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String houseType, @org.jetbrains.annotations.Nullable()
    java.lang.String houseTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String house, @org.jetbrains.annotations.Nullable()
    java.lang.String blockType, @org.jetbrains.annotations.Nullable()
    java.lang.String blockTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String block, @org.jetbrains.annotations.Nullable()
    java.lang.String entrance, @org.jetbrains.annotations.Nullable()
    java.lang.String floor, @org.jetbrains.annotations.Nullable()
    java.lang.String flatFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String flatCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String flatType, @org.jetbrains.annotations.Nullable()
    java.lang.String flatTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String flat, @org.jetbrains.annotations.Nullable()
    java.lang.String flatArea, @org.jetbrains.annotations.Nullable()
    java.lang.String squareMeterPrice, @org.jetbrains.annotations.Nullable()
    java.lang.String flatPrice, @org.jetbrains.annotations.Nullable()
    java.lang.String roomFiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String roomCadnum, @org.jetbrains.annotations.Nullable()
    java.lang.String roomType, @org.jetbrains.annotations.Nullable()
    java.lang.String roomTypeFull, @org.jetbrains.annotations.Nullable()
    java.lang.String room, @org.jetbrains.annotations.Nullable()
    java.lang.String postalBox, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasId, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasCode, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasLevel, @org.jetbrains.annotations.Nullable()
    java.lang.String fiasActualityState, @org.jetbrains.annotations.Nullable()
    java.lang.String kladrId, @org.jetbrains.annotations.Nullable()
    java.lang.String geonameId, @org.jetbrains.annotations.Nullable()
    java.lang.String capitalMarker, @org.jetbrains.annotations.Nullable()
    java.lang.String okato, @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String taxOffice, @org.jetbrains.annotations.Nullable()
    java.lang.String taxOfficeLegal, @org.jetbrains.annotations.Nullable()
    java.lang.String timezone, @org.jetbrains.annotations.Nullable()
    java.lang.String geoLat, @org.jetbrains.annotations.Nullable()
    java.lang.String geoLon, @org.jetbrains.annotations.Nullable()
    java.lang.String beltwayHit, @org.jetbrains.annotations.Nullable()
    java.lang.String beltwayDistance, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Metro> metro, @org.jetbrains.annotations.Nullable()
    java.lang.String divisions, @org.jetbrains.annotations.Nullable()
    java.lang.String qcGeo, @org.jetbrains.annotations.Nullable()
    java.lang.String qcComplete, @org.jetbrains.annotations.Nullable()
    java.lang.String qcHouse, @org.jetbrains.annotations.Nullable()
    java.lang.String historyValues, @org.jetbrains.annotations.Nullable()
    java.lang.String unparsedParts, @org.jetbrains.annotations.Nullable()
    java.lang.String source, @org.jetbrains.annotations.Nullable()
    java.lang.String qc) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}