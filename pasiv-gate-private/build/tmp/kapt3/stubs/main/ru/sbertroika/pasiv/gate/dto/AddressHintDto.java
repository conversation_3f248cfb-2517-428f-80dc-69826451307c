package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0430 \u0430\u0434\u0440\u0435\u0441\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\'\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001B\u009d\u0001\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0005\u0012\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0001\u0010\b\u001a\u00020\u0005\u0012\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0001\u0010\n\u001a\u00020\u0005\u0012\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0003\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0003\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0003\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0003\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u0010\u0010)\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0016J\u000b\u0010*\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010+\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010-\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010/\u001a\u00020\u0005H\u00c6\u0003J\u000b\u00100\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u00101\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u00102\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u0010\"J\u0010\u00103\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u0010\"J\u0010\u00104\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003\u00a2\u0006\u0002\u0010&J\u000b\u00105\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u00a4\u0001\u00106\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\b\u0003\u0010\u0006\u001a\u00020\u00052\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u00052\b\b\u0003\u0010\b\u001a\u00020\u00052\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u00052\b\b\u0003\u0010\n\u001a\u00020\u00052\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\r\u001a\u0004\u0018\u00010\u000e2\n\b\u0003\u0010\u000f\u001a\u0004\u0018\u00010\u000e2\n\b\u0003\u0010\u0010\u001a\u0004\u0018\u00010\u00112\n\b\u0003\u0010\u0012\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u00107J\u0013\u00108\u001a\u0002092\b\u0010:\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010;\u001a\u00020\u0003H\u00d6\u0001J\t\u0010<\u001a\u00020\u0005H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\u0017\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0019R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0019R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0019R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u0015\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b!\u0010\"R\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b$\u0010\"R\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\n\n\u0002\u0010\'\u001a\u0004\b%\u0010&R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u0019\u00a8\u0006="}, d2 = {"Lru/sbertroika/pasiv/gate/dto/AddressHintDto;", "", "index", "", "country", "", "region", "district", "city", "street", "house", "buildingOrHousing", "officeOrRoom", "longitude", "", "latitude", "oktmo", "", "fiac", "<init>", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Long;Ljava/lang/String;)V", "getIndex", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getCountry", "()Ljava/lang/String;", "getRegion", "getDistrict", "getCity", "getStreet", "getHouse", "getBuildingOrHousing", "getOfficeOrRoom", "getLongitude", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getLatitude", "getOktmo", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getFiac", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "copy", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Long;Ljava/lang/String;)Lru/sbertroika/pasiv/gate/dto/AddressHintDto;", "equals", "", "other", "hashCode", "toString", "pasiv-gate-private"})
public final class AddressHintDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer index = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String country = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String region = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String district = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String city = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String street = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String house = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String buildingOrHousing = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String officeOrRoom = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double longitude = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double latitude = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long oktmo = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String fiac = null;
    
    public AddressHintDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0447\u0442\u043e\u0432\u044b\u0439 \u0438\u043d\u0434\u0435\u043a\u0441")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u0430\u043d\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String country, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0435\u0433\u0438\u043e\u043d/\u043e\u0431\u043b\u0430\u0441\u0442\u044c")
    @org.jetbrains.annotations.NotNull()
    java.lang.String region, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0430\u0439\u043e\u043d")
    @org.jetbrains.annotations.Nullable()
    java.lang.String district, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0413\u043e\u0440\u043e\u0434")
    @org.jetbrains.annotations.NotNull()
    java.lang.String city, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u043b\u0438\u0446\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String street, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043c")
    @org.jetbrains.annotations.NotNull()
    java.lang.String house, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u043e\u0435\u043d\u0438\u0435/\u043a\u043e\u0440\u043f\u0443\u0441")
    @org.jetbrains.annotations.Nullable()
    java.lang.String buildingOrHousing, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0444\u0438\u0441/\u043a\u043e\u043c\u043d\u0430\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String officeOrRoom, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043b\u0433\u043e\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double longitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0428\u0438\u0440\u043e\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double latitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0422\u041c\u041e")
    @org.jetbrains.annotations.Nullable()
    java.lang.Long oktmo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0418\u0410\u0421")
    @org.jetbrains.annotations.Nullable()
    java.lang.String fiac) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getIndex() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCountry() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRegion() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDistrict() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCity() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreet() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHouse() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBuildingOrHousing() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOfficeOrRoom() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLongitude() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLatitude() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getOktmo() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFiac() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressHintDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0447\u0442\u043e\u0432\u044b\u0439 \u0438\u043d\u0434\u0435\u043a\u0441")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u0430\u043d\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String country, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0435\u0433\u0438\u043e\u043d/\u043e\u0431\u043b\u0430\u0441\u0442\u044c")
    @org.jetbrains.annotations.NotNull()
    java.lang.String region, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0430\u0439\u043e\u043d")
    @org.jetbrains.annotations.Nullable()
    java.lang.String district, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0413\u043e\u0440\u043e\u0434")
    @org.jetbrains.annotations.NotNull()
    java.lang.String city, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u043b\u0438\u0446\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String street, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043c")
    @org.jetbrains.annotations.NotNull()
    java.lang.String house, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u043e\u0435\u043d\u0438\u0435/\u043a\u043e\u0440\u043f\u0443\u0441")
    @org.jetbrains.annotations.Nullable()
    java.lang.String buildingOrHousing, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0444\u0438\u0441/\u043a\u043e\u043c\u043d\u0430\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String officeOrRoom, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043b\u0433\u043e\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double longitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0428\u0438\u0440\u043e\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double latitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0422\u041c\u041e")
    @org.jetbrains.annotations.Nullable()
    java.lang.Long oktmo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0418\u0410\u0421")
    @org.jetbrains.annotations.Nullable()
    java.lang.String fiac) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}