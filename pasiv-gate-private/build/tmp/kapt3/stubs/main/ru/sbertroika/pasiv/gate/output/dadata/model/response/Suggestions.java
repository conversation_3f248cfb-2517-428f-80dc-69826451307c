package ru.sbertroika.pasiv.gate.output.dadata.model.response;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0013\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u000b\u0010\t\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0015\u0010\n\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\u0005\u00a8\u0006\u0012"}, d2 = {"Lru/sbertroika/pasiv/gate/output/dadata/model/response/Suggestions;", "", "data", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrgData;", "<init>", "(Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrgData;)V", "getData", "()Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrgData;", "setData", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class Suggestions {
    @com.google.gson.annotations.SerializedName(value = "data")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData data;
    
    public Suggestions(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData data) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData getData() {
        return null;
    }
    
    public final void setData(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData p0) {
    }
    
    public Suggestions() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions copy(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData data) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}