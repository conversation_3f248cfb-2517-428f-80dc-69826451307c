package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0440\u043e\u0441 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430 \u0441 \u0432\u0430\u043b\u0438\u0434\u0430\u0446\u0438\u0435\u0439")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\u0010\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u0003H\u0002J\u0010\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0003H\u0002J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u0017\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00052\b\b\u0003\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\nR\u0014\u0010\u000e\u001a\u00020\u00038FX\u0087\u0004\u00a2\u0006\u0006\u001a\u0004\b\u000f\u0010\n\u00a8\u0006\u001e"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/CreateContactRequestDto;", "", "organizationId", "", "type", "Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "value", "<init>", "(Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;Ljava/lang/String;)V", "getOrganizationId", "()Ljava/lang/String;", "getType", "()Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "getValue", "validatedValue", "getValidatedValue", "validatePhone", "phone", "validateEmail", "email", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class CreateContactRequestDto {
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String organizationId = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.ContactTypeDto type = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String value = null;
    
    public CreateContactRequestDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrganizationId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactTypeDto getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValue() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValidatedValue() {
        return null;
    }
    
    private final java.lang.String validatePhone(java.lang.String phone) {
        return null;
    }
    
    private final java.lang.String validateEmail(java.lang.String email) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactTypeDto component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.CreateContactRequestDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}