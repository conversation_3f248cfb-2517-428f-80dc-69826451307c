package ru.sbertroika.pasiv.gate.output.repository;

@org.springframework.stereotype.Repository()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0017\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\u0017\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u0010\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0016J\u0010\u0010\u0012\u001a\u00020\u00022\u0006\u0010\u0013\u001a\u00020\u0014H\u0016J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0016\u001a\u00020\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0017J\u0018\u0010\u0018\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0016\u001a\u00020\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0017J \u0010\u0019\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u001a\u001a\u00020\u001bH\u0096@\u00a2\u0006\u0002\u0010\u001cJ\u001e\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u0016J\u000e\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00020\u001eH\u0016J\u001c\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00020\u001e2\u0006\u0010#\u001a\u00020$H\u0096@\u00a2\u0006\u0002\u0010%J,\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00020\u001e2\u0006\u0010#\u001a\u00020$2\u0006\u0010&\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u0096@\u00a2\u0006\u0002\u0010\'J\u0016\u0010(\u001a\u00020 2\u0006\u0010#\u001a\u00020$H\u0096@\u00a2\u0006\u0002\u0010%J\u001c\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00020\u001e2\u0006\u0010\u0016\u001a\u00020\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010*\u001a\u00020 2\u0006\u0010\u0016\u001a\u00020\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0017J,\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00020\u001e2\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010&\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u0096@\u00a2\u0006\u0002\u0010+J\u001e\u0010,\u001a\u00020-2\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010.\u001a\u00020$H\u0096@\u00a2\u0006\u0002\u0010/J,\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00020\u001e2\u0006\u00100\u001a\u0002012\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u0096@\u00a2\u0006\u0002\u00102J\u0016\u00103\u001a\u00020 2\u0006\u00100\u001a\u000201H\u0096@\u00a2\u0006\u0002\u00104J\u001c\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00020\u001e2\u0006\u00100\u001a\u000201H\u0096@\u00a2\u0006\u0002\u00104J\u001a\u00105\u001a\u00020\u000f2\u0006\u00100\u001a\u0002012\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u0012R\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0014\u0010\u0006\u001a\u00020\u0007X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u00066"}, d2 = {"Lru/sbertroika/pasiv/gate/output/repository/OrganizationRepository;", "Lru/sbertroika/pasiv/gate/output/repository/AbstractRepository;", "Lru/sbertroika/pasiv/gate/output/model/Organization;", "Lru/sbertroika/pasiv/gate/output/model/OrganizationPK;", "dbClient", "Lorg/springframework/r2dbc/core/DatabaseClient;", "repository", "Lru/sbertroika/pasiv/gate/output/repository/OrganizationCrudRepository;", "<init>", "(Lorg/springframework/r2dbc/core/DatabaseClient;Lru/sbertroika/pasiv/gate/output/repository/OrganizationCrudRepository;)V", "getDbClient", "()Lorg/springframework/r2dbc/core/DatabaseClient;", "getRepository", "()Lru/sbertroika/pasiv/gate/output/repository/OrganizationCrudRepository;", "getQuery", "", "isCount", "", "toEntity", "t", "Lio/r2dbc/spi/Readable;", "findById", "id", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "findDeletedById", "findByIdAndVersion", "version", "", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "findAll", "Lkotlinx/coroutines/flow/Flow;", "page", "", "limit", "findAllByProjectId", "project", "Ljava/util/UUID;", "(Ljava/util/UUID;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "offset", "(Ljava/util/UUID;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "countAllByProjectId", "getHistory", "getHistoryCount", "(Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleted", "", "userId", "(Ljava/lang/String;Ljava/util/UUID;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "filter", "Lru/sbertroika/pasiv/gate/v1/OrganizationFilter;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationFilter;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "countAll", "(Lru/sbertroika/pasiv/gate/v1/OrganizationFilter;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "buildRequestByFilter", "pasiv-gate-private"})
public class OrganizationRepository extends ru.sbertroika.pasiv.gate.output.repository.AbstractRepository<ru.sbertroika.pasiv.gate.output.model.Organization, ru.sbertroika.pasiv.gate.output.model.OrganizationPK> {
    @org.jetbrains.annotations.NotNull()
    private final org.springframework.r2dbc.core.DatabaseClient dbClient = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.OrganizationCrudRepository repository = null;
    
    public OrganizationRepository(@org.jetbrains.annotations.NotNull()
    org.springframework.r2dbc.core.DatabaseClient dbClient, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.OrganizationCrudRepository repository) {
        super(null, null);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public org.springframework.r2dbc.core.DatabaseClient getDbClient() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public ru.sbertroika.pasiv.gate.output.repository.OrganizationCrudRepository getRepository() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String getQuery(boolean isCount) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public ru.sbertroika.pasiv.gate.output.model.Organization toEntity(@org.jetbrains.annotations.NotNull()
    io.r2dbc.spi.Readable t) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object findById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.output.model.Organization> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object findDeletedById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.output.model.Organization> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object findByIdAndVersion(@org.jetbrains.annotations.NotNull()
    java.lang.String id, long version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.output.model.Organization> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization> findAll(int page, int limit) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization> findAll() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object findAllByProjectId(@org.jetbrains.annotations.NotNull()
    java.util.UUID project, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object findAllByProjectId(@org.jetbrains.annotations.NotNull()
    java.util.UUID project, int offset, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object countAllByProjectId(@org.jetbrains.annotations.NotNull()
    java.util.UUID project, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getHistoryCount(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String id, int offset, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleted(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.util.UUID userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object findAll(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationFilter filter, int page, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object countAll(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationFilter filter, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object findAll(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationFilter filter, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<ru.sbertroika.pasiv.gate.output.model.Organization>> $completion) {
        return null;
    }
    
    private java.lang.String buildRequestByFilter(ru.sbertroika.pasiv.gate.v1.OrganizationFilter filter, boolean isCount) {
        return null;
    }
}