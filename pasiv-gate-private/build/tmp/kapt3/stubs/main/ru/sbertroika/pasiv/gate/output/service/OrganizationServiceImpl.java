package ru.sbertroika.pasiv.gate.output.service;

@org.springframework.stereotype.Service()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0017\u0018\u0000 <2\u00020\u0001:\u0001<B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ.\u0010\n\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u000e0\u000b2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010\u0013J.\u0010\u0014\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u000e0\u000b2\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0011\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010\u0017J&\u0010\u0018\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u00190\u000b2\u0006\u0010\u001a\u001a\u00020\u001bH\u0096@\u00a2\u0006\u0002\u0010\u001cJ&\u0010\u001d\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u00190\u000b2\u0006\u0010\u001a\u001a\u00020\u001eH\u0096@\u00a2\u0006\u0002\u0010\u001fJ&\u0010 \u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u00160\u000b2\u0006\u0010\u001a\u001a\u00020!H\u0096@\u00a2\u0006\u0002\u0010\"J.\u0010#\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u000e0\u000b2\u0006\u0010\u001a\u001a\u00020$2\u0006\u0010\u0011\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010%J.\u0010&\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u000e0\u000b2\u0006\u0010\u001a\u001a\u00020$2\u0006\u0010\u0011\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010%J&\u0010\'\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020(0\u000b2\u0006\u0010\u001a\u001a\u00020)H\u0096@\u00a2\u0006\u0002\u0010*J.\u0010+\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u000e0\u000b2\u0006\u0010\u001a\u001a\u00020!2\u0006\u0010\u0011\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010,J.\u0010-\u001a\u0012\u0012\b\u0012\u00060\fj\u0002`\r\u0012\u0004\u0012\u00020\u000e0\u000b2\u0006\u0010\u001a\u001a\u00020!2\u0006\u0010\u0011\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010,J\"\u0010.\u001a\b\u0012\u0004\u0012\u0002000/2\f\u00101\u001a\b\u0012\u0004\u0012\u0002020/H\u0092@\u00a2\u0006\u0002\u00103J\u0010\u00104\u001a\u00020\u00162\u0006\u00105\u001a\u000202H\u0012J\u001e\u00106\u001a\u0002072\u0006\u00108\u001a\u0002092\u0006\u0010\u0011\u001a\u00020\u0012H\u0092@\u00a2\u0006\u0002\u0010:J\u001e\u0010;\u001a\u0002072\u0006\u00108\u001a\u0002092\u0006\u0010\u0011\u001a\u00020\u0012H\u0092@\u00a2\u0006\u0002\u0010:R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006="}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/OrganizationServiceImpl;", "Lru/sbertroika/pasiv/gate/output/service/OrganizationService;", "repository", "Lru/sbertroika/pasiv/gate/output/repository/OrganizationRepository;", "addressRepository", "Lru/sbertroika/pasiv/gate/output/repository/AddressRepository;", "projectOrganizationRepository", "Lru/sbertroika/pasiv/gate/output/repository/ProjectOrganizationRepository;", "<init>", "(Lru/sbertroika/pasiv/gate/output/repository/OrganizationRepository;Lru/sbertroika/pasiv/gate/output/repository/AddressRepository;Lru/sbertroika/pasiv/gate/output/repository/ProjectOrganizationRepository;)V", "createOrganization", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "", "organizationWithAddresses", "Lru/sbertroika/pasiv/gate/v1/OrganizationWithAddresses;", "userId", "", "(Lru/sbertroika/pasiv/gate/v1/OrganizationWithAddresses;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateOrganization", "organization", "Lru/sbertroika/pasiv/gate/v1/Organization;", "(Lru/sbertroika/pasiv/gate/v1/Organization;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "organizationList", "Lru/sbertroika/pasiv/gate/v1/OrganizationResult;", "request", "Lru/sbertroika/pasiv/gate/v1/OrganizationListRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "organizationListForProject", "Lru/sbertroika/pasiv/gate/v1/OrganizationListForProjectRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationListForProjectRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOrganization", "Lru/sbertroika/pasiv/gate/v1/ByIdRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addOrganizationInProject", "Lru/sbertroika/pasiv/gate/v1/OrganizationInProjectRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationInProjectRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeOrganizationInProject", "getHistory", "Lru/sbertroika/common/v1/HistoryResult;", "Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recoverOrganization", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOrganization", "mapObjectToHistory", "", "Lru/sbertroika/common/v1/History;", "result", "Lru/sbertroika/pasiv/gate/output/model/Organization;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "mapOrganizationToGrpc", "entity", "saveAddress", "Lru/sbertroika/pasiv/gate/output/model/Address;", "address", "Lru/sbertroika/pasiv/gate/v1/Address;", "(Lru/sbertroika/pasiv/gate/v1/Address;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addAddress", "Companion", "pasiv-gate-private"})
public class OrganizationServiceImpl implements ru.sbertroika.pasiv.gate.output.service.OrganizationService {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.OrganizationRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.AddressRepository addressRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.repository.ProjectOrganizationRepository projectOrganizationRepository = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SYSTEM_USER_ID = "00000000-f04d-40dd-8829-598f62ad47f5";
    @org.jetbrains.annotations.NotNull()
    public static final ru.sbertroika.pasiv.gate.output.service.OrganizationServiceImpl.Companion Companion = null;
    
    public OrganizationServiceImpl(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.OrganizationRepository repository, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.AddressRepository addressRepository, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.repository.ProjectOrganizationRepository projectOrganizationRepository) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object createOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses organizationWithAddresses, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Organization organization, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object organizationList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.OrganizationResult>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object organizationListForProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.OrganizationResult>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.Organization>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object addOrganizationInProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object removeOrganizationInProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getHistory(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.common.v1.HistoryResult>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object recoverOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion) {
        return null;
    }
    
    private java.lang.Object mapObjectToHistory(java.util.List<ru.sbertroika.pasiv.gate.output.model.Organization> result, kotlin.coroutines.Continuation<? super java.util.List<ru.sbertroika.common.v1.History>> $completion) {
        return null;
    }
    
    private ru.sbertroika.pasiv.gate.v1.Organization mapOrganizationToGrpc(ru.sbertroika.pasiv.gate.output.model.Organization entity) {
        return null;
    }
    
    private java.lang.Object saveAddress(ru.sbertroika.pasiv.gate.v1.Address address, java.lang.String userId, kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.output.model.Address> $completion) {
        return null;
    }
    
    private java.lang.Object addAddress(ru.sbertroika.pasiv.gate.v1.Address address, java.lang.String userId, kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.output.model.Address> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/OrganizationServiceImpl$Companion;", "", "<init>", "()V", "SYSTEM_USER_ID", "", "pasiv-gate-private"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}