package ru.sbertroika.pasiv.gate.output.service;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J \u0010\u0002\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0007\u001a\u00020\bH&\u00a8\u0006\t"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/DaDataService;", "", "getOrganizationHint", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "Lru/sbertroika/pasiv/gate/v1/OrganizationHintList;", "request", "Lru/sbertroika/pasiv/gate/v1/OrganizationHintRequest;", "pasiv-gate-private"})
public abstract interface DaDataService {
    
    @org.jetbrains.annotations.NotNull()
    public abstract arrow.core.Either<java.lang.Error, ru.sbertroika.pasiv.gate.v1.OrganizationHintList> getOrganizationHint(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest request);
}