package ru.sbertroika.pasiv.gate;

@org.springframework.boot.autoconfigure.SpringBootApplication()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0017\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lru/sbertroika/pasiv/gate/PASIVGatePrivateApplication;", "", "<init>", "()V", "pasiv-gate-private"})
public class PASIVGatePrivateApplication {
    
    public PASIVGatePrivateApplication() {
        super();
    }
}