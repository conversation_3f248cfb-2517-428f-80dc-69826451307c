package ru.sbertroika.pasiv.gate.dto;

/**
 * DTO для работы с адресами
 */
@io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0430\u0434\u0440\u0435\u0441\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/AddressTypeDto;", "", "<init>", "(Ljava/lang/String;I)V", "LEGAL", "ACTUAL", "MAILING", "pasiv-gate-private"})
public enum AddressTypeDto {
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u042e\u0440\u0438\u0434\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0430\u0434\u0440\u0435\u0441")
    /*public static final*/ LEGAL /* = new LEGAL() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0430\u043a\u0442\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0430\u0434\u0440\u0435\u0441")
    /*public static final*/ ACTUAL /* = new ACTUAL() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0447\u0442\u043e\u0432\u044b\u0439 \u0430\u0434\u0440\u0435\u0441")
    /*public static final*/ MAILING /* = new MAILING() */;
    
    AddressTypeDto() {
    }
    
    /**
     * DTO для работы с адресами
     */
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.dto.AddressTypeDto> getEntries() {
        return null;
    }
}