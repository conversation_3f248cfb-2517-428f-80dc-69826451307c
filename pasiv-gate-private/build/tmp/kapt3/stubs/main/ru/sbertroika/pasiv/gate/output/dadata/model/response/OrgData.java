package ru.sbertroika.pasiv.gate.output.dadata.model.response;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\bo\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\u00ff\u0002\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u0018\b\u0002\u0010\u0006\u001a\u0012\u0012\u0004\u0012\u00020\b0\u0007j\b\u0012\u0004\u0012\u00020\b`\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0016\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u0003\u0012\u0018\b\u0002\u0010\u001f\u001a\u0012\u0012\u0004\u0012\u00020 0\u0007j\b\u0012\u0004\u0012\u00020 `\t\u0012\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\"\u0012\u0010\b\u0002\u0010#\u001a\n\u0012\u0004\u0012\u00020%\u0018\u00010$\u0012\u0010\b\u0002\u0010&\u001a\n\u0012\u0004\u0012\u00020\'\u0018\u00010$\u0012\n\b\u0002\u0010(\u001a\u0004\u0018\u00010)\u0012\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0004\b,\u0010-J\u000b\u0010z\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010{\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0019\u0010|\u001a\u0012\u0012\u0004\u0012\u00020\b0\u0007j\b\u0012\u0004\u0012\u00020\b`\tH\u00c6\u0003J\u000b\u0010}\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010~\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u007f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0080\u0001\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u0010AJ\f\u0010\u0081\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0082\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0083\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0084\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0085\u0001\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003J\f\u0010\u0086\u0001\u001a\u0004\u0018\u00010\u0016H\u00c6\u0003J\f\u0010\u0087\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0088\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u0089\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008a\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008b\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008c\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008d\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u008e\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u001a\u0010\u008f\u0001\u001a\u0012\u0012\u0004\u0012\u00020 0\u0007j\b\u0012\u0004\u0012\u00020 `\tH\u00c6\u0003J\f\u0010\u0090\u0001\u001a\u0004\u0018\u00010\"H\u00c6\u0003J\u0012\u0010\u0091\u0001\u001a\n\u0012\u0004\u0012\u00020%\u0018\u00010$H\u00c6\u0003J\u0012\u0010\u0092\u0001\u001a\n\u0012\u0004\u0012\u00020\'\u0018\u00010$H\u00c6\u0003J\u0011\u0010\u0093\u0001\u001a\u0004\u0018\u00010)H\u00c6\u0003\u00a2\u0006\u0002\u0010rJ\f\u0010\u0094\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0095\u0001\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u0010AJ\u0088\u0003\u0010\u0096\u0001\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u0018\b\u0002\u0010\u0006\u001a\u0012\u0012\u0004\u0012\u00020\b0\u0007j\b\u0012\u0004\u0012\u00020\b`\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00162\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00032\u0018\b\u0002\u0010\u001f\u001a\u0012\u0012\u0004\u0012\u00020 0\u0007j\b\u0012\u0004\u0012\u00020 `\t2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\"2\u0010\b\u0002\u0010#\u001a\n\u0012\u0004\u0012\u00020%\u0018\u00010$2\u0010\b\u0002\u0010&\u001a\n\u0012\u0004\u0012\u00020\'\u0018\u00010$2\n\b\u0002\u0010(\u001a\u0004\u0018\u00010)2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001\u00a2\u0006\u0003\u0010\u0097\u0001J\u0016\u0010\u0098\u0001\u001a\u00030\u0099\u00012\t\u0010\u009a\u0001\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\n\u0010\u009b\u0001\u001a\u00020\u000eH\u00d6\u0001J\n\u0010\u009c\u0001\u001a\u00020\u0003H\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010/\"\u0004\b0\u00101R \u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b2\u00103\"\u0004\b4\u00105R.\u0010\u0006\u001a\u0012\u0012\u0004\u0012\u00020\b0\u0007j\b\u0012\u0004\u0012\u00020\b`\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b6\u00107\"\u0004\b8\u00109R \u0010\n\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b:\u0010/\"\u0004\b;\u00101R \u0010\u000b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b<\u0010/\"\u0004\b=\u00101R \u0010\f\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b>\u0010/\"\u0004\b?\u00101R\"\u0010\r\u001a\u0004\u0018\u00010\u000e8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010D\u001a\u0004\b@\u0010A\"\u0004\bB\u0010CR \u0010\u000f\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bE\u0010/\"\u0004\bF\u00101R \u0010\u0010\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bG\u0010/\"\u0004\bH\u00101R \u0010\u0011\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bI\u0010/\"\u0004\bJ\u00101R \u0010\u0012\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bK\u0010/\"\u0004\bL\u00101R \u0010\u0013\u001a\u0004\u0018\u00010\u00148\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010N\"\u0004\bO\u0010PR \u0010\u0015\u001a\u0004\u0018\u00010\u00168\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bQ\u0010R\"\u0004\bS\u0010TR \u0010\u0017\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bU\u0010/\"\u0004\bV\u00101R \u0010\u0018\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bW\u0010/\"\u0004\bX\u00101R \u0010\u0019\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bY\u0010/\"\u0004\bZ\u00101R \u0010\u001a\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b[\u0010/\"\u0004\b\\\u00101R \u0010\u001b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b]\u0010/\"\u0004\b^\u00101R \u0010\u001c\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b_\u0010/\"\u0004\b`\u00101R \u0010\u001d\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\ba\u0010/\"\u0004\bb\u00101R \u0010\u001e\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bc\u0010/\"\u0004\bd\u00101R.\u0010\u001f\u001a\u0012\u0012\u0004\u0012\u00020 0\u0007j\b\u0012\u0004\u0012\u00020 `\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\be\u00107\"\u0004\bf\u00109R \u0010!\u001a\u0004\u0018\u00010\"8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bg\u0010h\"\u0004\bi\u0010jR&\u0010#\u001a\n\u0012\u0004\u0012\u00020%\u0018\u00010$8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bk\u0010l\"\u0004\bm\u0010nR&\u0010&\u001a\n\u0012\u0004\u0012\u00020\'\u0018\u00010$8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bo\u0010l\"\u0004\bp\u0010nR\"\u0010(\u001a\u0004\u0018\u00010)8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010u\u001a\u0004\bq\u0010r\"\u0004\bs\u0010tR \u0010*\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bv\u0010/\"\u0004\bw\u00101R\"\u0010+\u001a\u0004\u0018\u00010\u000e8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010D\u001a\u0004\bx\u0010A\"\u0004\by\u0010C\u00a8\u0006\u009d\u0001"}, d2 = {"Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrgData;", "", "kpp", "", "management", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Management;", "managers", "Ljava/util/ArrayList;", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Managers;", "Lkotlin/collections/ArrayList;", "predecessors", "successors", "branchType", "branchCount", "", "source", "qc", "hid", "type", "opf", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Opf;", "name", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Name;", "inn", "ogrn", "okpo", "okato", "oktmo", "okogu", "okfs", "okved", "okveds", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Okveds;", "address", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Address;", "phones", "", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Phones;", "emails", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Emails;", "ogrnDate", "", "okvedType", "employeeCount", "<init>", "(Ljava/lang/String;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Management;Ljava/util/ArrayList;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Opf;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Name;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/ArrayList;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Address;Ljava/util/List;Ljava/util/List;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/Integer;)V", "getKpp", "()Ljava/lang/String;", "setKpp", "(Ljava/lang/String;)V", "getManagement", "()Lru/sbertroika/pasiv/gate/output/dadata/model/response/Management;", "setManagement", "(Lru/sbertroika/pasiv/gate/output/dadata/model/response/Management;)V", "getManagers", "()Ljava/util/ArrayList;", "setManagers", "(Ljava/util/ArrayList;)V", "getPredecessors", "setPredecessors", "getSuccessors", "setSuccessors", "getBranchType", "setBranchType", "getBranchCount", "()Ljava/lang/Integer;", "setBranchCount", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "getSource", "setSource", "getQc", "setQc", "getHid", "setHid", "getType", "setType", "getOpf", "()Lru/sbertroika/pasiv/gate/output/dadata/model/response/Opf;", "setOpf", "(Lru/sbertroika/pasiv/gate/output/dadata/model/response/Opf;)V", "getName", "()Lru/sbertroika/pasiv/gate/output/dadata/model/response/Name;", "setName", "(Lru/sbertroika/pasiv/gate/output/dadata/model/response/Name;)V", "getInn", "setInn", "getOgrn", "setOgrn", "getOkpo", "setOkpo", "getOkato", "setOkato", "getOktmo", "setOktmo", "getOkogu", "setOkogu", "getOkfs", "setOkfs", "getOkved", "setOkved", "getOkveds", "setOkveds", "getAddress", "()Lru/sbertroika/pasiv/gate/output/dadata/model/response/Address;", "setAddress", "(Lru/sbertroika/pasiv/gate/output/dadata/model/response/Address;)V", "getPhones", "()Ljava/util/List;", "setPhones", "(Ljava/util/List;)V", "getEmails", "setEmails", "getOgrnDate", "()Ljava/lang/Long;", "setOgrnDate", "(Ljava/lang/Long;)V", "Ljava/lang/Long;", "getOkvedType", "setOkvedType", "getEmployeeCount", "setEmployeeCount", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "copy", "(Ljava/lang/String;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Management;Ljava/util/ArrayList;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Opf;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Name;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/ArrayList;Lru/sbertroika/pasiv/gate/output/dadata/model/response/Address;Ljava/util/List;Ljava/util/List;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/Integer;)Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrgData;", "equals", "", "other", "hashCode", "toString", "pasiv-gate-private"})
public final class OrgData {
    @com.google.gson.annotations.SerializedName(value = "kpp")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String kpp;
    @com.google.gson.annotations.SerializedName(value = "management")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.dadata.model.response.Management management;
    @com.google.gson.annotations.SerializedName(value = "managers")
    @org.jetbrains.annotations.NotNull()
    private java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Managers> managers;
    @com.google.gson.annotations.SerializedName(value = "predecessors")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String predecessors;
    @com.google.gson.annotations.SerializedName(value = "successors")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String successors;
    @com.google.gson.annotations.SerializedName(value = "branch_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String branchType;
    @com.google.gson.annotations.SerializedName(value = "branch_count")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer branchCount;
    @com.google.gson.annotations.SerializedName(value = "source")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String source;
    @com.google.gson.annotations.SerializedName(value = "qc")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String qc;
    @com.google.gson.annotations.SerializedName(value = "hid")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String hid;
    @com.google.gson.annotations.SerializedName(value = "type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String type;
    @com.google.gson.annotations.SerializedName(value = "opf")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.dadata.model.response.Opf opf;
    @com.google.gson.annotations.SerializedName(value = "name")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.dadata.model.response.Name name;
    @com.google.gson.annotations.SerializedName(value = "inn")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String inn;
    @com.google.gson.annotations.SerializedName(value = "ogrn")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String ogrn;
    @com.google.gson.annotations.SerializedName(value = "okpo")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okpo;
    @com.google.gson.annotations.SerializedName(value = "okato")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okato;
    @com.google.gson.annotations.SerializedName(value = "oktmo")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String oktmo;
    @com.google.gson.annotations.SerializedName(value = "okogu")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okogu;
    @com.google.gson.annotations.SerializedName(value = "okfs")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okfs;
    @com.google.gson.annotations.SerializedName(value = "okved")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okved;
    @com.google.gson.annotations.SerializedName(value = "okveds")
    @org.jetbrains.annotations.NotNull()
    private java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Okveds> okveds;
    @com.google.gson.annotations.SerializedName(value = "address")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.dadata.model.response.Address address;
    @com.google.gson.annotations.SerializedName(value = "phones")
    @org.jetbrains.annotations.Nullable()
    private java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Phones> phones;
    @com.google.gson.annotations.SerializedName(value = "emails")
    @org.jetbrains.annotations.Nullable()
    private java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Emails> emails;
    @com.google.gson.annotations.SerializedName(value = "ogrn_date")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Long ogrnDate;
    @com.google.gson.annotations.SerializedName(value = "okved_type")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String okvedType;
    @com.google.gson.annotations.SerializedName(value = "employee_count")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer employeeCount;
    
    public OrgData(@org.jetbrains.annotations.Nullable()
    java.lang.String kpp, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Management management, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Managers> managers, @org.jetbrains.annotations.Nullable()
    java.lang.String predecessors, @org.jetbrains.annotations.Nullable()
    java.lang.String successors, @org.jetbrains.annotations.Nullable()
    java.lang.String branchType, @org.jetbrains.annotations.Nullable()
    java.lang.Integer branchCount, @org.jetbrains.annotations.Nullable()
    java.lang.String source, @org.jetbrains.annotations.Nullable()
    java.lang.String qc, @org.jetbrains.annotations.Nullable()
    java.lang.String hid, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Opf opf, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Name name, @org.jetbrains.annotations.Nullable()
    java.lang.String inn, @org.jetbrains.annotations.Nullable()
    java.lang.String ogrn, @org.jetbrains.annotations.Nullable()
    java.lang.String okpo, @org.jetbrains.annotations.Nullable()
    java.lang.String okato, @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String okogu, @org.jetbrains.annotations.Nullable()
    java.lang.String okfs, @org.jetbrains.annotations.Nullable()
    java.lang.String okved, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Okveds> okveds, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Address address, @org.jetbrains.annotations.Nullable()
    java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Phones> phones, @org.jetbrains.annotations.Nullable()
    java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Emails> emails, @org.jetbrains.annotations.Nullable()
    java.lang.Long ogrnDate, @org.jetbrains.annotations.Nullable()
    java.lang.String okvedType, @org.jetbrains.annotations.Nullable()
    java.lang.Integer employeeCount) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getKpp() {
        return null;
    }
    
    public final void setKpp(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Management getManagement() {
        return null;
    }
    
    public final void setManagement(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Management p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Managers> getManagers() {
        return null;
    }
    
    public final void setManagers(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Managers> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPredecessors() {
        return null;
    }
    
    public final void setPredecessors(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSuccessors() {
        return null;
    }
    
    public final void setSuccessors(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBranchType() {
        return null;
    }
    
    public final void setBranchType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getBranchCount() {
        return null;
    }
    
    public final void setBranchCount(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSource() {
        return null;
    }
    
    public final void setSource(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getQc() {
        return null;
    }
    
    public final void setQc(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHid() {
        return null;
    }
    
    public final void setHid(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getType() {
        return null;
    }
    
    public final void setType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Opf getOpf() {
        return null;
    }
    
    public final void setOpf(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Opf p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Name getName() {
        return null;
    }
    
    public final void setName(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Name p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getInn() {
        return null;
    }
    
    public final void setInn(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOgrn() {
        return null;
    }
    
    public final void setOgrn(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkpo() {
        return null;
    }
    
    public final void setOkpo(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkato() {
        return null;
    }
    
    public final void setOkato(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOktmo() {
        return null;
    }
    
    public final void setOktmo(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkogu() {
        return null;
    }
    
    public final void setOkogu(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkfs() {
        return null;
    }
    
    public final void setOkfs(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkved() {
        return null;
    }
    
    public final void setOkved(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Okveds> getOkveds() {
        return null;
    }
    
    public final void setOkveds(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Okveds> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Address getAddress() {
        return null;
    }
    
    public final void setAddress(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Address p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Phones> getPhones() {
        return null;
    }
    
    public final void setPhones(@org.jetbrains.annotations.Nullable()
    java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Phones> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Emails> getEmails() {
        return null;
    }
    
    public final void setEmails(@org.jetbrains.annotations.Nullable()
    java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Emails> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getOgrnDate() {
        return null;
    }
    
    public final void setOgrnDate(@org.jetbrains.annotations.Nullable()
    java.lang.Long p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOkvedType() {
        return null;
    }
    
    public final void setOkvedType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getEmployeeCount() {
        return null;
    }
    
    public final void setEmployeeCount(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    public OrgData() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Opf component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Name component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Management component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Okveds> component22() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.Address component23() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Phones> component24() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Emails> component25() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component26() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component27() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component28() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Managers> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData copy(@org.jetbrains.annotations.Nullable()
    java.lang.String kpp, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Management management, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Managers> managers, @org.jetbrains.annotations.Nullable()
    java.lang.String predecessors, @org.jetbrains.annotations.Nullable()
    java.lang.String successors, @org.jetbrains.annotations.Nullable()
    java.lang.String branchType, @org.jetbrains.annotations.Nullable()
    java.lang.Integer branchCount, @org.jetbrains.annotations.Nullable()
    java.lang.String source, @org.jetbrains.annotations.Nullable()
    java.lang.String qc, @org.jetbrains.annotations.Nullable()
    java.lang.String hid, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Opf opf, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Name name, @org.jetbrains.annotations.Nullable()
    java.lang.String inn, @org.jetbrains.annotations.Nullable()
    java.lang.String ogrn, @org.jetbrains.annotations.Nullable()
    java.lang.String okpo, @org.jetbrains.annotations.Nullable()
    java.lang.String okato, @org.jetbrains.annotations.Nullable()
    java.lang.String oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String okogu, @org.jetbrains.annotations.Nullable()
    java.lang.String okfs, @org.jetbrains.annotations.Nullable()
    java.lang.String okved, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Okveds> okveds, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.dadata.model.response.Address address, @org.jetbrains.annotations.Nullable()
    java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Phones> phones, @org.jetbrains.annotations.Nullable()
    java.util.List<ru.sbertroika.pasiv.gate.output.dadata.model.response.Emails> emails, @org.jetbrains.annotations.Nullable()
    java.lang.Long ogrnDate, @org.jetbrains.annotations.Nullable()
    java.lang.String okvedType, @org.jetbrains.annotations.Nullable()
    java.lang.Integer employeeCount) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}