package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u042d\u043b\u0435\u043c\u0435\u043d\u0442 \u0438\u0441\u0442\u043e\u0440\u0438\u0438")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B9\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0001\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0001\u0010\b\u001a\u00020\u0003\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J;\u0010\u0017\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00052\b\b\u0003\u0010\u0006\u001a\u00020\u00052\b\b\u0003\u0010\u0007\u001a\u00020\u00032\b\b\u0003\u0010\b\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\fR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\f\u00a8\u0006\u001e"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/HistoryItemDto;", "", "id", "", "version", "", "createdAt", "createdBy", "data", "<init>", "(Ljava/lang/String;JJLjava/lang/String;Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "getVersion", "()J", "getCreatedAt", "getCreatedBy", "getData", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class HistoryItemDto {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    private final long version = 0L;
    private final long createdAt = 0L;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String createdBy = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String data = null;
    
    public HistoryItemDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 \u0437\u0430\u043f\u0438\u0441\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0435\u0440\u0441\u0438\u044f \u0437\u0430\u043f\u0438\u0441\u0438", example = "1")
    long version, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0440\u0435\u043c\u044f \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f (timestamp)", example = "1640995200")
    long createdAt, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044c, \u0441\u043e\u0437\u0434\u0430\u0432\u0448\u0438\u0439 \u0437\u0430\u043f\u0438\u0441\u044c", example = "user123")
    @org.jetbrains.annotations.NotNull()
    java.lang.String createdBy, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u043d\u043d\u044b\u0435 \u0437\u0430\u043f\u0438\u0441\u0438 \u0432 JSON \u0444\u043e\u0440\u043c\u0430\u0442\u0435")
    @org.jetbrains.annotations.NotNull()
    java.lang.String data) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    public final long getVersion() {
        return 0L;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCreatedBy() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long component3() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.HistoryItemDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 \u0437\u0430\u043f\u0438\u0441\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0435\u0440\u0441\u0438\u044f \u0437\u0430\u043f\u0438\u0441\u0438", example = "1")
    long version, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0440\u0435\u043c\u044f \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f (timestamp)", example = "1640995200")
    long createdAt, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044c, \u0441\u043e\u0437\u0434\u0430\u0432\u0448\u0438\u0439 \u0437\u0430\u043f\u0438\u0441\u044c", example = "user123")
    @org.jetbrains.annotations.NotNull()
    java.lang.String createdBy, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u043d\u043d\u044b\u0435 \u0437\u0430\u043f\u0438\u0441\u0438 \u0432 JSON \u0444\u043e\u0440\u043c\u0430\u0442\u0435")
    @org.jetbrains.annotations.NotNull()
    java.lang.String data) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}