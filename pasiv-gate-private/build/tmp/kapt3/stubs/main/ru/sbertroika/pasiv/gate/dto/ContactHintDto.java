package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0430 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u001b\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContactHintDto;", "", "type", "Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "value", "", "<init>", "(Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;Ljava/lang/String;)V", "getType", "()Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "getValue", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class ContactHintDto {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.ContactTypeDto type = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String value = null;
    
    public ContactHintDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactTypeDto getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValue() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactTypeDto component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactHintDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}