package ru.sbertroika.pasiv.gate.config;

@org.springframework.context.annotation.Configuration()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b\u0017\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\b\u0010\u0004\u001a\u00020\u0005H\u0017\u00a8\u0006\u0006"}, d2 = {"Lru/sbertroika/pasiv/gate/config/OpenApiConfig;", "", "<init>", "()V", "customOpenAPI", "Lio/swagger/v3/oas/models/OpenAPI;", "pasiv-gate-private"})
public class OpenApiConfig {
    
    public OpenApiConfig() {
        super();
    }
    
    @org.springframework.context.annotation.Bean()
    @org.jetbrains.annotations.NotNull()
    public io.swagger.v3.oas.models.OpenAPI customOpenAPI() {
        return null;
    }
}