package ru.sbertroika.pasiv.gate.output.dadata.model.response;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B!\u0012\u0018\b\u0002\u0010\u0002\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0019\u0010\u000b\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u0005H\u00c6\u0003J#\u0010\f\u001a\u00020\u00002\u0018\b\u0002\u0010\u0002\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u0005H\u00c6\u0001J\u0013\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001R.\u0010\u0002\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u00058\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\t\"\u0004\b\n\u0010\u0007\u00a8\u0006\u0014"}, d2 = {"Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrganizationSuggestionResponse;", "", "suggestions", "Ljava/util/ArrayList;", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Suggestions;", "Lkotlin/collections/ArrayList;", "<init>", "(Ljava/util/ArrayList;)V", "getSuggestions", "()Ljava/util/ArrayList;", "setSuggestions", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class OrganizationSuggestionResponse {
    @com.google.gson.annotations.SerializedName(value = "suggestions")
    @org.jetbrains.annotations.NotNull()
    private java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions> suggestions;
    
    public OrganizationSuggestionResponse(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions> suggestions) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions> getSuggestions() {
        return null;
    }
    
    public final void setSuggestions(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions> p0) {
    }
    
    public OrganizationSuggestionResponse() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.dadata.model.response.OrganizationSuggestionResponse copy(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions> suggestions) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}