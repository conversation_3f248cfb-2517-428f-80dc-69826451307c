package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043f\u0438\u0441\u043e\u043a \u043f\u043e\u0434\u0441\u043a\u0430\u0437\u043e\u043a \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u0017\u0012\u000e\b\u0001\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\u000f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0019\u0010\n\u001a\u00020\u00002\u000e\b\u0003\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u0012"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/OrganizationHintListDto;", "", "organizationHints", "", "Lru/sbertroika/pasiv/gate/dto/OrganizationHintDto;", "<init>", "(Ljava/util/List;)V", "getOrganizationHints", "()Ljava/util/List;", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class OrganizationHintListDto {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationHintDto> organizationHints = null;
    
    public OrganizationHintListDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0438 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationHintDto> organizationHints) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationHintDto> getOrganizationHints() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationHintDto> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationHintListDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0434\u0441\u043a\u0430\u0437\u043a\u0438 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationHintDto> organizationHints) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}