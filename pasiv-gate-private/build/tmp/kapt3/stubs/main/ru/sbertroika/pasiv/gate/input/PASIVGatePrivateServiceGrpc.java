package ru.sbertroika.pasiv.gate.input;

@org.lognet.springboot.grpc.GRpcService(port = 5000)
@org.springframework.security.access.annotation.Secured(value = {"ROLE_pasiv_console_admin"})
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0017\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0016\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0015H\u0096@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0011\u001a\u00020\u0019H\u0096@\u00a2\u0006\u0002\u0010\u001aJ\u0016\u0010\u001b\u001a\u00020\u00182\u0006\u0010\u0011\u001a\u00020\u001cH\u0096@\u00a2\u0006\u0002\u0010\u001dJ\u0016\u0010\u001e\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u001fH\u0096@\u00a2\u0006\u0002\u0010 J\u0016\u0010!\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u001fH\u0096@\u00a2\u0006\u0002\u0010 J\u0016\u0010\"\u001a\u00020#2\u0006\u0010\u0011\u001a\u00020$H\u0096@\u00a2\u0006\u0002\u0010%J\u0016\u0010&\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\'H\u0096@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\'H\u0096@\u00a2\u0006\u0002\u0010(J\u0016\u0010*\u001a\u00020+2\u0006\u0010\u0011\u001a\u00020,H\u0096@\u00a2\u0006\u0002\u0010-J\u0016\u0010.\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020/H\u0096@\u00a2\u0006\u0002\u00100J\u0016\u00101\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020/H\u0096@\u00a2\u0006\u0002\u00100J\u0016\u00102\u001a\u0002032\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u00106\u001a\u0002072\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u00108\u001a\u0002092\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u0010:\u001a\u00020;2\u0006\u0010\u0011\u001a\u00020<H\u0096@\u00a2\u0006\u0002\u0010=J\u0016\u0010>\u001a\u00020;2\u0006\u0010\u0011\u001a\u00020<H\u0096@\u00a2\u0006\u0002\u0010=J\u0016\u0010?\u001a\u00020;2\u0006\u0010\u0011\u001a\u00020<H\u0096@\u00a2\u0006\u0002\u0010=J\u0016\u0010@\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u0010A\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u0010B\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u0010C\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u0010D\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u0010E\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u000204H\u0096@\u00a2\u0006\u0002\u00105J\u0016\u0010F\u001a\u00020G2\u0006\u0010\u0011\u001a\u00020HH\u0096@\u00a2\u0006\u0002\u0010IR\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0092\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\f\u001a\n \u000e*\u0004\u0018\u00010\r0\rX\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006J"}, d2 = {"Lru/sbertroika/pasiv/gate/input/PASIVGatePrivateServiceGrpc;", "Lru/sbertroika/pasiv/gate/v1/PASIVGatePrivateServiceGrpcKt$PASIVGatePrivateServiceCoroutineImplBase;", "organizationService", "Lru/sbertroika/pasiv/gate/output/service/OrganizationService;", "addressService", "Lru/sbertroika/pasiv/gate/output/service/AddressService;", "contactService", "Lru/sbertroika/pasiv/gate/output/service/ContactService;", "daDataService", "Lru/sbertroika/pasiv/gate/output/service/DaDataService;", "<init>", "(Lru/sbertroika/pasiv/gate/output/service/OrganizationService;Lru/sbertroika/pasiv/gate/output/service/AddressService;Lru/sbertroika/pasiv/gate/output/service/ContactService;Lru/sbertroika/pasiv/gate/output/service/DaDataService;)V", "log", "Lorg/slf4j/Logger;", "kotlin.jvm.PlatformType", "createOrganization", "Lru/sbertroika/common/v1/EmptyResponse;", "request", "Lru/sbertroika/pasiv/gate/v1/OrganizationWithAddresses;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationWithAddresses;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateOrganization", "Lru/sbertroika/pasiv/gate/v1/Organization;", "(Lru/sbertroika/pasiv/gate/v1/Organization;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "organizationList", "Lru/sbertroika/pasiv/gate/v1/OrganizationListResponse;", "Lru/sbertroika/pasiv/gate/v1/OrganizationListRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "organizationListForProject", "Lru/sbertroika/pasiv/gate/v1/OrganizationListForProjectRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationListForProjectRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addOrganizationInProject", "Lru/sbertroika/pasiv/gate/v1/OrganizationInProjectRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationInProjectRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeOrganizationInProject", "addressList", "Lru/sbertroika/pasiv/gate/v1/AddressListResponse;", "Lru/sbertroika/pasiv/gate/v1/AddressListRequest;", "(Lru/sbertroika/pasiv/gate/v1/AddressListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createAddress", "Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;", "(Lru/sbertroika/pasiv/gate/v1/AddressCreateOrDelete;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAddress", "contactList", "Lru/sbertroika/pasiv/gate/v1/ContactListResponse;", "Lru/sbertroika/pasiv/gate/v1/ContactListRequest;", "(Lru/sbertroika/pasiv/gate/v1/ContactListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createContact", "Lru/sbertroika/pasiv/gate/v1/Contact;", "(Lru/sbertroika/pasiv/gate/v1/Contact;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateContact", "addressById", "Lru/sbertroika/pasiv/gate/v1/AddressResponse;", "Lru/sbertroika/pasiv/gate/v1/ByIdRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "contactById", "Lru/sbertroika/pasiv/gate/v1/ContactResponse;", "organizationById", "Lru/sbertroika/pasiv/gate/v1/OrganizationResponse;", "addressHistoryById", "Lru/sbertroika/common/v1/HistoryResponse;", "Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "contactHistoryById", "organizationHistoryById", "deleteAddress", "recoverAddress", "deleteContact", "recoverContact", "deleteOrganization", "recoverOrganization", "organizationHintByINN", "Lru/sbertroika/pasiv/gate/v1/OrganizationHintResponse;", "Lru/sbertroika/pasiv/gate/v1/OrganizationHintRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationHintRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "pasiv-gate-private"})
public class PASIVGatePrivateServiceGrpc extends ru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineImplBase {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.service.OrganizationService organizationService = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.service.AddressService addressService = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.service.ContactService contactService = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.service.DaDataService daDataService = null;
    private final org.slf4j.Logger log = null;
    
    public PASIVGatePrivateServiceGrpc(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.service.OrganizationService organizationService, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.service.AddressService addressService, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.service.ContactService contactService, @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.service.DaDataService daDataService) {
        super(null);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object createOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Organization request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object organizationList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.OrganizationListResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object organizationListForProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.OrganizationListResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object addOrganizationInProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object removeOrganizationInProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object addressList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.AddressListResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object createAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.AddressCreateOrDelete request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object contactList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ContactListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.ContactListResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object createContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Contact request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Contact request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object addressById(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.AddressResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object contactById(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.ContactResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object organizationById(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.OrganizationResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object addressHistoryById(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.HistoryResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object contactHistoryById(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.HistoryResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object organizationHistoryById(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.HistoryResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object recoverAddress(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object recoverContact(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object recoverOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.common.v1.EmptyResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object organizationHintByINN(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super ru.sbertroika.pasiv.gate.v1.OrganizationHintResponse> $completion) {
        return null;
    }
}