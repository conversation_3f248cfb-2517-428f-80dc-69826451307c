package ru.sbertroika.pasiv.gate.output.model;

@org.springframework.data.relational.core.mapping.Table(value = "address")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\bU\b\u0087\b\u0018\u00002\u00020\u0001B\u00f5\u0001\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0015\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0019\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\n\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u001c\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\u000b\u0010W\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010X\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010$J\u000b\u0010Y\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u0010\u0010\\\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010$J\u000b\u0010]\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010^\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010_\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010`\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010a\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010b\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010c\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010d\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u0010\u0010e\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u0010EJ\u0010\u0010f\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u0010EJ\u000b\u0010g\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u0010\u0010h\u001a\u0004\u0018\u00010\u0019H\u00c6\u0003\u00a2\u0006\u0002\u0010NJ\u000b\u0010i\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\t\u0010j\u001a\u00020\u001cH\u00c6\u0003J\u00fc\u0001\u0010k\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00192\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u001b\u001a\u00020\u001cH\u00c6\u0001\u00a2\u0006\u0002\u0010lJ\u0013\u0010m\u001a\u00020\u001c2\b\u0010n\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010o\u001a\u00020\u0005H\u00d6\u0001J\t\u0010p\u001a\u00020\nH\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\"\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010\'\u001a\u0004\b#\u0010$\"\u0004\b%\u0010&R \u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010)\"\u0004\b*\u0010+R \u0010\b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010 \"\u0004\b-\u0010\"R \u0010\t\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010/\"\u0004\b0\u00101R\"\u0010\u000b\u001a\u0004\u0018\u00010\u00058\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010\'\u001a\u0004\b2\u0010$\"\u0004\b3\u0010&R \u0010\f\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u0010/\"\u0004\b5\u00101R \u0010\r\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b6\u0010/\"\u0004\b7\u00101R \u0010\u000e\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b8\u0010/\"\u0004\b9\u00101R \u0010\u000f\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b:\u0010/\"\u0004\b;\u00101R \u0010\u0010\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b<\u0010/\"\u0004\b=\u00101R \u0010\u0011\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b>\u0010/\"\u0004\b?\u00101R \u0010\u0012\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b@\u0010/\"\u0004\bA\u00101R \u0010\u0013\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bB\u0010/\"\u0004\bC\u00101R\"\u0010\u0014\u001a\u0004\u0018\u00010\u00158\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010H\u001a\u0004\bD\u0010E\"\u0004\bF\u0010GR\"\u0010\u0016\u001a\u0004\u0018\u00010\u00158\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010H\u001a\u0004\bI\u0010E\"\u0004\bJ\u0010GR \u0010\u0017\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bK\u0010/\"\u0004\bL\u00101R\"\u0010\u0018\u001a\u0004\u0018\u00010\u00198\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010Q\u001a\u0004\bM\u0010N\"\u0004\bO\u0010PR \u0010\u001a\u001a\u0004\u0018\u00010\n8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bR\u0010/\"\u0004\bS\u00101R\u001e\u0010\u001b\u001a\u00020\u001c8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010T\"\u0004\bU\u0010V\u00a8\u0006q"}, d2 = {"Lru/sbertroika/pasiv/gate/output/model/Address;", "", "id", "Ljava/util/UUID;", "version", "", "versionCreatedAt", "Ljava/sql/Timestamp;", "versionCreatedBy", "name", "", "index", "country", "region", "district", "city", "street", "house", "buildingOrHousing", "officeOrRoom", "longitude", "", "latitude", "comment", "oktmo", "", "fiac", "isDeleted", "", "<init>", "(Ljava/util/UUID;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Z)V", "getId", "()Ljava/util/UUID;", "setId", "(Ljava/util/UUID;)V", "getVersion", "()Ljava/lang/Integer;", "setVersion", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "getVersionCreatedAt", "()Ljava/sql/Timestamp;", "setVersionCreatedAt", "(Ljava/sql/Timestamp;)V", "getVersionCreatedBy", "setVersionCreatedBy", "getName", "()Ljava/lang/String;", "setName", "(Ljava/lang/String;)V", "getIndex", "setIndex", "getCountry", "setCountry", "getRegion", "setRegion", "getDistrict", "setDistrict", "getCity", "setCity", "getStreet", "setStreet", "getHouse", "setHouse", "getBuildingOrHousing", "setBuildingOrHousing", "getOfficeOrRoom", "setOfficeOrRoom", "getLongitude", "()Ljava/lang/Double;", "setLongitude", "(Ljava/lang/Double;)V", "Ljava/lang/Double;", "getLatitude", "setLatitude", "getComment", "setComment", "getOktmo", "()Ljava/lang/Long;", "setOktmo", "(Ljava/lang/Long;)V", "Ljava/lang/Long;", "getFiac", "setFiac", "()Z", "setDeleted", "(Z)V", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component20", "copy", "(Ljava/util/UUID;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Z)Lru/sbertroika/pasiv/gate/output/model/Address;", "equals", "other", "hashCode", "toString", "pasiv-gate-private"})
public final class Address {
    @ru.sbertroika.history.api.HistoryId()
    @org.springframework.data.relational.core.mapping.Column(value = "a_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID id;
    @ru.sbertroika.history.api.HistoryVersion()
    @org.springframework.data.relational.core.mapping.Column(value = "a_version")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer version;
    @ru.sbertroika.history.api.HistoryVersionAt()
    @org.springframework.data.relational.core.mapping.Column(value = "a_version_created_at")
    @org.jetbrains.annotations.Nullable()
    private java.sql.Timestamp versionCreatedAt;
    @ru.sbertroika.history.api.HistoryVersionBy()
    @org.springframework.data.relational.core.mapping.Column(value = "a_version_created_by")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID versionCreatedBy;
    @org.springframework.data.relational.core.mapping.Column(value = "a_name")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String name;
    @org.springframework.data.relational.core.mapping.Column(value = "a_index")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer index;
    @org.springframework.data.relational.core.mapping.Column(value = "a_country")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String country;
    @org.springframework.data.relational.core.mapping.Column(value = "a_region")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String region;
    @org.springframework.data.relational.core.mapping.Column(value = "a_district")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String district;
    @org.springframework.data.relational.core.mapping.Column(value = "a_city")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String city;
    @org.springframework.data.relational.core.mapping.Column(value = "a_street")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String street;
    @org.springframework.data.relational.core.mapping.Column(value = "a_house")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String house;
    @org.springframework.data.relational.core.mapping.Column(value = "a_building_or_housing")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String buildingOrHousing;
    @org.springframework.data.relational.core.mapping.Column(value = "a_office_or_room")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String officeOrRoom;
    @org.springframework.data.relational.core.mapping.Column(value = "a_longitude")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Double longitude;
    @org.springframework.data.relational.core.mapping.Column(value = "a_latitude")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Double latitude;
    @org.springframework.data.relational.core.mapping.Column(value = "a_comment")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String comment;
    @org.springframework.data.relational.core.mapping.Column(value = "a_oktmo")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Long oktmo;
    @org.springframework.data.relational.core.mapping.Column(value = "a_fiac")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fiac;
    @ru.sbertroika.history.api.HistoryStatus()
    @org.springframework.data.relational.core.mapping.Column(value = "a_is_deleted")
    private boolean isDeleted;
    
    public Address(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @org.jetbrains.annotations.Nullable()
    java.lang.String country, @org.jetbrains.annotations.Nullable()
    java.lang.String region, @org.jetbrains.annotations.Nullable()
    java.lang.String district, @org.jetbrains.annotations.Nullable()
    java.lang.String city, @org.jetbrains.annotations.Nullable()
    java.lang.String street, @org.jetbrains.annotations.Nullable()
    java.lang.String house, @org.jetbrains.annotations.Nullable()
    java.lang.String buildingOrHousing, @org.jetbrains.annotations.Nullable()
    java.lang.String officeOrRoom, @org.jetbrains.annotations.Nullable()
    java.lang.Double longitude, @org.jetbrains.annotations.Nullable()
    java.lang.Double latitude, @org.jetbrains.annotations.Nullable()
    java.lang.String comment, @org.jetbrains.annotations.Nullable()
    java.lang.Long oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String fiac, boolean isDeleted) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getId() {
        return null;
    }
    
    public final void setId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getVersion() {
        return null;
    }
    
    public final void setVersion(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp getVersionCreatedAt() {
        return null;
    }
    
    public final void setVersionCreatedAt(@org.jetbrains.annotations.Nullable()
    java.sql.Timestamp p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getVersionCreatedBy() {
        return null;
    }
    
    public final void setVersionCreatedBy(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getName() {
        return null;
    }
    
    public final void setName(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getIndex() {
        return null;
    }
    
    public final void setIndex(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCountry() {
        return null;
    }
    
    public final void setCountry(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRegion() {
        return null;
    }
    
    public final void setRegion(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDistrict() {
        return null;
    }
    
    public final void setDistrict(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCity() {
        return null;
    }
    
    public final void setCity(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreet() {
        return null;
    }
    
    public final void setStreet(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHouse() {
        return null;
    }
    
    public final void setHouse(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBuildingOrHousing() {
        return null;
    }
    
    public final void setBuildingOrHousing(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOfficeOrRoom() {
        return null;
    }
    
    public final void setOfficeOrRoom(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLongitude() {
        return null;
    }
    
    public final void setLongitude(@org.jetbrains.annotations.Nullable()
    java.lang.Double p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLatitude() {
        return null;
    }
    
    public final void setLatitude(@org.jetbrains.annotations.Nullable()
    java.lang.Double p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getComment() {
        return null;
    }
    
    public final void setComment(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getOktmo() {
        return null;
    }
    
    public final void setOktmo(@org.jetbrains.annotations.Nullable()
    java.lang.Long p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFiac() {
        return null;
    }
    
    public final void setFiac(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    public final void setDeleted(boolean p0) {
    }
    
    public Address() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component2() {
        return null;
    }
    
    public final boolean component20() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.model.Address copy(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @org.jetbrains.annotations.Nullable()
    java.lang.String country, @org.jetbrains.annotations.Nullable()
    java.lang.String region, @org.jetbrains.annotations.Nullable()
    java.lang.String district, @org.jetbrains.annotations.Nullable()
    java.lang.String city, @org.jetbrains.annotations.Nullable()
    java.lang.String street, @org.jetbrains.annotations.Nullable()
    java.lang.String house, @org.jetbrains.annotations.Nullable()
    java.lang.String buildingOrHousing, @org.jetbrains.annotations.Nullable()
    java.lang.String officeOrRoom, @org.jetbrains.annotations.Nullable()
    java.lang.Double longitude, @org.jetbrains.annotations.Nullable()
    java.lang.Double latitude, @org.jetbrains.annotations.Nullable()
    java.lang.String comment, @org.jetbrains.annotations.Nullable()
    java.lang.Long oktmo, @org.jetbrains.annotations.Nullable()
    java.lang.String fiac, boolean isDeleted) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}