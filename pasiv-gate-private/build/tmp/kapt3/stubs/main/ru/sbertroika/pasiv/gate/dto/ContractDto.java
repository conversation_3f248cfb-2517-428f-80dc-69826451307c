package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u0433\u043e\u0432\u043e\u0440")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b<\b\u0087\b\u0018\u00002\u00020\u0001B\u00df\u0001\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0001\u0010\b\u001a\u00020\t\u0012\b\b\u0001\u0010\n\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0001\u0010\f\u001a\u00020\r\u0012\b\b\u0001\u0010\u000e\u001a\u00020\r\u0012\b\b\u0001\u0010\u000f\u001a\u00020\r\u0012\b\b\u0001\u0010\u0010\u001a\u00020\u0011\u0012\n\b\u0003\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u0012\n\b\u0003\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0017\u001a\u0004\u0018\u00010\u0018\u0012\n\b\u0003\u0010\u0019\u001a\u0004\u0018\u00010\u0015\u0012\b\b\u0003\u0010\u001a\u001a\u00020\u001b\u0012\b\b\u0001\u0010\u001c\u001a\u00020\r\u0012\n\b\u0003\u0010\u001d\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0004\b\u001e\u0010\u001fJ\u000b\u0010=\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010>\u001a\u00020\u0003H\u00c6\u0003J\t\u0010?\u001a\u00020\u0003H\u00c6\u0003J\t\u0010@\u001a\u00020\u0007H\u00c6\u0003J\t\u0010A\u001a\u00020\tH\u00c6\u0003J\t\u0010B\u001a\u00020\u0003H\u00c6\u0003J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\t\u0010D\u001a\u00020\rH\u00c6\u0003J\t\u0010E\u001a\u00020\rH\u00c6\u0003J\t\u0010F\u001a\u00020\rH\u00c6\u0003J\t\u0010G\u001a\u00020\u0011H\u00c6\u0003J\u000b\u0010H\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010I\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010J\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u00103J\u000b\u0010K\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010L\u001a\u0004\u0018\u00010\u0018H\u00c6\u0003\u00a2\u0006\u0002\u00107J\u0010\u0010M\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u00103J\t\u0010N\u001a\u00020\u001bH\u00c6\u0003J\t\u0010O\u001a\u00020\rH\u00c6\u0003J\u000b\u0010P\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u00e6\u0001\u0010Q\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00032\b\b\u0003\u0010\u0005\u001a\u00020\u00032\b\b\u0003\u0010\u0006\u001a\u00020\u00072\b\b\u0003\u0010\b\u001a\u00020\t2\b\b\u0003\u0010\n\u001a\u00020\u00032\b\b\u0003\u0010\u000b\u001a\u00020\u00032\b\b\u0003\u0010\f\u001a\u00020\r2\b\b\u0003\u0010\u000e\u001a\u00020\r2\b\b\u0003\u0010\u000f\u001a\u00020\r2\b\b\u0003\u0010\u0010\u001a\u00020\u00112\n\b\u0003\u0010\u0012\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0013\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0014\u001a\u0004\u0018\u00010\u00152\n\b\u0003\u0010\u0016\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0017\u001a\u0004\u0018\u00010\u00182\n\b\u0003\u0010\u0019\u001a\u0004\u0018\u00010\u00152\b\b\u0003\u0010\u001a\u001a\u00020\u001b2\b\b\u0003\u0010\u001c\u001a\u00020\r2\n\b\u0003\u0010\u001d\u001a\u0004\u0018\u00010\rH\u00c6\u0001\u00a2\u0006\u0002\u0010RJ\u0013\u0010S\u001a\u00020\u001b2\b\u0010T\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010U\u001a\u00020\u0018H\u00d6\u0001J\t\u0010V\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010!R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010!R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010!R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010!R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\u000e\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010+R\u0011\u0010\u000f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010+R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010!R\u0013\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010!R\u0015\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\n\n\u0002\u00104\u001a\u0004\b2\u00103R\u0013\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010!R\u0015\u0010\u0017\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\n\n\u0002\u00108\u001a\u0004\b6\u00107R\u0015\u0010\u0019\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\n\n\u0002\u00104\u001a\u0004\b9\u00103R\u0011\u0010\u001a\u001a\u00020\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010:R\u0011\u0010\u001c\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010+R\u0013\u0010\u001d\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010+\u00a8\u0006W"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContractDto;", "", "id", "", "projectCode", "projectName", "projectType", "Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;", "contractType", "Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;", "contractName", "contractNumber", "signatureDate", "Ljava/time/LocalDateTime;", "conclusionDate", "completionDate", "status", "Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;", "externalId1C", "description", "totalAmount", "", "currency", "paymentTerms", "", "vatRate", "isDeleted", "", "createdDate", "lastSyncDate", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;Ljava/lang/String;Ljava/lang/String;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Double;ZLjava/time/LocalDateTime;Ljava/time/LocalDateTime;)V", "getId", "()Ljava/lang/String;", "getProjectCode", "getProjectName", "getProjectType", "()Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;", "getContractType", "()Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;", "getContractName", "getContractNumber", "getSignatureDate", "()Ljava/time/LocalDateTime;", "getConclusionDate", "getCompletionDate", "getStatus", "()Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;", "getExternalId1C", "getDescription", "getTotalAmount", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getCurrency", "getPaymentTerms", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getVatRate", "()Z", "getCreatedDate", "getLastSyncDate", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component20", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;Ljava/lang/String;Ljava/lang/String;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Double;ZLjava/time/LocalDateTime;Ljava/time/LocalDateTime;)Lru/sbertroika/pasiv/gate/dto/ContractDto;", "equals", "other", "hashCode", "toString", "pasiv-gate-private"})
public final class ContractDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String id = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String projectCode = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String projectName = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.ProjectTypeDto projectType = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.ContractTypeDto contractType = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String contractName = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String contractNumber = null;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime signatureDate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime conclusionDate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime completionDate = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.ContractStatusDto status = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String externalId1C = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String description = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double totalAmount = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String currency = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer paymentTerms = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double vatRate = null;
    private final boolean isDeleted = false;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime createdDate = null;
    @org.jetbrains.annotations.Nullable()
    private final java.time.LocalDateTime lastSyncDate = null;
    
    public ContractDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u0434 \u043f\u0440\u043e\u0435\u043a\u0442\u0430", example = "MSK_METRO")
    @org.jetbrains.annotations.NotNull()
    java.lang.String projectCode, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u043f\u0440\u043e\u0435\u043a\u0442\u0430", example = "\u041c\u043e\u0441\u043a\u043e\u0432\u0441\u043a\u0438\u0439 \u043c\u0435\u0442\u0440\u043e\u043f\u043e\u043b\u0438\u0442\u0435\u043d")
    @org.jetbrains.annotations.NotNull()
    java.lang.String projectName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043f\u0440\u043e\u0435\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ProjectTypeDto projectType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContractTypeDto contractType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u043e\u0433\u043e\u0432\u043e\u0440 \u043d\u0430 \u043e\u043a\u0430\u0437\u0430\u043d\u0438\u0435 \u0442\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u044b\u0445 \u0443\u0441\u043b\u0443\u0433")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u043e\u043c\u0435\u0440 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u041e\u0413-2024-001")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractNumber, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u043f\u043e\u0434\u043f\u0438\u0441\u0430\u043d\u0438\u044f", example = "2024-01-15T10:30:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime signatureDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0437\u0430\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u044f", example = "2024-01-10T09:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime conclusionDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0438\u044f", example = "2024-12-31T23:59:59")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime completionDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u0442\u0443\u0441 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContractStatusDto status, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u043d\u0435\u0448\u043d\u0438\u0439 ID \u0432 1\u0421", example = "1C_CONTRACT_123")
    @org.jetbrains.annotations.Nullable()
    java.lang.String externalId1C, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u043e\u0433\u043e\u0432\u043e\u0440 \u043d\u0430 \u043e\u043a\u0430\u0437\u0430\u043d\u0438\u0435 \u0443\u0441\u043b\u0443\u0433 \u043f\u043e \u043f\u0435\u0440\u0435\u0432\u043e\u0437\u043a\u0435 \u043f\u0430\u0441\u0441\u0430\u0436\u0438\u0440\u043e\u0432")
    @org.jetbrains.annotations.Nullable()
    java.lang.String description, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0431\u0449\u0430\u044f \u0441\u0443\u043c\u043c\u0430 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "1000000.50")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double totalAmount, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0430\u043b\u044e\u0442\u0430", example = "RUB")
    @org.jetbrains.annotations.Nullable()
    java.lang.String currency, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043b\u043e\u0432\u0438\u044f \u043e\u043f\u043b\u0430\u0442\u044b (\u0434\u043d\u0438)", example = "30")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer paymentTerms, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u0432\u043a\u0430 \u041d\u0414\u0421 (%)", example = "20.0")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double vatRate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u0434\u043e\u0433\u043e\u0432\u043e\u0440")
    boolean isDeleted, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime createdDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u0441\u0438\u043d\u0445\u0440\u043e\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime lastSyncDate) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getProjectCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getProjectName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ProjectTypeDto getProjectType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractTypeDto getContractType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getContractName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getContractNumber() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getSignatureDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getConclusionDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getCompletionDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractStatusDto getStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getExternalId1C() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getTotalAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrency() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getPaymentTerms() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getVatRate() {
        return null;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getCreatedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime getLastSyncDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractStatusDto component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component17() {
        return null;
    }
    
    public final boolean component18() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime component20() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ProjectTypeDto component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractTypeDto component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u0434 \u043f\u0440\u043e\u0435\u043a\u0442\u0430", example = "MSK_METRO")
    @org.jetbrains.annotations.NotNull()
    java.lang.String projectCode, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u043f\u0440\u043e\u0435\u043a\u0442\u0430", example = "\u041c\u043e\u0441\u043a\u043e\u0432\u0441\u043a\u0438\u0439 \u043c\u0435\u0442\u0440\u043e\u043f\u043e\u043b\u0438\u0442\u0435\u043d")
    @org.jetbrains.annotations.NotNull()
    java.lang.String projectName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043f\u0440\u043e\u0435\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ProjectTypeDto projectType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContractTypeDto contractType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u043e\u0433\u043e\u0432\u043e\u0440 \u043d\u0430 \u043e\u043a\u0430\u0437\u0430\u043d\u0438\u0435 \u0442\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u044b\u0445 \u0443\u0441\u043b\u0443\u0433")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u043e\u043c\u0435\u0440 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u041e\u0413-2024-001")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractNumber, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u043f\u043e\u0434\u043f\u0438\u0441\u0430\u043d\u0438\u044f", example = "2024-01-15T10:30:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime signatureDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0437\u0430\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u044f", example = "2024-01-10T09:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime conclusionDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0438\u044f", example = "2024-12-31T23:59:59")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime completionDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u0442\u0443\u0441 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContractStatusDto status, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u043d\u0435\u0448\u043d\u0438\u0439 ID \u0432 1\u0421", example = "1C_CONTRACT_123")
    @org.jetbrains.annotations.Nullable()
    java.lang.String externalId1C, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u043e\u0433\u043e\u0432\u043e\u0440 \u043d\u0430 \u043e\u043a\u0430\u0437\u0430\u043d\u0438\u0435 \u0443\u0441\u043b\u0443\u0433 \u043f\u043e \u043f\u0435\u0440\u0435\u0432\u043e\u0437\u043a\u0435 \u043f\u0430\u0441\u0441\u0430\u0436\u0438\u0440\u043e\u0432")
    @org.jetbrains.annotations.Nullable()
    java.lang.String description, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0431\u0449\u0430\u044f \u0441\u0443\u043c\u043c\u0430 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "1000000.50")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double totalAmount, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u0430\u043b\u044e\u0442\u0430", example = "RUB")
    @org.jetbrains.annotations.Nullable()
    java.lang.String currency, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043b\u043e\u0432\u0438\u044f \u043e\u043f\u043b\u0430\u0442\u044b (\u0434\u043d\u0438)", example = "30")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer paymentTerms, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u0432\u043a\u0430 \u041d\u0414\u0421 (%)", example = "20.0")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double vatRate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u0434\u043e\u0433\u043e\u0432\u043e\u0440")
    boolean isDeleted, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime createdDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u0441\u0438\u043d\u0445\u0440\u043e\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime lastSyncDate) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}