package ru.sbertroika.pasiv.gate.output.service;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bf\u0018\u00002\u00020\u0001J.\u0010\u0002\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u000bJ.\u0010\f\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u000fJ&\u0010\u0010\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00110\u00032\u0006\u0010\u0012\u001a\u00020\u0013H\u00a6@\u00a2\u0006\u0002\u0010\u0014J&\u0010\u0015\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00110\u00032\u0006\u0010\u0012\u001a\u00020\u0016H\u00a6@\u00a2\u0006\u0002\u0010\u0017J.\u0010\u0018\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0012\u001a\u00020\u00192\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u001aJ.\u0010\u001b\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0012\u001a\u00020\u00192\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u001aJ&\u0010\u001c\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u000e0\u00032\u0006\u0010\u0012\u001a\u00020\u001dH\u00a6@\u00a2\u0006\u0002\u0010\u001eJ&\u0010\u001f\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020 0\u00032\u0006\u0010\u0012\u001a\u00020!H\u00a6@\u00a2\u0006\u0002\u0010\"J.\u0010#\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0012\u001a\u00020\u001d2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010$J.\u0010%\u001a\u0012\u0012\b\u0012\u00060\u0004j\u0002`\u0005\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0012\u001a\u00020\u001d2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010$\u00a8\u0006&"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/OrganizationService;", "", "createOrganization", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "", "organizationWithAddresses", "Lru/sbertroika/pasiv/gate/v1/OrganizationWithAddresses;", "userId", "", "(Lru/sbertroika/pasiv/gate/v1/OrganizationWithAddresses;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateOrganization", "organization", "Lru/sbertroika/pasiv/gate/v1/Organization;", "(Lru/sbertroika/pasiv/gate/v1/Organization;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "organizationList", "Lru/sbertroika/pasiv/gate/v1/OrganizationResult;", "request", "Lru/sbertroika/pasiv/gate/v1/OrganizationListRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationListRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "organizationListForProject", "Lru/sbertroika/pasiv/gate/v1/OrganizationListForProjectRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationListForProjectRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addOrganizationInProject", "Lru/sbertroika/pasiv/gate/v1/OrganizationInProjectRequest;", "(Lru/sbertroika/pasiv/gate/v1/OrganizationInProjectRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeOrganizationInProject", "getOrganization", "Lru/sbertroika/pasiv/gate/v1/ByIdRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHistory", "Lru/sbertroika/common/v1/HistoryResult;", "Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;", "(Lru/sbertroika/pasiv/gate/v1/ByIdWithPaginationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOrganization", "(Lru/sbertroika/pasiv/gate/v1/ByIdRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recoverOrganization", "pasiv-gate-private"})
public abstract interface OrganizationService {
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationWithAddresses organizationWithAddresses, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.Organization organization, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object organizationList(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationListRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.OrganizationResult>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object organizationListForProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationListForProjectRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.OrganizationResult>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addOrganizationInProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removeOrganizationInProject(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationInProjectRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.pasiv.gate.v1.Organization>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getHistory(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdWithPaginationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, ru.sbertroika.common.v1.HistoryResult>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object recoverOrganization(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.ByIdRequest request, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super arrow.core.Either<? extends java.lang.Error, kotlin.Unit>> $completion);
}