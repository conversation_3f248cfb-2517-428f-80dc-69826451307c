package ru.sbertroika.pasiv.gate.output.dadata;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\'\u00a8\u0006\u0007"}, d2 = {"Lru/sbertroika/pasiv/gate/output/dadata/DaDataClient;", "", "organizationByInn", "Lretrofit2/Call;", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrganizationSuggestionResponse;", "request", "Lru/sbertroika/pasiv/gate/output/dadata/model/OrganizationSuggestionRequest;", "pasiv-gate-private"})
public abstract interface DaDataClient {
    
    @retrofit2.http.POST(value = "suggestions/api/4_1/rs/findById/party")
    @org.jetbrains.annotations.NotNull()
    public abstract retrofit2.Call<ru.sbertroika.pasiv.gate.output.dadata.model.response.OrganizationSuggestionResponse> organizationByInn(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.dadata.model.OrganizationSuggestionRequest request);
}