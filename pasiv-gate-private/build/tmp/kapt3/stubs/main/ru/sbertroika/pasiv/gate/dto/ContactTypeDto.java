package ru.sbertroika.pasiv.gate.dto;

/**
 * DTO для работы с контактами
 */
@io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "", "<init>", "(Ljava/lang/String;I)V", "PHONE", "EMAIL", "pasiv-gate-private"})
public enum ContactTypeDto {
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0435\u043b\u0435\u0444\u043e\u043d")
    /*public static final*/ PHONE /* = new PHONE() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "Email")
    /*public static final*/ EMAIL /* = new EMAIL() */;
    
    ContactTypeDto() {
    }
    
    /**
     * DTO для работы с контактами
     */
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.dto.ContactTypeDto> getEntries() {
        return null;
    }
}