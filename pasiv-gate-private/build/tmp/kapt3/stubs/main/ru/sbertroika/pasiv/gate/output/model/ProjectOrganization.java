package ru.sbertroika.pasiv.gate.output.model;

@org.springframework.data.relational.core.mapping.Table(value = "project_organization")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b)\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001Bs\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u000b\u0010,\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010-\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0016J\u000b\u0010.\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010/\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00100\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00101\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u00103\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u00104\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003Jz\u00105\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001\u00a2\u0006\u0002\u00106J\u0013\u00107\u001a\u0002082\b\u00109\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010:\u001a\u00020\u0005H\u00d6\u0001J\t\u0010;\u001a\u00020<H\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R\"\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010\u0019\u001a\u0004\b\u0015\u0010\u0016\"\u0004\b\u0017\u0010\u0018R \u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR \u0010\b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u0012\"\u0004\b\u001f\u0010\u0014R \u0010\t\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010\u0012\"\u0004\b!\u0010\u0014R \u0010\n\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010\u0012\"\u0004\b#\u0010\u0014R \u0010\u000b\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010\u001b\"\u0004\b%\u0010\u001dR \u0010\f\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\u001b\"\u0004\b\'\u0010\u001dR \u0010\r\u001a\u0004\u0018\u00010\u000e8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010)\"\u0004\b*\u0010+\u00a8\u0006="}, d2 = {"Lru/sbertroika/pasiv/gate/output/model/ProjectOrganization;", "", "id", "Ljava/util/UUID;", "version", "", "versionCreatedAt", "Ljava/sql/Timestamp;", "versionCreatedBy", "projectId", "organizationId", "activeFrom", "activeTill", "status", "Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatus;", "<init>", "(Ljava/util/UUID;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/util/UUID;Ljava/util/UUID;Ljava/sql/Timestamp;Ljava/sql/Timestamp;Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatus;)V", "getId", "()Ljava/util/UUID;", "setId", "(Ljava/util/UUID;)V", "getVersion", "()Ljava/lang/Integer;", "setVersion", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "getVersionCreatedAt", "()Ljava/sql/Timestamp;", "setVersionCreatedAt", "(Ljava/sql/Timestamp;)V", "getVersionCreatedBy", "setVersionCreatedBy", "getProjectId", "setProjectId", "getOrganizationId", "setOrganizationId", "getActiveFrom", "setActiveFrom", "getActiveTill", "setActiveTill", "getStatus", "()Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatus;", "setStatus", "(Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatus;)V", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/util/UUID;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/util/UUID;Ljava/util/UUID;Ljava/sql/Timestamp;Ljava/sql/Timestamp;Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatus;)Lru/sbertroika/pasiv/gate/output/model/ProjectOrganization;", "equals", "", "other", "hashCode", "toString", "", "pasiv-gate-private"})
public final class ProjectOrganization {
    @org.springframework.data.relational.core.mapping.Column(value = "po_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID id;
    @org.springframework.data.relational.core.mapping.Column(value = "po_version")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer version;
    @org.springframework.data.relational.core.mapping.Column(value = "po_version_created_at")
    @org.jetbrains.annotations.Nullable()
    private java.sql.Timestamp versionCreatedAt;
    @org.springframework.data.relational.core.mapping.Column(value = "po_version_created_by")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID versionCreatedBy;
    @org.springframework.data.relational.core.mapping.Column(value = "po_project_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID projectId;
    @org.springframework.data.relational.core.mapping.Column(value = "po_organization_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID organizationId;
    @org.springframework.data.relational.core.mapping.Column(value = "po_active_from")
    @org.jetbrains.annotations.Nullable()
    private java.sql.Timestamp activeFrom;
    @org.springframework.data.relational.core.mapping.Column(value = "po_active_till")
    @org.jetbrains.annotations.Nullable()
    private java.sql.Timestamp activeTill;
    @org.springframework.data.relational.core.mapping.Column(value = "po_status")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus status;
    
    public ProjectOrganization(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.util.UUID projectId, @org.jetbrains.annotations.Nullable()
    java.util.UUID organizationId, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp activeFrom, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp activeTill, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus status) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getId() {
        return null;
    }
    
    public final void setId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getVersion() {
        return null;
    }
    
    public final void setVersion(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp getVersionCreatedAt() {
        return null;
    }
    
    public final void setVersionCreatedAt(@org.jetbrains.annotations.Nullable()
    java.sql.Timestamp p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getVersionCreatedBy() {
        return null;
    }
    
    public final void setVersionCreatedBy(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getProjectId() {
        return null;
    }
    
    public final void setProjectId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getOrganizationId() {
        return null;
    }
    
    public final void setOrganizationId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp getActiveFrom() {
        return null;
    }
    
    public final void setActiveFrom(@org.jetbrains.annotations.Nullable()
    java.sql.Timestamp p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp getActiveTill() {
        return null;
    }
    
    public final void setActiveTill(@org.jetbrains.annotations.Nullable()
    java.sql.Timestamp p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus getStatus() {
        return null;
    }
    
    public final void setStatus(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus p0) {
    }
    
    public ProjectOrganization() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.model.ProjectOrganization copy(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.util.UUID projectId, @org.jetbrains.annotations.Nullable()
    java.util.UUID organizationId, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp activeFrom, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp activeTill, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus status) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}