package ru.sbertroika.pasiv.gate.output.service;

@org.springframework.stereotype.Service()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0017\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J \u0010\u0006\u001a\u0012\u0012\b\u0012\u00060\bj\u0002`\t\u0012\u0004\u0012\u00020\n0\u00072\u0006\u0010\u000b\u001a\u00020\fH\u0016J \u0010\r\u001a\u00020\n2\u0016\u0010\u000e\u001a\u0012\u0012\u0004\u0012\u00020\u00100\u000fj\b\u0012\u0004\u0012\u00020\u0010`\u0011H\u0012J\u0016\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\u0006\u0010\u0015\u001a\u00020\u0016H\u0012J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0016H\u0012J\u0012\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0018H\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lru/sbertroika/pasiv/gate/output/service/DaDataServiceImpl;", "Lru/sbertroika/pasiv/gate/output/service/DaDataService;", "client", "Lru/sbertroika/pasiv/gate/output/dadata/DaDataClient;", "<init>", "(Lru/sbertroika/pasiv/gate/output/dadata/DaDataClient;)V", "getOrganizationHint", "Larrow/core/Either;", "Ljava/lang/Error;", "Lkotlin/Error;", "Lru/sbertroika/pasiv/gate/v1/OrganizationHintList;", "request", "Lru/sbertroika/pasiv/gate/v1/OrganizationHintRequest;", "map", "list", "Ljava/util/ArrayList;", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/Suggestions;", "Lkotlin/collections/ArrayList;", "getContactsHints", "", "Lru/sbertroika/pasiv/gate/v1/ContactHint;", "it", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/OrgData;", "getAddress", "Lru/sbertroika/pasiv/gate/output/dadata/model/response/AddressData;", "org", "getOfficeOrRoom", "", "data", "pasiv-gate-private"})
public class DaDataServiceImpl implements ru.sbertroika.pasiv.gate.output.service.DaDataService {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.output.dadata.DaDataClient client = null;
    
    public DaDataServiceImpl(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.output.dadata.DaDataClient client) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public arrow.core.Either<java.lang.Error, ru.sbertroika.pasiv.gate.v1.OrganizationHintList> getOrganizationHint(@org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.v1.OrganizationHintRequest request) {
        return null;
    }
    
    private ru.sbertroika.pasiv.gate.v1.OrganizationHintList map(java.util.ArrayList<ru.sbertroika.pasiv.gate.output.dadata.model.response.Suggestions> list) {
        return null;
    }
    
    private java.util.List<ru.sbertroika.pasiv.gate.v1.ContactHint> getContactsHints(ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData it) {
        return null;
    }
    
    private ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData getAddress(ru.sbertroika.pasiv.gate.output.dadata.model.response.OrgData org) {
        return null;
    }
    
    private java.lang.String getOfficeOrRoom(ru.sbertroika.pasiv.gate.output.dadata.model.response.AddressData data) {
        return null;
    }
}