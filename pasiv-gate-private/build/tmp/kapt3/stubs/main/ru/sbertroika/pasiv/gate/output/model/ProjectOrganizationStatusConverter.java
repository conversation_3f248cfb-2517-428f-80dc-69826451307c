package ru.sbertroika.pasiv.gate.output.model;

@org.springframework.data.convert.WritingConverter()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004\u00a8\u0006\u0005"}, d2 = {"Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatusConverter;", "Lorg/springframework/data/r2dbc/convert/EnumWriteSupport;", "Lru/sbertroika/pasiv/gate/output/model/ProjectOrganizationStatus;", "<init>", "()V", "pasiv-gate-private"})
public final class ProjectOrganizationStatusConverter extends org.springframework.data.r2dbc.convert.EnumWriteSupport<ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatus> {
    
    public ProjectOrganizationStatusConverter() {
        super();
    }
}