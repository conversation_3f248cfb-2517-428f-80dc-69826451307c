package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0440\u043e\u0441 \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u043e\u0432")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u001f\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J!\u0010\u000e\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContactListRequestDto;", "", "pagination", "Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;", "filters", "Lru/sbertroika/pasiv/gate/dto/ContactFilterDto;", "<init>", "(Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;Lru/sbertroika/pasiv/gate/dto/ContactFilterDto;)V", "getPagination", "()Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;", "getFilters", "()Lru/sbertroika/pasiv/gate/dto/ContactFilterDto;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class ContactListRequestDto {
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.ContactFilterDto filters = null;
    
    public ContactListRequestDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0438\u043b\u044c\u0442\u0440\u044b \u043f\u043e\u0438\u0441\u043a\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ContactFilterDto filters) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationRequestDto getPagination() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ContactFilterDto getFilters() {
        return null;
    }
    
    public ContactListRequestDto() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationRequestDto component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ContactFilterDto component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactListRequestDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationRequestDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0438\u043b\u044c\u0442\u0440\u044b \u043f\u043e\u0438\u0441\u043a\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ContactFilterDto filters) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}