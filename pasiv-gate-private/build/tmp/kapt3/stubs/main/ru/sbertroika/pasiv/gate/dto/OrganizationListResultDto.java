package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u0441\u043f\u0438\u0441\u043a\u0430 \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B/\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u000e\b\u0001\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0004\b\t\u0010\nJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J1\u0010\u0014\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u000e\b\u0003\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001c"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/OrganizationListResultDto;", "", "pagination", "Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;", "filter", "Lru/sbertroika/pasiv/gate/dto/OrganizationFilterDto;", "organizations", "", "Lru/sbertroika/pasiv/gate/dto/OrganizationDto;", "<init>", "(Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;Lru/sbertroika/pasiv/gate/dto/OrganizationFilterDto;Ljava/util/List;)V", "getPagination", "()Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;", "getFilter", "()Lru/sbertroika/pasiv/gate/dto/OrganizationFilterDto;", "getOrganizations", "()Ljava/util/List;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class OrganizationListResultDto {
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.OrganizationFilterDto filter = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationDto> organizations = null;
    
    public OrganizationListResultDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0440\u0438\u043c\u0435\u043d\u0435\u043d\u043d\u044b\u0435 \u0444\u0438\u043b\u044c\u0442\u0440\u044b")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OrganizationFilterDto filter, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043f\u0438\u0441\u043e\u043a \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationDto> organizations) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto getPagination() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationFilterDto getFilter() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationDto> getOrganizations() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationFilterDto component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationDto> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.OrganizationListResultDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0440\u0438\u043c\u0435\u043d\u0435\u043d\u043d\u044b\u0435 \u0444\u0438\u043b\u044c\u0442\u0440\u044b")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OrganizationFilterDto filter, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043f\u0438\u0441\u043e\u043a \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0439")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.OrganizationDto> organizations) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}