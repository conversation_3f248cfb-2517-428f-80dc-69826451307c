package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u0443\u0441\u0442\u043e\u0439 \u043e\u0442\u0432\u0435\u0442")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\f\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u001d\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0003\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001f\u0010\u000e\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00052\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/EmptyResponseDto;", "", "error", "Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;", "success", "", "<init>", "(Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;Z)V", "getError", "()Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;", "getSuccess", "()Z", "component1", "component2", "copy", "equals", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class EmptyResponseDto {
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.OperationErrorDto error = null;
    private final boolean success = false;
    
    public EmptyResponseDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0448\u0438\u0431\u043a\u0430 (\u0435\u0441\u043b\u0438 \u0435\u0441\u0442\u044c)")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OperationErrorDto error, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043f\u0435\u0448\u043d\u043e\u0441\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438")
    boolean success) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OperationErrorDto getError() {
        return null;
    }
    
    public final boolean getSuccess() {
        return false;
    }
    
    public EmptyResponseDto() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OperationErrorDto component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.EmptyResponseDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0448\u0438\u0431\u043a\u0430 (\u0435\u0441\u043b\u0438 \u0435\u0441\u0442\u044c)")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OperationErrorDto error, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043f\u0435\u0448\u043d\u043e\u0441\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438")
    boolean success) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}