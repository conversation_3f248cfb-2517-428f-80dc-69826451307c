package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435 \u0438\u043b\u0438 \u0443\u0434\u0430\u043b\u0435\u043d\u0438\u0435 \u0430\u0434\u0440\u0435\u0441\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0013\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00052\b\b\u0003\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001a"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/AddressCreateOrDeleteDto;", "", "address", "Lru/sbertroika/pasiv/gate/dto/AddressDto;", "type", "Lru/sbertroika/pasiv/gate/dto/AddressTypeDto;", "organizationId", "", "<init>", "(Lru/sbertroika/pasiv/gate/dto/AddressDto;Lru/sbertroika/pasiv/gate/dto/AddressTypeDto;Ljava/lang/String;)V", "getAddress", "()Lru/sbertroika/pasiv/gate/dto/AddressDto;", "getType", "()Lru/sbertroika/pasiv/gate/dto/AddressTypeDto;", "getOrganizationId", "()Ljava/lang/String;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class AddressCreateOrDeleteDto {
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.AddressDto address = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.AddressTypeDto type = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String organizationId = null;
    
    public AddressCreateOrDeleteDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressDto address, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0430\u0434\u0440\u0435\u0441\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto getAddress() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressTypeDto getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrganizationId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressTypeDto component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressCreateOrDeleteDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u0434\u0440\u0435\u0441")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressDto address, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0430\u0434\u0440\u0435\u0441\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.AddressTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}