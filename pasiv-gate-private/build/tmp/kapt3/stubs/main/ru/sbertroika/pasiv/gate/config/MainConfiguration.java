package ru.sbertroika.pasiv.gate.config;

@org.springframework.context.annotation.Configuration()
@org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories(basePackages = {"ru.sbertroika.pasiv.gate.output.repository"})
@org.springframework.transaction.annotation.EnableTransactionManagement()
@org.springframework.data.r2dbc.config.EnableR2dbcAuditing()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0017\u0018\u00002\u00020\u0001B\u0011\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\b\u0010\u0006\u001a\u00020\u0007H\u0017J\u0012\u0010\b\u001a\u0004\u0018\u00010\t2\u0006\u0010\n\u001a\u00020\u000bH\u0017R\u000e\u0010\u0002\u001a\u00020\u0003X\u0092\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lru/sbertroika/pasiv/gate/config/MainConfiguration;", "", "url", "", "<init>", "(Ljava/lang/String;)V", "connectionFactoryOptionsBuilderCustomizer", "Lorg/springframework/boot/autoconfigure/r2dbc/ConnectionFactoryOptionsBuilderCustomizer;", "r2dbcCustomConversions", "Lorg/springframework/data/r2dbc/convert/R2dbcCustomConversions;", "databaseClient", "Lorg/springframework/r2dbc/core/DatabaseClient;", "pasiv-gate-private"})
public class MainConfiguration {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String url = null;
    
    public MainConfiguration(@org.springframework.beans.factory.annotation.Value(value = "${spring.r2dbc.url}")
    @org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        super();
    }
    
    @org.springframework.context.annotation.Bean()
    @org.jetbrains.annotations.NotNull()
    public org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryOptionsBuilderCustomizer connectionFactoryOptionsBuilderCustomizer() {
        return null;
    }
    
    @org.springframework.context.annotation.Bean()
    @org.jetbrains.annotations.Nullable()
    public org.springframework.data.r2dbc.convert.R2dbcCustomConversions r2dbcCustomConversions(@org.jetbrains.annotations.NotNull()
    org.springframework.r2dbc.core.DatabaseClient databaseClient) {
        return null;
    }
}