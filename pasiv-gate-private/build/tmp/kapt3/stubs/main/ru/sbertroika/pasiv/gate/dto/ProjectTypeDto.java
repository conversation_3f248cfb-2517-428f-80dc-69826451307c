package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043f\u0440\u043e\u0435\u043a\u0442\u0430")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;", "", "<init>", "(Ljava/lang/String;I)V", "TRANSPORT_SYSTEM", "METRO_SYSTEM", "BUS_SYSTEM", "TAXI_SYSTEM", "pasiv-gate-private"})
public enum ProjectTypeDto {
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442\u043d\u0430\u044f \u0441\u0438\u0441\u0442\u0435\u043c\u0430")
    /*public static final*/ TRANSPORT_SYSTEM /* = new TRANSPORT_SYSTEM() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0438\u0441\u0442\u0435\u043c\u0430 \u043c\u0435\u0442\u0440\u043e")
    /*public static final*/ METRO_SYSTEM /* = new METRO_SYSTEM() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u0432\u0442\u043e\u0431\u0443\u0441\u043d\u0430\u044f \u0441\u0438\u0441\u0442\u0435\u043c\u0430")
    /*public static final*/ BUS_SYSTEM /* = new BUS_SYSTEM() */,
    @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0438\u0441\u0442\u0435\u043c\u0430 \u0442\u0430\u043a\u0441\u0438")
    /*public static final*/ TAXI_SYSTEM /* = new TAXI_SYSTEM() */;
    
    ProjectTypeDto() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<ru.sbertroika.pasiv.gate.dto.ProjectTypeDto> getEntries() {
        return null;
    }
}