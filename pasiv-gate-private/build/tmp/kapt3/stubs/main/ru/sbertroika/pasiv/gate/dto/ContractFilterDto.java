package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0438\u043b\u044c\u0442\u0440 \u0434\u043b\u044f \u043f\u043e\u0438\u0441\u043a\u0430 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u043e\u0432")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001Bg\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0003\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0003\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0003\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003Jn\u0010&\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0003\u0010\n\u001a\u0004\u0018\u00010\u000b2\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0003\u0010\u000e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\'J\u0013\u0010(\u001a\u00020\u00032\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020+H\u00d6\u0001J\t\u0010,\u001a\u00020\u0005H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0002\u0010\u0011R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0013\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0014\u00a8\u0006-"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContractFilterDto;", "", "isDeleted", "", "projectCode", "", "contractNumber", "contractName", "status", "Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;", "contractType", "Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;", "projectType", "Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;", "organizationId", "<init>", "(Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;Ljava/lang/String;)V", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getProjectCode", "()Ljava/lang/String;", "getContractNumber", "getContractName", "getStatus", "()Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;", "getContractType", "()Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;", "getProjectType", "()Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;", "getOrganizationId", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/ContractStatusDto;Lru/sbertroika/pasiv/gate/dto/ContractTypeDto;Lru/sbertroika/pasiv/gate/dto/ProjectTypeDto;Ljava/lang/String;)Lru/sbertroika/pasiv/gate/dto/ContractFilterDto;", "equals", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class ContractFilterDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean isDeleted = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String projectCode = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String contractNumber = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String contractName = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.ContractStatusDto status = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.ContractTypeDto contractType = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.ProjectTypeDto projectType = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String organizationId = null;
    
    public ContractFilterDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0443\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u044b")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isDeleted, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u0434 \u043f\u0440\u043e\u0435\u043a\u0442\u0430", example = "MSK_METRO")
    @org.jetbrains.annotations.Nullable()
    java.lang.String projectCode, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u043e\u043c\u0435\u0440 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u041e\u0413-2024")
    @org.jetbrains.annotations.Nullable()
    java.lang.String contractNumber, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0442\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442")
    @org.jetbrains.annotations.Nullable()
    java.lang.String contractName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u0442\u0443\u0441 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ContractStatusDto status, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ContractTypeDto contractType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043f\u0440\u043e\u0435\u043a\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ProjectTypeDto projectType, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String organizationId) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean isDeleted() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getProjectCode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getContractNumber() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getContractName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ContractStatusDto getStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ContractTypeDto getContractType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ProjectTypeDto getProjectType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOrganizationId() {
        return null;
    }
    
    public ContractFilterDto() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ContractStatusDto component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ContractTypeDto component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.ProjectTypeDto component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractFilterDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0443\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u044b")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isDeleted, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u0434 \u043f\u0440\u043e\u0435\u043a\u0442\u0430", example = "MSK_METRO")
    @org.jetbrains.annotations.Nullable()
    java.lang.String projectCode, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u043e\u043c\u0435\u0440 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0414\u041e\u0413-2024")
    @org.jetbrains.annotations.Nullable()
    java.lang.String contractNumber, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "\u0442\u0440\u0430\u043d\u0441\u043f\u043e\u0440\u0442")
    @org.jetbrains.annotations.Nullable()
    java.lang.String contractName, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0430\u0442\u0443\u0441 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ContractStatusDto status, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ContractTypeDto contractType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043f\u0440\u043e\u0435\u043a\u0442\u0430")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.ProjectTypeDto projectType, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String organizationId) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}