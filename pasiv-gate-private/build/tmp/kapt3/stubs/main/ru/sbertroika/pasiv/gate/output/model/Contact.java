package ru.sbertroika.pasiv.gate.output.model;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b-\b\u0086\b\u0018\u00002\u00020\u0001Be\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u000b\u0010.\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010/\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J\u000b\u00100\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u00101\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00103\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\u000b\u00104\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\t\u00105\u001a\u00020\u000fH\u00c6\u0003Jl\u00106\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u00c6\u0001\u00a2\u0006\u0002\u00107J\u0013\u00108\u001a\u00020\u000f2\b\u00109\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010:\u001a\u00020\u0005H\u00d6\u0001J\t\u0010;\u001a\u00020\rH\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0013\"\u0004\b\u0014\u0010\u0015R\"\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006@\u0006X\u0087\u000e\u00a2\u0006\u0010\n\u0002\u0010\u001a\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019R \u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u001c\"\u0004\b\u001d\u0010\u001eR \u0010\b\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010\u0013\"\u0004\b \u0010\u0015R \u0010\t\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\u0013\"\u0004\b\"\u0010\u0015R \u0010\n\u001a\u0004\u0018\u00010\u000b8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010$\"\u0004\b%\u0010&R \u0010\f\u001a\u0004\u0018\u00010\r8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010(\"\u0004\b)\u0010*R\u001e\u0010\u000e\u001a\u00020\u000f8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010+\"\u0004\b,\u0010-\u00a8\u0006<"}, d2 = {"Lru/sbertroika/pasiv/gate/output/model/Contact;", "", "id", "Ljava/util/UUID;", "version", "", "versionCreatedAt", "Ljava/sql/Timestamp;", "versionCreatedBy", "organizationId", "type", "Lru/sbertroika/pasiv/gate/output/model/ContactType;", "value", "", "isDeleted", "", "<init>", "(Ljava/util/UUID;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/util/UUID;Lru/sbertroika/pasiv/gate/output/model/ContactType;Ljava/lang/String;Z)V", "getId", "()Ljava/util/UUID;", "setId", "(Ljava/util/UUID;)V", "getVersion", "()Ljava/lang/Integer;", "setVersion", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "getVersionCreatedAt", "()Ljava/sql/Timestamp;", "setVersionCreatedAt", "(Ljava/sql/Timestamp;)V", "getVersionCreatedBy", "setVersionCreatedBy", "getOrganizationId", "setOrganizationId", "getType", "()Lru/sbertroika/pasiv/gate/output/model/ContactType;", "setType", "(Lru/sbertroika/pasiv/gate/output/model/ContactType;)V", "getValue", "()Ljava/lang/String;", "setValue", "(Ljava/lang/String;)V", "()Z", "setDeleted", "(Z)V", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Ljava/util/UUID;Ljava/lang/Integer;Ljava/sql/Timestamp;Ljava/util/UUID;Ljava/util/UUID;Lru/sbertroika/pasiv/gate/output/model/ContactType;Ljava/lang/String;Z)Lru/sbertroika/pasiv/gate/output/model/Contact;", "equals", "other", "hashCode", "toString", "pasiv-gate-private"})
public final class Contact {
    @ru.sbertroika.history.api.HistoryId()
    @org.springframework.data.relational.core.mapping.Column(value = "c_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID id;
    @ru.sbertroika.history.api.HistoryVersion()
    @org.springframework.data.relational.core.mapping.Column(value = "c_version")
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer version;
    @ru.sbertroika.history.api.HistoryVersionAt()
    @org.springframework.data.relational.core.mapping.Column(value = "c_version_created_at")
    @org.jetbrains.annotations.Nullable()
    private java.sql.Timestamp versionCreatedAt;
    @ru.sbertroika.history.api.HistoryVersionBy()
    @org.springframework.data.relational.core.mapping.Column(value = "c_version_created_by")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID versionCreatedBy;
    @org.springframework.data.relational.core.mapping.Column(value = "c_organization_id")
    @org.jetbrains.annotations.Nullable()
    private java.util.UUID organizationId;
    @org.springframework.data.relational.core.mapping.Column(value = "c_type")
    @org.jetbrains.annotations.Nullable()
    private ru.sbertroika.pasiv.gate.output.model.ContactType type;
    @org.springframework.data.relational.core.mapping.Column(value = "c_value")
    @org.jetbrains.annotations.Nullable()
    private java.lang.String value;
    @ru.sbertroika.history.api.HistoryStatus()
    @org.springframework.data.relational.core.mapping.Column(value = "c_is_deleted")
    private boolean isDeleted;
    
    public Contact(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.util.UUID organizationId, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.model.ContactType type, @org.jetbrains.annotations.Nullable()
    java.lang.String value, boolean isDeleted) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getId() {
        return null;
    }
    
    public final void setId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getVersion() {
        return null;
    }
    
    public final void setVersion(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp getVersionCreatedAt() {
        return null;
    }
    
    public final void setVersionCreatedAt(@org.jetbrains.annotations.Nullable()
    java.sql.Timestamp p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getVersionCreatedBy() {
        return null;
    }
    
    public final void setVersionCreatedBy(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID getOrganizationId() {
        return null;
    }
    
    public final void setOrganizationId(@org.jetbrains.annotations.Nullable()
    java.util.UUID p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.model.ContactType getType() {
        return null;
    }
    
    public final void setType(@org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.model.ContactType p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getValue() {
        return null;
    }
    
    public final void setValue(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    public final void setDeleted(boolean p0) {
    }
    
    public Contact() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.sql.Timestamp component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.UUID component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.output.model.ContactType component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.output.model.Contact copy(@org.jetbrains.annotations.Nullable()
    java.util.UUID id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer version, @org.jetbrains.annotations.Nullable()
    java.sql.Timestamp versionCreatedAt, @org.jetbrains.annotations.Nullable()
    java.util.UUID versionCreatedBy, @org.jetbrains.annotations.Nullable()
    java.util.UUID organizationId, @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.output.model.ContactType type, @org.jetbrains.annotations.Nullable()
    java.lang.String value, boolean isDeleted) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}