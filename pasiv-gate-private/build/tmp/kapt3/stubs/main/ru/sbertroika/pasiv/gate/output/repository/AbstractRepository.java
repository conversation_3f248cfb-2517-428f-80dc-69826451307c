package ru.sbertroika.pasiv.gate.output.repository;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\b&\u0018\u0000*\u0004\b\u0000\u0010\u0001*\u0004\b\u0001\u0010\u00022\u00020\u0003B#\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u0012\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u0011H&J\u0015\u0010\u0012\u001a\u00028\u00002\u0006\u0010\u0013\u001a\u00020\u0014H&\u00a2\u0006\u0002\u0010\u0015J\u0018\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018H\u0004J\u0018\u0010\u001a\u001a\u0004\u0018\u00018\u00002\u0006\u0010\u001b\u001a\u00020\u000fH\u00a6@\u00a2\u0006\u0002\u0010\u001cJ\u001e\u0010\u001d\u001a\b\u0012\u0004\u0012\u00028\u00000\u001e2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018H&J\u000e\u0010\u001d\u001a\b\u0012\u0004\u0012\u00028\u00000\u001eH&J\u000e\u0010\u001f\u001a\u00020\u0018H\u0086@\u00a2\u0006\u0002\u0010 J\u001e\u0010!\u001a\u00020\"2\u0006\u0010\u001b\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020$H\u00a6@\u00a2\u0006\u0002\u0010%J\u0016\u0010&\u001a\u00028\u00002\u0006\u0010\'\u001a\u00028\u0000H\u0086@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00182\u0006\u0010*\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u001e\u0010+\u001a\u00020\u000f2\u0006\u0010*\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018R\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR \u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0007X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006,"}, d2 = {"Lru/sbertroika/pasiv/gate/output/repository/AbstractRepository;", "E", "K", "", "dbClient", "Lorg/springframework/r2dbc/core/DatabaseClient;", "repository", "Lorg/springframework/data/repository/kotlin/CoroutineCrudRepository;", "<init>", "(Lorg/springframework/r2dbc/core/DatabaseClient;Lorg/springframework/data/repository/kotlin/CoroutineCrudRepository;)V", "getDbClient", "()Lorg/springframework/r2dbc/core/DatabaseClient;", "getRepository", "()Lorg/springframework/data/repository/kotlin/CoroutineCrudRepository;", "getQuery", "", "isCount", "", "toEntity", "t", "Lio/r2dbc/spi/Readable;", "(Lio/r2dbc/spi/Readable;)Ljava/lang/Object;", "getPageRequest", "page", "", "limit", "findById", "id", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "findAll", "Lkotlinx/coroutines/flow/Flow;", "countAll", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleted", "", "userId", "Ljava/util/UUID;", "(Ljava/lang/String;Ljava/util/UUID;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "save", "entity", "(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCountByQuery", "query", "getByQueryPageRequest", "pasiv-gate-private"})
public abstract class AbstractRepository<E extends java.lang.Object, K extends java.lang.Object> {
    @org.jetbrains.annotations.NotNull()
    private final org.springframework.r2dbc.core.DatabaseClient dbClient = null;
    @org.jetbrains.annotations.NotNull()
    private final org.springframework.data.repository.kotlin.CoroutineCrudRepository<E, K> repository = null;
    
    public AbstractRepository(@org.jetbrains.annotations.NotNull()
    org.springframework.r2dbc.core.DatabaseClient dbClient, @org.jetbrains.annotations.NotNull()
    org.springframework.data.repository.kotlin.CoroutineCrudRepository<E, K> repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public org.springframework.r2dbc.core.DatabaseClient getDbClient() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public org.springframework.data.repository.kotlin.CoroutineCrudRepository<E, K> getRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract java.lang.String getQuery(boolean isCount);
    
    public abstract E toEntity(@org.jetbrains.annotations.NotNull()
    io.r2dbc.spi.Readable t);
    
    @org.jetbrains.annotations.NotNull()
    protected final java.lang.String getPageRequest(int page, int limit) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object findById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super E> $completion);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<E> findAll(int page, int limit);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<E> findAll();
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object countAll(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleted(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.util.UUID userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object save(E entity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super E> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCountByQuery(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getByQueryPageRequest(@org.jetbrains.annotations.NotNull()
    java.lang.String query, int page, int limit) {
        return null;
    }
}