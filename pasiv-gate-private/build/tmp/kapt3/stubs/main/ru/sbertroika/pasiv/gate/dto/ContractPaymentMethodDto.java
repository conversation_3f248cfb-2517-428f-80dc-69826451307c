package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u043f\u043e\u0441\u043e\u0431 \u043e\u043f\u043b\u0430\u0442\u044b \u043f\u043e \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0443")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b \n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B}\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0001\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0001\u0010\b\u001a\u00020\u0003\u0012\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0003\u0010\n\u001a\u00020\u000b\u0012\b\b\u0003\u0010\f\u001a\u00020\u000b\u0012\b\b\u0001\u0010\r\u001a\u00020\u000e\u0012\n\b\u0003\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0003\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0011\u0010\u0012J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0006H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\'\u001a\u00020\u000bH\u00c6\u0003J\t\u0010(\u001a\u00020\u000eH\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u007f\u0010+\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00032\b\b\u0003\u0010\u0005\u001a\u00020\u00062\b\b\u0003\u0010\u0007\u001a\u00020\u00032\b\b\u0003\u0010\b\u001a\u00020\u00032\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\n\u001a\u00020\u000b2\b\b\u0003\u0010\f\u001a\u00020\u000b2\b\b\u0003\u0010\r\u001a\u00020\u000e2\n\b\u0003\u0010\u000f\u001a\u0004\u0018\u00010\u000e2\n\b\u0003\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010,\u001a\u00020\u000b2\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020/H\u00d6\u0001J\t\u00100\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0014R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0014R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u001bR\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u001bR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001dR\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0014\u00a8\u00061"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContractPaymentMethodDto;", "", "id", "", "contractId", "methodType", "Lru/sbertroika/pasiv/gate/dto/PaymentMethodTypeDto;", "code", "name", "description", "isActive", "", "isDeleted", "createdDate", "Ljava/time/LocalDateTime;", "lastSyncDate", "externalId", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/PaymentMethodTypeDto;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZLjava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "getContractId", "getMethodType", "()Lru/sbertroika/pasiv/gate/dto/PaymentMethodTypeDto;", "getCode", "getName", "getDescription", "()Z", "getCreatedDate", "()Ljava/time/LocalDateTime;", "getLastSyncDate", "getExternalId", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "copy", "equals", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class ContractPaymentMethodDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String id = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String contractId = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.PaymentMethodTypeDto methodType = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String code = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String name = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String description = null;
    private final boolean isActive = false;
    private final boolean isDeleted = false;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime createdDate = null;
    @org.jetbrains.annotations.Nullable()
    private final java.time.LocalDateTime lastSyncDate = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String externalId = null;
    
    public ContractPaymentMethodDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0441\u043f\u043e\u0441\u043e\u0431\u0430 \u043e\u043f\u043b\u0430\u0442\u044b", example = "123e4567-e89b-12d3-a456-************")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-************")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0441\u043f\u043e\u0441\u043e\u0431\u0430 \u043e\u043f\u043b\u0430\u0442\u044b")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.PaymentMethodTypeDto methodType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u0434 \u0442\u0438\u043f\u0430 (\u0434\u043b\u044f \u0441\u043e\u0432\u043c\u0435\u0441\u0442\u0438\u043c\u043e\u0441\u0442\u0438)", example = "BANK_CARD")
    @org.jetbrains.annotations.NotNull()
    java.lang.String code, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0441\u043f\u043e\u0441\u043e\u0431\u0430 \u043e\u043f\u043b\u0430\u0442\u044b", example = "\u0411\u0430\u043d\u043a\u043e\u0432\u0441\u043a\u0438\u0435 \u043a\u0430\u0440\u0442\u044b Visa/MasterCard")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435", example = "\u041e\u043f\u043b\u0430\u0442\u0430 \u0431\u0430\u043d\u043a\u043e\u0432\u0441\u043a\u0438\u043c\u0438 \u043a\u0430\u0440\u0442\u0430\u043c\u0438 \u043c\u0435\u0436\u0434\u0443\u043d\u0430\u0440\u043e\u0434\u043d\u044b\u0445 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u044b\u0445 \u0441\u0438\u0441\u0442\u0435\u043c")
    @org.jetbrains.annotations.Nullable()
    java.lang.String description, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u043a\u0442\u0438\u0432\u0435\u043d \u043b\u0438 \u0441\u043f\u043e\u0441\u043e\u0431 \u043e\u043f\u043b\u0430\u0442\u044b")
    boolean isActive, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u0441\u043f\u043e\u0441\u043e\u0431 \u043e\u043f\u043b\u0430\u0442\u044b")
    boolean isDeleted, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime createdDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u0441\u0438\u043d\u0445\u0440\u043e\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime lastSyncDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u043d\u0435\u0448\u043d\u0438\u0439 ID", example = "EXT_PAYMENT_123")
    @org.jetbrains.annotations.Nullable()
    java.lang.String externalId) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getContractId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.PaymentMethodTypeDto getMethodType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDescription() {
        return null;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getCreatedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime getLastSyncDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getExternalId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.PaymentMethodTypeDto component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContractPaymentMethodDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0441\u043f\u043e\u0441\u043e\u0431\u0430 \u043e\u043f\u043b\u0430\u0442\u044b", example = "123e4567-e89b-12d3-a456-************")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430", example = "123e4567-e89b-12d3-a456-************")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contractId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u0441\u043f\u043e\u0441\u043e\u0431\u0430 \u043e\u043f\u043b\u0430\u0442\u044b")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.PaymentMethodTypeDto methodType, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u0434 \u0442\u0438\u043f\u0430 (\u0434\u043b\u044f \u0441\u043e\u0432\u043c\u0435\u0441\u0442\u0438\u043c\u043e\u0441\u0442\u0438)", example = "BANK_CARD")
    @org.jetbrains.annotations.NotNull()
    java.lang.String code, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0441\u043f\u043e\u0441\u043e\u0431\u0430 \u043e\u043f\u043b\u0430\u0442\u044b", example = "\u0411\u0430\u043d\u043a\u043e\u0432\u0441\u043a\u0438\u0435 \u043a\u0430\u0440\u0442\u044b Visa/MasterCard")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435", example = "\u041e\u043f\u043b\u0430\u0442\u0430 \u0431\u0430\u043d\u043a\u043e\u0432\u0441\u043a\u0438\u043c\u0438 \u043a\u0430\u0440\u0442\u0430\u043c\u0438 \u043c\u0435\u0436\u0434\u0443\u043d\u0430\u0440\u043e\u0434\u043d\u044b\u0445 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u044b\u0445 \u0441\u0438\u0441\u0442\u0435\u043c")
    @org.jetbrains.annotations.Nullable()
    java.lang.String description, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u043a\u0442\u0438\u0432\u0435\u043d \u043b\u0438 \u0441\u043f\u043e\u0441\u043e\u0431 \u043e\u043f\u043b\u0430\u0442\u044b")
    boolean isActive, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u0441\u043f\u043e\u0441\u043e\u0431 \u043e\u043f\u043b\u0430\u0442\u044b")
    boolean isDeleted, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u044f", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime createdDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u0430\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u0441\u0438\u043d\u0445\u0440\u043e\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "2024-01-01T12:00:00")
    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd\'T\'HH:mm:ss")
    @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime lastSyncDate, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0412\u043d\u0435\u0448\u043d\u0438\u0439 ID", example = "EXT_PAYMENT_123")
    @org.jetbrains.annotations.Nullable()
    java.lang.String externalId) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}