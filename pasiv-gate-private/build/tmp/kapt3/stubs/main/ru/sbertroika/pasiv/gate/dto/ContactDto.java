package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u043d\u0442\u0430\u043a\u0442")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B;\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0001\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0003\u0010\b\u001a\u00020\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\tH\u00c6\u0003J=\u0010\u0018\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00032\b\b\u0003\u0010\u0005\u001a\u00020\u00062\b\b\u0003\u0010\u0007\u001a\u00020\u00032\b\b\u0003\u0010\b\u001a\u00020\tH\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\t2\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0012\u00a8\u0006\u001e"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/ContactDto;", "", "id", "", "organizationId", "type", "Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "value", "isDeleted", "", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;Ljava/lang/String;Z)V", "getId", "()Ljava/lang/String;", "getOrganizationId", "getType", "()Lru/sbertroika/pasiv/gate/dto/ContactTypeDto;", "getValue", "()Z", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "pasiv-gate-private"})
public final class ContactDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String id = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String organizationId = null;
    @org.jetbrains.annotations.NotNull()
    private final ru.sbertroika.pasiv.gate.dto.ContactTypeDto type = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String value = null;
    private final boolean isDeleted = false;
    
    public ContactDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "+7 (495) 123-45-67")
    @org.jetbrains.annotations.NotNull()
    java.lang.String value, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u043a\u043e\u043d\u0442\u0430\u043a\u0442")
    boolean isDeleted) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrganizationId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactTypeDto getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValue() {
        return null;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactTypeDto component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.ContactDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "ID \u043e\u0440\u0433\u0430\u043d\u0438\u0437\u0430\u0446\u0438\u0438", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.NotNull()
    java.lang.String organizationId, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430")
    @org.jetbrains.annotations.NotNull()
    ru.sbertroika.pasiv.gate.dto.ContactTypeDto type, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u043a\u043e\u043d\u0442\u0430\u043a\u0442\u0430", example = "+7 (495) 123-45-67")
    @org.jetbrains.annotations.NotNull()
    java.lang.String value, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u043a\u043e\u043d\u0442\u0430\u043a\u0442")
    boolean isDeleted) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}