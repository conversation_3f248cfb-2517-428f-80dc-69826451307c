package ru.sbertroika.pasiv.gate.util;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000e\u0010\f\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005J\u0010\u0010\u000e\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005H\u0002J\u0010\u0010\u000f\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005H\u0002J\u0010\u0010\u0010\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005H\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\t\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lru/sbertroika/pasiv/gate/util/LogUtil;", "", "<init>", "()V", "HIDDEN_STRING", "", "PATTERN_FOR_HEADERS", "Ljava/util/regex/Pattern;", "kotlin.jvm.PlatformType", "PATTERN_FOR_PARAMS", "PATTERN_FOR_BODY", "PATTERN_FOR_BODY_TOKEN", "sanitizeLog", "str", "sanitizeHeaders", "sanitizeParams", "sanitizeBody", "pasiv-gate-private"})
public final class LogUtil {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String HIDDEN_STRING = "***HIDDEN***";
    private static final java.util.regex.Pattern PATTERN_FOR_HEADERS = null;
    private static final java.util.regex.Pattern PATTERN_FOR_PARAMS = null;
    private static final java.util.regex.Pattern PATTERN_FOR_BODY = null;
    private static final java.util.regex.Pattern PATTERN_FOR_BODY_TOKEN = null;
    @org.jetbrains.annotations.NotNull()
    public static final ru.sbertroika.pasiv.gate.util.LogUtil INSTANCE = null;
    
    private LogUtil() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String sanitizeLog(@org.jetbrains.annotations.NotNull()
    java.lang.String str) {
        return null;
    }
    
    private final java.lang.String sanitizeHeaders(java.lang.String str) {
        return null;
    }
    
    private final java.lang.String sanitizeParams(java.lang.String str) {
        return null;
    }
    
    private final java.lang.String sanitizeBody(java.lang.String str) {
        return null;
    }
}