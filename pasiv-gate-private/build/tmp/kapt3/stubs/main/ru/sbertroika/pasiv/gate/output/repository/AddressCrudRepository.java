package ru.sbertroika.pasiv.gate.output.repository;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001\u00a8\u0006\u0004"}, d2 = {"Lru/sbertroika/pasiv/gate/output/repository/AddressCrudRepository;", "Lorg/springframework/data/repository/kotlin/CoroutineCrudRepository;", "Lru/sbertroika/pasiv/gate/output/model/Address;", "Lru/sbertroika/pasiv/gate/output/model/AddressPK;", "pasiv-gate-private"})
public abstract interface AddressCrudRepository extends org.springframework.data.repository.kotlin.CoroutineCrudRepository<ru.sbertroika.pasiv.gate.output.model.Address, ru.sbertroika.pasiv.gate.output.model.AddressPK> {
}