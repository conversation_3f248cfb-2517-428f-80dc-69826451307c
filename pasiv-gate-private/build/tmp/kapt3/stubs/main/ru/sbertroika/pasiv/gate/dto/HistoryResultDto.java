package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u0438\u0441\u0442\u043e\u0440\u0438\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B#\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u000e\b\u0001\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J%\u0010\u000f\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u000e\b\u0003\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0017"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/HistoryResultDto;", "", "pagination", "Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;", "items", "", "Lru/sbertroika/pasiv/gate/dto/HistoryItemDto;", "<init>", "(Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;Ljava/util/List;)V", "getPagination", "()Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;", "getItems", "()Ljava/util/List;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class HistoryResultDto {
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryItemDto> items = null;
    
    public HistoryResultDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u042d\u043b\u0435\u043c\u0435\u043d\u0442\u044b \u0438\u0441\u0442\u043e\u0440\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryItemDto> items) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto getPagination() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryItemDto> getItems() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryItemDto> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.HistoryResultDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u042d\u043b\u0435\u043c\u0435\u043d\u0442\u044b \u0438\u0441\u0442\u043e\u0440\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryItemDto> items) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}