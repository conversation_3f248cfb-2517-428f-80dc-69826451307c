package ru.sbertroika.pasiv.gate.dto;

/**
 * Базовые DTO классы для REST API
 */
@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0440\u043e\u0441 \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u001b\u0012\b\b\u0003\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0003\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\f\u001a\u00020\u00002\b\b\u0003\u0010\u0002\u001a\u00020\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\b\u00a8\u0006\u0013"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/PaginationRequestDto;", "", "page", "", "size", "<init>", "(II)V", "getPage", "()I", "getSize", "component1", "component2", "copy", "equals", "", "other", "hashCode", "toString", "", "pasiv-gate-private"})
public final class PaginationRequestDto {
    @error.NonExistentClass()
    private final int page = 0;
    @error.NonExistentClass()
    private final int size = 0;
    
    public PaginationRequestDto(@io.swagger.v3.oas.annotations.media.Schema(minimum = "0", description = "\u041d\u043e\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b (\u043d\u0430\u0447\u0438\u043d\u0430\u044f \u0441 0)", example = "0")
    int page, @io.swagger.v3.oas.annotations.media.Schema(maximum = "100", minimum = "1", description = "\u0420\u0430\u0437\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b", example = "20")
    int size) {
        super();
    }
    
    public final int getPage() {
        return 0;
    }
    
    public final int getSize() {
        return 0;
    }
    
    public PaginationRequestDto() {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.PaginationRequestDto copy(@io.swagger.v3.oas.annotations.media.Schema(minimum = "0", description = "\u041d\u043e\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b (\u043d\u0430\u0447\u0438\u043d\u0430\u044f \u0441 0)", example = "0")
    int page, @io.swagger.v3.oas.annotations.media.Schema(maximum = "100", minimum = "1", description = "\u0420\u0430\u0437\u043c\u0435\u0440 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u044b", example = "20")
    int size) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}