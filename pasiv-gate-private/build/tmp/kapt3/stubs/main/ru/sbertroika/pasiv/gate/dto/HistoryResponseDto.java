package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0442\u0432\u0435\u0442 \u0441 \u0438\u0441\u0442\u043e\u0440\u0438\u0435\u0439 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0012\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B9\u0012\u000e\b\u0001\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0003\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\b\u0003\u0010\t\u001a\u00020\n\u00a2\u0006\u0004\b\u000b\u0010\fJ\u000f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010\u0018\u001a\u00020\nH\u00c6\u0003J;\u0010\u0019\u001a\u00020\u00002\u000e\b\u0003\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0003\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\b\u0003\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\n2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006 "}, d2 = {"Lru/sbertroika/pasiv/gate/dto/HistoryResponseDto;", "", "records", "", "Lru/sbertroika/pasiv/gate/dto/HistoryRecordDto;", "pagination", "Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;", "error", "Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;", "success", "", "<init>", "(Ljava/util/List;Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;Z)V", "getRecords", "()Ljava/util/List;", "getPagination", "()Lru/sbertroika/pasiv/gate/dto/PaginationResponseDto;", "getError", "()Lru/sbertroika/pasiv/gate/dto/OperationErrorDto;", "getSuccess", "()Z", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "", "pasiv-gate-private"})
public final class HistoryResponseDto {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryRecordDto> records = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination = null;
    @org.jetbrains.annotations.Nullable()
    private final ru.sbertroika.pasiv.gate.dto.OperationErrorDto error = null;
    private final boolean success = false;
    
    public HistoryResponseDto(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0438\u0441\u0438 \u0438\u0441\u0442\u043e\u0440\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryRecordDto> records, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0448\u0438\u0431\u043a\u0430 (\u0435\u0441\u043b\u0438 \u0435\u0441\u0442\u044c)")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OperationErrorDto error, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043f\u0435\u0448\u043d\u043e\u0441\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438")
    boolean success) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryRecordDto> getRecords() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto getPagination() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OperationErrorDto getError() {
        return null;
    }
    
    public final boolean getSuccess() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryRecordDto> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.PaginationResponseDto component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final ru.sbertroika.pasiv.gate.dto.OperationErrorDto component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.HistoryResponseDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "\u0417\u0430\u043f\u0438\u0441\u0438 \u0438\u0441\u0442\u043e\u0440\u0438\u0438")
    @org.jetbrains.annotations.NotNull()
    java.util.List<ru.sbertroika.pasiv.gate.dto.HistoryRecordDto> records, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043f\u0430\u0433\u0438\u043d\u0430\u0446\u0438\u0438")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.PaginationResponseDto pagination, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0448\u0438\u0431\u043a\u0430 (\u0435\u0441\u043b\u0438 \u0435\u0441\u0442\u044c)")
    @org.jetbrains.annotations.Nullable()
    ru.sbertroika.pasiv.gate.dto.OperationErrorDto error, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0441\u043f\u0435\u0448\u043d\u043e\u0441\u0442\u044c \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438")
    boolean success) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}