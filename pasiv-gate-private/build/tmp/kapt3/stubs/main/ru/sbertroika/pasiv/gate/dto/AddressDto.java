package ru.sbertroika.pasiv.gate.dto;

@io.swagger.v3.oas.annotations.media.Schema(description = "\u0410\u0434\u0440\u0435\u0441")
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b2\b\u0087\b\u0018\u00002\u00020\u0001B\u00c9\u0001\u0012\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0003\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\b\u001a\u00020\u0003\u0012\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\n\u001a\u00020\u0003\u0012\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0001\u0010\f\u001a\u00020\u0003\u0012\n\b\u0003\u0010\r\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0003\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0003\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0003\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u0012\n\b\u0003\u0010\u0015\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0003\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\u0004\b\u0018\u0010\u0019J\u000b\u00102\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0003H\u00c6\u0003J\u0010\u00104\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001eJ\u000b\u00105\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u00106\u001a\u00020\u0003H\u00c6\u0003J\u000b\u00107\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\u000b\u00109\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010:\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010;\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010<\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010=\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010)J\u0010\u0010>\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010)J\u000b\u0010?\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010@\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003\u00a2\u0006\u0002\u0010.J\u000b\u0010A\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010B\u001a\u00020\u0017H\u00c6\u0003J\u00d0\u0001\u0010C\u001a\u00020\u00002\n\b\u0003\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\u0004\u001a\u00020\u00032\n\b\u0003\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0003\u0010\u0007\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\b\u001a\u00020\u00032\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\n\u001a\u00020\u00032\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\f\u001a\u00020\u00032\n\b\u0003\u0010\r\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u000e\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u000f\u001a\u0004\u0018\u00010\u00102\n\b\u0003\u0010\u0011\u001a\u0004\u0018\u00010\u00102\n\b\u0003\u0010\u0012\u001a\u0004\u0018\u00010\u00032\n\b\u0003\u0010\u0013\u001a\u0004\u0018\u00010\u00142\n\b\u0003\u0010\u0015\u001a\u0004\u0018\u00010\u00032\b\b\u0003\u0010\u0016\u001a\u00020\u0017H\u00c6\u0001\u00a2\u0006\u0002\u0010DJ\u0013\u0010E\u001a\u00020\u00172\b\u0010F\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010G\u001a\u00020\u0006H\u00d6\u0001J\t\u0010H\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001bR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\n\n\u0002\u0010\u001f\u001a\u0004\b\u001d\u0010\u001eR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001bR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001bR\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001bR\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001bR\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001bR\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001bR\u0013\u0010\r\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001bR\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001bR\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\n\n\u0002\u0010*\u001a\u0004\b(\u0010)R\u0015\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\n\n\u0002\u0010*\u001a\u0004\b+\u0010)R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\u001bR\u0015\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\n\n\u0002\u0010/\u001a\u0004\b-\u0010.R\u0013\u0010\u0015\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\u001bR\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u00101\u00a8\u0006I"}, d2 = {"Lru/sbertroika/pasiv/gate/dto/AddressDto;", "", "id", "", "name", "index", "", "country", "region", "district", "city", "street", "house", "buildingOrHousing", "officeOrRoom", "longitude", "", "latitude", "comment", "oktmo", "", "fiac", "isDeleted", "", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Z)V", "getId", "()Ljava/lang/String;", "getName", "getIndex", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getCountry", "getRegion", "getDistrict", "getCity", "getStreet", "getHouse", "getBuildingOrHousing", "getOfficeOrRoom", "getLongitude", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getLatitude", "getComment", "getOktmo", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getFiac", "()Z", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Z)Lru/sbertroika/pasiv/gate/dto/AddressDto;", "equals", "other", "hashCode", "toString", "pasiv-gate-private"})
public final class AddressDto {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String id = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String name = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer index = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String country = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String region = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String district = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String city = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String street = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String house = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String buildingOrHousing = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String officeOrRoom = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double longitude = null;
    @error.NonExistentClass()
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double latitude = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String comment = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long oktmo = null;
    @error.NonExistentClass()
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String fiac = null;
    private final boolean isDeleted = false;
    
    public AddressDto(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0430\u0434\u0440\u0435\u0441\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u0430\u0434\u0440\u0435\u0441\u0430", example = "\u0413\u043e\u043b\u043e\u0432\u043d\u043e\u0439 \u043e\u0444\u0438\u0441")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0447\u0442\u043e\u0432\u044b\u0439 \u0438\u043d\u0434\u0435\u043a\u0441", example = "123456")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u0430\u043d\u0430", example = "\u0420\u043e\u0441\u0441\u0438\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String country, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0435\u0433\u0438\u043e\u043d/\u043e\u0431\u043b\u0430\u0441\u0442\u044c", example = "\u041c\u043e\u0441\u043a\u043e\u0432\u0441\u043a\u0430\u044f \u043e\u0431\u043b\u0430\u0441\u0442\u044c")
    @org.jetbrains.annotations.NotNull()
    java.lang.String region, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0430\u0439\u043e\u043d", example = "\u041f\u043e\u0434\u043e\u043b\u044c\u0441\u043a\u0438\u0439 \u0440\u0430\u0439\u043e\u043d")
    @org.jetbrains.annotations.Nullable()
    java.lang.String district, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0413\u043e\u0440\u043e\u0434", example = "\u041c\u043e\u0441\u043a\u0432\u0430")
    @org.jetbrains.annotations.NotNull()
    java.lang.String city, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u043b\u0438\u0446\u0430", example = "\u0443\u043b. \u041b\u0435\u043d\u0438\u043d\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String street, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043c", example = "\u0434. 1")
    @org.jetbrains.annotations.NotNull()
    java.lang.String house, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u043e\u0435\u043d\u0438\u0435/\u043a\u043e\u0440\u043f\u0443\u0441", example = "\u0441\u0442\u0440. 1")
    @org.jetbrains.annotations.Nullable()
    java.lang.String buildingOrHousing, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0444\u0438\u0441/\u043a\u043e\u043c\u043d\u0430\u0442\u0430", example = "\u043e\u0444. 101")
    @org.jetbrains.annotations.Nullable()
    java.lang.String officeOrRoom, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043b\u0433\u043e\u0442\u0430", example = "37.6173")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double longitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0428\u0438\u0440\u043e\u0442\u0430", example = "55.7558")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double latitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u043c\u043c\u0435\u043d\u0442\u0430\u0440\u0438\u0439", example = "\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String comment, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0422\u041c\u041e", example = "45123000001")
    @org.jetbrains.annotations.Nullable()
    java.lang.Long oktmo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0418\u0410\u0421", example = "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
    @org.jetbrains.annotations.Nullable()
    java.lang.String fiac, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u0430\u0434\u0440\u0435\u0441")
    boolean isDeleted) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getIndex() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCountry() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRegion() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDistrict() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCity() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStreet() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHouse() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBuildingOrHousing() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOfficeOrRoom() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLongitude() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLatitude() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getComment() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getOktmo() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFiac() {
        return null;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component16() {
        return null;
    }
    
    public final boolean component17() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final ru.sbertroika.pasiv.gate.dto.AddressDto copy(@io.swagger.v3.oas.annotations.media.Schema(description = "ID \u0430\u0434\u0440\u0435\u0441\u0430", example = "123e4567-e89b-12d3-a456-426614174000")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u0430\u0434\u0440\u0435\u0441\u0430", example = "\u0413\u043e\u043b\u043e\u0432\u043d\u043e\u0439 \u043e\u0444\u0438\u0441")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041f\u043e\u0447\u0442\u043e\u0432\u044b\u0439 \u0438\u043d\u0434\u0435\u043a\u0441", example = "123456")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u0430\u043d\u0430", example = "\u0420\u043e\u0441\u0441\u0438\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String country, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0435\u0433\u0438\u043e\u043d/\u043e\u0431\u043b\u0430\u0441\u0442\u044c", example = "\u041c\u043e\u0441\u043a\u043e\u0432\u0441\u043a\u0430\u044f \u043e\u0431\u043b\u0430\u0441\u0442\u044c")
    @org.jetbrains.annotations.NotNull()
    java.lang.String region, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0420\u0430\u0439\u043e\u043d", example = "\u041f\u043e\u0434\u043e\u043b\u044c\u0441\u043a\u0438\u0439 \u0440\u0430\u0439\u043e\u043d")
    @org.jetbrains.annotations.Nullable()
    java.lang.String district, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0413\u043e\u0440\u043e\u0434", example = "\u041c\u043e\u0441\u043a\u0432\u0430")
    @org.jetbrains.annotations.NotNull()
    java.lang.String city, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u043b\u0438\u0446\u0430", example = "\u0443\u043b. \u041b\u0435\u043d\u0438\u043d\u0430")
    @org.jetbrains.annotations.Nullable()
    java.lang.String street, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043c", example = "\u0434. 1")
    @org.jetbrains.annotations.NotNull()
    java.lang.String house, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0421\u0442\u0440\u043e\u0435\u043d\u0438\u0435/\u043a\u043e\u0440\u043f\u0443\u0441", example = "\u0441\u0442\u0440. 1")
    @org.jetbrains.annotations.Nullable()
    java.lang.String buildingOrHousing, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u0444\u0438\u0441/\u043a\u043e\u043c\u043d\u0430\u0442\u0430", example = "\u043e\u0444. 101")
    @org.jetbrains.annotations.Nullable()
    java.lang.String officeOrRoom, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0414\u043e\u043b\u0433\u043e\u0442\u0430", example = "37.6173")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double longitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0428\u0438\u0440\u043e\u0442\u0430", example = "55.7558")
    @org.jetbrains.annotations.Nullable()
    java.lang.Double latitude, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041a\u043e\u043c\u043c\u0435\u043d\u0442\u0430\u0440\u0438\u0439", example = "\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u0430\u044f \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f")
    @org.jetbrains.annotations.Nullable()
    java.lang.String comment, @io.swagger.v3.oas.annotations.media.Schema(description = "\u041e\u041a\u0422\u041c\u041e", example = "45123000001")
    @org.jetbrains.annotations.Nullable()
    java.lang.Long oktmo, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0424\u0418\u0410\u0421", example = "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
    @org.jetbrains.annotations.Nullable()
    java.lang.String fiac, @io.swagger.v3.oas.annotations.media.Schema(description = "\u0423\u0434\u0430\u043b\u0435\u043d \u043b\u0438 \u0430\u0434\u0440\u0435\u0441")
    boolean isDeleted) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}