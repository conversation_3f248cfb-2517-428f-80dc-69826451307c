syntax = "proto3";

package ru.sbertroika.common.manifest.v1;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.manifest.v1";

import "google/protobuf/timestamp.proto";
import 'common-manifest-core.proto';
import 'common-manifest-pro.proto';
import 'common-manifest-pasiv.proto';
import 'common-manifest-tms.proto';
import 'common-manifest-agent-gateway.proto';

message ManifestRequest {
  string projectId = 1;
  optional string startDate = 2; // Если не указан то с текущей даты
}

message Manifest {
  message Service {
    optional pasiv.ManifestPasiv pasiv = 1;
    optional pro.ManifestPro pro = 2;
    optional tms.ManifestTms tms = 3;
    optional agent.gateway.ManifestAgentGate agentGate = 4;
    optional pro.ManifestProEmv proEmv = 101;
    optional pro.ManifestProCash proCash = 102;
    optional pro.ManifestProTroika proTroika = 103;
    optional pro.ManifestProAbt proAbt = 104;
  }
  string id = 1;
  uint32 version = 2;
  optional google.protobuf.Timestamp validFrom = 3;
  optional google.protobuf.Timestamp validTill = 4;
  optional uint32 projectIndex = 5;
  Service service = 6;
}
