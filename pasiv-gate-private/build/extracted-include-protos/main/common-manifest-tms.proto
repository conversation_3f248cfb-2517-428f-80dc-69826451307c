syntax = "proto3";

package ru.sbertroika.common.manifest.v1.tms;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.manifest.v1.tms";

import 'common-manifest-core.proto';

message ManifestTmsDict {
  repeated TerminalUser user = 1;
}

message ManifestTms {
  repeated core.TkpFeature features = 1;
  ManifestTmsDict dict = 2;
}

message TerminalUser {
  string id = 1;
  string role = 2;
  string surname = 3;
  string name = 4;
  string middleName = 5;
  string personalNumber = 6;
  string pinHash = 7;
}