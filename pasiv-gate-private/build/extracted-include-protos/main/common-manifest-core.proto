syntax = "proto3";

package ru.sbertroika.common.manifest.v1.core;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.manifest.v1.core";

import 'common.proto';

message ConstraintException {
  string id = 1;
}

message Constraint {
  common.v1.ConstraintType type = 1;
  common.v1.ConstraintBaseRule baseRule = 2;
  repeated ConstraintException exception = 3;
}

message TkpFeature {
  enum FeatureState {
    FS_ACTIVE = 0;
  }
  string name = 1;
  FeatureState state = 2;
}