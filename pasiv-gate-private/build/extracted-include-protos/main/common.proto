syntax = "proto3";

package ru.sbertroika.common.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.v1";

enum ErrorType {
  UNKNOWN_ERROR = 0;
  SERVICE_ERROR = 1;          // Сервис временно не доступен
  UNSUPPORTED_OPERATION = 2;  // Операция не поддерживается
  BAD_REQUEST = 3;            // Неправильно сформирован запрос
  AUTHENTICATION_ERROR = 4;   // Ошибка авторизации
  NOT_FOUND = 5;              // Запрашиваемый объект отсутсвует
}

message OperationError {
  ErrorType type = 1;
  string message = 2;
  int32 code = 3;
}

enum TransportType {
  BUS = 0;          // Автобус
  TROLLEYBUS = 1;   // Троллейбус
  TRAM = 2;         // Трамвай
  METRO = 3;        // Метро
}

enum OperationStatus {
  NEW = 0;          // Проезд зафиксирован но транзакция еще не авторизовалась
  SUCCESS = 1;      // Успешная авторизация
  FAILED = 2;       // Отказ в аторизации
}

enum AbonementType {
  WALLET = 0;             // Кошелек
  TRAVEL = 1;             // Поездочный
  UNLIMITED = 2;          // Безлимитный
}

enum SubscriptionCounterType {
  SCT_ALL = 0;                    // Один счетчик на все виды транспорта
  SCT_ALLOW_LIST = 1;             // Один счетчик на все разрешенные виды транспорта
  SCT_SINGLE = 2;                 // Для каждого перечисленного вида транспорта свой счетчик
  SCT_ALL_UNLIMITED = 3;          // Неограниченное число поездок
  SCT_ALLOW_LIST_UNLIMITED = 4;   // Неограниченное число поездок на все разрешенные виды транспорта
}

enum PaymentType {
  EMV = 0;          // Банковская карта
  CARRIER = 1;      // Привязанная к аккаунту карта
  SBER_PAY = 2;     // Сбер Пэй
  SBP = 3;          // СБП
  BINDING = 4;      // Привязанный способ оплаты
}

enum ProlongType {
  PT_PERIOD = 0;        // Заданный период
  PT_END_DATE = 1;      // С даты окончания билета
  PT_ACTIVE_DATE = 2;   // C даты активации
}

enum PaymentAttribute {
  PAYMENT_TYPE = 0;       // Тип платежной системы
  CLIENT_ID = 1;          // Идентификатор клиента (сервис, который принял платеж)
  USER_ID = 3;            // Идентификатор пользователя
  ORDER_ID = 4;           // Идентификатор платежа
  PAYMENT_DATE = 5;       // Дата/время совершения платежа
  RETURN_URL = 6;         // Ссылка на которую требуется перенаправить пользователя в случае успешной оплаты
  FAIL_URL = 7;           // Ссылка на которую требуется перенаправить пользователя в случае неуспешной оплаты
  ITEMS = 8;              // Список позиций в корзине
  CONFIG = 9;             // Конфигурация
  JSON_PARAMS = 10;       // JSON Параметры
  USER_EMAIL = 11;        // Email пользователя
  USER_PHONE = 12;        // Телефон пользователя
}

enum AuthType {
  OPEN_ID = 0;
  MTLS = 1;
}

message PaginationRequest {
  int32 page = 1;
  int32 limit = 2;
}

message PaginationResponse {
  int32 page = 1;
  int32 limit = 2;
  int32 totalPage = 3;
  int32 totalCount = 4;
}

message PaymentMethod {
  string paymentId = 1;         // Идентификатор способа оплаты
  PaymentType type = 2;         // Способ оплаты
  string name = 3;              // Наименовани способа оплаты
  string label = 4;             // Лэйбл способа оплаты
}

message Card {
  string cardNum = 1;     // PAN (example: ****************)
  string cardDate = 2;    // Дата окончания срока действия карты month/year (example: 11/24)
  string cardHolder = 3;  // Держатель карты (example: IVAN IVANOV)
  int32 cvc = 4;          // CVV/CVC (example: 123)
}


message Sorted {
  string column = 1; // название колонки
  SortedType type = 2; // тип сортировки
}

message Filter {
  string column = 1; // название колонки
  string value = 2; // значение фильтранции
}

enum SortedType {
  ASC = 0;
  DESC = 1;
}

message Position {
  double latitude = 1;
  double longitude = 2;
}

enum ConstraintType {
  TARIFF = 0;
  ROUTE = 1;
  TRANSPORT = 2;
  SERVICE = 3;
  ORGANIZATION = 4;
}

enum ConstraintBaseRule {
  ALLOW = 0;
  DENY = 1;
}

enum RouteScheme {
  DIRECTIONAL = 0;
  CIRCLE = 1;
}

message EmptyResponse {
  oneof response {
    OperationError error = 1;
    google.protobuf.Empty empty = 2;
  }
}

message CreateResponse {
  oneof response {
    OperationError error = 1;
    string id = 2;
  }
}

enum ModelStatus {
  ACTIVE = 0;
  DISABLED = 1;
  BLOCKED = 2;
  IS_DELETED = 3;
}

message HistoryChange {
  string field = 1;
  optional string oldValue = 2;
  optional string value = 3;
}

message History {
  int64 version = 1;
  string versionCreateBy = 2;
  google.protobuf.Timestamp versionCreateAt = 3;
  repeated HistoryChange change = 4;
  optional ModelStatus status = 5;
}

message HistoryResult {
  optional common.v1.PaginationResponse pagination = 1;
  repeated History history = 2;
}

message HistoryResponse {
  oneof response {
    common.v1.OperationError error = 1;
    HistoryResult result = 2;
  }
}

message ByIdRequest{
  string id = 1;
}