syntax = "proto3";

package ru.sbertroika.common.manifest.v1.pro;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.manifest.v1.pro";

import 'google/protobuf/timestamp.proto';
import 'common.proto';
import 'common-manifest-core.proto';

message ManifestProDict {
  repeated Station station = 1;
  repeated Route route = 2;
  repeated Transport transport = 3;
  repeated Product product = 4;
  repeated Tariff tariff = 5;
  repeated ProductMenu menu = 6;
  repeated Employee employee = 7;
}

message ManifestPro {
  repeated core.TkpFeature features = 1;
  ManifestProDict dict = 2;
}

message Product {
  string id = 1;
  string name = 2;
  uint32 version = 3;
}

message Station {
  string id = 1;
  string name = 2;
  double lat = 3;
  double lon = 4;
  uint32 version = 5;
}

message RouteStation {
  string id = 1;
  uint32 pos = 2;
  uint32 version = 3;
}

message Transport {
  string id = 1;
  string number = 2;
  common.v1.TransportType type = 3;
  repeated core.Constraint constraint = 4;
  uint32 version = 5;
}

message DispatchingRouteOrganization {
  string id = 1;
}

message Route {
  string id = 1;
  string name = 2;
  common.v1.RouteScheme scheme = 3;
  repeated RouteStation station = 4;
  repeated core.Constraint constraint = 5;
  uint32 routeIndex = 6;
  string number = 7;
  repeated DispatchingRouteOrganization dispatchingOrganization = 8;
  uint32 version = 9;
}

message Tariff {
  string id = 1;
  string name = 2;
  repeated core.Constraint constraint = 3;
  uint32 version = 4;
}

message PriceRuleMatrixItem {
  string stationFrom = 1;
  string stationTo = 2;
  uint32 price = 3;
  uint32 version = 4;
}

message PriceRule {
  enum TPaymentType {
    CASH = 0;
    EMV = 1;
    TROIKA_TICKET = 2;
    TROIKA_WALLET = 3;
    ABT_TICKET = 4;
    ABT_WALLET = 5;
    PROSTOR_TICKET = 6;
    QR_TICKET = 7;
    QR_WALLET = 8;
  }
  TPaymentType paymentType = 1;
  uint32 price = 2;
  repeated PriceRuleMatrixItem matrix = 3;
  uint32 version = 4;
}

message ProductMenu {
  string productId = 1;
  string tariffId = 2;
  repeated PriceRule priceRules = 3;
  uint32 version = 4;
}

message Employee {
  string id = 1;
  string role = 2;
  string surname = 3;
  string name = 4;
  string middleName = 5;
  uint32 version = 6;
}

/* Emv */

message ManifestProEmv {
  repeated core.TkpFeature features = 1;
}

/* Cash */

message ManifestProCash {
  repeated core.TkpFeature features = 1;
}

/* Troika */
message ManifestProTroika {
  repeated core.TkpFeature features = 1;
  ManifestProTroikaDict dict = 2;
}

message ManifestProTroikaDict {
  repeated TroikaTemplate templates = 1;
}

message TroikaTemplate {
  // Идентификатор шаблона абонемента
  string id = 1;
  // Код приложения
  uint32 appCode = 2;
  // Код билета
  uint32 crdCode = 3;
  // Наименование абонемента
  string name = 4;
  // Тип абонемента
  common.v1.AbonementType type = 5;
  // Кол-во поездок которые будут записаны при активации
  uint32 limit = 6;
  // Тип активации продления
  common.v1.ProlongType prolongType = 7;
  // Дата и время начала действия абонемента
  google.protobuf.Timestamp prolongStartDate = 8;
  // Дата и время окончания действия абонемента
  google.protobuf.Timestamp prolongEndDate = 9;
  // Кол-во дней действия билета с начала даты активации
  uint32 prolongDays = 10;
  // Код продленного билета
  uint32 prolongCardCode = 11;
  // Версия справочника
  uint32 version = 12;
}

/* ABT */
message ManifestProAbt {
  repeated core.TkpFeature features = 1;
  ManifestProAbtDict dict = 2;
}

message ManifestProAbtDict {
  repeated SubscriptionTemplate template = 1;
}

message SubscriptionTemplate {
  // Идентификатор шаблона абонемента
  string id = 1;
  // Версия справочника
  uint32 version = 2;
  // Код приложения
  uint32 appCode = 3;
  // Код билета
  uint32 crdCode = 4;
  // Наименование абонемента
  string name = 5;
  // Тип абонемента
  common.v1.AbonementType type = 6;
  // Правила обработки предъявлений
  repeated SubscriptionTemplatePassRule rules = 7;
  // Социальный
  bool isSocial = 8;
  // Счетчики
  repeated SubscriptionTemplateCounter counter = 9;
  // Список типов карт
  repeated AbtCardType cardType = 10;
  // Тип срока действия
  ValidTimeType validTimeType = 11;
  // Начало действия
  optional google.protobuf.Timestamp validTimeStart = 12;
  // Период действия(дней)
  optional int32 validTimeDays = 13;
  // Окончание действия
  optional google.protobuf.Timestamp validTimeEnd = 14;
}

enum ValidTimeType {
    // С периодом на календарный месяц
    INTERVAL = 0;
    // Списание в период(дней) от первой поездки
    DAYS = 1;
    // Списание в период(дней) от первой поездки, но не позднее даты
    INTERVAL_AND_DAYS = 2;
}

message SubscriptionTemplatePassRule {
  // Идентификатор правила списания
  string id = 1;
  // Версия справочника
  uint32 version = 2;
  // Порядковый номер предъявления
  uint32 index = 3;
  // Правило
  string action = 4;
}

message SubscriptionTemplateCounter {
  // Идентификатор счетчика
  string id = 1;
  // Версия справочника
  uint32 version = 2;
  // Тип счетчика
  common.v1.SubscriptionCounterType type = 3;
  // Базовое значение счетчика
  uint32 value = 4;
  // Доступность на транспорте
  bool isBus = 51;
  bool isTrolleybus = 52;
  bool isTram = 53;
  bool isMetro = 54;
}

message AbtCardType {
  string id = 1;
}