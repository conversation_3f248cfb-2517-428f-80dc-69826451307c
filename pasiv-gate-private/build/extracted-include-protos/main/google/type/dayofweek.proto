// Copyright 2019 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.type;

option go_package = "google.golang.org/genproto/googleapis/type/dayofweek;dayofweek";
option java_multiple_files = true;
option java_outer_classname = "DayOfWeekProto";
option java_package = "com.google.type";
option objc_class_prefix = "GTP";

// Represents a day of week.
enum DayOfWeek {
  // The unspecified day-of-week.
  DAY_OF_WEEK_UNSPECIFIED = 0;

  // The day-of-week of Monday.
  MONDAY = 1;

  // The day-of-week of Tuesday.
  TUESDAY = 2;

  // The day-of-week of Wednesday.
  WEDNESDAY = 3;

  // The day-of-week of Thursday.
  THURSDAY = 4;

  // The day-of-week of Friday.
  FRIDAY = 5;

  // The day-of-week of Saturday.
  SATURDAY = 6;

  // The day-of-week of Sunday.
  SUNDAY = 7;
}
