// Copyright 2019 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.type;

option go_package = "google.golang.org/genproto/googleapis/type/expr;expr";
option java_multiple_files = true;
option java_outer_classname = "ExprProto";
option java_package = "com.google.type";
option objc_class_prefix = "GTP";

// Represents an expression text. Example:
//
//     title: "User account presence"
//     description: "Determines whether the request has a user account"
//     expression: "size(request.user) > 0"
message Expr {
  // Textual representation of an expression in
  // Common Expression Language syntax.
  //
  // The application context of the containing message determines which
  // well-known feature set of CEL is supported.
  string expression = 1;

  // An optional title for the expression, i.e. a short string describing
  // its purpose. This can be used e.g. in UIs which allow to enter the
  // expression.
  string title = 2;

  // An optional description of the expression. This is a longer text which
  // describes the expression, e.g. when hovered over it in a UI.
  string description = 3;

  // An optional string indicating the location of the expression for error
  // reporting, e.g. a file name and a position in the file.
  string location = 4;
}
