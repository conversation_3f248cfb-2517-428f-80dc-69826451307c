syntax = "proto3";

package ru.sbertroika.pasiv.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.pasiv.gate.v1";

service PASIVGatePrivateService {
  // Создать организацию
  rpc createOrganization(OrganizationWithAddresses) returns (common.v1.EmptyResponse);
  // Обновить организацию
  rpc updateOrganization(Organization) returns (common.v1.EmptyResponse);
  // Список организаций
  rpc organizationList(OrganizationListRequest) returns (OrganizationListResponse);
  // Вернуть организацию по id
  rpc organizationById(ByIdRequest) returns (OrganizationResponse);
  rpc deleteOrganization(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc recoverOrganization(ByIdRequest) returns (common.v1.EmptyResponse);

  // Список организаций для проекта
  rpc organizationListForProject(OrganizationListForProjectRequest) returns (OrganizationListResponse);
  // Добавление организации в проект
  rpc addOrganizationInProject(OrganizationInProjectRequest) returns (common.v1.EmptyResponse);
  // Удалени организации из проекта
  rpc removeOrganizationInProject(OrganizationInProjectRequest) returns (common.v1.EmptyResponse);

  // Создать адрес
  rpc createAddress(AddressCreateOrDelete) returns (common.v1.EmptyResponse);
  // Обновить адрес
  rpc updateAddress(AddressCreateOrDelete) returns (common.v1.EmptyResponse);
  // Вернуть адрес по id
  rpc addressById(ByIdRequest) returns (AddressResponse);
  // Список адресов
  rpc addressList(AddressListRequest) returns (AddressListResponse);
  rpc deleteAddress(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc recoverAddress(ByIdRequest) returns (common.v1.EmptyResponse);

  // Создать контакт
  rpc createContact(Contact) returns (common.v1.EmptyResponse);
  // Обновить контакт
  rpc updateContact(Contact) returns (common.v1.EmptyResponse);
  // Список контактов
  rpc contactList(ContactListRequest) returns (ContactListResponse);
  // Вернуть контакт по id
  rpc contactById(ByIdRequest) returns (ContactResponse);
  rpc deleteContact(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc recoverContact(ByIdRequest) returns (common.v1.EmptyResponse);

  // Вернуть историю контакта по id
  rpc contactHistoryById(ByIdWithPaginationRequest) returns (common.v1.HistoryResponse);
  // Вернуть историю адреса по id
  rpc addressHistoryById(ByIdWithPaginationRequest) returns (common.v1.HistoryResponse);
  // Вернуть историю организации по id
  rpc organizationHistoryById(ByIdWithPaginationRequest) returns (common.v1.HistoryResponse);

  // Подсказка для полей организации по ИНН
  rpc organizationHintByINN(OrganizationHintRequest) returns (OrganizationHintResponse);

  // Contract methods
  rpc contractList(ContractListRequest) returns (ContractListResponse);
  rpc contractById(ByIdRequest) returns (ContractResponse);
  rpc createContract(ContractWithOrganizations) returns (common.v1.EmptyResponse);
  rpc updateContract(Contract) returns (common.v1.EmptyResponse);
  rpc deleteContract(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc contractsByProject(ContractsByProjectRequest) returns (ContractListResponse);

  // Payment Method methods
  rpc paymentMethodList(PaymentMethodListRequest) returns (PaymentMethodListResponse);
  rpc paymentMethodById(ByIdRequest) returns (PaymentMethodResponse);
  rpc createPaymentMethod(ContractPaymentMethod) returns (common.v1.EmptyResponse);
  rpc updatePaymentMethod(ContractPaymentMethod) returns (common.v1.EmptyResponse);
  rpc deletePaymentMethod(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc paymentMethodsByContract(PaymentMethodsByContractRequest) returns (PaymentMethodListResponse);

}

message OrganizationResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Organization result = 2;
  }
}

message OrganizationWithAddresses {
  Organization organization  = 1;
  Address addressLegal = 2;
  optional Address addressActual = 3;
  optional Address addressMailing = 4;
}

message Organization {
  string id = 1;                      // id
  optional Organization parent = 2;   // родительская организация
  string name = 3;                    // Наименование организации
  string shortName = 4;               // Сокращенное наименование
  string kpp = 5;                     // КПП
  string inn = 6;                     // ИНН
  optional string note = 7;           // заметка
  optional string okpo = 8;           // ОКПО
  optional string oktmo = 9;          // ОКТМО
  optional string okved = 10;         // ОКВЭД
  optional string fioDirector = 11;   // ФИО руководителя
  optional string addressLegal = 12;  // Юридический адрес
  optional string addressActual = 13; // Фактический адрес
  optional string addressMailing = 14;// Почтовый адрес
  optional string managerActionReason = 15; //Основание действия руководителя
  bool isDeleted = 16;
  string ogrn = 17;                   // ОГРН
}

message OrganizationFilter {
  optional bool isDeleted = 1;    // по умолчанию считаем isDeleted = false
  optional string name = 2;
  optional string inn = 3;
  optional string kpp = 4;
}

message OrganizationListRequest {
  optional common.v1.PaginationRequest pagination = 1;
  optional OrganizationFilter filter = 2;
}

message OrganizationResult {
  optional common.v1.PaginationResponse pagination = 1;
  optional OrganizationFilter filter = 2;
  repeated Organization organization = 3;
}

message OrganizationListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    OrganizationResult result = 2;
  }
}

message OrganizationListForProjectRequest {
  optional common.v1.PaginationRequest pagination = 1;
  string projectId = 2;
}

message OrganizationInProjectRequest {
  string organizationId = 1;
  string projectId = 2;
}

message Address {
  optional string id = 1;                   // id
  string name = 2;                          // Наименование адреса организации
  optional uint32 index = 3;                // индекс
  optional string country = 4;              // страна
  string region = 5;                        // область
  optional string district = 6;             // район
  string city = 7;                          // город
  optional string street = 8;               // улица
  string house = 9;                         // дом
  optional string buildingOrHousing = 10;   // строение
  optional string officeOrRoom = 11;        // офис комната
  optional double longitude = 12;           // долгота
  optional double latitude = 13;            // широта
  optional string comment = 14;             // комментарий
  optional uint64 oktmo = 15;               // ОКТМО
  optional string fiac = 16;                // ФИАС
  bool isDeleted = 17;
}

enum ContactType {
  CT_PHONE = 0;
  CT_EMAIL = 1;
}

message Contact{
  optional string id = 1;                   // id
  string organizationId = 2;                // id организации
  ContactType type = 3;                     // тип контакта
  string value = 4;                         // значение
  bool isDeleted = 5;
}

message AddressFilter {
  optional string organizationId = 1;
  optional string city = 2;
  optional string street = 3;
  optional bool isDeleted = 4;    // по умолчанию считаем isDeleted = false
}

message AddressListRequest{
  optional common.v1.PaginationRequest pagination = 1;
  optional AddressFilter filters = 2;
}

message AddressListResult{
  optional common.v1.PaginationResponse pagination = 1;
  optional AddressFilter filters = 2;
  repeated Address address = 3;
}

message AddressListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    AddressListResult result = 2;
  }
}

message ContactFilter{
  optional string organizationId = 1;
  optional bool isDeleted = 2;    // по умолчанию считаем isDeleted = false
}

message ContactListRequest{
  optional common.v1.PaginationRequest pagination = 1;
  optional ContactFilter filters = 2;
}

message ContactListResult {
  optional common.v1.PaginationResponse pagination = 1;
  optional ContactFilter filters = 2;
  repeated Contact contacts = 3;
}

message ContactListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    ContactListResult result = 2;
  }
}

message ByIdRequest {
  string id = 1;
  optional int64 version = 2;
}

enum AddressType {
  AT_LEGAL = 0; //Юридический
  AT_ACTUAL = 1; //Физический
  AT_MAILING = 2; //Почтовый
}

message AddressCreateOrDelete {
  Address address = 1;
  AddressType type = 2;
  string organizationId = 3;
}

message AddressResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Address result = 2;
  }
}

message ContactResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Contact result = 2;
  }
}

message ByIdWithPaginationRequest {
  string id = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message OrganizationHintRequest {
  string inn = 1;
  optional string kpp = 2;
}

message OrganizationHintResponse {
  oneof response {
    common.v1.OperationError error = 1;
    OrganizationHintList result = 2;
  }
}

message OrganizationHintList {
  repeated OrganizationHint organizationHint = 1;
}

message OrganizationHint {
  string name = 1;                          // Наименование организации
  string shortName = 2;                     // Сокращенное наименование
  string kpp = 3;                           // КПП
  string inn = 4;                           // ИНН
  optional string note = 5;                 // заметка
  optional string okpo = 6;                 // ОКПО
  optional string oktmo = 7;                // ОКТМО
  optional string okved = 8;                // ОКВЭД
  optional string fioDirector = 9;          // ФИО руководителя
  optional string managerActionReason = 10; // Основание действия руководителя
  string ogrn = 11;                         // ОГРН
  optional AddressHint addressLegalHint = 12;
  repeated ContactHint contactHints = 13;
}

message ContactHint {
  ContactType type = 3;                     // тип контакта
  string value = 4;
}

message AddressHint {
  optional uint32 index = 1;                // индекс
  optional string country = 2;              // страна
  string region = 3;                        // область
  optional string district = 4;             // район
  string city = 5;                          // город
  optional string street = 6;               // улица
  string house = 7;                         // дом
  optional string buildingOrHousing = 8;    // строение
  optional string officeOrRoom = 9;         // офис комната
  optional double longitude = 10;           // долгота
  optional double latitude = 11;            // широта
  optional uint64 oktmo = 12;               // ОКТМО
  optional string fiac = 14;                // ФИАС
}

// Contract definitions
enum ContractStatus {
  CS_DRAFT = 0;       // Черновик
  CS_ACTIVE = 1;      // Активный
  CS_EXPIRING = 2;    // Истекает
  CS_COMPLETED = 3;   // Завершен
  CS_TERMINATED = 4;  // Расторгнут
}

enum ContractType {
  CT_SYSTEM_RULES = 0;    // Правила системы
  CT_SERVICE = 1;         // Сервисный договор
  CT_TRANSPORT = 2;       // Транспортный договор
  CT_PROCESSING = 3;      // Процессинговый договор
}

enum ProjectType {
  PT_TRANSPORT_SYSTEM = 0; // Транспортная система
  PT_METRO_SYSTEM = 1;     // Система метро
  PT_BUS_SYSTEM = 2;       // Автобусная система
  PT_TAXI_SYSTEM = 3;      // Система такси
}

enum OrganizationRole {
  OR_OPERATOR = 0;           // Оператор
  OR_CARRIER = 1;            // Перевозчик
  OR_PROCESSING_CENTER = 2;  // Процессинговый центр
  OR_CONTRACTOR = 3;         // Контрагент
  OR_PARTNER = 4;            // Партнер
}

message Contract {
  string id = 1;                              // ID договора
  string projectCode = 2;                     // Код проекта
  string projectName = 3;                     // Название проекта
  ProjectType projectType = 4;                // Тип проекта
  ContractType contractType = 5;              // Тип договора
  string contractName = 6;                    // Название договора
  string contractNumber = 7;                  // Номер договора
  google.protobuf.Timestamp signatureDate = 8;    // Дата подписания
  google.protobuf.Timestamp conclusionDate = 9;   // Дата заключения
  google.protobuf.Timestamp completionDate = 10;  // Дата завершения
  ContractStatus status = 11;                 // Статус договора
  optional string externalId1C = 12;         // Внешний ID в 1С
  optional string description = 13;          // Описание договора
  optional double totalAmount = 14;          // Общая сумма договора
  optional string currency = 15;             // Валюта (по умолчанию RUB)
  optional int32 paymentTerms = 16;          // Условия оплаты (дни)
  optional double vatRate = 17;              // Ставка НДС (%)
  bool isDeleted = 18;                       // Удален
  google.protobuf.Timestamp createdDate = 19;     // Дата создания
  optional google.protobuf.Timestamp lastSyncDate = 20; // Дата последней синхронизации
}

message ContractOrganization {
  string id = 1;                              // ID связи
  string contractId = 2;                      // ID договора
  string organizationId = 3;                  // ID организации
  string organizationName = 4;                // Название организации
  OrganizationRole role = 5;                  // Роль организации
  optional string roleDescription = 6;       // Описание роли
  google.protobuf.Timestamp activeFrom = 7;       // Активна с
  optional google.protobuf.Timestamp activeTill = 8;    // Активна до
  bool isDeleted = 9;                         // Удалена
}

message ContractWithOrganizations {
  Contract contract = 1;                      // Договор
  repeated ContractOrganization organizations = 2; // Организации в договоре
}

message ContractFilter {
  optional bool isDeleted = 1;               // По умолчанию false
  optional string projectCode = 2;          // Код проекта
  optional string contractNumber = 3;       // Номер договора
  optional string contractName = 4;         // Название договора
  optional ContractStatus status = 5;       // Статус договора
  optional ContractType contractType = 6;   // Тип договора
  optional ProjectType projectType = 7;     // Тип проекта
  optional string organizationId = 8;       // ID организации
}

message ContractListRequest {
  optional common.v1.PaginationRequest pagination = 1;
  optional ContractFilter filter = 2;
}

message ContractResult {
  optional common.v1.PaginationResponse pagination = 1;
  optional ContractFilter filter = 2;
  repeated Contract contracts = 3;
}

message ContractListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ContractResult result = 2;
  }
}

message ContractResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Contract result = 2;
  }
}

message ContractsByProjectRequest {
  string projectCode = 1;                    // Код проекта
  optional common.v1.PaginationRequest pagination = 2;
}

// Payment Method definitions
enum PaymentMethodType {
  PMT_BANK_CARD = 0;                        // Банковская карта
  PMT_CASH = 1;                             // Наличные денежные средства
  PMT_TROIKA_SINGLE = 2;                    // Транспортная карта "Тройка" (разовые поездки)
  PMT_TROIKA_SUBSCRIPTION = 3;              // Транспортная карта "Тройка" (абонемент)
  PMT_MPC_DISCOUNT = 4;                     // МПК Дисконт
  PMT_MPC_SOCIAL = 5;                       // МПК Социальная карта
  PMT_MPC_SCHOOL = 6;                       // МПК "Карта Школьника"
  PMT_MPC_STUDENT_SINGLE = 7;               // МПК "Карта Студента" (разовые поездки)
  PMT_MPC_STUDENT_SUBSCRIPTION = 8;         // МПК "Карта Студента" (абонемент)
  PMT_TC_RESIDENT = 9;                      // ТК Карта жителя
  PMT_MOBILE_BC = 10;                       // Мобильное приложение БК
  PMT_MOBILE_VIRTUAL_TC = 11;               // Мобильное приложение Виртуальная ТК
  PMT_MOBILE_SBP = 12;                      // Мобильное приложение СБП
  PMT_REGIONAL_TC = 13;                     // Транспортная карта региона
  PMT_SOCIAL_TC = 14;                       // Социальная транспортная карта
  PMT_OTHER_CARDS = 15;                     // Иные карты, предусмотренные договором
}

message ContractPaymentMethod {
  string id = 1;                            // ID средства оплаты
  string contractId = 2;                    // ID договора
  PaymentMethodType methodType = 3;         // Тип средства оплаты
  string code = 4;                          // Код типа (для совместимости)
  string name = 5;                          // Название средства оплаты
  optional string description = 6;          // Описание
  bool isActive = 7;                        // Активно
  bool isDeleted = 8;                       // Удалено
  google.protobuf.Timestamp createdDate = 9;     // Дата создания
  optional google.protobuf.Timestamp lastSyncDate = 10; // Дата последней синхронизации
  optional string externalId = 11;         // Внешний ID
}

message PaymentMethodFilter {
  optional bool isDeleted = 1;              // По умолчанию false
  optional string contractId = 2;          // ID договора
  optional PaymentMethodType methodType = 3; // Тип средства оплаты
  optional string code = 4;                // Код типа
  optional bool isActive = 5;              // Активность
}

message PaymentMethodListRequest {
  optional common.v1.PaginationRequest pagination = 1;
  optional PaymentMethodFilter filter = 2;
}

message PaymentMethodResult {
  optional common.v1.PaginationResponse pagination = 1;
  optional PaymentMethodFilter filter = 2;
  repeated ContractPaymentMethod paymentMethods = 3;
}

message PaymentMethodListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    PaymentMethodResult result = 2;
  }
}

message PaymentMethodResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ContractPaymentMethod result = 2;
  }
}

message PaymentMethodsByContractRequest {
  string contractId = 1;                    // ID договора
  optional common.v1.PaginationRequest pagination = 2;
  optional bool includeInactive = 3;       // Включить неактивные (по умолчанию false)
}