syntax = "proto3";

package ru.sbertroika.common.manifest.v1.pasiv;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.manifest.v1.pasiv";

import 'common-manifest-core.proto';

message ManifestPasivDict {
  repeated Organization organization = 1;
}

message ManifestPasiv {
  repeated core.TkpFeature features = 1;
  ManifestPasivDict dict = 2;
}

message Organization {
  string id = 1;                    // Идентификатор организации
  string name = 2;                  // Полное наименование организации
  string shortName = 3;             // Краткое наименование организации
  string inn = 4;                   // ИНН
  string kpp = 5;                   // КПП
  string address = 6;               // Адрес организации
  string paymentPlace = 7;          // Платежное место
}