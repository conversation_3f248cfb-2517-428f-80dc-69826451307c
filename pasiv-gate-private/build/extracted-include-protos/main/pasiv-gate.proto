syntax = "proto3";

package ru.sbertroika.pasiv.gate.v1;

import "google/protobuf/empty.proto";
import "common.proto";
import "common-manifest.proto";
import "common-manifest-pasiv.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.pasiv.gate.v1";

service PASIVGateService {
  // Запрос манифеста
  rpc getManifest(common.manifest.v1.ManifestRequest) returns (ManifestResponse);
}

message ManifestResponse {
  oneof response {
    common.v1.OperationError error = 1;
    common.manifest.v1.pasiv.ManifestPasiv manifest = 2;
  }
}
