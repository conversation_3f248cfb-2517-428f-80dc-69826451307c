/ Header Record For PersistentHashMapValueStoragec bru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineImplBaseC Borg.springframework.data.repository.kotlin.CoroutineCrudRepositoryC Borg.springframework.data.repository.kotlin.CoroutineCrudRepositoryC Borg.springframework.data.repository.kotlin.CoroutineCrudRepositoryC Borg.springframework.data.repository.kotlin.CoroutineCrudRepositoryC Borg.springframework.data.repository.kotlin.CoroutineCrudRepository> =ru.sbertroika.pasiv.gate.output.repository.AbstractRepository> =ru.sbertroika.pasiv.gate.output.repository.AbstractRepository> =ru.sbertroika.pasiv.gate.output.repository.AbstractRepository java.io.Serializable kotlin.Enum8 7org.springframework.data.r2dbc.convert.EnumWriteSupport java.io.Serializable kotlin.Enum8 7org.springframework.data.r2dbc.convert.EnumWriteSupport java.io.Serializable java.io.Serializable< ;ru.sbertroika.pasiv.gate.output.service.OrganizationService8 7ru.sbertroika.pasiv.gate.output.service.ManifestService7 6ru.sbertroika.pasiv.gate.output.service.ContactService6 5ru.sbertroika.pasiv.gate.output.service.DaDataService7 6ru.sbertroika.pasiv.gate.output.service.AddressService okhttp3.Interceptor okhttp3.Interceptor okhttp3.Interceptor4 3ru.sbertroika.pasiv.gate.output.dadata.TokenStorage kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum