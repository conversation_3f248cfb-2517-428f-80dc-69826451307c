:<EMAIL>@<EMAIL><ru.sbertroika.pasiv.gate.output.repository.ContactRepositoryAru.sbertroika.pasiv.gate.output.repository.OrganizationRepository<ru.sbertroika.pasiv.gate.output.repository.AddressRepository/ru.sbertroika.pasiv.gate.output.model.ContactPK1ru.sbertroika.pasiv.gate.output.model.ContactType:ru.sbertroika.pasiv.gate.output.model.ContactTypeConverter;ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationPK?ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatusHru.sbertroika.pasiv.gate.output.model.ProjectOrganizationStatusConverter/ru.sbertroika.pasiv.gate.output.model.AddressPK4ru.sbertroika.pasiv.gate.output.model.OrganizationPK?ru.sbertroika.pasiv.gate.output.service.OrganizationServiceImpl;ru.sbertroika.pasiv.gate.output.service.ManifestServiceImpl:ru.sbertroika.pasiv.gate.output.service.ContactServiceImpl9ru.sbertroika.pasiv.gate.output.service.DaDataServiceImpl:<EMAIL>+ru.sbertroika.pasiv.gate.dto.AddressTypeDto+ru.sbertroika.pasiv.gate.dto.ContactTypeDto.ru.sbertroika.pasiv.gate.dto.ContractStatusDto,ru.sbertroika.pasiv.gate.dto.ContractTypeDto+ru.sbertroika.pasiv.gate.dto.ProjectTypeDto0ru.sbertroika.pasiv.gate.dto.OrganizationRoleDto1ru.sbertroika.pasiv.gate.dto.PaymentMethodTypeDto                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      