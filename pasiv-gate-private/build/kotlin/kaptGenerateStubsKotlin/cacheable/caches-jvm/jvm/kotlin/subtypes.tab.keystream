bru.sbertroika.pasiv.gate.v1.PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineImplBaseBorg.springframework.data.repository.kotlin.CoroutineCrudRepository=ru.sbertroika.pasiv.gate.output.repository.AbstractRepositoryjava.io.Serializablekotlin.Enum7org.springframework.data.r2dbc.convert.EnumWriteSupport;ru.sbertroika.pasiv.gate.output.service.OrganizationService7ru.sbertroika.pasiv.gate.output.service.ManifestService6ru.sbertroika.pasiv.gate.output.service.ContactService5ru.sbertroika.pasiv.gate.output.service.DaDataService6ru.sbertroika.pasiv.gate.output.service.AddressServiceokhttp3.Interceptor3ru.sbertroika.pasiv.gate.output.dadata.TokenStorage                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   