FROM gradle:8.5.0-jdk17-alpine as builder

ARG MAVEN_USER
ARG MAVEN_PASSWORD

ENV MAVEN_USER=${MAVEN_USER}
ENV MAVEN_PASSWORD=${MAVEN_PASSWORD}

RUN apk add gcompat
WORKDIR /build
COPY . .

RUN chmod +x ./gradlew

RUN ./gradlew --no-daemon \
    -PmavenUser=$MAVEN_USER  \
    -PmavenPassword=$MAVEN_PASSWORD  \
    :pasiv-gate:bootJar -i

FROM openjdk:17-alpine
COPY --from=builder /build/pasiv-gate/build/libs/pasiv-gate-*.jar ./pasiv-gate.jar

EXPOSE 5000
EXPOSE 5005
EXPOSE 8080

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "pasiv-gate.jar"]