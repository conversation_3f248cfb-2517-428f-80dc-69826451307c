package ru.sbertroika.pasiv.gate.input

import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import ru.sbertroika.common.v1.PaginationRequest
import ru.sbertroika.pasiv.gate.v1.*
import java.util.concurrent.TimeUnit

class PASIVGatePrivateServiceGrpcTest {

    companion object {
        private val server = "localhost:5005"
        private val PROJECT_ID = "a0a6edd4-efe2-46e2-98a1-159cca863368"
        private val ADDRESS_ID = "51542497-ac7f-42f2-aa74-014ab962e187"
        private val CONTACT_ID = "7dff6a59-6a0f-414d-b102-2e8259d1421a"
        private val ORG_ID = "8d8244d1-125e-492d-a03e-99e4ba09f9bc"
    }

    @Test
    fun createOrganization(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = OrganizationWithAddresses.newBuilder().setOrganization(
                Organization.newBuilder()
                    .setName("ООО СберТройка")
                    .setOgrn("fsdfsf")
                .build()
            ).setAddressLegal(
                Address.newBuilder()
                    .setName("ООО СберТройка")
                    .setRegion("")
                    .setDistrict("")
                    .setCity("г. Москва")
                    .setStreet("муниципальный округ Мещанский вн.тер.г., ул. Щепкина")
                    .setHouse("д. 23")
                    .setIsDeleted(false)
                    .build()
            ).build()
            val response = client.createOrganization(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }
    @Test
    fun updateOrganization(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request =
                Organization.newBuilder()
                .setId(ORG_ID)
                .setName("ООО СберТройка upd")
                .setAddressLegal(ADDRESS_ID)
                    .setParent(Organization.newBuilder().setId("30d4f9b1-4009-4d94-9793-f4a0f9a3ff4d"))
                .build()
            val response = client.updateOrganization(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getOrganizationListTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = OrganizationListRequest.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                    .setLimit(10)
                    .setPage(0))
                .build()
            val response = client.organizationList(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getAddressListTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = AddressListRequest.newBuilder()
                .setFilters(AddressFilter.newBuilder().setOrganizationId(ORG_ID))
                .setPagination(PaginationRequest.newBuilder()
                    .setLimit(10)
                    .setPage(0))
                .build()
            val response = client.addressList(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getContactListTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ContactListRequest.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                    .setLimit(10)
                    .setPage(0))
                .build()
            val response = client.contactList(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun addAddress(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = AddressCreateOrDelete.newBuilder()
                .setOrganizationId(ORG_ID)
                .setType(AddressType.AT_ACTUAL)
                .setAddress(
                    Address.newBuilder()
                        .setName("ООО СберТройка")
                        .setRegion("")
                        .setDistrict("")
                        .setCity("г. Москва")
                        .setStreet("муниципальный округ Мещанский вн.тер.г., ул. Щепкина")
                        .setHouse("д. 23")
                        .setIsDeleted(false)
                        .build()
            ).build()
            val response = client.createAddress(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun updateAddress(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = AddressCreateOrDelete.newBuilder()
                .setOrganizationId(ORG_ID)
                .setType(AddressType.AT_ACTUAL)
                .setAddress(
                    Address.newBuilder()
                        .setId(ADDRESS_ID)
                        .setName("ООО СберТройка upd")
                        .setRegion("")
                        .setDistrict("")
                        .setCity("г. Москва")
                        .setStreet("муниципальный округ Мещанский вн.тер.г., ул. Щепкина")
                        .setHouse("д. 24")
                        .setIsDeleted(false)
                        .build()
                ).build()
            val response = client.updateAddress(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun addOrganizationInProjectTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)

            val requestList = OrganizationListRequest.newBuilder()
                .build()
            val responseList = client.organizationList(requestList, KeycloakUtils.metadata())
            Assertions.assertFalse(responseList.hasError())
            Assertions.assertTrue(responseList.hasResult())
            Assertions.assertTrue(responseList.result.organizationList != null && responseList.result.organizationList.isNotEmpty())


            val request = OrganizationInProjectRequest.newBuilder()
                .setOrganizationId(responseList.result.organizationList.last().id)
                .setProjectId(PROJECT_ID)
                .build()
            val response = client.addOrganizationInProject(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun removeOrganizationInProject(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)


            val requestList = OrganizationListRequest.newBuilder()
                .build()
            val responseList = client.organizationList(requestList, KeycloakUtils.metadata())
            Assertions.assertFalse(responseList.hasError())
            Assertions.assertTrue(responseList.hasResult())
            Assertions.assertTrue(responseList.result.organizationList != null && responseList.result.organizationList.isNotEmpty())

            val request = OrganizationInProjectRequest.newBuilder()
                .setOrganizationId(responseList.result.organizationList.last().id)
                .setProjectId(PROJECT_ID)
                .build()
            val response = client.removeOrganizationInProject(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun organizationListForProjectTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = OrganizationListForProjectRequest.newBuilder()
                .setProjectId(PROJECT_ID)
                .setPagination(PaginationRequest.newBuilder().setPage(0).setLimit(10))
                .build()
            val response = client.organizationListForProject(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getOrganization(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(ORG_ID)
                .build()
            val response = client.organizationById(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getAddress(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(ADDRESS_ID)
                .build()
            val response = client.addressById(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun createContact(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = Contact.newBuilder()
                .setIsDeleted(false)
                .setOrganizationId(ORG_ID)
                .setValue("<EMAIL>")
                .setType(ContactType.CT_EMAIL)
                .build()
            val response = client.createContact(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getContact(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(CONTACT_ID)
                .build()
            val response = client.contactById(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getAddressHistory(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdWithPaginationRequest.newBuilder()
                .setId(ADDRESS_ID)
                .setPagination(PaginationRequest.newBuilder().setPage(0).setLimit(10))
                .build()
            val response = client.addressHistoryById(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getContactHistory(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdWithPaginationRequest.newBuilder()
                .setId(CONTACT_ID)
                .setPagination(PaginationRequest.newBuilder().setPage(0).setLimit(10))
                .build()
            val response = client.contactHistoryById(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun getOrganizationHistory(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdWithPaginationRequest.newBuilder()
                .setId(ORG_ID)
                .setPagination(PaginationRequest.newBuilder().setPage(0).setLimit(10))
                .build()
            val response = client.organizationHistoryById(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun deleteOrganizationTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(ORG_ID)
                .build()
            val response = client.deleteOrganization(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun recoverOrganizationTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(ORG_ID)
                .build()
            val response = client.recoverOrganization(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun deleteAddressTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(ADDRESS_ID)
                .build()
            val response = client.deleteAddress(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun recoverAddressTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(ADDRESS_ID)
                .build()
            val response = client.recoverAddress(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun deleteContactTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(CONTACT_ID)
                .build()
            val response = client.deleteContact(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun recoverContactTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = ByIdRequest.newBuilder()
                .setId(CONTACT_ID)
                .build()
            val response = client.recoverContact(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun organizationHintByINNTest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGatePrivateServiceGrpcKt.PASIVGatePrivateServiceCoroutineStub(channel)
            val request = OrganizationHintRequest.newBuilder()
                .setInn("9702027017")
                .build()
            val response = client.organizationHintByINN(request, KeycloakUtils.metadata())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }
}