package ru.sbertroika.pasiv.gate.input

import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import ru.sbertroika.common.manifest.v1.manifestRequest
import ru.sbertroika.pasiv.gate.v1.PASIVGateServiceGrpcKt
import java.util.concurrent.TimeUnit

class PASIVGateServiceGrpcTest {

    companion object {
        private val server = "localhost:5000"
    }

    @Test
    fun getManifest(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = PASIVGateServiceGrpcKt.PASIVGateServiceCoroutineStub(channel)
            val response = client.getManifest(
                manifestRequest {
                    projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                }
            )
            println("response: $response")
            assertFalse(response.hasError())
            assertNotNull(response.manifest)
        } catch (ex: Exception) {
            println("ex: $ex")
            fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }
}