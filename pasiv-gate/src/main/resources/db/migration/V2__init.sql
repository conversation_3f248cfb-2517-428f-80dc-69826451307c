create table public.organization
(
    o_id                 uuid      default gen_random_uuid() not null,
    o_version            integer   default 1                 not null,
    o_version_created_at timestamp default now()             not null,
    o_version_created_by uuid                                not null,
    o_name               varchar(64)                         not null,
    o_short_name         varchar(32)                         not null,
    o_inn                varchar(32)                         not null,
    o_kpp                varchar(32)                         not null,
    o_ogrn               varchar(32)                         not null,
    o_address            varchar(255)                        not null,
    o_payment_place      varchar(255)                        not null,
    primary key (o_id, o_version)
);

alter table public.organization
    owner to pasiv;

CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;
CREATE INDEX idx_organization_by_inn_and_last_version ON public.organization (o_inn, o_version DESC);
CREATE INDEX idx_organization_by_name ON public.organization USING gin (o_name);
CREATE INDEX idx_organization_by_ogrp ON public.organization (o_ogrn);
CREATE INDEX idx_organization_by_kpp ON public.organization (o_kpp);
CREATE INDEX idx_organization_by_inn ON public.organization (o_inn);

INSERT INTO public.organization
VALUES ('f4b9fa1a-d95d-4288-a044-32ff3d8ce463',
        1,
        current_timestamp,
        '00000000-f04d-40dd-8829-598f62ad47f5',
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "СБЕРТРОЙКА"',
        'Сбертройка, ООО',
        '9702027017',
        '770201001',
        '1207700477820',
        'г. Москва, муниципальный округ Мещанский вн.тер.г., ул. Щепкина, д. 51/4, стр. 2, ЭТАЖ 5',
        'Разъездная торговля');

CREATE TABLE public.project_function
(
    pf_id                 uuid      default gen_random_uuid() not null,
    pf_version            integer   default 1                 not null,
    pf_version_created_at timestamp default now()             not null,
    pf_version_created_by uuid,
    pf_type               varchar(150)                        not null,
    pf_project_id         uuid                                not null,
    pf_project_version    integer                             not null,
    pf_active_from        timestamp default now()             not null,
    pf_active_till        timestamp,
    pf_status             integer   default 0                 not null,
    primary key (pf_id, pf_version)
);

CREATE TYPE project_organization_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');

CREATE TABLE public.project_organization
(
    po_id                 uuid      default gen_random_uuid() not null,
    po_version            integer   default 1                 not null,
    po_version_created_at timestamp default now()             not null,
    po_version_created_by uuid                                not null,
    po_project_id         uuid                                not null,
    po_organization_id    uuid                                not null,
    po_active_from        timestamp default now()             not null,
    po_active_till        timestamp,
    po_status             project_organization_status         not null,
    primary key (po_id, po_version)
);

INSERT INTO public.project_organization
VALUES (gen_random_uuid(),
        1,
        current_timestamp,
        '00000000-f04d-40dd-8829-598f62ad47f5',
        '43db9de7-72ec-4eae-8978-8aef1c46873a',
        'f4b9fa1a-d95d-4288-a044-32ff3d8ce463',
        current_timestamp,
        null,
        'ACTIVE'::project_organization_status);

CREATE MATERIALIZED VIEW active_organization_by_project AS
SELECT res.*
FROM (select o1.*, po1.po_project_id as project_id
      from project_organization po1
               left join organization o1 on po1.po_organization_id = o1.o_id
      where po1.po_status = 'ACTIVE'::project_organization_status) res;
