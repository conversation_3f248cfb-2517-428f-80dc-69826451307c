-- добавляем новые столбцы
ALTER TABLE public.organization ADD COLUMN o_parent_id uuid;
ALTER TABLE public.organization ADD COLUMN o_note CHAR(128);
ALTER TABLE public.organization ADD COLUMN o_okpo CHAR(128);
ALTER TABLE public.organization ADD COLUMN o_oktmo CHAR(128);
ALTER TABLE public.organization ADD COLUMN o_okved CHAR(128);
ALTER TABLE public.organization ADD COLUMN o_fio_director CHAR(128);
ALTER TABLE public.organization ADD COLUMN o_manager_action_reason CHAR(256);
ALTER TABLE public.organization ADD COLUMN o_address_legal_id uuid;
ALTER TABLE public.organization ADD COLUMN o_address_actual_id uuid;
ALTER TABLE public.organization ADD COLUMN o_address_mailing_id uuid;
ALTER TABLE public.organization ADD COLUMN o_is_deleted bool default false not null;
-- создаем индексы
CREATE INDEX idx_organization_address_legal_id ON public.organization(o_address_legal_id);
CREATE INDEX idx_organization_address_actual_id ON public.organization(o_address_actual_id);
CREATE INDEX idx_organization_address_mailing_id ON public.organization(o_address_mailing_id);

-- создаем таблицу адресов
CREATE TABLE  public.address (
     a_id                 uuid      default gen_random_uuid() not null,
     a_version            integer   default 1                 not null,
     a_version_created_at timestamp default now()             not null,
     a_version_created_by uuid                                not null,
     a_name               varchar(64)                         not null,
     a_index              integer,
     a_country            varchar(64),
     a_region             varchar(64)                         not null,
     a_district           varchar(64)                         not null,
     a_city               varchar(64)                         not null,
     a_street             varchar(64)                         not null,
     a_house              varchar(64)                         not null,
     a_building_or_housing varchar(64),
     a_office_or_room     varchar(64),
     a_longitude          float8,
     a_latitude           float8,
     a_comment            varchar(128),
     a_oktmo              integer,
     a_fiac               integer,
     a_is_deleted         bool      default false             not null
);


-- функция которая возвращает город из массива строк
create or replace function getCity(address_array text[]) returns text as $$
DECLARE
    x text;
begin
    FOREACH x IN ARRAY $1
        LOOP
            if x like '%г. %' then
                return x;
            end if;
        END LOOP;
    return '';
end;
$$ LANGUAGE plpgsql;

-- функция которая возвращает улицу из массива строк
create or replace function getStreet(address_array text[]) returns text as $$
DECLARE
    x text;
begin
    FOREACH x IN ARRAY $1
        LOOP
            if x like '%ул. %' or x like '%пр-т. %' then
                return x;
            end if;
        END LOOP;
    return '';
end;
$$ LANGUAGE plpgsql;

-- функция которая возвращает дом из массива строк
create or replace function getHouse(address_array text[]) returns text as $$
DECLARE
    x text;
begin
    FOREACH x IN ARRAY $1
        LOOP
            if x like '%д. %' then
                return x;
            end if;
        END LOOP;
    return '';
end;
$$ LANGUAGE plpgsql;

-- функция которая возвращает строение из массива строк
create or replace function getBuilding(address_array text[]) returns text as $$
DECLARE
    x text;
begin
    FOREACH x IN ARRAY $1
        LOOP
            if x like '%стр. %' or x like '%к. %' then
                return x;
            end if;
        END LOOP;
    return '';
end;
$$ LANGUAGE plpgsql;

-- функция которая возвращает квартиру из массива строк
create or replace function getOfficeOrRoom(address_array text[]) returns text as $$
DECLARE
begin
    return address_array[array_upper(address_array, 1)];
end;
$$ LANGUAGE plpgsql;

-- добавляем временный столбец для миграции адреса из организации в таблицу
ALTER TABLE public.address ADD COLUMN a_org_id uuid;

-- миграция адреса из организации в таблицу
WITH result AS (
    INSERT INTO public.address (a_id, a_version, a_version_created_at,a_version_created_by, a_name,a_region,a_district, a_city, a_street, a_house, a_building_or_housing, a_office_or_room, a_is_deleted, a_org_id)
        SELECT
            gen_random_uuid() as a_id,
            1 as a_version,
            now() as a_version_created_at,
            '00000000-f04d-40dd-8829-598f62ad47f5' as a_version_created_by,
            ('Адрес'  || ' ' || address.o_name) as a_name,
            '' as a_region, '' as a_district,
            getCity(address.ad) as a_city,
            getStreet(address.ad) as a_street,
            getHouse(address.ad) as a_house,
            getBuilding(address.ad) as a_building_or_housing,
            getOfficeOrRoom(address.ad) as a_office_or_room,
            false as a_is_deleted,
            o_id as a_org_id
        FROM (
                 SELECT string_to_array(org.o_address, ',') as ad, org.o_name, org.o_id FROM public.organization org
             ) address RETURNING * )
UPDATE public.organization SET o_address_legal_id = ress.a_id FROM (select  * from result) as ress where o_id = ress.a_org_id;
-- удаляем временную колонку
ALTER TABLE public.address DROP COLUMN a_org_id;


--удаляем старую вью
DROP MATERIALIZED VIEW active_organization_by_project;

--удаляем индекс
DROP INDEX idx_organization_by_ogrp;

--удаляем старые колонки в организации
ALTER TABLE public.organization DROP COLUMN o_ogrn;
ALTER TABLE public.organization DROP COLUMN o_address;
ALTER TABLE public.organization DROP COLUMN o_payment_place;

--пересоздаем вью для проектов с организациями
CREATE MATERIALIZED VIEW active_organization_by_project AS
SELECT res.*
FROM (select o1.*, po1.po_project_id as project_id
      from project_organization po1
               left join organization o1 on po1.po_organization_id = o1.o_id
      where po1.po_status = 'ACTIVE'::project_organization_status) res;

--делаем бридический адрес обязательный
ALTER TABLE public.organization ALTER COLUMN o_address_legal_id SET NOT NULL;

--создаем контакт

CREATE TYPE contact_type AS ENUM ('EMAIL', 'PHONE');
CREATE TABLE public.contact (
    c_id                 uuid      default gen_random_uuid() not null,
    c_organization_id    uuid,
    c_version            integer   default 1                 not null,
    c_version_created_at timestamp default now()             not null,
    c_version_created_by uuid                                not null,
    c_type               contact_type                        not null,
    c_value              varchar(64)                         not null,
    c_is_deleted         bool      default false             not null
);

CREATE INDEX idx_contact_organization_id ON public.contact(c_organization_id);

--создаем вьюхи для последних версий
CREATE MATERIALIZED VIEW active_organization AS
SELECT res.*, org.o_name as o_parent_name
    FROM (SELECT o.* FROM organization o INNER JOIN (SELECT o_id, MAX(o_version) vers FROM organization GROUP BY o_id) o2
            ON o.o_id = o2.o_id AND o.o_version = o2.vers) res LEFT JOIN organization org ON org.o_id = res.o_parent_id;

CREATE FUNCTION refresh_mv_active_organization() RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW active_organization;
    RETURN null;
END $$;

CREATE TRIGGER refresh_mv_active_organization
    AFTER insert OR update OR delete OR truncate
    ON organization
EXECUTE PROCEDURE refresh_mv_active_organization();


CREATE MATERIALIZED VIEW active_address AS
SELECT res.*
FROM (SELECT o.* FROM address o
    INNER JOIN (SELECT a_id, MAX(a_version) vers FROM address GROUP BY a_id) o2
        ON o.a_id = o2.a_id AND o.a_version = o2.vers) res;

CREATE FUNCTION refresh_mv_active_address() RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW active_address;
    RETURN null;
END $$;

CREATE TRIGGER refresh_mv_active_address
    AFTER insert OR update OR delete OR truncate
    ON address
EXECUTE PROCEDURE refresh_mv_active_address();



CREATE MATERIALIZED VIEW active_contact AS
SELECT res.*
FROM (SELECT o.* FROM contact o
    INNER JOIN (SELECT c_id, MAX(c_version) vers FROM contact GROUP BY c_id) o2
        ON o.c_id = o2.c_id AND o.c_version = o2.vers) res;

CREATE FUNCTION refresh_mv_active_contact() RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW active_contact;
    RETURN null;
END $$;

CREATE TRIGGER refresh_mv_active_contact
    AFTER insert OR update OR delete OR truncate
    ON contact
EXECUTE PROCEDURE refresh_mv_active_contact();