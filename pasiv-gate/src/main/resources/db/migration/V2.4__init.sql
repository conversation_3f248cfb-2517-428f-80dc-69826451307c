DROP MATERIALIZED VIEW IF EXISTS active_organization;
DROP MATERIALIZED VIEW IF EXISTS active_organization_by_project;
DROP MATERIALIZED VIEW IF EXISTS active_address;
DROP MATERIALIZED VIEW IF EXISTS active_contact;

DROP FUNCTION IF EXISTS refresh_mv_active_organization() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_active_organization_by_project() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_active_address() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_active_contact() CASCADE;

CREATE VIEW active_organization AS
SELECT res.*, org.o_name as o_parent_name
FROM (SELECT o.* FROM organization o
    INNER JOIN (SELECT o_id, MAX(o_version) vers FROM organization GROUP BY o_id) o2
                                                ON o.o_id = o2.o_id AND o.o_version = o2.vers) res
    LEFT JOIN organization org ON org.o_id = res.o_parent_id AND org.o_version = (SELECT MAX(ov.o_version) FROM organization ov WHERE ov.o_id = org.o_id);

--пересоздаем вью для проектов с организациями
CREATE VIEW active_organization_by_project AS
SELECT res.*
FROM (select o1.*, po1.po_project_id as project_id
      from project_organization po1
               left join organization o1 on po1.po_organization_id = o1.o_id
      where po1.po_status = 'ACTIVE'::project_organization_status) res;


CREATE VIEW active_address AS
SELECT res.*
FROM (SELECT o.* FROM address o
                          INNER JOIN (SELECT a_id, MAX(a_version) vers FROM address GROUP BY a_id) o2
                                     ON o.a_id = o2.a_id AND o.a_version = o2.vers) res;

CREATE VIEW active_contact AS
SELECT res.*
FROM (SELECT o.* FROM contact o
                          INNER JOIN (SELECT c_id, MAX(c_version) vers FROM contact GROUP BY c_id) o2
                                     ON o.c_id = o2.c_id AND o.c_version = o2.vers) res;



ALTER TABLE address ADD PRIMARY KEY (a_id, a_version);
ALTER TABLE contact ADD PRIMARY KEY (c_id, c_version);
CREATE INDEX idx_address_for_street ON address USING GIN (a_street gin_trgm_ops);
CREATE INDEX idx_address_for_city ON address USING GIN (a_city gin_trgm_ops);



