ALTER TABLE public.organization ADD COLUMN o_ogrn varchar(32) not null default '';

ALTER TABLE public.address ALTER COLUMN a_street DROP NOT NULL;
ALTER TABLE public.address ALTER COLUMN a_district DROP NOT NULL;

UPDATE public.organization SET o_ogrn='1207700477820' WHERE o_id='f4b9fa1a-d95d-4288-a044-32ff3d8ce463';

DROP MATERIALIZED VIEW active_organization;
DROP MATERIALIZED VIEW active_organization_by_project;

CREATE MATERIALIZED VIEW active_organization AS
SELECT res.*, org.o_name as o_parent_name
FROM (SELECT o.* FROM organization o INNER JOIN (SELECT o_id, MAX(o_version) vers FROM organization GROUP BY o_id) o2
                                                ON o.o_id = o2.o_id AND o.o_version = o2.vers) res LEFT JOIN organization org ON org.o_id = res.o_parent_id;

--пересоздаем вью для проектов с организациями
CREATE MATERIALIZED VIEW active_organization_by_project AS
SELECT res.*
FROM (select o1.*, po1.po_project_id as project_id
      from project_organization po1
               left join organization o1 on po1.po_organization_id = o1.o_id
      where po1.po_status = 'ACTIVE'::project_organization_status) res;