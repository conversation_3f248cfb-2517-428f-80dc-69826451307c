package ru.sbertroika.pasiv.gate.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.data.repository.query.Param
import ru.sbertroika.pasiv.gate.output.model.ProjectOrganization
import ru.sbertroika.pasiv.gate.output.model.ProjectOrganizationPK
import java.util.UUID

interface ProjectOrganizationRepository : CoroutineCrudRepository<ProjectOrganization, ProjectOrganizationPK> {

    @Query("SELECT * FROM project_organization o " +
            "INNER JOIN (" +
        "    SELECT po_id, MAX(po_version) vers " +
                "    FROM project_organization " +
                "    GROUP BY po_id " +
                ") o2 ON o.po_id = o2.po_id AND o.po_version = o2.vers " +
                " AND o.po_organization_id = :organization AND o.po_project_id = :project LIMIT 1")
    fun findByOrganizationIdAndProjectId(@Param("organization") organization: UUID, @Param("project") project: UUID): Flow<ProjectOrganization>
}