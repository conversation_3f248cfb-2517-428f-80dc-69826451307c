package ru.sbertroika.pasiv.gate.output.dadata.model.response

import com.google.gson.annotations.SerializedName

data class OrganizationSuggestionResponse (
    @SerializedName("suggestions" ) var suggestions : ArrayList<Suggestions> = arrayListOf()
)

data class Management (
    @SerializedName("name"         ) var name         : String? = null,
    @SerializedName("post"         ) var post         : String? = null,
    @SerializedName("disqualified" ) var disqualified : String? = null
)

data class Fio (
    @SerializedName("surname"    ) var surname    : String? = null,
    @SerializedName("name"       ) var name       : String? = null,
    @SerializedName("patronymic" ) var patronymic : String? = null,
    @SerializedName("gender"     ) var gender     : String? = null,
    @SerializedName("source"     ) var source     : String? = null,
    @SerializedName("qc"         ) var qc         : String? = null
)

data class Managers (
    @SerializedName("inn"        ) var inn        : String? = null,
    @SerializedName("fio"        ) var fio        : Fio?    = Fio(),
    @SerializedName("post"       ) var post       : String? = null,
    @SerializedName("hid"        ) var hid        : String? = null,
    @SerializedName("type"       ) var type       : String? = null,
    @SerializedName("invalidity" ) var invalidity : String? = null
)

data class Opf (
    @SerializedName("type"  ) var type  : String? = null,
    @SerializedName("code"  ) var code  : String? = null,
    @SerializedName("full"  ) var full  : String? = null,
    @SerializedName("short" ) var short : String? = null
)

data class Name (
    @SerializedName("full_with_opf"  ) var fullWithOpf  : String? = null,
    @SerializedName("short_with_opf" ) var shortWithOpf : String? = null,
    @SerializedName("latin"          ) var latin        : String? = null,
    @SerializedName("full"           ) var full         : String? = null,
    @SerializedName("short"          ) var short        : String? = null
)

data class Okveds (
    @SerializedName("main" ) var main : Boolean? = null,
    @SerializedName("type" ) var type : String?  = null,
    @SerializedName("code" ) var code : String?  = null,
    @SerializedName("name" ) var name : String?  = null
)

data class Metro (
    @SerializedName("name"     ) var name     : String? = null,
    @SerializedName("line"     ) var line     : String? = null,
    @SerializedName("distance" ) var distance : Double? = null
)

data class AddressData (
    @SerializedName("postal_code"             ) var postalCode           : String?          = null,
    @SerializedName("country"                 ) var country              : String?          = null,
    @SerializedName("country_iso_code"        ) var countryIsoCode       : String?          = null,
    @SerializedName("federal_district"        ) var federalDistrict      : String?          = null,
    @SerializedName("region_fias_id"          ) var regionFiasId         : String?          = null,
    @SerializedName("region_kladr_id"         ) var regionKladrId        : String?          = null,
    @SerializedName("region_iso_code"         ) var regionIsoCode        : String?          = null,
    @SerializedName("region_with_type"        ) var regionWithType       : String?          = null,
    @SerializedName("region_type"             ) var regionType           : String?          = null,
    @SerializedName("region_type_full"        ) var regionTypeFull       : String?          = null,
    @SerializedName("region"                  ) var region               : String?          = null,
    @SerializedName("area_fias_id"            ) var areaFiasId           : String?          = null,
    @SerializedName("area_kladr_id"           ) var areaKladrId          : String?          = null,
    @SerializedName("area_with_type"          ) var areaWithType         : String?          = null,
    @SerializedName("area_type"               ) var areaType             : String?          = null,
    @SerializedName("area_type_full"          ) var areaTypeFull         : String?          = null,
    @SerializedName("area"                    ) var area                 : String?          = null,
    @SerializedName("city_fias_id"            ) var cityFiasId           : String?          = null,
    @SerializedName("city_kladr_id"           ) var cityKladrId          : String?          = null,
    @SerializedName("city_with_type"          ) var cityWithType         : String?          = null,
    @SerializedName("city_type"               ) var cityType             : String?          = null,
    @SerializedName("city_type_full"          ) var cityTypeFull         : String?          = null,
    @SerializedName("city"                    ) var city                 : String?          = null,
    @SerializedName("city_area"               ) var cityArea             : String?          = null,
    @SerializedName("city_district_fias_id"   ) var cityDistrictFiasId   : String?          = null,
    @SerializedName("city_district_kladr_id"  ) var cityDistrictKladrId  : String?          = null,
    @SerializedName("city_district_with_type" ) var cityDistrictWithType : String?          = null,
    @SerializedName("city_district_type"      ) var cityDistrictType     : String?          = null,
    @SerializedName("city_district_type_full" ) var cityDistrictTypeFull : String?          = null,
    @SerializedName("city_district"           ) var cityDistrict         : String?          = null,
    @SerializedName("settlement_fias_id"      ) var settlementFiasId     : String?          = null,
    @SerializedName("settlement_kladr_id"     ) var settlementKladrId    : String?          = null,
    @SerializedName("settlement_with_type"    ) var settlementWithType   : String?          = null,
    @SerializedName("settlement_type"         ) var settlementType       : String?          = null,
    @SerializedName("settlement_type_full"    ) var settlementTypeFull   : String?          = null,
    @SerializedName("settlement"              ) var settlement           : String?          = null,
    @SerializedName("street_fias_id"          ) var streetFiasId         : String?          = null,
    @SerializedName("street_kladr_id"         ) var streetKladrId        : String?          = null,
    @SerializedName("street_with_type"        ) var streetWithType       : String?          = null,
    @SerializedName("street_type"             ) var streetType           : String?          = null,
    @SerializedName("street_type_full"        ) var streetTypeFull       : String?          = null,
    @SerializedName("street"                  ) var street               : String?          = null,
    @SerializedName("stead_fias_id"           ) var steadFiasId          : String?          = null,
    @SerializedName("stead_cadnum"            ) var steadCadnum          : String?          = null,
    @SerializedName("stead_type"              ) var steadType            : String?          = null,
    @SerializedName("stead_type_full"         ) var steadTypeFull        : String?          = null,
    @SerializedName("stead"                   ) var stead                : String?          = null,
    @SerializedName("house_fias_id"           ) var houseFiasId          : String?          = null,
    @SerializedName("house_kladr_id"          ) var houseKladrId         : String?          = null,
    @SerializedName("house_cadnum"            ) var houseCadnum          : String?          = null,
    @SerializedName("house_type"              ) var houseType            : String?          = null,
    @SerializedName("house_type_full"         ) var houseTypeFull        : String?          = null,
    @SerializedName("house"                   ) var house                : String?          = null,
    @SerializedName("block_type"              ) var blockType            : String?          = null,
    @SerializedName("block_type_full"         ) var blockTypeFull        : String?          = null,
    @SerializedName("block"                   ) var block                : String?          = null,
    @SerializedName("entrance"                ) var entrance             : String?          = null,
    @SerializedName("floor"                   ) var floor                : String?          = null,
    @SerializedName("flat_fias_id"            ) var flatFiasId           : String?          = null,
    @SerializedName("flat_cadnum"             ) var flatCadnum           : String?          = null,
    @SerializedName("flat_type"               ) var flatType             : String?          = null,
    @SerializedName("flat_type_full"          ) var flatTypeFull         : String?          = null,
    @SerializedName("flat"                    ) var flat                 : String?          = null,
    @SerializedName("flat_area"               ) var flatArea             : String?          = null,
    @SerializedName("square_meter_price"      ) var squareMeterPrice     : String?          = null,
    @SerializedName("flat_price"              ) var flatPrice            : String?          = null,
    @SerializedName("room_fias_id"            ) var roomFiasId           : String?          = null,
    @SerializedName("room_cadnum"             ) var roomCadnum           : String?          = null,
    @SerializedName("room_type"               ) var roomType             : String?          = null,
    @SerializedName("room_type_full"          ) var roomTypeFull         : String?          = null,
    @SerializedName("room"                    ) var room                 : String?          = null,
    @SerializedName("postal_box"              ) var postalBox            : String?          = null,
    @SerializedName("fias_id"                 ) var fiasId               : String?          = null,
    @SerializedName("fias_code"               ) var fiasCode             : String?          = null,
    @SerializedName("fias_level"              ) var fiasLevel            : String?          = null,
    @SerializedName("fias_actuality_state"    ) var fiasActualityState   : String?          = null,
    @SerializedName("kladr_id"                ) var kladrId              : String?          = null,
    @SerializedName("geoname_id"              ) var geonameId            : String?          = null,
    @SerializedName("capital_marker"          ) var capitalMarker        : String?          = null,
    @SerializedName("okato"                   ) var okato                : String?          = null,
    @SerializedName("oktmo"                   ) var oktmo                : String?          = null,
    @SerializedName("tax_office"              ) var taxOffice            : String?          = null,
    @SerializedName("tax_office_legal"        ) var taxOfficeLegal       : String?          = null,
    @SerializedName("timezone"                ) var timezone             : String?          = null,
    @SerializedName("geo_lat"                 ) var geoLat               : String?          = null,
    @SerializedName("geo_lon"                 ) var geoLon               : String?          = null,
    @SerializedName("beltway_hit"             ) var beltwayHit           : String?          = null,
    @SerializedName("beltway_distance"        ) var beltwayDistance      : String?          = null,
    @SerializedName("metro"                   ) var metro                : ArrayList<Metro> = arrayListOf(),
    @SerializedName("divisions"               ) var divisions            : String?          = null,
    @SerializedName("qc_geo"                  ) var qcGeo                : String?          = null,
    @SerializedName("qc_complete"             ) var qcComplete           : String?          = null,
    @SerializedName("qc_house"                ) var qcHouse              : String?          = null,
    @SerializedName("history_values"          ) var historyValues        : String?          = null,
    @SerializedName("unparsed_parts"          ) var unparsedParts        : String?          = null,
    @SerializedName("source"                  ) var source               : String?          = null,
    @SerializedName("qc"                      ) var qc                   : String?          = null
)

data class Address (
    @SerializedName("value"              ) var value             : String? = null,
    @SerializedName("unrestricted_value" ) var unrestrictedValue : String? = null,
    @SerializedName("invalidity"         ) var invalidity        : String? = null,
    @SerializedName("data"               ) var data              : AddressData?   = AddressData()
)

data class Emails (
    @SerializedName("value"              ) var value             : String? = null,
)

data class OrgData (
    @SerializedName("kpp"            ) var kpp           : String?             = null,
    @SerializedName("management"     ) var management    : Management?         = Management(),
    @SerializedName("managers"       ) var managers      : ArrayList<Managers> = arrayListOf(),
    @SerializedName("predecessors"   ) var predecessors  : String?             = null,
    @SerializedName("successors"     ) var successors    : String?             = null,
    @SerializedName("branch_type"    ) var branchType    : String?             = null,
    @SerializedName("branch_count"   ) var branchCount   : Int?                = null,
    @SerializedName("source"         ) var source        : String?             = null,
    @SerializedName("qc"             ) var qc            : String?             = null,
    @SerializedName("hid"            ) var hid           : String?             = null,
    @SerializedName("type"           ) var type          : String?             = null,
    @SerializedName("opf"            ) var opf           : Opf?                = Opf(),
    @SerializedName("name"           ) var name          : Name?               = Name(),
    @SerializedName("inn"            ) var inn           : String?             = null,
    @SerializedName("ogrn"           ) var ogrn          : String?             = null,
    @SerializedName("okpo"           ) var okpo          : String?             = null,
    @SerializedName("okato"          ) var okato         : String?             = null,
    @SerializedName("oktmo"          ) var oktmo         : String?             = null,
    @SerializedName("okogu"          ) var okogu         : String?             = null,
    @SerializedName("okfs"           ) var okfs          : String?             = null,
    @SerializedName("okved"          ) var okved         : String?             = null,
    @SerializedName("okveds"         ) var okveds        : ArrayList<Okveds>   = arrayListOf(),
    @SerializedName("address"        ) var address       : Address?            = Address(),
    @SerializedName("phones"         ) var phones        : MutableList<Phones>?   = mutableListOf(),
    @SerializedName("emails"         ) var emails        : MutableList<Emails>?   = mutableListOf(),
    @SerializedName("ogrn_date"      ) var ogrnDate      : Long?                = null,
    @SerializedName("okved_type"     ) var okvedType     : String?             = null,
    @SerializedName("employee_count" ) var employeeCount : Int?                = null
)

data class Phones(
    @SerializedName("value") var value     : String?             = null,
)

data class Suggestions (
    @SerializedName("data") var data        : OrgData?   = OrgData()
)

