package ru.sbertroika.pasiv.gate.output.model

import org.springframework.data.convert.WritingConverter
import org.springframework.data.r2dbc.convert.EnumWriteSupport
import org.springframework.data.relational.core.mapping.Column
import ru.sbertroika.history.api.*
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class ContactPK(
    val id: UUID? = null,
    val version: Int? = null
): Serializable
data class Contact(

    @HistoryId
    @Column("c_id")
    var id: UUID? = null,

    @HistoryVersion
    @Column("c_version")
    var version: Int? = null,

    @HistoryVersionAt
    @Column("c_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @HistoryVersionBy
    @Column("c_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("c_organization_id")
    var organizationId: UUID? = null,

    @Column("c_type")
    var type :ContactType? = null,

    @Column("c_value")
    var value :String? = null,

    @HistoryStatus
    @Column("c_is_deleted")
    var isDeleted:Boolean = false
)

enum class ContactType{
    EMAIL, PHONE
}



@WritingConverter
class ContactTypeConverter: EnumWriteSupport<ContactType>()
