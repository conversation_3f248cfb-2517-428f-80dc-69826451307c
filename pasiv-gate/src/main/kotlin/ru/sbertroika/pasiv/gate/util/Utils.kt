package ru.sbertroika.pasiv.gate.util

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.google.protobuf.Timestamp
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.coroutines.CoroutineContext

val DT_FORMAT: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")

fun mapper() = ObjectMapper().configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true)
    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    .registerKotlinModule()

fun <E : CoroutineContext.Element> CoroutineContext.param(key: CoroutineContext.Key<E>): Either<Error, E> {
    return this[key]?.right() ?: Error().left()
}

inline fun <T, R> T?.notNull(block: (T) -> R): Either<Error, R> = if (this != null) block(this).right() else Error().left()

fun calcTotalPage(totalElements: Int, limit: Int) = if (totalElements % limit == 0) totalElements / limit else totalElements / limit + 1


fun Timestamp.toZonedDateTime(): ZonedDateTime {
    val timestamp = java.sql.Timestamp(this.seconds * 1000)
    timestamp.nanos = this.nanos
    return ZonedDateTime.ofInstant(timestamp.toInstant(), ZoneId.of("UTC"))
}

fun ZonedDateTime.toTimestamp(): Timestamp = Timestamp.newBuilder()
    .setSeconds(this.toEpochSecond())
    .setNanos(this.nano)
    .build()

