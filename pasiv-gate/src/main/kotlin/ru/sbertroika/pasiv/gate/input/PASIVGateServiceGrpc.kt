package ru.sbertroika.pasiv.gate.input

import org.lognet.springboot.grpc.GRpcService
import ru.sbertroika.common.manifest.v1.ManifestRequest
import ru.sbertroika.common.toOperationError
import ru.sbertroika.pasiv.gate.output.service.ManifestService
import ru.sbertroika.pasiv.gate.v1.ManifestResponse
import ru.sbertroika.pasiv.gate.v1.PASIVGateServiceGrpcKt
import ru.sbertroika.pasiv.gate.v1.manifestResponse

@GRpcService
class PASIVGateServiceGrpc(
    private val manifestService: ManifestService
) : PASIVGateServiceGrpcKt.PASIVGateServiceCoroutineImplBase() {

    override suspend fun getManifest(request: ManifestRequest): ManifestResponse {
        return manifestService.getManifest(request).fold(
            {
                manifestResponse {
                    error = toOperationError(Error(it))
                }
            },
            { res ->
                manifestResponse {
                    manifest = res
                }
            }
        )
    }
}