package ru.sbertroika.pasiv.gate.output.dadata

import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Component
import ru.sbertroika.pasiv.gate.output.repository.DaDataValueRepository

@Component
class TokenStorageImpl(
    private val repository: DaDataValueRepository
): TokenStorage {
    override fun get(key: String): String = runBlocking {
        return@runBlocking repository.findById(key)?.value!!
    }
}