package ru.sbertroika.pasiv.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.pasiv.gate.output.model.Address
import ru.sbertroika.pasiv.gate.output.model.AddressPK
import ru.sbertroika.pasiv.gate.util.timestampNow
import ru.sbertroika.pasiv.gate.v1.AddressFilter
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*
import java.util.stream.Collectors

@Repository
class AddressRepository (
    override val dbClient: DatabaseClient,
    override val repository: AddressCrudRepository
): AbstractRepository<Address, AddressPK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM active_address o WHERE  o.a_is_deleted=false"

    override fun toEntity(t: Readable) = Address(
        id  = t.get("a_id") as UUID,
        version = t.get("a_version") as Int,
        versionCreatedAt = Timestamp.valueOf(t.get("a_version_created_at") as LocalDateTime),
        versionCreatedBy = t.get("a_version_created_by") as UUID,
        name = t.get("a_name") as String?,
        index = t.get("a_index") as Int?,
        country = t.get("a_country") as String?,
        region = t.get("a_region") as String?,
        district = t.get("a_district") as String?,
        city = t.get("a_city") as String?,
        street = t.get("a_street") as String?,
        comment = t.get("a_comment") as String?,
        house = t.get("a_house") as String?,
        buildingOrHousing = t.get("a_building_or_housing") as String?,
        oktmo = try {t.get("a_oktmo") as Long?} catch (e: Exception) {null},
        fiac = t.get("a_fiac") as String?,
        officeOrRoom = t.get("a_office_or_room") as String?,
        longitude = try {t.get("a_longitude") as Double?}  catch (e: Exception) {null} ,
        latitude = try {t.get("a_latitude") as Double?}  catch (e: Exception) {null},
        isDeleted = t.get("a_is_deleted") as Boolean
    )

    override suspend fun findById(id: String): Address? {
        return dbClient.sql("${getQuery()} AND  o.a_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    suspend fun findDeletedById(id: String): Address? {
        return dbClient.sql("SELECT * FROM active_address o WHERE o.a_is_deleted=true AND o.a_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    suspend fun findByIdAndVersion(id: String, version: Long): Address? {
        return dbClient.sql("select  * from  address o WHERE o.a_id = '$id' AND o.a_version = $version")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Address> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    fun findWhereIdIN(ids: List<UUID>): Flow<Address> {
        val idsStr ="'" + ids.stream().map { it.toString() }.collect(Collectors.joining("', '")) + "'"
        return dbClient.sql("${getQuery()} AND o.a_id IN ($idsStr)")
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Address> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    suspend fun getHistory(id: String): Flow<Address> {
        return dbClient.sql("select * from address where a_id = '$id'")
            .map(::toEntity).flow()
    }

    suspend fun getHistoryCount(id: String):Int {
        return dbClient.sql("select count(*) from address where a_id = '$id'").map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    suspend fun getHistory(id: String, offset: Int, limit: Int): Flow<Address> {
        return dbClient.sql("select * from address where a_id = '$id' OFFSET $offset LIMIT $limit")
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && !entity.isDeleted) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                isDeleted = true
            ))
        }
    }


    suspend fun findAll(filter: AddressFilter, page: Int, limit: Int): Flow<Address> {
        val query = "${buildRequestByFilter(filter)} OFFSET ${page * limit} LIMIT $limit"
        return dbClient.sql(query).map(::toEntity).flow()
    }

    suspend fun findAll(filter: AddressFilter): Flow<Address> {
        val query = buildRequestByFilter(filter)
        return dbClient.sql(query).map(::toEntity).flow()
    }

    suspend fun countAll(filter: AddressFilter): Int {
        val query = buildRequestByFilter(filter, true)
        return dbClient.sql(query).map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    private fun buildRequestByFilter(filter: AddressFilter, isCount: Boolean  = false): String {
        var query = if (filter.hasIsDeleted() && filter.isDeleted) {
            (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM active_address o WHERE o.a_is_deleted=true"
        } else getQuery(isCount)
        if(filter.hasCity() && !filter.city.isNullOrEmpty())
            query += " AND o.a_city LIKE '%${filter.city}%'"
        if(filter.hasStreet() && !filter.street.isNullOrEmpty())
            query += " AND o.a_street LIKE '%${filter.street}%'"
        return query
    }


}
