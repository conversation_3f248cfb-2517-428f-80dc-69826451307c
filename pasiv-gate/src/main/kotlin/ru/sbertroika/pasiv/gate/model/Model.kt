package ru.sbertroika.pasiv.gate.model

import ru.sbertroika.common.Pagination

data class Organization(
    var inn: String? = null,
    var oParentId: String? = null,
    var oName: String? = null,
    var oShortName: String? = null,
    var oManager: String? = null,
    var oManagerReason: String? = null,
    var oOgrn: String? = null,
    var oKpp: String? = null
)

data class OrganizationFilter(
    val inn: String? = null,
    val parentId: String? = null,
    val oName: String? = null,
    val ogrn: String? = null,
    val kpp: String? = null
)

data class OrganizationListResult(
    val result: List<Organization>,
    val pagination: Pagination
)