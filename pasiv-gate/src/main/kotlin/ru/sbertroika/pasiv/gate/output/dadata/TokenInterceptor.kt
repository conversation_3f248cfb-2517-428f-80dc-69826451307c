package ru.sbertroika.pasiv.gate.output.dadata

import okhttp3.Interceptor
import okhttp3.Response

class TokenInterceptor(private val tokenStorage: TokenStorage): Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val requestBuilder = chain.request().newBuilder()
        val token = tokenStorage.get("MAIN")
        token.let {
            requestBuilder.addHeader("Authorization", "Token $token")
        }
        return chain.proceed(requestBuilder.build())
    }
}