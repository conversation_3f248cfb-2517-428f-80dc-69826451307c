version: '3.7'

services:
  pasiv_gate:
    build: .
    container_name: pasiv_gate
    ports:
      - 5011:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro

  postgresql:
    image: docker.io/bitnami/postgresql:latest
    ports:
      - 5432:5432
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - POSTGRESQL_USERNAME=postgres
      - POSTGRESQL_DATABASE=pasiv
      - POSTGRESQL_PASSWORD=postgres
