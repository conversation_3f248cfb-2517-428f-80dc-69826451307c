stages:
  - aie
  - build
  - testing
  - deploy
  - deploy-prod

variables:
  DOCKER_REPOSITORY_ADDR: $DOCKER_REPOSITORY_ADDR
  GIT_SUBMODULE_STRATEGY: recursive
  KUBEVALURL: "https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz"

aie:
  stage: aie
  rules:
    - if: '$AIE_ENABLE == "true"'
      when: always
    - when: never
  tags:
    - docker
  script:
    - docker run --rm -v $(pwd):/app:rw -u $(id -u):$(id -g) -v /var/aisa:/aisa:rw --workdir /app aisa-linux:latest aisa --project-settings-file $CI_PROJECT_NAME.aiproj --scan-target ./ --reports "HTML,JSON" --reports-folder ".report"
  artifacts:
    expire_in: 14 day
    paths:
      - .report/

#Сборка контейнера приложения
build:
  stage: build
  dependencies:
    - aie
  when: on_success
  tags:
    - docker
  script:
    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi

    # Сборка и пуш образа в репозиторий Docker
    - docker build
      -t $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$TAG
      --pull -f Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$TAG
    #- docker image rm $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$CI_COMMIT_SHORT_SHA
  rules:
    #Запускаем сборку при коммите в dev, release/*  hotfix/* ветки
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

#Установка HELM манифестов
deploy_chart:
  stage: deploy
  dependencies:
    - build
  when: on_success
  tags:
    - docker
  script:

    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi
    # Деплой Helm-чарта с установкой образа и его параметрами
    - mkdir .kube
    - cat $KUBECONFIG > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $CI_PROJECT_NAME /apps/charts/$CI_PROJECT_NAME
      --install
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

#Установка HELM манифестов
deploy_chart_prod:
  stage: deploy-prod
  dependencies:
    - build
  when: manual
  tags:
    - docker
  script:

    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi
    # Деплой Helm-чарта с установкой образа и его параметрами
    - mkdir .kube
    - cat $KUBECONFIG_PROD > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $CI_PROJECT_NAME /apps/charts/$CI_PROJECT_NAME
      --install
      --create-namespace
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --set env.keycloak.realm.url=$KEYCLOAK_REALM_URL_PROD
      --set env.keycloak.client_id=$KEYCLOAK_CLIENT_ID_PROD
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'
