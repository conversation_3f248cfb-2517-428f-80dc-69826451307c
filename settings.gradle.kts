rootProject.name = "pasive-domain"

include(
    ":pasiv-gate",
    ":pasiv-api-private",
    ":pasiv-api",
    ":pasiv-gate-private",
    ":pasiv-ui"
    )

pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
            credentials {
                username = providers.gradleProperty("mavenUser").orNull ?: System.getenv("MAVEN_USER")
                password = providers.gradleProperty("mavenPassword").orNull ?: System.getenv("MAVEN_PASSWORD")
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").orNull ?: System.getenv("MAVEN_USER")
                password = providers.gradleProperty("mavenPassword").orNull ?: System.getenv("MAVEN_PASSWORD")
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").orNull ?: System.getenv("MAVEN_USER")
                password = providers.gradleProperty("mavenPassword").orNull ?: System.getenv("MAVEN_PASSWORD")
            }
        }
    }

    plugins {
        id("org.jetbrains.kotlin.jvm") version "1.9.0"
        id("org.jetbrains.kotlin.plugin.spring") version "1.9.0"
        id("com.google.protobuf") version "0.9.4"
        id("net.researchgate.release") version "2.8.1"
    }
}

dependencyResolutionManagement {
    repositories {
        gradlePluginPortal()
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }

    versionCatalogs {
        create("libs") {
            from("ru.sbertroika.sharedcatalog:gradle-common:1.3")
        }
    }
}