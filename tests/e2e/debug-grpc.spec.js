const { test, expect } = require('@playwright/test');

test.describe('Debug gRPC Issues', () => {
    test('should check for gRPC errors in console', async ({ page }) => {
        const consoleMessages = [];
        const errors = [];

        // Перехватываем все сообщения консоли
        page.on('console', msg => {
            consoleMessages.push({
                type: msg.type(),
                text: msg.text()
            });
            console.log(`Console ${msg.type()}: ${msg.text()}`);
        });

        // Перехватываем все ошибки
        page.on('pageerror', error => {
            errors.push(error.message);
            console.log(`Page error: ${error.message}`);
        });

        // Переходим на главную страницу
        await page.goto('http://localhost:5175');

        // Ждем немного для загрузки
        await page.waitForTimeout(3000);

        // Проверяем, что нет ошибок связанных с gRPC
        const grpcErrors = errors.filter(error => 
            error.includes('UNARY') || 
            error.includes('_container') || 
            error.includes('require is not defined') ||
            error.includes('jspb') ||
            error.includes('goog')
        );

        console.log('All console messages:', consoleMessages);
        console.log('All errors:', errors);
        console.log('gRPC related errors:', grpcErrors);

        // Проверяем, что нет gRPC ошибок
        expect(grpcErrors).toHaveLength(0);

        // Проверяем, что polyfill инициализировался
        const polyfillMessages = consoleMessages.filter(msg => 
            msg.text.includes('gRPC polyfill initialized')
        );
        expect(polyfillMessages.length).toBeGreaterThan(0);
    });

    test('should check grpc objects availability', async ({ page }) => {
        await page.goto('http://localhost:5175');
        await page.waitForTimeout(2000);

        // Проверяем доступность grpc объектов
        const grpcCheck = await page.evaluate(() => {
            return {
                grpcExists: typeof window.grpc !== 'undefined',
                grpcWebExists: typeof window.grpc?.web !== 'undefined',
                unaryExists: typeof window.grpc?.web?.MethodType?.UNARY !== 'undefined',
                requireExists: typeof window.require !== 'undefined',
                jspbExists: typeof window.jspb !== 'undefined',
                googExists: typeof window.goog !== 'undefined'
            };
        });

        console.log('gRPC objects check:', grpcCheck);

        expect(grpcCheck.grpcExists).toBe(true);
        expect(grpcCheck.grpcWebExists).toBe(true);
        expect(grpcCheck.unaryExists).toBe(true);
        expect(grpcCheck.requireExists).toBe(true);
        expect(grpcCheck.jspbExists).toBe(true);
        expect(grpcCheck.googExists).toBe(true);
    });
});
