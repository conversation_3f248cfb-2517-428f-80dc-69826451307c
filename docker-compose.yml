version: '3.7'

services:
  pasiv_gate:
    build:
      context: .
      dockerfile: pasiv-gate/Dockerfile
      args:
        MAVEN_USER: ${MAVEN_USER}
        MAVEN_PASSWORD: ${MAVEN_PASSWORD}
    container_name: pasiv_gate
    ports:
      - 5011:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - pasiv_db

  pasiv_db:
    image: docker.io/bitnami/postgresql:latest
    container_name: pasiv-db
    ports:
      - 5432:5432
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - POSTGRESQL_USERNAME=pasiv
      - POSTGRESQL_DATABASE=pasiv
      - POSTGRESQL_PASSWORD=pasiv
