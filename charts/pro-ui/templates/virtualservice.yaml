kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: pro-ui
  namespace: pro
spec:
  hosts:
    - crm-pro-dev.sbertroika.tech
  gateways:
    - istio-ingressgateway/pro-ui-gateway
  http:
    - match:
        - uri:
            prefix: /api/
      route:
        - destination:
            host: pro-gate-private.pro.svc.cluster.local
            port:
              number: 5005
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: pro-ui.pro.svc.cluster.local
            port:
              number: 80
status: {}
