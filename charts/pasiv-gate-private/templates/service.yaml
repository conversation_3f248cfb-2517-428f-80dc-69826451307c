apiVersion: v1
kind: Service
metadata:
  name: {{ include "pasiv-gate-private.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "pasiv-gate-private.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
  selector:
    {{- include "pasiv-gate-private.selectorLabels" . | nindent 4 }}