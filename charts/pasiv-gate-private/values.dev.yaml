# Default values for pasiv-gate-private.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: pasiv-gate-private
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"


resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi


env:
  profile: "all,log"
  client:
    logging:
      enable: true
  db:
    migration:
      enable: true
  keycloak:
    client_id: "test-auth"
    realm:
      url: "https://dev-auth.sbertroika.tech/realms/test-asop"
  tms_gate_service: "tms-gate.tms.svc.cluster.local:5005"
