# Default values for pasiv-gate.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: pasiv-gate
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "prod"


resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi


env:
  profile: "all,log"
  client:
    logging:
      enable: true
  db:
    migration:
      enable: true
  keycloak:
    client_id:
    realm:
      url:
  tms_gate_service:
