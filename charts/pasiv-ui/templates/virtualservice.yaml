kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: pasiv-ui
  namespace: pasiv
spec:
  hosts:
    - crm-pasiv-dev.sbertroika.tech
  gateways:
    - istio-ingressgateway/pasiv-ui-gateway
  http:
    - match:
        - uri:
            prefix: /api/
      route:
        - destination:
            host: pasiv-gate-private.pasiv.svc.cluster.local
            port:
              number: 5005
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: pasiv-ui.pasiv.svc.cluster.local
            port:
              number: 80
status: {}
