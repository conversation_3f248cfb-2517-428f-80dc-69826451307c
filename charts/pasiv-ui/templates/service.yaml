apiVersion: v1
kind: Service
metadata:
  name: {{ include "pasiv-ui.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "pasiv-ui.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
  selector:
    {{- include "pasiv-ui.selectorLabels" . | nindent 4 }}
