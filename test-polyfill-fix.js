/**
 * Тест для проверки исправления polyfill
 * Проверяет, что OrganizationListResponse.deserializeBinary доступен
 */

// Имитируем браузерную среду
global.globalThis = global;
global.window = global;

// Загружаем polyfill (копируем логику из TypeScript файла)
// Поскольку Node.js не может загрузить .ts файл напрямую, воспроизводим логику polyfill

// Создаем глобальные объекты
if (typeof globalThis.require === 'undefined') {
  console.log('✅ Creating new require function');
  const moduleCache = {};

  globalThis.require = function(moduleName) {
    if (moduleCache[moduleName]) {
      return moduleCache[moduleName];
    }

    let moduleExports = {};

    switch (moduleName) {
      case './pasiv-gate-private_pb.js':
      case 'pasiv-gate-private_pb':
        moduleExports = {
          Organization: class Organization {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          OrganizationListRequest: class OrganizationListRequest {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          OrganizationListResponse: class OrganizationListResponse {
            constructor() {}
            static deserializeBinary(bytes) {
              console.log('🔍 OrganizationListResponse.deserializeBinary called');
              return new this();
            }
            serializeBinary() { return new Uint8Array(); }
          },
          OrganizationResponse: class OrganizationResponse {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          ByIdRequest: class ByIdRequest {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          AddressResponse: class AddressResponse {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          AddressListResponse: class AddressListResponse {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          ContactResponse: class ContactResponse {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          ContactListResponse: class ContactListResponse {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          ContractResponse: class ContractResponse {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          },
          ContractListResponse: class ContractListResponse {
            constructor() {}
            static deserializeBinary(bytes) { return new this(); }
            serializeBinary() { return new Uint8Array(); }
          }
        };
        break;
      default:
        moduleExports = {};
        break;
    }

    moduleCache[moduleName] = moduleExports;
    return moduleExports;
  };
}

console.log('🧪 Тестирование polyfill исправления...');

// Проверяем, что require функция создана
console.log('✅ require функция доступна:', typeof globalThis.require === 'function');

// Тестируем загрузку pasiv-gate-private_pb модуля
try {
  const pasivPb = globalThis.require('./pasiv-gate-private_pb.js');
  console.log('✅ pasiv-gate-private_pb модуль загружен');
  
  // Проверяем основные классы
  const tests = [
    'Organization',
    'OrganizationListRequest', 
    'OrganizationListResponse',
    'OrganizationResponse',
    'ByIdRequest',
    'AddressResponse',
    'AddressListResponse',
    'ContactResponse',
    'ContactListResponse',
    'ContractResponse',
    'ContractListResponse'
  ];
  
  console.log('\n📋 Проверка классов:');
  tests.forEach(className => {
    const classExists = !!pasivPb[className];
    const hasDeserialize = pasivPb[className] && typeof pasivPb[className].deserializeBinary === 'function';

    const status = classExists && hasDeserialize ? '✅' : '❌';
    console.log(`${status} ${className}: exists=${classExists}, deserializeBinary=${hasDeserialize}`);

    if (classExists && hasDeserialize) {
      // Тестируем вызов deserializeBinary
      try {
        const instance = pasivPb[className].deserializeBinary(new Uint8Array());
        console.log(`  ✅ ${className}.deserializeBinary() работает`);
      } catch (error) {
        console.log(`  ❌ ${className}.deserializeBinary() ошибка:`, error.message);
      }
    }
  });
  
  // Проверяем proto объект
  console.log('\n📋 Проверка proto объекта:');
  if (globalThis.proto && globalThis.proto.ru && globalThis.proto.ru.sbertroika &&
      globalThis.proto.ru.sbertroika.pasiv && globalThis.proto.ru.sbertroika.pasiv.gate &&
      globalThis.proto.ru.sbertroika.pasiv.gate.v1) {
    const protoV1 = globalThis.proto.ru.sbertroika.pasiv.gate.v1;
    
    tests.forEach(className => {
      const classExists = !!protoV1[className];
      const hasDeserialize = protoV1[className] && typeof protoV1[className].deserializeBinary === 'function';

      const status = classExists && hasDeserialize ? '✅' : '❌';
      console.log(`${status} proto.ru.sbertroika.pasiv.gate.v1.${className}: exists=${classExists}, deserializeBinary=${hasDeserialize}`);
    });
  } else {
    console.log('❌ proto.ru.sbertroika.pasiv.gate.v1 не найден');
  }
  
  console.log('\n🎯 Специальная проверка OrganizationListResponse:');
  const orgListResponse = pasivPb.OrganizationListResponse;
  if (orgListResponse && typeof orgListResponse.deserializeBinary === 'function') {
    console.log('✅ OrganizationListResponse.deserializeBinary найден в модуле');
    
    // Проверяем в proto объекте
    const protoOrgListResponse = globalThis.proto && globalThis.proto.ru && globalThis.proto.ru.sbertroika &&
                                globalThis.proto.ru.sbertroika.pasiv && globalThis.proto.ru.sbertroika.pasiv.gate &&
                                globalThis.proto.ru.sbertroika.pasiv.gate.v1 && globalThis.proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse;
    if (protoOrgListResponse && typeof protoOrgListResponse.deserializeBinary === 'function') {
      console.log('✅ proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinary найден');
      console.log('🎉 ИСПРАВЛЕНИЕ УСПЕШНО! Ошибка должна быть устранена.');
    } else {
      console.log('❌ proto.ru.sbertroika.pasiv.gate.v1.OrganizationListResponse.deserializeBinary НЕ найден');
      console.log('⚠️ Возможно, нужно проверить настройку proto объекта');
    }
  } else {
    console.log('❌ OrganizationListResponse.deserializeBinary НЕ найден в модуле');
    console.log('❌ ИСПРАВЛЕНИЕ НЕ РАБОТАЕТ');
  }
  
} catch (error) {
  console.log('❌ Ошибка при загрузке pasiv-gate-private_pb:', error.message);
}

console.log('\n✅ Тест завершен');
