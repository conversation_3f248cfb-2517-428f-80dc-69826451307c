import net.researchgate.release.ReleaseExtension

plugins {
    kotlin("jvm") version libs.versions.kotlin.get() apply false
    kotlin("plugin.spring") version libs.versions.kotlin.get() apply false
    id("com.google.protobuf") version libs.versions.protobufPlugin.get() apply false
    id("net.researchgate.release") version libs.versions.releasePlugin.get()
}

group = "ru.sbertroika.pasiv"
version = rootProject.version

allprojects {
    repositories {
        mavenLocal()
        gradlePluginPortal()
        mavenCentral()
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
            credentials {
                username = (findProperty("mavenUser") ?: System.getenv("MAVEN_USER")) as String?
                password = (findProperty("mavenPassword") ?: System.getenv("MAVEN_PASSWORD")) as String?
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = (findProperty("mavenUser") ?: System.getenv("MAVEN_USER")) as String?
                password = (findProperty("mavenPassword") ?: System.getenv("MAVEN_PASSWORD")) as String?
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = (findProperty("mavenUser") ?: System.getenv("MAVEN_USER")) as String?
                password = (findProperty("mavenPassword") ?: System.getenv("MAVEN_PASSWORD")) as String?
            }
        }
    }
}

extra["version"] = version as String

configure<ReleaseExtension> {
    tagTemplate.set("v${version}")
    versionPropertyFile.set("gradle.properties")
    git {
        requireBranch.set("develop")
    }
}

tasks.named("afterReleaseBuild") {
    dependsOn(
        ":pasiv-gate:publish",
        ":pasiv-api-private:publish",
        ":pasiv-api:publish"
    )
}
